package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseAttachmentCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseAttachmentCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentRemovedEvent;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseAttachmentDomainConverterImpl implements TestcaseAttachmentDomainConverter {

    @Override
    public TestcaseAttachmentAddedEvent converter(AddTestcaseAttachmentCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseAttachmentAddedEvent testcaseAttachmentAddedEvent = new TestcaseAttachmentAddedEvent();

        testcaseAttachmentAddedEvent.setCode( command.getAggregateId() );
        testcaseAttachmentAddedEvent.setAggregateId( command.getAggregateId() );
        testcaseAttachmentAddedEvent.setTransactor( command.getTransactor() );
        testcaseAttachmentAddedEvent.setOccurred( command.getOccurred() );
        testcaseAttachmentAddedEvent.setDomain( command.getDomain() );
        testcaseAttachmentAddedEvent.setBusinessCode( command.getBusinessCode() );
        testcaseAttachmentAddedEvent.setUrl( command.getUrl() );
        testcaseAttachmentAddedEvent.setRemoteFileId( command.getRemoteFileId() );
        testcaseAttachmentAddedEvent.setName( command.getName() );
        testcaseAttachmentAddedEvent.setType( command.getType() );
        testcaseAttachmentAddedEvent.setDocumentType( command.getDocumentType() );
        testcaseAttachmentAddedEvent.setSize( command.getSize() );
        testcaseAttachmentAddedEvent.setFileType( command.getFileType() );

        return testcaseAttachmentAddedEvent;
    }

    @Override
    public TestcaseAttachmentRemovedEvent converter(RemoveTestcaseAttachmentCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseAttachmentRemovedEvent testcaseAttachmentRemovedEvent = new TestcaseAttachmentRemovedEvent();

        testcaseAttachmentRemovedEvent.setCode( command.getAggregateId() );
        testcaseAttachmentRemovedEvent.setAggregateId( command.getAggregateId() );
        testcaseAttachmentRemovedEvent.setTransactor( command.getTransactor() );
        testcaseAttachmentRemovedEvent.setOccurred( command.getOccurred() );

        return testcaseAttachmentRemovedEvent;
    }
}
