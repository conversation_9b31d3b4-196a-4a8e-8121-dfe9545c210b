package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.enums.report.AutoExecuteResult;
import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.devops.qc.client.enums.report.ReviewType;
import com.zto.devops.qc.client.model.dto.QcNoticeResultEntityDO;
import com.zto.devops.qc.client.model.dto.ReviewInfoEntityDO;
import com.zto.devops.qc.client.model.dto.TestFunctionPointEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.report.query.PageReportQuery;
import com.zto.devops.qc.client.model.rpc.user.UserSelectVO;
import com.zto.devops.qc.client.model.testPlan.entity.TestFunctionPointVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendExternalTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendMobileTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendReviewReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendSimpleTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendTmAccessTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendTmOnlineSmokeTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendTmPermitTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddExternalTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddMobileTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddReviewReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddSimpleTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddTmAccessReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddTmOnlineSmokeReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddTmPermitReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendExternalTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendMobileTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendReviewReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendSimpleTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendTmAccessTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendTmOnlineSmokeTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendTmPermitTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditExternalTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditMobileTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditReviewReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditSimpleTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditTmAccessReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditTmOnlineSmokeReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditTmPermitReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.entity.ExternalTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.MobileTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoDTO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoUserHandleVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewRenewalVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewUserVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.SimpleTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmAccessReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmPermitReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmSmokeReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.event.ExternalReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ExternalReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.MobileTestReportAddedEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.MobileTestReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ReviewReportAddedEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ReviewReportEditedEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.SimpleReportAddedEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.SimpleTestReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TestReportSendEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmAccessReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmAccessReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmOnlineSmokeReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmOnlineSmokeReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmPermitReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmPermitReportEditedEvent;
import com.zto.devops.qc.client.model.testmanager.report.query.PageReportMqQuery;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:05+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmTestReportDomainConverterImpl implements TmTestReportDomainConverter {

    @Override
    public TmAccessReportDetailVO convertAccess(TmTestReportEntityDO report) {
        if ( report == null ) {
            return null;
        }

        TmAccessReportDetailVO tmAccessReportDetailVO = new TmAccessReportDetailVO();

        tmAccessReportDetailVO.setReportCode( report.getReportCode() );
        tmAccessReportDetailVO.setReportName( report.getReportName() );
        tmAccessReportDetailVO.setPlanCode( report.getPlanCode() );
        tmAccessReportDetailVO.setVersionCode( report.getVersionCode() );
        tmAccessReportDetailVO.setVersionName( report.getVersionName() );
        tmAccessReportDetailVO.setSummary( report.getSummary() );
        tmAccessReportDetailVO.setReportType( report.getReportType() );
        tmAccessReportDetailVO.setTestResult( report.getTestResult() );
        tmAccessReportDetailVO.setUiTestResult( report.getUiTestResult() );
        tmAccessReportDetailVO.setActualPresentationDate( report.getActualPresentationDate() );

        return tmAccessReportDetailVO;
    }

    @Override
    public TmSmokeReportDetailVO convertSmoke(TmTestReportEntityDO report) {
        if ( report == null ) {
            return null;
        }

        TmSmokeReportDetailVO tmSmokeReportDetailVO = new TmSmokeReportDetailVO();

        tmSmokeReportDetailVO.setActualPublishDate( report.getActualOnlineDate() );
        tmSmokeReportDetailVO.setReportCode( report.getReportCode() );
        tmSmokeReportDetailVO.setReportName( report.getReportName() );
        tmSmokeReportDetailVO.setPlanCode( report.getPlanCode() );
        tmSmokeReportDetailVO.setVersionCode( report.getVersionCode() );
        tmSmokeReportDetailVO.setVersionName( report.getVersionName() );
        tmSmokeReportDetailVO.setSummary( report.getSummary() );
        tmSmokeReportDetailVO.setReportType( report.getReportType() );
        tmSmokeReportDetailVO.setTestResult( report.getTestResult() );
        tmSmokeReportDetailVO.setUiTestResult( report.getUiTestResult() );
        tmSmokeReportDetailVO.setZuiTestResult( report.getZuiTestResult() );
        tmSmokeReportDetailVO.setAsPlanedOnline( report.getAsPlanedOnline() );
        tmSmokeReportDetailVO.setDelay( report.getDelay() );

        return tmSmokeReportDetailVO;
    }

    @Override
    public TmPermitReportDetailVO convertPermit(TmTestReportEntityDO report) {
        if ( report == null ) {
            return null;
        }

        TmPermitReportDetailVO tmPermitReportDetailVO = new TmPermitReportDetailVO();

        tmPermitReportDetailVO.setReportCode( report.getReportCode() );
        tmPermitReportDetailVO.setReportName( report.getReportName() );
        tmPermitReportDetailVO.setPlanCode( report.getPlanCode() );
        tmPermitReportDetailVO.setVersionCode( report.getVersionCode() );
        tmPermitReportDetailVO.setVersionName( report.getVersionName() );
        tmPermitReportDetailVO.setSummary( report.getSummary() );
        tmPermitReportDetailVO.setReportType( report.getReportType() );
        tmPermitReportDetailVO.setTestResult( report.getTestResult() );
        tmPermitReportDetailVO.setUiTestResult( report.getUiTestResult() );
        tmPermitReportDetailVO.setZuiTestResult( report.getZuiTestResult() );
        tmPermitReportDetailVO.setActualApprovalExitDate( report.getActualApprovalExitDate() );

        return tmPermitReportDetailVO;
    }

    @Override
    public ExternalTestReportDetailVO convertExternalVO(TmTestReportEntityDO report) {
        if ( report == null ) {
            return null;
        }

        ExternalTestReportDetailVO externalTestReportDetailVO = new ExternalTestReportDetailVO();

        externalTestReportDetailVO.setReportUserId( report.getModifierId() );
        externalTestReportDetailVO.setReportUserName( report.getModifier() );
        externalTestReportDetailVO.setActualPublishDate( report.getActualOnlineDate() );
        externalTestReportDetailVO.setReportCode( report.getReportCode() );
        externalTestReportDetailVO.setReportName( report.getReportName() );
        externalTestReportDetailVO.setReportType( report.getReportType() );
        externalTestReportDetailVO.setPlanCode( report.getPlanCode() );
        externalTestReportDetailVO.setVersionCode( report.getVersionCode() );
        externalTestReportDetailVO.setVersionName( report.getVersionName() );
        externalTestReportDetailVO.setDeveloperCount( report.getDeveloperCount() );
        externalTestReportDetailVO.setTesterCount( report.getTesterCount() );
        externalTestReportDetailVO.setTestResult( report.getTestResult() );
        externalTestReportDetailVO.setPlanPresentationDate( report.getPlanPresentationDate() );
        externalTestReportDetailVO.setActualPresentationDate( report.getActualPresentationDate() );
        externalTestReportDetailVO.setPlanApprovalExitDate( report.getPlanApprovalExitDate() );
        externalTestReportDetailVO.setActualApprovalExitDate( report.getActualApprovalExitDate() );
        externalTestReportDetailVO.setPlanOnlineDate( report.getPlanOnlineDate() );
        externalTestReportDetailVO.setActualTestStart( report.getActualTestStart() );
        externalTestReportDetailVO.setActualTestEnd( report.getActualTestEnd() );
        externalTestReportDetailVO.setCheckStartDate( report.getCheckStartDate() );
        externalTestReportDetailVO.setCheckEndDate( report.getCheckEndDate() );
        externalTestReportDetailVO.setSummary( report.getSummary() );
        externalTestReportDetailVO.setAutoTestResult( autoExecuteResultToAutoExecuteResult( report.getAutoTestResult() ) );
        externalTestReportDetailVO.setSecurityUserId( report.getSecurityUserId() );
        externalTestReportDetailVO.setSecurityUserName( report.getSecurityUserName() );
        externalTestReportDetailVO.setSecurityTestResult( report.getSecurityTestResult() );
        externalTestReportDetailVO.setStatus( report.getStatus() );
        externalTestReportDetailVO.setDelay( report.getDelay() );
        externalTestReportDetailVO.setUiTestResult( report.getUiTestResult() );
        externalTestReportDetailVO.setZuiTestResult( report.getZuiTestResult() );

        return externalTestReportDetailVO;
    }

    @Override
    public TmAccessReportAddEvent converter(AddTmAccessReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmAccessReportAddEvent tmAccessReportAddEvent = new TmAccessReportAddEvent();

        tmAccessReportAddEvent.setAggregateId( command.getAggregateId() );
        tmAccessReportAddEvent.setTransactor( command.getTransactor() );
        tmAccessReportAddEvent.setOccurred( command.getOccurred() );
        tmAccessReportAddEvent.setReportCode( command.getReportCode() );
        tmAccessReportAddEvent.setReportName( command.getReportName() );
        tmAccessReportAddEvent.setPlanCode( command.getPlanCode() );
        tmAccessReportAddEvent.setPlanName( command.getPlanName() );
        tmAccessReportAddEvent.setReportType( command.getReportType() );
        tmAccessReportAddEvent.setVersionCode( command.getVersionCode() );
        tmAccessReportAddEvent.setVersionName( command.getVersionName() );
        tmAccessReportAddEvent.setProductCode( command.getProductCode() );
        tmAccessReportAddEvent.setProductName( command.getProductName() );
        tmAccessReportAddEvent.setTestResult( command.getTestResult() );
        tmAccessReportAddEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        tmAccessReportAddEvent.setActualPresentationDate( command.getActualPresentationDate() );
        tmAccessReportAddEvent.setSummary( command.getSummary() );
        tmAccessReportAddEvent.setStatus( command.getStatus() );
        tmAccessReportAddEvent.setPreview( command.getPreview() );

        return tmAccessReportAddEvent;
    }

    @Override
    public TmAccessReportEditEvent converter(EditTmAccessReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmAccessReportEditEvent tmAccessReportEditEvent = new TmAccessReportEditEvent();

        tmAccessReportEditEvent.setAggregateId( command.getAggregateId() );
        tmAccessReportEditEvent.setTransactor( command.getTransactor() );
        tmAccessReportEditEvent.setOccurred( command.getOccurred() );
        tmAccessReportEditEvent.setReportCode( command.getReportCode() );
        tmAccessReportEditEvent.setReportName( command.getReportName() );
        tmAccessReportEditEvent.setPlanCode( command.getPlanCode() );
        tmAccessReportEditEvent.setPlanName( command.getPlanName() );
        tmAccessReportEditEvent.setReportType( command.getReportType() );
        tmAccessReportEditEvent.setVersionCode( command.getVersionCode() );
        tmAccessReportEditEvent.setVersionName( command.getVersionName() );
        tmAccessReportEditEvent.setProductCode( command.getProductCode() );
        tmAccessReportEditEvent.setProductName( command.getProductName() );
        tmAccessReportEditEvent.setTestResult( command.getTestResult() );
        tmAccessReportEditEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        tmAccessReportEditEvent.setActualPresentationDate( command.getActualPresentationDate() );
        tmAccessReportEditEvent.setSummary( command.getSummary() );
        tmAccessReportEditEvent.setStatus( command.getStatus() );
        tmAccessReportEditEvent.setPreview( command.getPreview() );

        return tmAccessReportEditEvent;
    }

    @Override
    public TmOnlineSmokeReportAddEvent converter(AddTmOnlineSmokeReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmOnlineSmokeReportAddEvent tmOnlineSmokeReportAddEvent = new TmOnlineSmokeReportAddEvent();

        tmOnlineSmokeReportAddEvent.setActualOnlineDate( command.getActualPublishDate() );
        tmOnlineSmokeReportAddEvent.setAggregateId( command.getAggregateId() );
        tmOnlineSmokeReportAddEvent.setTransactor( command.getTransactor() );
        tmOnlineSmokeReportAddEvent.setOccurred( command.getOccurred() );
        tmOnlineSmokeReportAddEvent.setReportCode( command.getReportCode() );
        tmOnlineSmokeReportAddEvent.setReportName( command.getReportName() );
        tmOnlineSmokeReportAddEvent.setPlanCode( command.getPlanCode() );
        tmOnlineSmokeReportAddEvent.setPlanName( command.getPlanName() );
        tmOnlineSmokeReportAddEvent.setReportType( command.getReportType() );
        tmOnlineSmokeReportAddEvent.setVersionCode( command.getVersionCode() );
        tmOnlineSmokeReportAddEvent.setVersionName( command.getVersionName() );
        tmOnlineSmokeReportAddEvent.setProductCode( command.getProductCode() );
        tmOnlineSmokeReportAddEvent.setProductName( command.getProductName() );
        tmOnlineSmokeReportAddEvent.setTestResult( command.getTestResult() );
        tmOnlineSmokeReportAddEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        tmOnlineSmokeReportAddEvent.setDelay( command.getDelay() );
        tmOnlineSmokeReportAddEvent.setSummary( command.getSummary() );
        tmOnlineSmokeReportAddEvent.setStatus( command.getStatus() );
        tmOnlineSmokeReportAddEvent.setPreview( command.getPreview() );
        tmOnlineSmokeReportAddEvent.setZuiTestResult( command.getZuiTestResult() );

        return tmOnlineSmokeReportAddEvent;
    }

    @Override
    public TmOnlineSmokeReportEditEvent converter(EditTmOnlineSmokeReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmOnlineSmokeReportEditEvent tmOnlineSmokeReportEditEvent = new TmOnlineSmokeReportEditEvent();

        tmOnlineSmokeReportEditEvent.setActualOnlineDate( command.getActualPublishDate() );
        tmOnlineSmokeReportEditEvent.setAggregateId( command.getAggregateId() );
        tmOnlineSmokeReportEditEvent.setTransactor( command.getTransactor() );
        tmOnlineSmokeReportEditEvent.setOccurred( command.getOccurred() );
        tmOnlineSmokeReportEditEvent.setReportCode( command.getReportCode() );
        tmOnlineSmokeReportEditEvent.setReportName( command.getReportName() );
        tmOnlineSmokeReportEditEvent.setPlanCode( command.getPlanCode() );
        tmOnlineSmokeReportEditEvent.setPlanName( command.getPlanName() );
        tmOnlineSmokeReportEditEvent.setReportType( command.getReportType() );
        tmOnlineSmokeReportEditEvent.setVersionCode( command.getVersionCode() );
        tmOnlineSmokeReportEditEvent.setVersionName( command.getVersionName() );
        tmOnlineSmokeReportEditEvent.setProductCode( command.getProductCode() );
        tmOnlineSmokeReportEditEvent.setProductName( command.getProductName() );
        tmOnlineSmokeReportEditEvent.setTestResult( command.getTestResult() );
        tmOnlineSmokeReportEditEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        tmOnlineSmokeReportEditEvent.setDelay( command.getDelay() );
        tmOnlineSmokeReportEditEvent.setSummary( command.getSummary() );
        tmOnlineSmokeReportEditEvent.setStatus( command.getStatus() );
        tmOnlineSmokeReportEditEvent.setPreview( command.getPreview() );
        tmOnlineSmokeReportEditEvent.setZuiTestResult( command.getZuiTestResult() );

        return tmOnlineSmokeReportEditEvent;
    }

    @Override
    public ReviewReportAddedEvent convert(AddAndSendReviewReportCommand command) {
        if ( command == null ) {
            return null;
        }

        ReviewReportAddedEvent reviewReportAddedEvent = new ReviewReportAddedEvent();

        reviewReportAddedEvent.setAggregateId( command.getAggregateId() );
        reviewReportAddedEvent.setTransactor( command.getTransactor() );
        reviewReportAddedEvent.setOccurred( command.getOccurred() );
        reviewReportAddedEvent.setReportCode( command.getReportCode() );
        reviewReportAddedEvent.setReportName( command.getReportName() );
        reviewReportAddedEvent.setPlanCode( command.getPlanCode() );
        reviewReportAddedEvent.setPlanName( command.getPlanName() );
        reviewReportAddedEvent.setReportType( command.getReportType() );
        reviewReportAddedEvent.setVersionCode( command.getVersionCode() );
        reviewReportAddedEvent.setVersionName( command.getVersionName() );
        reviewReportAddedEvent.setProductCode( command.getProductCode() );
        reviewReportAddedEvent.setProductName( command.getProductName() );
        reviewReportAddedEvent.setTestResult( command.getTestResult() );
        reviewReportAddedEvent.setSummary( command.getSummary() );
        reviewReportAddedEvent.setStatus( command.getStatus() );
        reviewReportAddedEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            reviewReportAddedEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            reviewReportAddedEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        reviewReportAddedEvent.setReviewInfo( reviewInfoVOToReviewInfoDTO( command.getReviewInfo() ) );
        List<ReviewOpinionVO> list2 = command.getReviewOpinions();
        if ( list2 != null ) {
            reviewReportAddedEvent.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list2 ) );
        }
        List<ReviewRenewalVO> list3 = command.getReviewRenewals();
        if ( list3 != null ) {
            reviewReportAddedEvent.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list3 ) );
        }
        List<AttachmentVO> list4 = command.getAttachments();
        if ( list4 != null ) {
            reviewReportAddedEvent.setAttachments( new ArrayList<AttachmentVO>( list4 ) );
        }

        return reviewReportAddedEvent;
    }

    @Override
    public TestReportSendEvent convertTestReportSendEvent(EditAndSendReviewReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            testReportSendEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public TestReportSendEvent convertTestReportSendEvent(AddAndSendReviewReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            testReportSendEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public ReviewReportEditedEvent convert(EditAndSendReviewReportCommand command) {
        if ( command == null ) {
            return null;
        }

        ReviewReportEditedEvent reviewReportEditedEvent = new ReviewReportEditedEvent();

        reviewReportEditedEvent.setAggregateId( command.getAggregateId() );
        reviewReportEditedEvent.setTransactor( command.getTransactor() );
        reviewReportEditedEvent.setOccurred( command.getOccurred() );
        reviewReportEditedEvent.setReportCode( command.getReportCode() );
        reviewReportEditedEvent.setReportName( command.getReportName() );
        reviewReportEditedEvent.setPlanCode( command.getPlanCode() );
        reviewReportEditedEvent.setPlanName( command.getPlanName() );
        reviewReportEditedEvent.setReviewInfo( reviewInfoVOToReviewInfoDTO( command.getReviewInfo() ) );
        List<ReviewOpinionVO> list = command.getReviewOpinions();
        if ( list != null ) {
            reviewReportEditedEvent.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list ) );
        }
        List<ReviewRenewalVO> list1 = command.getReviewRenewals();
        if ( list1 != null ) {
            reviewReportEditedEvent.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            reviewReportEditedEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return reviewReportEditedEvent;
    }

    @Override
    public TmPermitReportAddEvent converter(AddTmPermitReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmPermitReportAddEvent tmPermitReportAddEvent = new TmPermitReportAddEvent();

        tmPermitReportAddEvent.setAggregateId( command.getAggregateId() );
        tmPermitReportAddEvent.setTransactor( command.getTransactor() );
        tmPermitReportAddEvent.setOccurred( command.getOccurred() );
        tmPermitReportAddEvent.setReportCode( command.getReportCode() );
        tmPermitReportAddEvent.setReportName( command.getReportName() );
        tmPermitReportAddEvent.setPlanCode( command.getPlanCode() );
        tmPermitReportAddEvent.setPlanName( command.getPlanName() );
        tmPermitReportAddEvent.setReportType( command.getReportType() );
        tmPermitReportAddEvent.setVersionCode( command.getVersionCode() );
        tmPermitReportAddEvent.setVersionName( command.getVersionName() );
        tmPermitReportAddEvent.setProductCode( command.getProductCode() );
        tmPermitReportAddEvent.setProductName( command.getProductName() );
        tmPermitReportAddEvent.setTestResult( command.getTestResult() );
        tmPermitReportAddEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        tmPermitReportAddEvent.setSummary( command.getSummary() );
        tmPermitReportAddEvent.setStatus( command.getStatus() );
        tmPermitReportAddEvent.setPreview( command.getPreview() );
        tmPermitReportAddEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        tmPermitReportAddEvent.setDelayDays( command.getDelayDays() );
        tmPermitReportAddEvent.setCaseResultVO( command.getCaseResultVO() );
        tmPermitReportAddEvent.setSecurityUserId( command.getSecurityUserId() );
        tmPermitReportAddEvent.setSecurityUserName( command.getSecurityUserName() );
        tmPermitReportAddEvent.setSecurityTestResultCode( command.getSecurityTestResultCode() );
        tmPermitReportAddEvent.setSecurityTestResultDesc( command.getSecurityTestResultDesc() );
        List<TmModuleTestVO> list = command.getModuleTestVOS();
        if ( list != null ) {
            tmPermitReportAddEvent.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }
        tmPermitReportAddEvent.setUiTestResult( command.getUiTestResult() );
        tmPermitReportAddEvent.setZuiTestResult( command.getZuiTestResult() );

        return tmPermitReportAddEvent;
    }

    @Override
    public TmPermitReportEditedEvent converter(EditTmPermitReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmPermitReportEditedEvent tmPermitReportEditedEvent = new TmPermitReportEditedEvent();

        tmPermitReportEditedEvent.setAggregateId( command.getAggregateId() );
        tmPermitReportEditedEvent.setTransactor( command.getTransactor() );
        tmPermitReportEditedEvent.setOccurred( command.getOccurred() );
        tmPermitReportEditedEvent.setReportCode( command.getReportCode() );
        tmPermitReportEditedEvent.setReportName( command.getReportName() );
        tmPermitReportEditedEvent.setPlanCode( command.getPlanCode() );
        tmPermitReportEditedEvent.setPlanName( command.getPlanName() );
        tmPermitReportEditedEvent.setReportType( command.getReportType() );
        tmPermitReportEditedEvent.setVersionCode( command.getVersionCode() );
        tmPermitReportEditedEvent.setVersionName( command.getVersionName() );
        tmPermitReportEditedEvent.setProductCode( command.getProductCode() );
        tmPermitReportEditedEvent.setProductName( command.getProductName() );
        tmPermitReportEditedEvent.setTestResult( command.getTestResult() );
        tmPermitReportEditedEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        tmPermitReportEditedEvent.setSummary( command.getSummary() );
        tmPermitReportEditedEvent.setStatus( command.getStatus() );
        tmPermitReportEditedEvent.setPreview( command.getPreview() );
        tmPermitReportEditedEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        tmPermitReportEditedEvent.setDelayDays( command.getDelayDays() );
        tmPermitReportEditedEvent.setCaseResultVO( command.getCaseResultVO() );
        tmPermitReportEditedEvent.setSecurityUserId( command.getSecurityUserId() );
        tmPermitReportEditedEvent.setSecurityUserName( command.getSecurityUserName() );
        tmPermitReportEditedEvent.setSecurityTestResultCode( command.getSecurityTestResultCode() );
        tmPermitReportEditedEvent.setSecurityTestResultDesc( command.getSecurityTestResultDesc() );
        List<TmModuleTestVO> list = command.getModuleTestVOS();
        if ( list != null ) {
            tmPermitReportEditedEvent.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }
        tmPermitReportEditedEvent.setUiTestResult( command.getUiTestResult() );
        tmPermitReportEditedEvent.setZuiTestResult( command.getZuiTestResult() );

        return tmPermitReportEditedEvent;
    }

    @Override
    public SimpleReportAddedEvent convert(AddAndSendSimpleTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        SimpleReportAddedEvent simpleReportAddedEvent = new SimpleReportAddedEvent();

        simpleReportAddedEvent.setAggregateId( command.getAggregateId() );
        simpleReportAddedEvent.setTransactor( command.getTransactor() );
        simpleReportAddedEvent.setOccurred( command.getOccurred() );
        simpleReportAddedEvent.setReportCode( command.getReportCode() );
        simpleReportAddedEvent.setReportName( command.getReportName() );
        simpleReportAddedEvent.setPlanCode( command.getPlanCode() );
        simpleReportAddedEvent.setPlanName( command.getPlanName() );
        simpleReportAddedEvent.setReportType( command.getReportType() );
        simpleReportAddedEvent.setVersionCode( command.getVersionCode() );
        simpleReportAddedEvent.setVersionName( command.getVersionName() );
        simpleReportAddedEvent.setProductCode( command.getProductCode() );
        simpleReportAddedEvent.setProductName( command.getProductName() );
        simpleReportAddedEvent.setTestResult( command.getTestResult() );
        simpleReportAddedEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        simpleReportAddedEvent.setPlanPresentationDay( command.getPlanPresentationDay() );
        simpleReportAddedEvent.setActualPresentationDate( command.getActualPresentationDate() );
        simpleReportAddedEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        simpleReportAddedEvent.setPlanApprovalExitDay( command.getPlanApprovalExitDay() );
        simpleReportAddedEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        simpleReportAddedEvent.setActualOnlineDate( command.getActualOnlineDate() );
        simpleReportAddedEvent.setPlanOnlineDate( command.getPlanOnlineDate() );
        simpleReportAddedEvent.setStartDate( command.getStartDate() );
        simpleReportAddedEvent.setDeveloperCount( command.getDeveloperCount() );
        simpleReportAddedEvent.setTesterCount( command.getTesterCount() );
        simpleReportAddedEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        simpleReportAddedEvent.setDelay( command.getDelay() );
        simpleReportAddedEvent.setSummary( command.getSummary() );
        simpleReportAddedEvent.setStatus( command.getStatus() );
        simpleReportAddedEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            simpleReportAddedEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            simpleReportAddedEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            simpleReportAddedEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        simpleReportAddedEvent.setCaseExecuteResultVO( command.getCaseExecuteResultVO() );
        List<IssueInfoVO> list3 = command.getIssueInfoVOS();
        if ( list3 != null ) {
            simpleReportAddedEvent.setIssueInfoVOS( new ArrayList<IssueInfoVO>( list3 ) );
        }
        List<IssueLegacyVO> list4 = command.getIssueLegacyVOS();
        if ( list4 != null ) {
            simpleReportAddedEvent.setIssueLegacyVOS( new ArrayList<IssueLegacyVO>( list4 ) );
        }
        simpleReportAddedEvent.setCoverageResult( command.getCoverageResult() );
        List<CoverageReasonVO> list5 = command.getCoverageReasonVOS();
        if ( list5 != null ) {
            simpleReportAddedEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list5 ) );
        }
        simpleReportAddedEvent.setUiTestResult( command.getUiTestResult() );
        simpleReportAddedEvent.setZuiTestResult( command.getZuiTestResult() );

        return simpleReportAddedEvent;
    }

    @Override
    public CoverageReasonEditEvent convertAddSimpleReasonEvent(AddAndSendSimpleTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageReasonEditEvent coverageReasonEditEvent = new CoverageReasonEditEvent();

        coverageReasonEditEvent.setAggregateId( command.getAggregateId() );
        coverageReasonEditEvent.setTransactor( command.getTransactor() );
        coverageReasonEditEvent.setOccurred( command.getOccurred() );
        coverageReasonEditEvent.setVersionCode( command.getVersionCode() );
        coverageReasonEditEvent.setCoverageResult( command.getCoverageResult() );
        List<CoverageReasonVO> list = command.getCoverageReasonVOS();
        if ( list != null ) {
            coverageReasonEditEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list ) );
        }

        return coverageReasonEditEvent;
    }

    @Override
    public TestReportSendEvent convertTestReportSendEvent(AddAndSendSimpleTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        testReportSendEvent.setPlanPresentationDay( command.getPlanPresentationDay() );
        testReportSendEvent.setActualPresentationDate( command.getActualPresentationDate() );
        testReportSendEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        testReportSendEvent.setPlanApprovalExitDay( command.getPlanApprovalExitDay() );
        testReportSendEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        testReportSendEvent.setActualOnlineDate( command.getActualOnlineDate() );
        testReportSendEvent.setPlanOnlineDate( command.getPlanOnlineDate() );
        testReportSendEvent.setStartDate( command.getStartDate() );
        testReportSendEvent.setDeveloperCount( command.getDeveloperCount() );
        testReportSendEvent.setTesterCount( command.getTesterCount() );
        testReportSendEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        testReportSendEvent.setDelay( command.getDelay() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            testReportSendEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public SimpleTestReportEditEvent convert(EditAndSendSimpleTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        SimpleTestReportEditEvent simpleTestReportEditEvent = new SimpleTestReportEditEvent();

        simpleTestReportEditEvent.setAggregateId( command.getAggregateId() );
        simpleTestReportEditEvent.setTransactor( command.getTransactor() );
        simpleTestReportEditEvent.setOccurred( command.getOccurred() );
        simpleTestReportEditEvent.setReportCode( command.getReportCode() );
        simpleTestReportEditEvent.setReportName( command.getReportName() );
        simpleTestReportEditEvent.setPlanCode( command.getPlanCode() );
        simpleTestReportEditEvent.setPlanName( command.getPlanName() );
        simpleTestReportEditEvent.setReportType( command.getReportType() );
        simpleTestReportEditEvent.setVersionCode( command.getVersionCode() );
        simpleTestReportEditEvent.setVersionName( command.getVersionName() );
        simpleTestReportEditEvent.setProductCode( command.getProductCode() );
        simpleTestReportEditEvent.setProductName( command.getProductName() );
        simpleTestReportEditEvent.setTestResult( command.getTestResult() );
        simpleTestReportEditEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        simpleTestReportEditEvent.setPlanPresentationDay( command.getPlanPresentationDay() );
        simpleTestReportEditEvent.setActualPresentationDate( command.getActualPresentationDate() );
        simpleTestReportEditEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        simpleTestReportEditEvent.setPlanApprovalExitDay( command.getPlanApprovalExitDay() );
        simpleTestReportEditEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        simpleTestReportEditEvent.setActualOnlineDate( command.getActualOnlineDate() );
        simpleTestReportEditEvent.setPlanOnlineDate( command.getPlanOnlineDate() );
        simpleTestReportEditEvent.setStartDate( command.getStartDate() );
        simpleTestReportEditEvent.setDeveloperCount( command.getDeveloperCount() );
        simpleTestReportEditEvent.setTesterCount( command.getTesterCount() );
        simpleTestReportEditEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        simpleTestReportEditEvent.setDelay( command.getDelay() );
        simpleTestReportEditEvent.setSummary( command.getSummary() );
        simpleTestReportEditEvent.setStatus( command.getStatus() );
        simpleTestReportEditEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            simpleTestReportEditEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            simpleTestReportEditEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            simpleTestReportEditEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        simpleTestReportEditEvent.setCaseExecuteResultVO( command.getCaseExecuteResultVO() );
        List<IssueInfoVO> list3 = command.getIssueInfoVOS();
        if ( list3 != null ) {
            simpleTestReportEditEvent.setIssueInfoVOS( new ArrayList<IssueInfoVO>( list3 ) );
        }
        List<IssueLegacyVO> list4 = command.getIssueLegacyVOS();
        if ( list4 != null ) {
            simpleTestReportEditEvent.setIssueLegacyVOS( new ArrayList<IssueLegacyVO>( list4 ) );
        }
        simpleTestReportEditEvent.setCoverageResult( command.getCoverageResult() );
        List<CoverageReasonVO> list5 = command.getCoverageReasonVOS();
        if ( list5 != null ) {
            simpleTestReportEditEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list5 ) );
        }
        simpleTestReportEditEvent.setUiTestResult( command.getUiTestResult() );
        simpleTestReportEditEvent.setZuiTestResult( command.getZuiTestResult() );

        return simpleTestReportEditEvent;
    }

    @Override
    public CoverageReasonEditEvent convertEditSimpleReasonEvent(EditAndSendSimpleTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageReasonEditEvent coverageReasonEditEvent = new CoverageReasonEditEvent();

        coverageReasonEditEvent.setAggregateId( command.getAggregateId() );
        coverageReasonEditEvent.setTransactor( command.getTransactor() );
        coverageReasonEditEvent.setOccurred( command.getOccurred() );
        coverageReasonEditEvent.setVersionCode( command.getVersionCode() );
        coverageReasonEditEvent.setCoverageResult( command.getCoverageResult() );
        List<CoverageReasonVO> list = command.getCoverageReasonVOS();
        if ( list != null ) {
            coverageReasonEditEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list ) );
        }

        return coverageReasonEditEvent;
    }

    @Override
    public TestReportSendEvent convertTestReportSendEvent(EditAndSendSimpleTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        testReportSendEvent.setPlanPresentationDay( command.getPlanPresentationDay() );
        testReportSendEvent.setActualPresentationDate( command.getActualPresentationDate() );
        testReportSendEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        testReportSendEvent.setPlanApprovalExitDay( command.getPlanApprovalExitDay() );
        testReportSendEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        testReportSendEvent.setActualOnlineDate( command.getActualOnlineDate() );
        testReportSendEvent.setPlanOnlineDate( command.getPlanOnlineDate() );
        testReportSendEvent.setStartDate( command.getStartDate() );
        testReportSendEvent.setDeveloperCount( command.getDeveloperCount() );
        testReportSendEvent.setTesterCount( command.getTesterCount() );
        testReportSendEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        testReportSendEvent.setDelay( command.getDelay() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            testReportSendEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public ReviewReportDetailVO convertReviewVO(TmTestReportEntityDO report) {
        if ( report == null ) {
            return null;
        }

        ReviewReportDetailVO reviewReportDetailVO = new ReviewReportDetailVO();

        reviewReportDetailVO.setReportUserId( report.getModifierId() );
        reviewReportDetailVO.setReportUserName( report.getModifier() );
        reviewReportDetailVO.setReportCode( report.getReportCode() );
        reviewReportDetailVO.setReportName( report.getReportName() );
        reviewReportDetailVO.setReportType( report.getReportType() );
        reviewReportDetailVO.setPlanCode( report.getPlanCode() );
        reviewReportDetailVO.setVersionCode( report.getVersionCode() );
        reviewReportDetailVO.setVersionName( report.getVersionName() );
        reviewReportDetailVO.setDeveloperCount( report.getDeveloperCount() );
        reviewReportDetailVO.setTesterCount( report.getTesterCount() );
        reviewReportDetailVO.setTestResult( report.getTestResult() );
        reviewReportDetailVO.setPlanPresentationDate( report.getPlanPresentationDate() );
        reviewReportDetailVO.setActualPresentationDate( report.getActualPresentationDate() );
        reviewReportDetailVO.setPlanApprovalExitDate( report.getPlanApprovalExitDate() );
        reviewReportDetailVO.setActualApprovalExitDate( report.getActualApprovalExitDate() );
        reviewReportDetailVO.setPlanOnlineDate( report.getPlanOnlineDate() );
        reviewReportDetailVO.setActualTestStart( report.getActualTestStart() );
        reviewReportDetailVO.setActualTestEnd( report.getActualTestEnd() );
        reviewReportDetailVO.setCheckStartDate( report.getCheckStartDate() );
        reviewReportDetailVO.setCheckEndDate( report.getCheckEndDate() );
        reviewReportDetailVO.setSummary( report.getSummary() );
        reviewReportDetailVO.setAutoTestResult( autoExecuteResultToAutoExecuteResult( report.getAutoTestResult() ) );
        reviewReportDetailVO.setSecurityUserId( report.getSecurityUserId() );
        reviewReportDetailVO.setSecurityUserName( report.getSecurityUserName() );
        reviewReportDetailVO.setSecurityTestResult( report.getSecurityTestResult() );
        reviewReportDetailVO.setStatus( report.getStatus() );
        reviewReportDetailVO.setDelay( report.getDelay() );
        reviewReportDetailVO.setUiTestResult( report.getUiTestResult() );

        return reviewReportDetailVO;
    }

    @Override
    public ReviewInfoUserHandleVO convertReviewInfoHandle(ReviewInfoEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        ReviewInfoUserHandleVO reviewInfoUserHandleVO = new ReviewInfoUserHandleVO();

        reviewInfoUserHandleVO.setCode( entityDO.getCode() );
        reviewInfoUserHandleVO.setReportCode( entityDO.getReportCode() );
        reviewInfoUserHandleVO.setReviewDate( entityDO.getReviewDate() );
        reviewInfoUserHandleVO.setReviewType( reviewTypeToReviewType( entityDO.getReviewType() ) );
        reviewInfoUserHandleVO.setRecordUserId( entityDO.getRecordUserId() );
        reviewInfoUserHandleVO.setRecordUserName( entityDO.getRecordUserName() );

        reviewInfoUserHandleVO.setReviewTypeDesc( com.zto.devops.qc.client.enums.testmanager.report.ReviewType.getDesc(entityDO.getReviewType()) );

        return reviewInfoUserHandleVO;
    }

    @Override
    public List<ReviewUserVO> convertUser(List<UserSelectVO> userSelectVOList) {
        if ( userSelectVOList == null ) {
            return null;
        }

        List<ReviewUserVO> list = new ArrayList<ReviewUserVO>( userSelectVOList.size() );
        for ( UserSelectVO userSelectVO : userSelectVOList ) {
            list.add( userSelectVOToReviewUserVO( userSelectVO ) );
        }

        return list;
    }

    @Override
    public MobileTestReportDetailVO convertMobileVO(TmTestReportEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        MobileTestReportDetailVO mobileTestReportDetailVO = new MobileTestReportDetailVO();

        mobileTestReportDetailVO.setReportUserId( entityDO.getModifierId() );
        mobileTestReportDetailVO.setReportUserName( entityDO.getModifier() );
        mobileTestReportDetailVO.setReportCode( entityDO.getReportCode() );
        mobileTestReportDetailVO.setReportName( entityDO.getReportName() );
        mobileTestReportDetailVO.setReportType( entityDO.getReportType() );
        mobileTestReportDetailVO.setPlanCode( entityDO.getPlanCode() );
        mobileTestReportDetailVO.setVersionCode( entityDO.getVersionCode() );
        mobileTestReportDetailVO.setVersionName( entityDO.getVersionName() );
        mobileTestReportDetailVO.setDeveloperCount( entityDO.getDeveloperCount() );
        mobileTestReportDetailVO.setTesterCount( entityDO.getTesterCount() );
        mobileTestReportDetailVO.setTestResult( entityDO.getTestResult() );
        mobileTestReportDetailVO.setPlanPresentationDate( entityDO.getPlanPresentationDate() );
        mobileTestReportDetailVO.setActualPresentationDate( entityDO.getActualPresentationDate() );
        mobileTestReportDetailVO.setPlanApprovalExitDate( entityDO.getPlanApprovalExitDate() );
        mobileTestReportDetailVO.setActualApprovalExitDate( entityDO.getActualApprovalExitDate() );
        mobileTestReportDetailVO.setPlanOnlineDate( entityDO.getPlanOnlineDate() );
        mobileTestReportDetailVO.setCheckStartDate( entityDO.getCheckStartDate() );
        mobileTestReportDetailVO.setCheckEndDate( entityDO.getCheckEndDate() );
        mobileTestReportDetailVO.setSummary( entityDO.getSummary() );
        mobileTestReportDetailVO.setAutoTestResult( autoExecuteResultToAutoExecuteResult( entityDO.getAutoTestResult() ) );
        mobileTestReportDetailVO.setSecurityUserId( entityDO.getSecurityUserId() );
        mobileTestReportDetailVO.setSecurityUserName( entityDO.getSecurityUserName() );
        mobileTestReportDetailVO.setSecurityTestResult( entityDO.getSecurityTestResult() );
        mobileTestReportDetailVO.setStatus( entityDO.getStatus() );
        mobileTestReportDetailVO.setDelay( entityDO.getDelay() );
        mobileTestReportDetailVO.setUiTestResult( entityDO.getUiTestResult() );
        mobileTestReportDetailVO.setActualTestStart( entityDO.getActualTestStart() );
        mobileTestReportDetailVO.setActualTestEnd( entityDO.getActualTestEnd() );
        mobileTestReportDetailVO.setUpdateTestResultDate( entityDO.getUpdateTestResultDate() );

        return mobileTestReportDetailVO;
    }

    @Override
    public ReviewReportAddedEvent converter(AddReviewReportCommand command) {
        if ( command == null ) {
            return null;
        }

        ReviewReportAddedEvent reviewReportAddedEvent = new ReviewReportAddedEvent();

        reviewReportAddedEvent.setAggregateId( command.getAggregateId() );
        reviewReportAddedEvent.setTransactor( command.getTransactor() );
        reviewReportAddedEvent.setOccurred( command.getOccurred() );
        reviewReportAddedEvent.setReportCode( command.getReportCode() );
        reviewReportAddedEvent.setReportName( command.getReportName() );
        reviewReportAddedEvent.setPlanCode( command.getPlanCode() );
        reviewReportAddedEvent.setPlanName( command.getPlanName() );
        reviewReportAddedEvent.setReportType( command.getReportType() );
        reviewReportAddedEvent.setVersionCode( command.getVersionCode() );
        reviewReportAddedEvent.setVersionName( command.getVersionName() );
        reviewReportAddedEvent.setProductCode( command.getProductCode() );
        reviewReportAddedEvent.setProductName( command.getProductName() );
        reviewReportAddedEvent.setTestResult( command.getTestResult() );
        reviewReportAddedEvent.setStatus( command.getStatus() );
        reviewReportAddedEvent.setPreview( command.getPreview() );
        reviewReportAddedEvent.setReviewInfo( reviewInfoVOToReviewInfoDTO( command.getReviewInfo() ) );
        List<ReviewOpinionVO> list = command.getReviewOpinions();
        if ( list != null ) {
            reviewReportAddedEvent.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list ) );
        }
        List<ReviewRenewalVO> list1 = command.getReviewRenewals();
        if ( list1 != null ) {
            reviewReportAddedEvent.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            reviewReportAddedEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return reviewReportAddedEvent;
    }

    @Override
    public ReviewReportEditedEvent convert(EditReviewReportCommand command) {
        if ( command == null ) {
            return null;
        }

        ReviewReportEditedEvent reviewReportEditedEvent = new ReviewReportEditedEvent();

        reviewReportEditedEvent.setAggregateId( command.getAggregateId() );
        reviewReportEditedEvent.setTransactor( command.getTransactor() );
        reviewReportEditedEvent.setOccurred( command.getOccurred() );
        reviewReportEditedEvent.setReportCode( command.getReportCode() );
        reviewReportEditedEvent.setReportName( command.getReportName() );
        reviewReportEditedEvent.setPlanCode( command.getPlanCode() );
        reviewReportEditedEvent.setPlanName( command.getPlanName() );
        reviewReportEditedEvent.setReviewInfo( reviewInfoVOToReviewInfoDTO( command.getReviewInfo() ) );
        List<ReviewOpinionVO> list = command.getReviewOpinions();
        if ( list != null ) {
            reviewReportEditedEvent.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list ) );
        }
        List<ReviewRenewalVO> list1 = command.getReviewRenewals();
        if ( list1 != null ) {
            reviewReportEditedEvent.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            reviewReportEditedEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return reviewReportEditedEvent;
    }

    @Override
    public ExternalReportAddEvent convert(AddExternalTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        ExternalReportAddEvent externalReportAddEvent = new ExternalReportAddEvent();

        externalReportAddEvent.setActualOnlineDate( command.getActualPublishDate() );
        externalReportAddEvent.setAggregateId( command.getAggregateId() );
        externalReportAddEvent.setTransactor( command.getTransactor() );
        externalReportAddEvent.setOccurred( command.getOccurred() );
        externalReportAddEvent.setReportCode( command.getReportCode() );
        externalReportAddEvent.setReportName( command.getReportName() );
        externalReportAddEvent.setPlanCode( command.getPlanCode() );
        externalReportAddEvent.setPlanName( command.getPlanName() );
        externalReportAddEvent.setReportType( command.getReportType() );
        externalReportAddEvent.setVersionCode( command.getVersionCode() );
        externalReportAddEvent.setVersionName( command.getVersionName() );
        externalReportAddEvent.setProductCode( command.getProductCode() );
        externalReportAddEvent.setProductName( command.getProductName() );
        externalReportAddEvent.setTestResult( command.getTestResult() );
        externalReportAddEvent.setSummary( command.getSummary() );
        externalReportAddEvent.setStatus( command.getStatus() );
        externalReportAddEvent.setPreview( command.getPreview() );
        externalReportAddEvent.setZuiTestResult( command.getZuiTestResult() );
        externalReportAddEvent.setCheckStartDate( command.getCheckStartDate() );
        externalReportAddEvent.setCheckEndDate( command.getCheckEndDate() );
        externalReportAddEvent.setActualPublishDate( command.getActualPublishDate() );
        externalReportAddEvent.setDelay( command.getDelay() );
        externalReportAddEvent.setDelayDesc( command.getDelayDesc() );
        List<AttachmentVO> list = command.getAttachments();
        if ( list != null ) {
            externalReportAddEvent.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        externalReportAddEvent.setUiTestResult( command.getUiTestResult() );

        return externalReportAddEvent;
    }

    @Override
    public ExternalReportEditEvent convert(EditExternalTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        ExternalReportEditEvent externalReportEditEvent = new ExternalReportEditEvent();

        externalReportEditEvent.setActualOnlineDate( command.getActualPublishDate() );
        externalReportEditEvent.setAggregateId( command.getAggregateId() );
        externalReportEditEvent.setTransactor( command.getTransactor() );
        externalReportEditEvent.setOccurred( command.getOccurred() );
        externalReportEditEvent.setReportCode( command.getReportCode() );
        externalReportEditEvent.setReportName( command.getReportName() );
        externalReportEditEvent.setPlanCode( command.getPlanCode() );
        externalReportEditEvent.setPlanName( command.getPlanName() );
        externalReportEditEvent.setReportType( command.getReportType() );
        externalReportEditEvent.setVersionCode( command.getVersionCode() );
        externalReportEditEvent.setVersionName( command.getVersionName() );
        externalReportEditEvent.setProductCode( command.getProductCode() );
        externalReportEditEvent.setProductName( command.getProductName() );
        externalReportEditEvent.setTestResult( command.getTestResult() );
        externalReportEditEvent.setSummary( command.getSummary() );
        externalReportEditEvent.setStatus( command.getStatus() );
        externalReportEditEvent.setPreview( command.getPreview() );
        externalReportEditEvent.setZuiTestResult( command.getZuiTestResult() );
        externalReportEditEvent.setPresentationDate( command.getPresentationDate() );
        externalReportEditEvent.setPresentationDay( command.getPresentationDay() );
        externalReportEditEvent.setPublishDate( command.getPublishDate() );
        externalReportEditEvent.setPublishDay( command.getPublishDay() );
        externalReportEditEvent.setCheckStartDate( command.getCheckStartDate() );
        externalReportEditEvent.setCheckEndDate( command.getCheckEndDate() );
        externalReportEditEvent.setActualPublishDate( command.getActualPublishDate() );
        externalReportEditEvent.setDelay( command.getDelay() );
        externalReportEditEvent.setDelayDesc( command.getDelayDesc() );
        List<AttachmentVO> list = command.getAttachments();
        if ( list != null ) {
            externalReportEditEvent.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        externalReportEditEvent.setUiTestResult( command.getUiTestResult() );

        return externalReportEditEvent;
    }

    @Override
    public MobileTestReportAddedEvent convert(AddMobileTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        MobileTestReportAddedEvent mobileTestReportAddedEvent = new MobileTestReportAddedEvent();

        mobileTestReportAddedEvent.setAggregateId( command.getAggregateId() );
        mobileTestReportAddedEvent.setTransactor( command.getTransactor() );
        mobileTestReportAddedEvent.setOccurred( command.getOccurred() );
        mobileTestReportAddedEvent.setReportCode( command.getReportCode() );
        mobileTestReportAddedEvent.setReportName( command.getReportName() );
        mobileTestReportAddedEvent.setPlanCode( command.getPlanCode() );
        mobileTestReportAddedEvent.setPlanName( command.getPlanName() );
        mobileTestReportAddedEvent.setReportType( command.getReportType() );
        mobileTestReportAddedEvent.setVersionCode( command.getVersionCode() );
        mobileTestReportAddedEvent.setVersionName( command.getVersionName() );
        mobileTestReportAddedEvent.setProductCode( command.getProductCode() );
        mobileTestReportAddedEvent.setProductName( command.getProductName() );
        mobileTestReportAddedEvent.setTestResult( command.getTestResult() );
        mobileTestReportAddedEvent.setSummary( command.getSummary() );
        mobileTestReportAddedEvent.setStatus( command.getStatus() );
        mobileTestReportAddedEvent.setPreview( command.getPreview() );
        mobileTestReportAddedEvent.setActualTestStart( command.getActualTestStart() );
        mobileTestReportAddedEvent.setActualTestStartDay( command.getActualTestStartDay() );
        mobileTestReportAddedEvent.setActualTestEnd( command.getActualTestEnd() );
        mobileTestReportAddedEvent.setActualTestEndDay( command.getActualTestEndDay() );
        mobileTestReportAddedEvent.setUpdateTestResultDate( command.getUpdateTestResultDate() );
        List<TmModuleTestVO> list = command.getModuleTestVOS();
        if ( list != null ) {
            mobileTestReportAddedEvent.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }

        return mobileTestReportAddedEvent;
    }

    @Override
    public MobileTestReportEditEvent convert(EditMobileTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        MobileTestReportEditEvent mobileTestReportEditEvent = new MobileTestReportEditEvent();

        mobileTestReportEditEvent.setAggregateId( command.getAggregateId() );
        mobileTestReportEditEvent.setTransactor( command.getTransactor() );
        mobileTestReportEditEvent.setOccurred( command.getOccurred() );
        mobileTestReportEditEvent.setReportCode( command.getReportCode() );
        mobileTestReportEditEvent.setReportName( command.getReportName() );
        mobileTestReportEditEvent.setPlanCode( command.getPlanCode() );
        mobileTestReportEditEvent.setPlanName( command.getPlanName() );
        mobileTestReportEditEvent.setReportType( command.getReportType() );
        mobileTestReportEditEvent.setVersionCode( command.getVersionCode() );
        mobileTestReportEditEvent.setVersionName( command.getVersionName() );
        mobileTestReportEditEvent.setProductCode( command.getProductCode() );
        mobileTestReportEditEvent.setProductName( command.getProductName() );
        mobileTestReportEditEvent.setTestResult( command.getTestResult() );
        mobileTestReportEditEvent.setSummary( command.getSummary() );
        mobileTestReportEditEvent.setStatus( command.getStatus() );
        mobileTestReportEditEvent.setPreview( command.getPreview() );
        mobileTestReportEditEvent.setActualTestStart( command.getActualTestStart() );
        mobileTestReportEditEvent.setActualTestStartDay( command.getActualTestStartDay() );
        mobileTestReportEditEvent.setActualTestEnd( command.getActualTestEnd() );
        mobileTestReportEditEvent.setActualTestEndDay( command.getActualTestEndDay() );
        List<TmModuleTestVO> list = command.getModuleTestVOS();
        if ( list != null ) {
            mobileTestReportEditEvent.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }

        return mobileTestReportEditEvent;
    }

    @Override
    public SimpleTestReportDetailVO convertSimpleVO(TmTestReportEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SimpleTestReportDetailVO simpleTestReportDetailVO = new SimpleTestReportDetailVO();

        simpleTestReportDetailVO.setReportCode( entityDO.getReportCode() );
        simpleTestReportDetailVO.setReportName( entityDO.getReportName() );
        simpleTestReportDetailVO.setReportType( entityDO.getReportType() );
        simpleTestReportDetailVO.setPlanCode( entityDO.getPlanCode() );
        simpleTestReportDetailVO.setVersionCode( entityDO.getVersionCode() );
        simpleTestReportDetailVO.setVersionName( entityDO.getVersionName() );
        simpleTestReportDetailVO.setDeveloperCount( entityDO.getDeveloperCount() );
        simpleTestReportDetailVO.setTesterCount( entityDO.getTesterCount() );
        simpleTestReportDetailVO.setTestResult( entityDO.getTestResult() );
        simpleTestReportDetailVO.setPlanPresentationDate( entityDO.getPlanPresentationDate() );
        simpleTestReportDetailVO.setActualPresentationDate( entityDO.getActualPresentationDate() );
        simpleTestReportDetailVO.setPlanApprovalExitDate( entityDO.getPlanApprovalExitDate() );
        simpleTestReportDetailVO.setActualApprovalExitDate( entityDO.getActualApprovalExitDate() );
        simpleTestReportDetailVO.setPlanOnlineDate( entityDO.getPlanOnlineDate() );
        simpleTestReportDetailVO.setActualTestStart( entityDO.getActualTestStart() );
        simpleTestReportDetailVO.setActualTestEnd( entityDO.getActualTestEnd() );
        simpleTestReportDetailVO.setCheckStartDate( entityDO.getCheckStartDate() );
        simpleTestReportDetailVO.setCheckEndDate( entityDO.getCheckEndDate() );
        simpleTestReportDetailVO.setSummary( entityDO.getSummary() );
        simpleTestReportDetailVO.setAutoTestResult( autoExecuteResultToAutoExecuteResult( entityDO.getAutoTestResult() ) );
        simpleTestReportDetailVO.setSecurityUserId( entityDO.getSecurityUserId() );
        simpleTestReportDetailVO.setSecurityUserName( entityDO.getSecurityUserName() );
        simpleTestReportDetailVO.setSecurityTestResult( entityDO.getSecurityTestResult() );
        simpleTestReportDetailVO.setStatus( entityDO.getStatus() );
        simpleTestReportDetailVO.setUiTestResult( entityDO.getUiTestResult() );
        simpleTestReportDetailVO.setZuiTestResult( entityDO.getZuiTestResult() );
        simpleTestReportDetailVO.setActualOnlineDate( entityDO.getActualOnlineDate() );
        simpleTestReportDetailVO.setAsPlanedOnline( entityDO.getAsPlanedOnline() );
        simpleTestReportDetailVO.setDelay( entityDO.getDelay() );

        return simpleTestReportDetailVO;
    }

    @Override
    public SimpleReportAddedEvent convert(AddSimpleTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        SimpleReportAddedEvent simpleReportAddedEvent = new SimpleReportAddedEvent();

        simpleReportAddedEvent.setAggregateId( command.getAggregateId() );
        simpleReportAddedEvent.setTransactor( command.getTransactor() );
        simpleReportAddedEvent.setOccurred( command.getOccurred() );
        simpleReportAddedEvent.setReportCode( command.getReportCode() );
        simpleReportAddedEvent.setReportName( command.getReportName() );
        simpleReportAddedEvent.setPlanCode( command.getPlanCode() );
        simpleReportAddedEvent.setPlanName( command.getPlanName() );
        simpleReportAddedEvent.setReportType( command.getReportType() );
        simpleReportAddedEvent.setVersionCode( command.getVersionCode() );
        simpleReportAddedEvent.setVersionName( command.getVersionName() );
        simpleReportAddedEvent.setProductCode( command.getProductCode() );
        simpleReportAddedEvent.setProductName( command.getProductName() );
        simpleReportAddedEvent.setTestResult( command.getTestResult() );
        simpleReportAddedEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        simpleReportAddedEvent.setPlanPresentationDay( command.getPlanPresentationDay() );
        simpleReportAddedEvent.setActualPresentationDate( command.getActualPresentationDate() );
        simpleReportAddedEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        simpleReportAddedEvent.setPlanApprovalExitDay( command.getPlanApprovalExitDay() );
        simpleReportAddedEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        simpleReportAddedEvent.setActualOnlineDate( command.getActualOnlineDate() );
        simpleReportAddedEvent.setPlanOnlineDate( command.getPlanOnlineDate() );
        simpleReportAddedEvent.setStartDate( command.getStartDate() );
        simpleReportAddedEvent.setDeveloperCount( command.getDeveloperCount() );
        simpleReportAddedEvent.setTesterCount( command.getTesterCount() );
        simpleReportAddedEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        simpleReportAddedEvent.setDelay( command.getDelay() );
        simpleReportAddedEvent.setSummary( command.getSummary() );
        simpleReportAddedEvent.setStatus( command.getStatus() );
        simpleReportAddedEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            simpleReportAddedEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            simpleReportAddedEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            simpleReportAddedEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        simpleReportAddedEvent.setCaseExecuteResultVO( command.getCaseExecuteResultVO() );
        List<IssueInfoVO> list3 = command.getIssueInfoVOS();
        if ( list3 != null ) {
            simpleReportAddedEvent.setIssueInfoVOS( new ArrayList<IssueInfoVO>( list3 ) );
        }
        List<IssueLegacyVO> list4 = command.getIssueLegacyVOS();
        if ( list4 != null ) {
            simpleReportAddedEvent.setIssueLegacyVOS( new ArrayList<IssueLegacyVO>( list4 ) );
        }
        simpleReportAddedEvent.setUiTestResult( command.getUiTestResult() );
        simpleReportAddedEvent.setZuiTestResult( command.getZuiTestResult() );

        return simpleReportAddedEvent;
    }

    @Override
    public SimpleTestReportEditEvent convert(EditSimpleTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        SimpleTestReportEditEvent simpleTestReportEditEvent = new SimpleTestReportEditEvent();

        simpleTestReportEditEvent.setAggregateId( command.getAggregateId() );
        simpleTestReportEditEvent.setTransactor( command.getTransactor() );
        simpleTestReportEditEvent.setOccurred( command.getOccurred() );
        simpleTestReportEditEvent.setReportCode( command.getReportCode() );
        simpleTestReportEditEvent.setReportName( command.getReportName() );
        simpleTestReportEditEvent.setPlanCode( command.getPlanCode() );
        simpleTestReportEditEvent.setPlanName( command.getPlanName() );
        simpleTestReportEditEvent.setReportType( command.getReportType() );
        simpleTestReportEditEvent.setVersionCode( command.getVersionCode() );
        simpleTestReportEditEvent.setVersionName( command.getVersionName() );
        simpleTestReportEditEvent.setProductCode( command.getProductCode() );
        simpleTestReportEditEvent.setProductName( command.getProductName() );
        simpleTestReportEditEvent.setTestResult( command.getTestResult() );
        simpleTestReportEditEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        simpleTestReportEditEvent.setPlanPresentationDay( command.getPlanPresentationDay() );
        simpleTestReportEditEvent.setActualPresentationDate( command.getActualPresentationDate() );
        simpleTestReportEditEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        simpleTestReportEditEvent.setPlanApprovalExitDay( command.getPlanApprovalExitDay() );
        simpleTestReportEditEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        simpleTestReportEditEvent.setActualOnlineDate( command.getActualOnlineDate() );
        simpleTestReportEditEvent.setPlanOnlineDate( command.getPlanOnlineDate() );
        simpleTestReportEditEvent.setStartDate( command.getStartDate() );
        simpleTestReportEditEvent.setDeveloperCount( command.getDeveloperCount() );
        simpleTestReportEditEvent.setTesterCount( command.getTesterCount() );
        simpleTestReportEditEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        simpleTestReportEditEvent.setDelay( command.getDelay() );
        simpleTestReportEditEvent.setSummary( command.getSummary() );
        simpleTestReportEditEvent.setStatus( command.getStatus() );
        simpleTestReportEditEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            simpleTestReportEditEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            simpleTestReportEditEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            simpleTestReportEditEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        simpleTestReportEditEvent.setCaseExecuteResultVO( command.getCaseExecuteResultVO() );
        List<IssueInfoVO> list3 = command.getIssueInfoVOS();
        if ( list3 != null ) {
            simpleTestReportEditEvent.setIssueInfoVOS( new ArrayList<IssueInfoVO>( list3 ) );
        }
        List<IssueLegacyVO> list4 = command.getIssueLegacyVOS();
        if ( list4 != null ) {
            simpleTestReportEditEvent.setIssueLegacyVOS( new ArrayList<IssueLegacyVO>( list4 ) );
        }
        simpleTestReportEditEvent.setUiTestResult( command.getUiTestResult() );
        simpleTestReportEditEvent.setZuiTestResult( command.getZuiTestResult() );

        return simpleTestReportEditEvent;
    }

    @Override
    public TmAccessReportAddEvent convert(AddAndSendTmAccessTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmAccessReportAddEvent tmAccessReportAddEvent = new TmAccessReportAddEvent();

        tmAccessReportAddEvent.setAggregateId( command.getAggregateId() );
        tmAccessReportAddEvent.setTransactor( command.getTransactor() );
        tmAccessReportAddEvent.setOccurred( command.getOccurred() );
        tmAccessReportAddEvent.setReportCode( command.getReportCode() );
        tmAccessReportAddEvent.setReportName( command.getReportName() );
        tmAccessReportAddEvent.setPlanCode( command.getPlanCode() );
        tmAccessReportAddEvent.setPlanName( command.getPlanName() );
        tmAccessReportAddEvent.setReportType( command.getReportType() );
        tmAccessReportAddEvent.setVersionCode( command.getVersionCode() );
        tmAccessReportAddEvent.setVersionName( command.getVersionName() );
        tmAccessReportAddEvent.setProductCode( command.getProductCode() );
        tmAccessReportAddEvent.setProductName( command.getProductName() );
        tmAccessReportAddEvent.setTestResult( command.getTestResult() );
        tmAccessReportAddEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        tmAccessReportAddEvent.setActualPresentationDate( command.getActualPresentationDate() );
        tmAccessReportAddEvent.setTestCaseNum( command.getTestCaseNum() );
        tmAccessReportAddEvent.setPlanCaseNum( command.getPlanCaseNum() );
        tmAccessReportAddEvent.setPermitNum( command.getPermitNum() );
        tmAccessReportAddEvent.setSummary( command.getSummary() );
        tmAccessReportAddEvent.setStatus( command.getStatus() );
        tmAccessReportAddEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            tmAccessReportAddEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            tmAccessReportAddEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return tmAccessReportAddEvent;
    }

    @Override
    public TestReportSendEvent convertEmailSendEvent(AddAndSendTmAccessTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        testReportSendEvent.setActualPresentationDate( command.getActualPresentationDate() );
        testReportSendEvent.setTestCaseNum( command.getTestCaseNum() );
        testReportSendEvent.setPlanCaseNum( command.getPlanCaseNum() );
        testReportSendEvent.setPermitNum( command.getPermitNum() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public TmAccessReportEditEvent convert(EditAndSendTmAccessTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmAccessReportEditEvent tmAccessReportEditEvent = new TmAccessReportEditEvent();

        tmAccessReportEditEvent.setAggregateId( command.getAggregateId() );
        tmAccessReportEditEvent.setTransactor( command.getTransactor() );
        tmAccessReportEditEvent.setOccurred( command.getOccurred() );
        tmAccessReportEditEvent.setReportCode( command.getReportCode() );
        tmAccessReportEditEvent.setReportName( command.getReportName() );
        tmAccessReportEditEvent.setPlanCode( command.getPlanCode() );
        tmAccessReportEditEvent.setPlanName( command.getPlanName() );
        tmAccessReportEditEvent.setReportType( command.getReportType() );
        tmAccessReportEditEvent.setVersionCode( command.getVersionCode() );
        tmAccessReportEditEvent.setVersionName( command.getVersionName() );
        tmAccessReportEditEvent.setProductCode( command.getProductCode() );
        tmAccessReportEditEvent.setProductName( command.getProductName() );
        tmAccessReportEditEvent.setTestResult( command.getTestResult() );
        tmAccessReportEditEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        tmAccessReportEditEvent.setActualPresentationDate( command.getActualPresentationDate() );
        tmAccessReportEditEvent.setTestCaseNum( command.getTestCaseNum() );
        tmAccessReportEditEvent.setPlanCaseNum( command.getPlanCaseNum() );
        tmAccessReportEditEvent.setPermitNum( command.getPermitNum() );
        tmAccessReportEditEvent.setSummary( command.getSummary() );
        tmAccessReportEditEvent.setStatus( command.getStatus() );
        tmAccessReportEditEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            tmAccessReportEditEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            tmAccessReportEditEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return tmAccessReportEditEvent;
    }

    @Override
    public TestReportSendEvent convertEmailSendEvent(EditAndSendTmAccessTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setPlanPresentationDate( command.getPlanPresentationDate() );
        testReportSendEvent.setActualPresentationDate( command.getActualPresentationDate() );
        testReportSendEvent.setTestCaseNum( command.getTestCaseNum() );
        testReportSendEvent.setPlanCaseNum( command.getPlanCaseNum() );
        testReportSendEvent.setPermitNum( command.getPermitNum() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public ExternalReportAddEvent convert(AddAndSendExternalTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        ExternalReportAddEvent externalReportAddEvent = new ExternalReportAddEvent();

        externalReportAddEvent.setActualOnlineDate( command.getActualPublishDate() );
        externalReportAddEvent.setAggregateId( command.getAggregateId() );
        externalReportAddEvent.setTransactor( command.getTransactor() );
        externalReportAddEvent.setOccurred( command.getOccurred() );
        externalReportAddEvent.setReportCode( command.getReportCode() );
        externalReportAddEvent.setReportName( command.getReportName() );
        externalReportAddEvent.setPlanCode( command.getPlanCode() );
        externalReportAddEvent.setPlanName( command.getPlanName() );
        externalReportAddEvent.setReportType( command.getReportType() );
        externalReportAddEvent.setVersionCode( command.getVersionCode() );
        externalReportAddEvent.setVersionName( command.getVersionName() );
        externalReportAddEvent.setProductCode( command.getProductCode() );
        externalReportAddEvent.setProductName( command.getProductName() );
        externalReportAddEvent.setTestResult( command.getTestResult() );
        externalReportAddEvent.setSummary( command.getSummary() );
        externalReportAddEvent.setStatus( command.getStatus() );
        externalReportAddEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            externalReportAddEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            externalReportAddEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        externalReportAddEvent.setZuiTestResult( command.getZuiTestResult() );
        externalReportAddEvent.setPresentationDate( command.getPresentationDate() );
        externalReportAddEvent.setPresentationDay( command.getPresentationDay() );
        externalReportAddEvent.setPublishDate( command.getPublishDate() );
        try {
            if ( command.getPublishDay() != null ) {
                externalReportAddEvent.setPublishDay( new SimpleDateFormat().parse( command.getPublishDay() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        externalReportAddEvent.setCheckStartDate( command.getCheckStartDate() );
        externalReportAddEvent.setCheckEndDate( command.getCheckEndDate() );
        externalReportAddEvent.setActualPublishDate( command.getActualPublishDate() );
        externalReportAddEvent.setDelay( command.getDelay() );
        externalReportAddEvent.setDelayDesc( command.getDelayDesc() );
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            externalReportAddEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        externalReportAddEvent.setUiTestResult( command.getUiTestResult() );

        return externalReportAddEvent;
    }

    @Override
    public TestReportSendEvent convertTestReportSendEvent(AddAndSendExternalTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setActualOnlineDate( command.getActualPublishDate() );
        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setCheckStartDate( command.getCheckStartDate() );
        testReportSendEvent.setCheckEndDate( command.getCheckEndDate() );
        testReportSendEvent.setDelay( command.getDelay() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            testReportSendEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public ExternalReportEditEvent convert(EditAndSendExternalTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        ExternalReportEditEvent externalReportEditEvent = new ExternalReportEditEvent();

        externalReportEditEvent.setActualOnlineDate( command.getActualPublishDate() );
        externalReportEditEvent.setAggregateId( command.getAggregateId() );
        externalReportEditEvent.setTransactor( command.getTransactor() );
        externalReportEditEvent.setOccurred( command.getOccurred() );
        externalReportEditEvent.setReportCode( command.getReportCode() );
        externalReportEditEvent.setReportName( command.getReportName() );
        externalReportEditEvent.setPlanCode( command.getPlanCode() );
        externalReportEditEvent.setPlanName( command.getPlanName() );
        externalReportEditEvent.setReportType( command.getReportType() );
        externalReportEditEvent.setVersionCode( command.getVersionCode() );
        externalReportEditEvent.setVersionName( command.getVersionName() );
        externalReportEditEvent.setProductCode( command.getProductCode() );
        externalReportEditEvent.setProductName( command.getProductName() );
        externalReportEditEvent.setTestResult( command.getTestResult() );
        externalReportEditEvent.setSummary( command.getSummary() );
        externalReportEditEvent.setStatus( command.getStatus() );
        externalReportEditEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            externalReportEditEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            externalReportEditEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        externalReportEditEvent.setZuiTestResult( command.getZuiTestResult() );
        externalReportEditEvent.setPresentationDate( command.getPresentationDate() );
        externalReportEditEvent.setPresentationDay( command.getPresentationDay() );
        externalReportEditEvent.setPublishDate( command.getPublishDate() );
        try {
            if ( command.getPublishDay() != null ) {
                externalReportEditEvent.setPublishDay( new SimpleDateFormat().parse( command.getPublishDay() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        externalReportEditEvent.setCheckStartDate( command.getCheckStartDate() );
        externalReportEditEvent.setCheckEndDate( command.getCheckEndDate() );
        externalReportEditEvent.setActualPublishDate( command.getActualPublishDate() );
        externalReportEditEvent.setDelay( command.getDelay() );
        externalReportEditEvent.setDelayDesc( command.getDelayDesc() );
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            externalReportEditEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        externalReportEditEvent.setUiTestResult( command.getUiTestResult() );

        return externalReportEditEvent;
    }

    @Override
    public TestReportSendEvent convertTestReportSendEvent(EditAndSendExternalTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setActualOnlineDate( command.getActualPublishDate() );
        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setCheckStartDate( command.getCheckStartDate() );
        testReportSendEvent.setCheckEndDate( command.getCheckEndDate() );
        testReportSendEvent.setDelay( command.getDelay() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = command.getAttachments();
        if ( list2 != null ) {
            testReportSendEvent.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public MobileTestReportAddedEvent convert(AddAndSendMobileTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        MobileTestReportAddedEvent mobileTestReportAddedEvent = new MobileTestReportAddedEvent();

        mobileTestReportAddedEvent.setAggregateId( command.getAggregateId() );
        mobileTestReportAddedEvent.setTransactor( command.getTransactor() );
        mobileTestReportAddedEvent.setOccurred( command.getOccurred() );
        mobileTestReportAddedEvent.setReportCode( command.getReportCode() );
        mobileTestReportAddedEvent.setReportName( command.getReportName() );
        mobileTestReportAddedEvent.setPlanCode( command.getPlanCode() );
        mobileTestReportAddedEvent.setPlanName( command.getPlanName() );
        mobileTestReportAddedEvent.setReportType( command.getReportType() );
        mobileTestReportAddedEvent.setVersionCode( command.getVersionCode() );
        mobileTestReportAddedEvent.setVersionName( command.getVersionName() );
        mobileTestReportAddedEvent.setProductCode( command.getProductCode() );
        mobileTestReportAddedEvent.setProductName( command.getProductName() );
        mobileTestReportAddedEvent.setTestResult( command.getTestResult() );
        mobileTestReportAddedEvent.setStatus( command.getStatus() );
        mobileTestReportAddedEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            mobileTestReportAddedEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            mobileTestReportAddedEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        mobileTestReportAddedEvent.setActualTestStart( command.getActualTestStart() );
        mobileTestReportAddedEvent.setActualTestStartDay( command.getActualTestStartDay() );
        mobileTestReportAddedEvent.setActualTestEnd( command.getActualTestEnd() );
        mobileTestReportAddedEvent.setActualTestEndDay( command.getActualTestEndDay() );
        mobileTestReportAddedEvent.setUpdateTestResultDate( command.getUpdateTestResultDate() );
        List<TmModuleTestVO> list2 = command.getModuleTestVOS();
        if ( list2 != null ) {
            mobileTestReportAddedEvent.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list2 ) );
        }

        return mobileTestReportAddedEvent;
    }

    @Override
    public TestReportSendEvent convertTestReportSendEvent(AddAndSendMobileTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setActualTestStart( command.getActualTestStart() );
        testReportSendEvent.setActualTestEnd( command.getActualTestEnd() );
        testReportSendEvent.setUpdateTestResultDate( command.getUpdateTestResultDate() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public MobileTestReportEditEvent convert(EditAndSendMobileTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        MobileTestReportEditEvent mobileTestReportEditEvent = new MobileTestReportEditEvent();

        mobileTestReportEditEvent.setAggregateId( command.getAggregateId() );
        mobileTestReportEditEvent.setTransactor( command.getTransactor() );
        mobileTestReportEditEvent.setOccurred( command.getOccurred() );
        mobileTestReportEditEvent.setReportCode( command.getReportCode() );
        mobileTestReportEditEvent.setReportName( command.getReportName() );
        mobileTestReportEditEvent.setPlanCode( command.getPlanCode() );
        mobileTestReportEditEvent.setPlanName( command.getPlanName() );
        mobileTestReportEditEvent.setReportType( command.getReportType() );
        mobileTestReportEditEvent.setVersionCode( command.getVersionCode() );
        mobileTestReportEditEvent.setVersionName( command.getVersionName() );
        mobileTestReportEditEvent.setProductCode( command.getProductCode() );
        mobileTestReportEditEvent.setProductName( command.getProductName() );
        mobileTestReportEditEvent.setTestResult( command.getTestResult() );
        mobileTestReportEditEvent.setStatus( command.getStatus() );
        mobileTestReportEditEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            mobileTestReportEditEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            mobileTestReportEditEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        mobileTestReportEditEvent.setActualTestStart( command.getActualTestStart() );
        mobileTestReportEditEvent.setActualTestStartDay( command.getActualTestStartDay() );
        mobileTestReportEditEvent.setActualTestEnd( command.getActualTestEnd() );
        mobileTestReportEditEvent.setActualTestEndDay( command.getActualTestEndDay() );

        return mobileTestReportEditEvent;
    }

    @Override
    public TestReportSendEvent convertTestReportSendEvent(EditAndSendMobileTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setActualTestStart( command.getActualTestStart() );
        testReportSendEvent.setActualTestEnd( command.getActualTestEnd() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public TmOnlineSmokeReportAddEvent convert(AddAndSendTmOnlineSmokeTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmOnlineSmokeReportAddEvent tmOnlineSmokeReportAddEvent = new TmOnlineSmokeReportAddEvent();

        tmOnlineSmokeReportAddEvent.setActualOnlineDate( command.getActualPublishDate() );
        tmOnlineSmokeReportAddEvent.setAggregateId( command.getAggregateId() );
        tmOnlineSmokeReportAddEvent.setTransactor( command.getTransactor() );
        tmOnlineSmokeReportAddEvent.setOccurred( command.getOccurred() );
        tmOnlineSmokeReportAddEvent.setReportCode( command.getReportCode() );
        tmOnlineSmokeReportAddEvent.setReportName( command.getReportName() );
        tmOnlineSmokeReportAddEvent.setPlanCode( command.getPlanCode() );
        tmOnlineSmokeReportAddEvent.setPlanName( command.getPlanName() );
        tmOnlineSmokeReportAddEvent.setReportType( command.getReportType() );
        tmOnlineSmokeReportAddEvent.setVersionCode( command.getVersionCode() );
        tmOnlineSmokeReportAddEvent.setVersionName( command.getVersionName() );
        tmOnlineSmokeReportAddEvent.setProductCode( command.getProductCode() );
        tmOnlineSmokeReportAddEvent.setProductName( command.getProductName() );
        tmOnlineSmokeReportAddEvent.setTestResult( command.getTestResult() );
        tmOnlineSmokeReportAddEvent.setTestCaseNum( command.getTestCaseNum() );
        tmOnlineSmokeReportAddEvent.setPlanCaseNum( command.getPlanCaseNum() );
        tmOnlineSmokeReportAddEvent.setPermitNum( command.getPermitNum() );
        tmOnlineSmokeReportAddEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        tmOnlineSmokeReportAddEvent.setDelay( command.getDelay() );
        tmOnlineSmokeReportAddEvent.setSummary( command.getSummary() );
        tmOnlineSmokeReportAddEvent.setStatus( command.getStatus() );
        tmOnlineSmokeReportAddEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            tmOnlineSmokeReportAddEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            tmOnlineSmokeReportAddEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        tmOnlineSmokeReportAddEvent.setZuiTestResult( command.getZuiTestResult() );

        return tmOnlineSmokeReportAddEvent;
    }

    @Override
    public TestReportSendEvent convertEmailSendEvent(AddAndSendTmOnlineSmokeTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setActualOnlineDate( command.getActualPublishDate() );
        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setTestCaseNum( command.getTestCaseNum() );
        testReportSendEvent.setPlanCaseNum( command.getPlanCaseNum() );
        testReportSendEvent.setPermitNum( command.getPermitNum() );
        testReportSendEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        testReportSendEvent.setDelay( command.getDelay() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public TmOnlineSmokeReportEditEvent convert(EditAndSendTmOnlineSmokeTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmOnlineSmokeReportEditEvent tmOnlineSmokeReportEditEvent = new TmOnlineSmokeReportEditEvent();

        tmOnlineSmokeReportEditEvent.setActualOnlineDate( command.getActualPublishDate() );
        tmOnlineSmokeReportEditEvent.setAggregateId( command.getAggregateId() );
        tmOnlineSmokeReportEditEvent.setTransactor( command.getTransactor() );
        tmOnlineSmokeReportEditEvent.setOccurred( command.getOccurred() );
        tmOnlineSmokeReportEditEvent.setReportCode( command.getReportCode() );
        tmOnlineSmokeReportEditEvent.setReportName( command.getReportName() );
        tmOnlineSmokeReportEditEvent.setPlanCode( command.getPlanCode() );
        tmOnlineSmokeReportEditEvent.setPlanName( command.getPlanName() );
        tmOnlineSmokeReportEditEvent.setReportType( command.getReportType() );
        tmOnlineSmokeReportEditEvent.setVersionCode( command.getVersionCode() );
        tmOnlineSmokeReportEditEvent.setVersionName( command.getVersionName() );
        tmOnlineSmokeReportEditEvent.setProductCode( command.getProductCode() );
        tmOnlineSmokeReportEditEvent.setProductName( command.getProductName() );
        tmOnlineSmokeReportEditEvent.setTestResult( command.getTestResult() );
        tmOnlineSmokeReportEditEvent.setTestCaseNum( command.getTestCaseNum() );
        tmOnlineSmokeReportEditEvent.setPlanCaseNum( command.getPlanCaseNum() );
        tmOnlineSmokeReportEditEvent.setPermitNum( command.getPermitNum() );
        tmOnlineSmokeReportEditEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        tmOnlineSmokeReportEditEvent.setDelay( command.getDelay() );
        tmOnlineSmokeReportEditEvent.setSummary( command.getSummary() );
        tmOnlineSmokeReportEditEvent.setStatus( command.getStatus() );
        tmOnlineSmokeReportEditEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            tmOnlineSmokeReportEditEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            tmOnlineSmokeReportEditEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        tmOnlineSmokeReportEditEvent.setZuiTestResult( command.getZuiTestResult() );

        return tmOnlineSmokeReportEditEvent;
    }

    @Override
    public TestReportSendEvent convertEmailSendEvent(EditAndSendTmOnlineSmokeTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setActualOnlineDate( command.getActualPublishDate() );
        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setTestCaseNum( command.getTestCaseNum() );
        testReportSendEvent.setPlanCaseNum( command.getPlanCaseNum() );
        testReportSendEvent.setPermitNum( command.getPermitNum() );
        testReportSendEvent.setAsPlanedOnline( command.getAsPlanedOnline() );
        testReportSendEvent.setDelay( command.getDelay() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public TmPermitReportAddEvent convert(AddAndSendTmPermitTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmPermitReportAddEvent tmPermitReportAddEvent = new TmPermitReportAddEvent();

        tmPermitReportAddEvent.setAggregateId( command.getAggregateId() );
        tmPermitReportAddEvent.setTransactor( command.getTransactor() );
        tmPermitReportAddEvent.setOccurred( command.getOccurred() );
        tmPermitReportAddEvent.setReportCode( command.getReportCode() );
        tmPermitReportAddEvent.setReportName( command.getReportName() );
        tmPermitReportAddEvent.setPlanCode( command.getPlanCode() );
        tmPermitReportAddEvent.setPlanName( command.getPlanName() );
        tmPermitReportAddEvent.setReportType( command.getReportType() );
        tmPermitReportAddEvent.setVersionCode( command.getVersionCode() );
        tmPermitReportAddEvent.setVersionName( command.getVersionName() );
        tmPermitReportAddEvent.setProductCode( command.getProductCode() );
        tmPermitReportAddEvent.setProductName( command.getProductName() );
        tmPermitReportAddEvent.setTestResult( command.getTestResult() );
        tmPermitReportAddEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        tmPermitReportAddEvent.setTestCaseNum( command.getTestCaseNum() );
        tmPermitReportAddEvent.setPlanCaseNum( command.getPlanCaseNum() );
        tmPermitReportAddEvent.setPermitNum( command.getPermitNum() );
        tmPermitReportAddEvent.setSummary( command.getSummary() );
        tmPermitReportAddEvent.setStatus( command.getStatus() );
        tmPermitReportAddEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            tmPermitReportAddEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            tmPermitReportAddEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        tmPermitReportAddEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        tmPermitReportAddEvent.setDelayDays( command.getDelayDays() );
        tmPermitReportAddEvent.setCaseResultVO( command.getCaseResultVO() );
        tmPermitReportAddEvent.setSecurityUserId( command.getSecurityUserId() );
        tmPermitReportAddEvent.setSecurityUserName( command.getSecurityUserName() );
        tmPermitReportAddEvent.setSecurityTestResultCode( command.getSecurityTestResultCode() );
        tmPermitReportAddEvent.setSecurityTestResultDesc( command.getSecurityTestResultDesc() );
        List<TmModuleTestVO> list2 = command.getModuleTestVOS();
        if ( list2 != null ) {
            tmPermitReportAddEvent.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list2 ) );
        }
        tmPermitReportAddEvent.setCoverageResult( command.getCoverageResult() );
        List<CoverageReasonVO> list3 = command.getCoverageReasonVOS();
        if ( list3 != null ) {
            tmPermitReportAddEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list3 ) );
        }
        tmPermitReportAddEvent.setUiTestResult( command.getUiTestResult() );
        tmPermitReportAddEvent.setZuiTestResult( command.getZuiTestResult() );

        return tmPermitReportAddEvent;
    }

    @Override
    public CoverageReasonEditEvent convertAddPermitReasonEvent(AddAndSendTmPermitTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageReasonEditEvent coverageReasonEditEvent = new CoverageReasonEditEvent();

        coverageReasonEditEvent.setAggregateId( command.getAggregateId() );
        coverageReasonEditEvent.setTransactor( command.getTransactor() );
        coverageReasonEditEvent.setOccurred( command.getOccurred() );
        coverageReasonEditEvent.setVersionCode( command.getVersionCode() );
        coverageReasonEditEvent.setCoverageResult( command.getCoverageResult() );
        List<CoverageReasonVO> list = command.getCoverageReasonVOS();
        if ( list != null ) {
            coverageReasonEditEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list ) );
        }

        return coverageReasonEditEvent;
    }

    @Override
    public TestReportSendEvent convertEmailSendEvent(AddAndSendTmPermitTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        testReportSendEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        testReportSendEvent.setSecurityUserId( command.getSecurityUserId() );
        testReportSendEvent.setSecurityUserName( command.getSecurityUserName() );
        testReportSendEvent.setTestCaseNum( command.getTestCaseNum() );
        testReportSendEvent.setPlanCaseNum( command.getPlanCaseNum() );
        testReportSendEvent.setPermitNum( command.getPermitNum() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public TmPermitReportEditedEvent convert(EditAndSendTmPermitTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TmPermitReportEditedEvent tmPermitReportEditedEvent = new TmPermitReportEditedEvent();

        tmPermitReportEditedEvent.setAggregateId( command.getAggregateId() );
        tmPermitReportEditedEvent.setTransactor( command.getTransactor() );
        tmPermitReportEditedEvent.setOccurred( command.getOccurred() );
        tmPermitReportEditedEvent.setReportCode( command.getReportCode() );
        tmPermitReportEditedEvent.setReportName( command.getReportName() );
        tmPermitReportEditedEvent.setPlanCode( command.getPlanCode() );
        tmPermitReportEditedEvent.setPlanName( command.getPlanName() );
        tmPermitReportEditedEvent.setReportType( command.getReportType() );
        tmPermitReportEditedEvent.setVersionCode( command.getVersionCode() );
        tmPermitReportEditedEvent.setVersionName( command.getVersionName() );
        tmPermitReportEditedEvent.setProductCode( command.getProductCode() );
        tmPermitReportEditedEvent.setProductName( command.getProductName() );
        tmPermitReportEditedEvent.setTestResult( command.getTestResult() );
        tmPermitReportEditedEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        tmPermitReportEditedEvent.setTestCaseNum( command.getTestCaseNum() );
        tmPermitReportEditedEvent.setPlanCaseNum( command.getPlanCaseNum() );
        tmPermitReportEditedEvent.setPermitNum( command.getPermitNum() );
        tmPermitReportEditedEvent.setSummary( command.getSummary() );
        tmPermitReportEditedEvent.setStatus( command.getStatus() );
        tmPermitReportEditedEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            tmPermitReportEditedEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            tmPermitReportEditedEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        tmPermitReportEditedEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        tmPermitReportEditedEvent.setDelayDays( command.getDelayDays() );
        tmPermitReportEditedEvent.setCaseResultVO( command.getCaseResultVO() );
        tmPermitReportEditedEvent.setSecurityUserId( command.getSecurityUserId() );
        tmPermitReportEditedEvent.setSecurityUserName( command.getSecurityUserName() );
        tmPermitReportEditedEvent.setSecurityTestResultCode( command.getSecurityTestResultCode() );
        tmPermitReportEditedEvent.setSecurityTestResultDesc( command.getSecurityTestResultDesc() );
        List<TmModuleTestVO> list2 = command.getModuleTestVOS();
        if ( list2 != null ) {
            tmPermitReportEditedEvent.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list2 ) );
        }
        tmPermitReportEditedEvent.setCoverageResult( command.getCoverageResult() );
        List<CoverageReasonVO> list3 = command.getCoverageReasonVOS();
        if ( list3 != null ) {
            tmPermitReportEditedEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list3 ) );
        }
        tmPermitReportEditedEvent.setUiTestResult( command.getUiTestResult() );
        tmPermitReportEditedEvent.setZuiTestResult( command.getZuiTestResult() );

        return tmPermitReportEditedEvent;
    }

    @Override
    public CoverageReasonEditEvent convertEditPermitReasonEvent(EditAndSendTmPermitTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageReasonEditEvent coverageReasonEditEvent = new CoverageReasonEditEvent();

        coverageReasonEditEvent.setAggregateId( command.getAggregateId() );
        coverageReasonEditEvent.setTransactor( command.getTransactor() );
        coverageReasonEditEvent.setOccurred( command.getOccurred() );
        coverageReasonEditEvent.setVersionCode( command.getVersionCode() );
        coverageReasonEditEvent.setCoverageResult( command.getCoverageResult() );
        List<CoverageReasonVO> list = command.getCoverageReasonVOS();
        if ( list != null ) {
            coverageReasonEditEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list ) );
        }

        return coverageReasonEditEvent;
    }

    @Override
    public TestReportSendEvent convertEmailSendEvent(EditAndSendTmPermitTestReportCommand command) {
        if ( command == null ) {
            return null;
        }

        TestReportSendEvent testReportSendEvent = new TestReportSendEvent();

        testReportSendEvent.setAggregateId( command.getAggregateId() );
        testReportSendEvent.setTransactor( command.getTransactor() );
        testReportSendEvent.setOccurred( command.getOccurred() );
        testReportSendEvent.setReportCode( command.getReportCode() );
        testReportSendEvent.setReportName( command.getReportName() );
        testReportSendEvent.setPlanCode( command.getPlanCode() );
        testReportSendEvent.setPlanName( command.getPlanName() );
        testReportSendEvent.setReportType( command.getReportType() );
        testReportSendEvent.setVersionCode( command.getVersionCode() );
        testReportSendEvent.setVersionName( command.getVersionName() );
        testReportSendEvent.setProductCode( command.getProductCode() );
        testReportSendEvent.setProductName( command.getProductName() );
        testReportSendEvent.setTestResult( command.getTestResult() );
        testReportSendEvent.setPlanApprovalExitDate( command.getPlanApprovalExitDate() );
        testReportSendEvent.setActualApprovalExitDate( command.getActualApprovalExitDate() );
        testReportSendEvent.setSecurityUserId( command.getSecurityUserId() );
        testReportSendEvent.setSecurityUserName( command.getSecurityUserName() );
        testReportSendEvent.setTestCaseNum( command.getTestCaseNum() );
        testReportSendEvent.setPlanCaseNum( command.getPlanCaseNum() );
        testReportSendEvent.setPermitNum( command.getPermitNum() );
        testReportSendEvent.setSummary( command.getSummary() );
        testReportSendEvent.setStatus( command.getStatus() );
        testReportSendEvent.setPreview( command.getPreview() );
        List<SendUserInfoVO> list = command.getReceiveUsers();
        if ( list != null ) {
            testReportSendEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = command.getCcUsers();
        if ( list1 != null ) {
            testReportSendEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return testReportSendEvent;
    }

    @Override
    public PageReportQuery convert(PageReportMqQuery query) {
        if ( query == null ) {
            return null;
        }

        PageReportQuery pageReportQuery = new PageReportQuery();

        pageReportQuery.setPage( query.getPage() );
        pageReportQuery.setSize( query.getSize() );
        pageReportQuery.setCodeOrName( query.getCodeOrName() );
        List<String> list = query.getProductCodes();
        if ( list != null ) {
            pageReportQuery.setProductCodes( new ArrayList<String>( list ) );
        }
        List<Long> list1 = query.getReportUserIds();
        if ( list1 != null ) {
            pageReportQuery.setReportUserIds( new ArrayList<Long>( list1 ) );
        }
        pageReportQuery.setReportTypes( reportTypeListToReportTypeList( query.getReportTypes() ) );
        pageReportQuery.setGmtCreateStart( query.getGmtCreateStart() );
        pageReportQuery.setGmtCreateEnd( query.getGmtCreateEnd() );
        pageReportQuery.setStatus( query.getStatus() );
        pageReportQuery.setVersionCode( query.getVersionCode() );
        pageReportQuery.setTransactor( query.getTransactor() );
        pageReportQuery.setCreatorId( query.getCreatorId() );
        pageReportQuery.setOrderField( query.getOrderField() );
        pageReportQuery.setOrderType( query.getOrderType() );

        return pageReportQuery;
    }

    @Override
    public List<TestFunctionPointVO> convertToVo(List<TestFunctionPointEntityDO> doList) {
        if ( doList == null ) {
            return null;
        }

        List<TestFunctionPointVO> list = new ArrayList<TestFunctionPointVO>( doList.size() );
        for ( TestFunctionPointEntityDO testFunctionPointEntityDO : doList ) {
            list.add( testFunctionPointEntityDOToTestFunctionPointVO( testFunctionPointEntityDO ) );
        }

        return list;
    }

    @Override
    public List<SendUserInfoVO> convert(List<QcNoticeResultEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<SendUserInfoVO> list = new ArrayList<SendUserInfoVO>( entityDOList.size() );
        for ( QcNoticeResultEntityDO qcNoticeResultEntityDO : entityDOList ) {
            list.add( qcNoticeResultEntityDOToSendUserInfoVO( qcNoticeResultEntityDO ) );
        }

        return list;
    }

    protected AutoExecuteResult autoExecuteResultToAutoExecuteResult(com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult autoExecuteResult) {
        if ( autoExecuteResult == null ) {
            return null;
        }

        AutoExecuteResult autoExecuteResult1;

        switch ( autoExecuteResult ) {
            case UNKNOWN: autoExecuteResult1 = AutoExecuteResult.UNKNOWN;
            break;
            case WAIT_EXECUTED: autoExecuteResult1 = AutoExecuteResult.WAIT_EXECUTED;
            break;
            case EXECUTING: autoExecuteResult1 = AutoExecuteResult.EXECUTING;
            break;
            case NO_EXECUTE: autoExecuteResult1 = AutoExecuteResult.NO_EXECUTE;
            break;
            case PASS_BY: autoExecuteResult1 = AutoExecuteResult.PASS_BY;
            break;
            case NO_PASS: autoExecuteResult1 = AutoExecuteResult.NO_PASS;
            break;
            case FAILURE: autoExecuteResult1 = AutoExecuteResult.FAILURE;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + autoExecuteResult );
        }

        return autoExecuteResult1;
    }

    protected ReviewType reviewTypeToReviewType(com.zto.devops.qc.client.enums.testmanager.report.ReviewType reviewType) {
        if ( reviewType == null ) {
            return null;
        }

        ReviewType reviewType1;

        switch ( reviewType ) {
            case UNKNOWN: reviewType1 = ReviewType.UNKNOWN;
            break;
            case MEET_REVIEW: reviewType1 = ReviewType.MEET_REVIEW;
            break;
            case MAIL_REVIEW: reviewType1 = ReviewType.MAIL_REVIEW;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + reviewType );
        }

        return reviewType1;
    }

    protected ReviewInfoDTO reviewInfoVOToReviewInfoDTO(ReviewInfoVO reviewInfoVO) {
        if ( reviewInfoVO == null ) {
            return null;
        }

        ReviewInfoDTO reviewInfoDTO = new ReviewInfoDTO();

        reviewInfoDTO.setCode( reviewInfoVO.getCode() );
        reviewInfoDTO.setReportCode( reviewInfoVO.getReportCode() );
        reviewInfoDTO.setReviewDate( reviewInfoVO.getReviewDate() );
        reviewInfoDTO.setReviewType( reviewTypeToReviewType( reviewInfoVO.getReviewType() ) );
        reviewInfoDTO.setReviewTypeDesc( reviewInfoVO.getReviewTypeDesc() );
        reviewInfoDTO.setRecordUserId( reviewInfoVO.getRecordUserId() );
        reviewInfoDTO.setRecordUserName( reviewInfoVO.getRecordUserName() );

        return reviewInfoDTO;
    }

    protected ReviewUserVO userSelectVOToReviewUserVO(UserSelectVO userSelectVO) {
        if ( userSelectVO == null ) {
            return null;
        }

        ReviewUserVO reviewUserVO = new ReviewUserVO();

        reviewUserVO.setSsoUserId( userSelectVO.getSsoUserId() );
        reviewUserVO.setCnName( userSelectVO.getCnName() );
        reviewUserVO.setDeptId( userSelectVO.getDeptId() );
        reviewUserVO.setDeptName( userSelectVO.getDeptName() );
        reviewUserVO.setStationName( userSelectVO.getStationName() );
        reviewUserVO.setAvatar( userSelectVO.getAvatar() );

        return reviewUserVO;
    }

    protected ReportType reportTypeToReportType(com.zto.devops.qc.client.enums.testmanager.report.ReportType reportType) {
        if ( reportType == null ) {
            return null;
        }

        ReportType reportType1;

        switch ( reportType ) {
            case NULL: reportType1 = ReportType.NULL;
            break;
            case UNKNOWN: reportType1 = ReportType.UNKNOWN;
            break;
            case TEST_ACCESS: reportType1 = ReportType.TEST_ACCESS;
            break;
            case TEST_PERMIT: reportType1 = ReportType.TEST_PERMIT;
            break;
            case SPECIAL_MOBILE: reportType1 = ReportType.SPECIAL_MOBILE;
            break;
            case INTEGRATION_TEST: reportType1 = ReportType.INTEGRATION_TEST;
            break;
            case ONLINE_SMOKE: reportType1 = ReportType.ONLINE_SMOKE;
            break;
            case CHECED_TEST: reportType1 = ReportType.CHECED_TEST;
            break;
            case SIMPLE_PROCESS: reportType1 = ReportType.SIMPLE_PROCESS;
            break;
            case CASE_REVIEW: reportType1 = ReportType.CASE_REVIEW;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + reportType );
        }

        return reportType1;
    }

    protected List<ReportType> reportTypeListToReportTypeList(List<com.zto.devops.qc.client.enums.testmanager.report.ReportType> list) {
        if ( list == null ) {
            return null;
        }

        List<ReportType> list1 = new ArrayList<ReportType>( list.size() );
        for ( com.zto.devops.qc.client.enums.testmanager.report.ReportType reportType : list ) {
            list1.add( reportTypeToReportType( reportType ) );
        }

        return list1;
    }

    protected TestFunctionPointVO testFunctionPointEntityDOToTestFunctionPointVO(TestFunctionPointEntityDO testFunctionPointEntityDO) {
        if ( testFunctionPointEntityDO == null ) {
            return null;
        }

        TestFunctionPointVO testFunctionPointVO = new TestFunctionPointVO();

        testFunctionPointVO.setCode( testFunctionPointEntityDO.getCode() );
        testFunctionPointVO.setType( testFunctionPointEntityDO.getType() );
        testFunctionPointVO.setFunctionPoint( testFunctionPointEntityDO.getFunctionPoint() );
        testFunctionPointVO.setDirectorId( testFunctionPointEntityDO.getDirectorId() );
        testFunctionPointVO.setDirectorName( testFunctionPointEntityDO.getDirectorName() );
        testFunctionPointVO.setBusinessCode( testFunctionPointEntityDO.getBusinessCode() );
        testFunctionPointVO.setNumber( testFunctionPointEntityDO.getNumber() );

        return testFunctionPointVO;
    }

    protected SendUserInfoVO qcNoticeResultEntityDOToSendUserInfoVO(QcNoticeResultEntityDO qcNoticeResultEntityDO) {
        if ( qcNoticeResultEntityDO == null ) {
            return null;
        }

        SendUserInfoVO sendUserInfoVO = new SendUserInfoVO();

        sendUserInfoVO.setUserName( qcNoticeResultEntityDO.getUserName() );
        if ( qcNoticeResultEntityDO.getUserId() != null ) {
            sendUserInfoVO.setUserId( qcNoticeResultEntityDO.getUserId().longValue() );
        }
        sendUserInfoVO.setUserType( qcNoticeResultEntityDO.getUserType() );
        sendUserInfoVO.setDeptName( qcNoticeResultEntityDO.getDeptName() );
        sendUserInfoVO.setEmail( qcNoticeResultEntityDO.getEmail() );

        return sendUserInfoVO;
    }
}
