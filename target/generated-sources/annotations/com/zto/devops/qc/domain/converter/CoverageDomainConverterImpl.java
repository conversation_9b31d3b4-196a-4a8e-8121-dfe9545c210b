package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.rpc.pipeline.event.ReleasedQcEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.command.CoverageNotStandardReasonEditCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.EditCoverageReasonCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordBasicVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageStatusVO;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageNotStandardReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.ReportCoveredDto;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageReportClassInfoResp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class CoverageDomainConverterImpl implements CoverageDomainConverter {

    @Override
    public CoverageStatusVO converter(CoverageRecordVO coverageRecordVO) {
        if ( coverageRecordVO == null ) {
            return null;
        }

        CoverageStatusVO coverageStatusVO = new CoverageStatusVO();

        if ( coverageRecordVO.getBranchGmtCreate() != null ) {
            coverageStatusVO.setBranchGmtCreate( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( coverageRecordVO.getBranchGmtCreate() ) );
        }
        coverageStatusVO.setCode( coverageRecordVO.getCode() );
        coverageStatusVO.setVersionCode( coverageRecordVO.getVersionCode() );
        coverageStatusVO.setAppId( coverageRecordVO.getAppId() );
        coverageStatusVO.setStandardRate( coverageRecordVO.getStandardRate() );
        coverageStatusVO.setBranchRecordRate( coverageRecordVO.getBranchRecordRate() );
        coverageStatusVO.setBranchStatus( coverageRecordVO.getBranchStatus() );
        coverageStatusVO.setBranchRecordUrl( coverageRecordVO.getBranchRecordUrl() );
        coverageStatusVO.setBranchRemark( coverageRecordVO.getBranchRemark() );
        coverageStatusVO.setBranchRecordErrorMsg( coverageRecordVO.getBranchRecordErrorMsg() );
        coverageStatusVO.setBranchCreator( coverageRecordVO.getBranchCreator() );
        coverageStatusVO.setComment( coverageRecordVO.getComment() );

        return coverageStatusVO;
    }

    @Override
    public CoveragePublishQuery converter(ReleasedQcEvent event) {
        if ( event == null ) {
            return null;
        }

        CoveragePublishQuery coveragePublishQuery = new CoveragePublishQuery();

        coveragePublishQuery.setCreatorId( eventTransactorUserId( event ) );
        coveragePublishQuery.setCreator( eventTransactorUserName( event ) );
        List<String> list = event.getVersionCodes();
        if ( list != null ) {
            coveragePublishQuery.setVersionCodes( new ArrayList<String>( list ) );
        }
        coveragePublishQuery.setBranchName( event.getBranchName() );
        List<String> list1 = event.getAppIds();
        if ( list1 != null ) {
            coveragePublishQuery.setAppIds( new ArrayList<String>( list1 ) );
        }

        return coveragePublishQuery;
    }

    @Override
    public CoverageReasonEditEvent converter(EditCoverageReasonCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageReasonEditEvent coverageReasonEditEvent = new CoverageReasonEditEvent();

        coverageReasonEditEvent.setAggregateId( command.getAggregateId() );
        coverageReasonEditEvent.setTransactor( command.getTransactor() );
        coverageReasonEditEvent.setOccurred( command.getOccurred() );
        List<CoverageReasonVO> list = command.getCoverageReasonVOS();
        if ( list != null ) {
            coverageReasonEditEvent.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list ) );
        }

        return coverageReasonEditEvent;
    }

    @Override
    public CoverageNotStandardReasonEditEvent converter(CoverageNotStandardReasonEditCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageNotStandardReasonEditEvent coverageNotStandardReasonEditEvent = new CoverageNotStandardReasonEditEvent();

        coverageNotStandardReasonEditEvent.setAggregateId( command.getAggregateId() );
        coverageNotStandardReasonEditEvent.setTransactor( command.getTransactor() );
        coverageNotStandardReasonEditEvent.setOccurred( command.getOccurred() );
        coverageNotStandardReasonEditEvent.setVersionCode( command.getVersionCode() );
        coverageNotStandardReasonEditEvent.setAppId( command.getAppId() );
        coverageNotStandardReasonEditEvent.setProductCode( command.getProductCode() );
        List<String> list = command.getReasonList();
        if ( list != null ) {
            coverageNotStandardReasonEditEvent.setReasonList( new ArrayList<String>( list ) );
        }
        coverageNotStandardReasonEditEvent.setCustomReason( command.getCustomReason() );

        return coverageNotStandardReasonEditEvent;
    }

    @Override
    public CoverageReportClassInfoResp converter(String classInfo) {
        if ( classInfo == null ) {
            return null;
        }

        CoverageReportClassInfoResp coverageReportClassInfoResp = new CoverageReportClassInfoResp();

        return coverageReportClassInfoResp;
    }

    @Override
    public CoverageRecordGenerateVO converter(CoverageRecordBasicVO coverageRecord) {
        if ( coverageRecord == null ) {
            return null;
        }

        CoverageRecordGenerateVO coverageRecordGenerateVO = new CoverageRecordGenerateVO();

        coverageRecordGenerateVO.setEnable( coverageRecord.getEnable() );
        coverageRecordGenerateVO.setCreatorId( coverageRecord.getCreatorId() );
        coverageRecordGenerateVO.setCreator( coverageRecord.getCreator() );
        coverageRecordGenerateVO.setGmtCreate( coverageRecord.getGmtCreate() );
        coverageRecordGenerateVO.setModifierId( coverageRecord.getModifierId() );
        coverageRecordGenerateVO.setModifier( coverageRecord.getModifier() );
        coverageRecordGenerateVO.setGmtModified( coverageRecord.getGmtModified() );
        coverageRecordGenerateVO.setId( coverageRecord.getId() );
        coverageRecordGenerateVO.setVersionCode( coverageRecord.getVersionCode() );
        coverageRecordGenerateVO.setVersionName( coverageRecord.getVersionName() );
        coverageRecordGenerateVO.setAppId( coverageRecord.getAppId() );
        coverageRecordGenerateVO.setRecordType( coverageRecord.getRecordType() );
        coverageRecordGenerateVO.setGenerateType( coverageRecord.getGenerateType() );
        coverageRecordGenerateVO.setBranchName( coverageRecord.getBranchName() );
        coverageRecordGenerateVO.setCommitId( coverageRecord.getCommitId() );
        coverageRecordGenerateVO.setBasicBranchName( coverageRecord.getBasicBranchName() );
        coverageRecordGenerateVO.setBasicCommitId( coverageRecord.getBasicCommitId() );
        coverageRecordGenerateVO.setDiffType( coverageRecord.getDiffType() );
        coverageRecordGenerateVO.setEnvName( coverageRecord.getEnvName() );
        coverageRecordGenerateVO.setProductCode( coverageRecord.getProductCode() );
        if ( coverageRecord.getFlowLaneType() != null ) {
            coverageRecordGenerateVO.setFlowLaneType( coverageRecord.getFlowLaneType().name() );
        }
        coverageRecordGenerateVO.setBucketName( coverageRecord.getBucketName() );
        coverageRecordGenerateVO.setFileName( coverageRecord.getFileName() );

        return coverageRecordGenerateVO;
    }

    @Override
    public ReportCoveredDto converterReportCoveredSelf(ReportCoveredDto coverage) {
        if ( coverage == null ) {
            return null;
        }

        ReportCoveredDto reportCoveredDto = new ReportCoveredDto();

        reportCoveredDto.setName( coverage.getName() );
        reportCoveredDto.setMissed( coverage.getMissed() );
        reportCoveredDto.setCovered( coverage.getCovered() );

        return reportCoveredDto;
    }

    private Long eventTransactorUserId(ReleasedQcEvent releasedQcEvent) {
        if ( releasedQcEvent == null ) {
            return null;
        }
        User transactor = releasedQcEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(ReleasedQcEvent releasedQcEvent) {
        if ( releasedQcEvent == null ) {
            return null;
        }
        User transactor = releasedQcEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }
}
