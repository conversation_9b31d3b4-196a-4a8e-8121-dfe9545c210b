package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteCallbackCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTask;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskExecutedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskTerminatedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AutomaticSchedulerExecutionCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticPreExecutionUpdateEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerExecutionEvent;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticTaskEventConverterImpl implements AutomaticTaskEventConverter {

    @Override
    public ExecuteCallbackEvent converter(ExecuteCallbackCommand command) {
        if ( command == null ) {
            return null;
        }

        ExecuteCallbackEvent executeCallbackEvent = new ExecuteCallbackEvent();

        executeCallbackEvent.setCode( command.getAggregateId() );
        executeCallbackEvent.setAggregateId( command.getAggregateId() );
        executeCallbackEvent.setTransactor( command.getTransactor() );
        executeCallbackEvent.setOccurred( command.getOccurred() );
        executeCallbackEvent.setBuildId( command.getBuildId() );
        executeCallbackEvent.setStatus( command.getStatus() );
        executeCallbackEvent.setOssPath( command.getOssPath() );

        return executeCallbackEvent;
    }

    @Override
    public void convert(AutomaticTask domain, AutomaticTaskTerminatedEvent event) {
        if ( domain == null ) {
            return;
        }

        event.setCode( domain.getCode() );
        event.setBuildId( domain.getBuildId() );
        event.setTaskId( domain.getTaskId() );
        event.setType( domain.getType() );
        event.setProductCode( domain.getProductCode() );
        event.setTestPlanCode( domain.getTestPlanCode() );
        event.setTestStage( domain.getTestStage() );
        event.setTrigMode( domain.getTrigMode() );
    }

    @Override
    public AutomaticTaskExecutedEvent convert(AutomaticTaskEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        AutomaticTaskExecutedEvent automaticTaskExecutedEvent = new AutomaticTaskExecutedEvent();

        automaticTaskExecutedEvent.setTag( entityDO.getExecuteTag() );
        automaticTaskExecutedEvent.setCode( entityDO.getCode() );
        automaticTaskExecutedEvent.setTaskId( entityDO.getTaskId() );
        automaticTaskExecutedEvent.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        automaticTaskExecutedEvent.setSourceAddress( entityDO.getSourceAddress() );
        automaticTaskExecutedEvent.setFilename( entityDO.getFilename() );
        automaticTaskExecutedEvent.setEnv( entityDO.getEnv() );
        automaticTaskExecutedEvent.setTestPlanCode( entityDO.getTestPlanCode() );
        automaticTaskExecutedEvent.setTestStage( entityDO.getTestStage() );
        automaticTaskExecutedEvent.setVersionCode( entityDO.getVersionCode() );
        automaticTaskExecutedEvent.setTrigMode( entityDO.getTrigMode() );
        automaticTaskExecutedEvent.setType( entityDO.getType() );
        automaticTaskExecutedEvent.setBranchName( entityDO.getBranchName() );
        automaticTaskExecutedEvent.setWorkDir( entityDO.getWorkDir() );
        automaticTaskExecutedEvent.setCommitId( entityDO.getCommitId() );
        automaticTaskExecutedEvent.setCoverageFlag( entityDO.getCoverageFlag() );
        automaticTaskExecutedEvent.setSchedulerCode( entityDO.getSchedulerCode() );
        automaticTaskExecutedEvent.setProductCode( entityDO.getProductCode() );

        return automaticTaskExecutedEvent;
    }

    @Override
    public AutomaticSchedulerExecutionEvent converter(AutomaticSchedulerExecutionCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticSchedulerExecutionEvent automaticSchedulerExecutionEvent = new AutomaticSchedulerExecutionEvent();

        automaticSchedulerExecutionEvent.setSchedulerCode( command.getAggregateId() );
        automaticSchedulerExecutionEvent.setAggregateId( command.getAggregateId() );
        automaticSchedulerExecutionEvent.setTransactor( command.getTransactor() );
        automaticSchedulerExecutionEvent.setOccurred( command.getOccurred() );
        automaticSchedulerExecutionEvent.setPreCode( command.getPreCode() );
        automaticSchedulerExecutionEvent.setTrigMode( command.getTrigMode() );

        return automaticSchedulerExecutionEvent;
    }

    @Override
    public AutomaticPreExecutionUpdateEvent convert(AutomaticSchedulerExecutionCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticPreExecutionUpdateEvent automaticPreExecutionUpdateEvent = new AutomaticPreExecutionUpdateEvent();

        automaticPreExecutionUpdateEvent.setSchedulerCode( command.getAggregateId() );
        automaticPreExecutionUpdateEvent.setAggregateId( command.getAggregateId() );
        automaticPreExecutionUpdateEvent.setTransactor( command.getTransactor() );
        automaticPreExecutionUpdateEvent.setOccurred( command.getOccurred() );
        automaticPreExecutionUpdateEvent.setPreCode( command.getPreCode() );

        return automaticPreExecutionUpdateEvent;
    }
}
