package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.model.issue.entity.IssueMatterDataResp;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueRemovedEvent;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueMatterMatterMqConverterImpl implements IssueMatterMatterMqConverter {

    @Override
    public IssueMatterDataResp convert(IssueAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueMatterDataResp issueMatterDataResp = new IssueMatterDataResp();

        if ( event.getCode() != null ) {
            issueMatterDataResp.setCode( event.getCode() );
        }
        if ( event.getTitle() != null ) {
            issueMatterDataResp.setTitle( event.getTitle() );
        }
        if ( event.getStatus() != null ) {
            issueMatterDataResp.setStatus( event.getStatus().name() );
        }
        if ( event.getPriority() != null ) {
            issueMatterDataResp.setPriority( event.getPriority().name() );
        }
        if ( event.getRootCause() != null ) {
            issueMatterDataResp.setRootCause( event.getRootCause().name() );
        }
        if ( event.getType() != null ) {
            issueMatterDataResp.setType( event.getType().name() );
        }
        if ( event.getTestMethod() != null ) {
            issueMatterDataResp.setTestMethod( event.getTestMethod().name() );
        }
        if ( event.getFindStage() != null ) {
            issueMatterDataResp.setFindStage( event.getFindStage().name() );
        }
        if ( event.getFindEnv() != null ) {
            issueMatterDataResp.setFindEnv( event.getFindEnv().name() );
        }
        if ( event.getRepetitionRate() != null ) {
            issueMatterDataResp.setRepetitionRate( event.getRepetitionRate().name() );
        }
        if ( event.getFindTime() != null ) {
            issueMatterDataResp.setFindTime( event.getFindTime() );
        }
        if ( event.getUpdateTime() != null ) {
            issueMatterDataResp.setUpdateTime( event.getUpdateTime() );
        }
        if ( event.getDescription() != null ) {
            issueMatterDataResp.setDescription( event.getDescription() );
        }
        if ( event.getVersionConfirm() != null ) {
            issueMatterDataResp.setVersionConfirm( event.getVersionConfirm() );
        }

        return issueMatterDataResp;
    }

    @Override
    public IssueMatterDataResp convert(IssueEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueMatterDataResp issueMatterDataResp = new IssueMatterDataResp();

        String code = eventFindVersionCode( event );
        if ( code != null ) {
            issueMatterDataResp.setFindVersionCode( code );
        }
        String name = eventFindVersionName( event );
        if ( name != null ) {
            issueMatterDataResp.setFindVersionName( name );
        }
        String code1 = eventFixVersionCode( event );
        if ( code1 != null ) {
            issueMatterDataResp.setFixVersionCode( code1 );
        }
        String name1 = eventFixVersionName( event );
        if ( name1 != null ) {
            issueMatterDataResp.setFixVersionName( name1 );
        }
        if ( event.getCode() != null ) {
            issueMatterDataResp.setCode( event.getCode() );
        }
        if ( event.getTitle() != null ) {
            issueMatterDataResp.setTitle( event.getTitle() );
        }
        if ( event.getPriority() != null ) {
            issueMatterDataResp.setPriority( event.getPriority().name() );
        }
        if ( event.getRootCause() != null ) {
            issueMatterDataResp.setRootCause( event.getRootCause().name() );
        }
        if ( event.getType() != null ) {
            issueMatterDataResp.setType( event.getType().name() );
        }
        if ( event.getTestMethod() != null ) {
            issueMatterDataResp.setTestMethod( event.getTestMethod().name() );
        }
        if ( event.getFindStage() != null ) {
            issueMatterDataResp.setFindStage( event.getFindStage().name() );
        }
        if ( event.getFindEnv() != null ) {
            issueMatterDataResp.setFindEnv( event.getFindEnv().name() );
        }
        if ( event.getRepetitionRate() != null ) {
            issueMatterDataResp.setRepetitionRate( event.getRepetitionRate().name() );
        }
        if ( event.getUpdateTime() != null ) {
            issueMatterDataResp.setUpdateTime( event.getUpdateTime() );
        }
        if ( event.getDescription() != null ) {
            issueMatterDataResp.setDescription( event.getDescription() );
        }
        if ( event.getVersionConfirm() != null ) {
            issueMatterDataResp.setVersionConfirm( event.getVersionConfirm() );
        }

        return issueMatterDataResp;
    }

    @Override
    public IssueMatterDataResp convert(IssueRemovedEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueMatterDataResp issueMatterDataResp = new IssueMatterDataResp();

        if ( event.getCode() != null ) {
            issueMatterDataResp.setCode( event.getCode() );
        }
        if ( event.getStatus() != null ) {
            issueMatterDataResp.setStatus( event.getStatus().name() );
        }

        return issueMatterDataResp;
    }

    @Override
    public IssueMatterDataResp convert(IssueVO issueVO) {
        if ( issueVO == null ) {
            return null;
        }

        IssueMatterDataResp issueMatterDataResp = new IssueMatterDataResp();

        if ( issueVO.getCode() != null ) {
            issueMatterDataResp.setCode( issueVO.getCode() );
        }
        if ( issueVO.getTitle() != null ) {
            issueMatterDataResp.setTitle( issueVO.getTitle() );
        }
        if ( issueVO.getStatus() != null ) {
            issueMatterDataResp.setStatus( issueVO.getStatus().name() );
        }
        if ( issueVO.getPriority() != null ) {
            issueMatterDataResp.setPriority( issueVO.getPriority().name() );
        }
        if ( issueVO.getRootCause() != null ) {
            issueMatterDataResp.setRootCause( issueVO.getRootCause().name() );
        }
        if ( issueVO.getType() != null ) {
            issueMatterDataResp.setType( issueVO.getType().name() );
        }
        if ( issueVO.getTestMethod() != null ) {
            issueMatterDataResp.setTestMethod( issueVO.getTestMethod().name() );
        }
        if ( issueVO.getFindStage() != null ) {
            issueMatterDataResp.setFindStage( issueVO.getFindStage().name() );
        }
        if ( issueVO.getFindEnv() != null ) {
            issueMatterDataResp.setFindEnv( issueVO.getFindEnv().name() );
        }
        if ( issueVO.getRepetitionRate() != null ) {
            issueMatterDataResp.setRepetitionRate( issueVO.getRepetitionRate().name() );
        }
        if ( issueVO.getGmtCreate() != null ) {
            issueMatterDataResp.setGmtCreate( issueVO.getGmtCreate() );
        }
        if ( issueVO.getReopenTime() != null ) {
            issueMatterDataResp.setReopenTime( issueVO.getReopenTime() );
        }
        if ( issueVO.getActualWorkingHours() != null ) {
            issueMatterDataResp.setActualWorkingHours( issueVO.getActualWorkingHours() );
        }
        if ( issueVO.getStartFixTime() != null ) {
            issueMatterDataResp.setStartFixTime( issueVO.getStartFixTime() );
        }
        if ( issueVO.getDelayFixTime() != null ) {
            issueMatterDataResp.setDelayFixTime( issueVO.getDelayFixTime() );
        }
        if ( issueVO.getDeliverTime() != null ) {
            issueMatterDataResp.setDeliverTime( issueVO.getDeliverTime() );
        }
        if ( issueVO.getRejectTime() != null ) {
            issueMatterDataResp.setRejectTime( issueVO.getRejectTime() );
        }
        if ( issueVO.getCloseTime() != null ) {
            issueMatterDataResp.setCloseTime( issueVO.getCloseTime() );
        }
        if ( issueVO.getFindTime() != null ) {
            issueMatterDataResp.setFindTime( issueVO.getFindTime() );
        }
        if ( issueVO.getUpdateTime() != null ) {
            issueMatterDataResp.setUpdateTime( issueVO.getUpdateTime() );
        }
        if ( issueVO.getProductCode() != null ) {
            issueMatterDataResp.setProductCode( issueVO.getProductCode() );
        }
        if ( issueVO.getProductName() != null ) {
            issueMatterDataResp.setProductName( issueVO.getProductName() );
        }
        if ( issueVO.getRequirementCode() != null ) {
            issueMatterDataResp.setRequirementCode( issueVO.getRequirementCode() );
        }
        if ( issueVO.getRequirementName() != null ) {
            issueMatterDataResp.setRequirementName( issueVO.getRequirementName() );
        }
        if ( issueVO.getFindVersionCode() != null ) {
            issueMatterDataResp.setFindVersionCode( issueVO.getFindVersionCode() );
        }
        if ( issueVO.getFindVersionName() != null ) {
            issueMatterDataResp.setFindVersionName( issueVO.getFindVersionName() );
        }
        if ( issueVO.getFixVersionCode() != null ) {
            issueMatterDataResp.setFixVersionCode( issueVO.getFixVersionCode() );
        }
        if ( issueVO.getFixVersionName() != null ) {
            issueMatterDataResp.setFixVersionName( issueVO.getFixVersionName() );
        }
        if ( issueVO.getSprintCode() != null ) {
            issueMatterDataResp.setSprintCode( issueVO.getSprintCode() );
        }
        if ( issueVO.getSprintName() != null ) {
            issueMatterDataResp.setSprintName( issueVO.getSprintName() );
        }
        if ( issueVO.getFindUserId() != null ) {
            issueMatterDataResp.setFindUserId( issueVO.getFindUserId() );
        }
        if ( issueVO.getFindUserName() != null ) {
            issueMatterDataResp.setFindUserName( issueVO.getFindUserName() );
        }
        if ( issueVO.getUpdateUserId() != null ) {
            issueMatterDataResp.setUpdateUserId( issueVO.getUpdateUserId() );
        }
        if ( issueVO.getUpdateUserName() != null ) {
            issueMatterDataResp.setUpdateUserName( issueVO.getUpdateUserName() );
        }
        if ( issueVO.getHandleUserName() != null ) {
            issueMatterDataResp.setHandleUserName( issueVO.getHandleUserName() );
        }
        if ( issueVO.getHandleUserId() != null ) {
            issueMatterDataResp.setHandleUserId( issueVO.getHandleUserId() );
        }
        if ( issueVO.getDevelopUserId() != null ) {
            issueMatterDataResp.setDevelopUserId( issueVO.getDevelopUserId() );
        }
        if ( issueVO.getDevelopUserName() != null ) {
            issueMatterDataResp.setDevelopUserName( issueVO.getDevelopUserName() );
        }
        if ( issueVO.getTestUserId() != null ) {
            issueMatterDataResp.setTestUserId( issueVO.getTestUserId() );
        }
        if ( issueVO.getTestUserName() != null ) {
            issueMatterDataResp.setTestUserName( issueVO.getTestUserName() );
        }
        if ( issueVO.getCreatorId() != null ) {
            issueMatterDataResp.setCreatorId( issueVO.getCreatorId() );
        }
        if ( issueVO.getCreator() != null ) {
            issueMatterDataResp.setCreator( issueVO.getCreator() );
        }
        if ( issueVO.getDescription() != null ) {
            issueMatterDataResp.setDescription( issueVO.getDescription() );
        }
        if ( issueVO.getVersionConfirm() != null ) {
            issueMatterDataResp.setVersionConfirm( issueVO.getVersionConfirm() );
        }

        return issueMatterDataResp;
    }

    @Override
    public void converter(IssueEditedEvent event, IssueMatterDataResp resp) {
        if ( event == null ) {
            return;
        }

        String code = eventFindVersionCode( event );
        if ( code != null ) {
            resp.setFindVersionCode( code );
        }
        String name = eventFindVersionName( event );
        if ( name != null ) {
            resp.setFindVersionName( name );
        }
        String code1 = eventFixVersionCode( event );
        if ( code1 != null ) {
            resp.setFixVersionCode( code1 );
        }
        String name1 = eventFixVersionName( event );
        if ( name1 != null ) {
            resp.setFixVersionName( name1 );
        }
        if ( event.getCode() != null ) {
            resp.setCode( event.getCode() );
        }
        if ( event.getTitle() != null ) {
            resp.setTitle( event.getTitle() );
        }
        if ( event.getPriority() != null ) {
            resp.setPriority( event.getPriority().name() );
        }
        if ( event.getRootCause() != null ) {
            resp.setRootCause( event.getRootCause().name() );
        }
        if ( event.getType() != null ) {
            resp.setType( event.getType().name() );
        }
        if ( event.getTestMethod() != null ) {
            resp.setTestMethod( event.getTestMethod().name() );
        }
        if ( event.getFindStage() != null ) {
            resp.setFindStage( event.getFindStage().name() );
        }
        if ( event.getFindEnv() != null ) {
            resp.setFindEnv( event.getFindEnv().name() );
        }
        if ( event.getRepetitionRate() != null ) {
            resp.setRepetitionRate( event.getRepetitionRate().name() );
        }
        if ( event.getUpdateTime() != null ) {
            resp.setUpdateTime( event.getUpdateTime() );
        }
        if ( event.getDescription() != null ) {
            resp.setDescription( event.getDescription() );
        }
        if ( event.getVersionConfirm() != null ) {
            resp.setVersionConfirm( event.getVersionConfirm() );
        }
    }

    private String eventFindVersionCode(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version findVersion = issueEditedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String code = findVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFindVersionName(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version findVersion = issueEditedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String name = findVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFixVersionCode(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version fixVersion = issueEditedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String code = fixVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFixVersionName(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version fixVersion = issueEditedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String name = fixVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }
}
