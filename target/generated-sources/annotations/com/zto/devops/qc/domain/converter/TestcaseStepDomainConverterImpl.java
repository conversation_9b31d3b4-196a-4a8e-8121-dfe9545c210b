package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.TestcaseStepEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseStepDomainConverterImpl implements TestcaseStepDomainConverter {

    @Override
    public List<TestcaseStepVO> converter(List<TestcaseStepEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<TestcaseStepVO> list1 = new ArrayList<TestcaseStepVO>( list.size() );
        for ( TestcaseStepEntityDO testcaseStepEntityDO : list ) {
            list1.add( testcaseStepEntityDOToTestcaseStepVO( testcaseStepEntityDO ) );
        }

        return list1;
    }

    protected TestcaseStepVO testcaseStepEntityDOToTestcaseStepVO(TestcaseStepEntityDO testcaseStepEntityDO) {
        if ( testcaseStepEntityDO == null ) {
            return null;
        }

        TestcaseStepVO testcaseStepVO = new TestcaseStepVO();

        testcaseStepVO.setTestcaseCode( testcaseStepEntityDO.getTestcaseCode() );
        testcaseStepVO.setStepDesc( testcaseStepEntityDO.getStepDesc() );
        testcaseStepVO.setExpectResult( testcaseStepEntityDO.getExpectResult() );
        testcaseStepVO.setSort( testcaseStepEntityDO.getSort() );

        return testcaseStepVO;
    }
}
