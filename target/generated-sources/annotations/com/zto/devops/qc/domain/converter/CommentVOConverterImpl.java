package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddCommentCommand;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:05+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class CommentVOConverterImpl implements CommentVOConverter {

    @Override
    public CommentVO convert(AddCommentCommand command) {
        if ( command == null ) {
            return null;
        }

        CommentVO commentVO = new CommentVO();

        commentVO.setDomain( command.getDomain() );
        commentVO.setCode( command.getCode() );
        commentVO.setBusinessCode( command.getBusinessCode() );
        commentVO.setContent( command.getContent() );
        commentVO.setRepliedCode( command.getRepliedCode() );
        commentVO.setTopRepliedCode( command.getTopRepliedCode() );
        commentVO.setRepliedUserId( command.getRepliedUserId() );
        commentVO.setRepliedUserName( command.getRepliedUserName() );
        commentVO.setGmtCreate( command.getGmtCreate() );

        return commentVO;
    }
}
