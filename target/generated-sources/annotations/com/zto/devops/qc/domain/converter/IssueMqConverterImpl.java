package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.model.issue.entity.IssueMqResp;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueRelatedRequirementEvent;
import com.zto.devops.qc.client.model.issue.event.IssueRemovedEvent;
import com.zto.devops.qc.client.service.issue.model.IssueTagResp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueMqConverterImpl implements IssueMqConverter {

    @Override
    public IssueMqResp convert(IssueAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueMqResp issueMqResp = new IssueMqResp();

        Long userId = eventFinderUserId( event );
        if ( userId != null ) {
            issueMqResp.setFindUserId( userId );
        }
        String userName = eventFinderUserName( event );
        if ( userName != null ) {
            issueMqResp.setFindUserName( userName );
        }
        Long userId1 = eventHandlerUserId( event );
        if ( userId1 != null ) {
            issueMqResp.setHandleUserId( userId1 );
        }
        String userName1 = eventHandlerUserName( event );
        if ( userName1 != null ) {
            issueMqResp.setHandleUserName( userName1 );
        }
        Long userId2 = eventDeveloperUserId( event );
        if ( userId2 != null ) {
            issueMqResp.setDevelopUserId( userId2 );
        }
        String userName2 = eventDeveloperUserName( event );
        if ( userName2 != null ) {
            issueMqResp.setDevelopUserName( userName2 );
        }
        Long userId3 = eventTesterUserId( event );
        if ( userId3 != null ) {
            issueMqResp.setTestUserId( userId3 );
        }
        String userName3 = eventTesterUserName( event );
        if ( userName3 != null ) {
            issueMqResp.setTestUserName( userName3 );
        }
        Long userId4 = eventTransactorUserId( event );
        if ( userId4 != null ) {
            issueMqResp.setUpdateUserId( userId4 );
        }
        String userName4 = eventTransactorUserName( event );
        if ( userName4 != null ) {
            issueMqResp.setUpdateUserName( userName4 );
        }
        String code = eventProductCode( event );
        if ( code != null ) {
            issueMqResp.setProductCode( code );
        }
        String name = eventProductName( event );
        if ( name != null ) {
            issueMqResp.setProductName( name );
        }
        String code1 = eventRequirementCode( event );
        if ( code1 != null ) {
            issueMqResp.setRequirementCode( code1 );
        }
        String name1 = eventRequirementName( event );
        if ( name1 != null ) {
            issueMqResp.setRequirementName( name1 );
        }
        String code2 = eventFindVersionCode( event );
        if ( code2 != null ) {
            issueMqResp.setFindVersionCode( code2 );
        }
        String name2 = eventFindVersionName( event );
        if ( name2 != null ) {
            issueMqResp.setFindVersionName( name2 );
        }
        String code3 = eventFixVersionCode( event );
        if ( code3 != null ) {
            issueMqResp.setFixVersionCode( code3 );
        }
        String name3 = eventFixVersionName( event );
        if ( name3 != null ) {
            issueMqResp.setFixVersionName( name3 );
        }
        String code4 = eventSprintCode( event );
        if ( code4 != null ) {
            issueMqResp.setSprintCode( code4 );
        }
        String name4 = eventSprintName( event );
        if ( name4 != null ) {
            issueMqResp.setSprintName( name4 );
        }
        if ( event.getCode() != null ) {
            issueMqResp.setCode( event.getCode() );
        }
        if ( event.getTitle() != null ) {
            issueMqResp.setTitle( event.getTitle() );
        }
        if ( event.getStatus() != null ) {
            issueMqResp.setStatus( event.getStatus().name() );
        }
        if ( event.getPriority() != null ) {
            issueMqResp.setPriority( event.getPriority().name() );
        }
        if ( event.getRootCause() != null ) {
            issueMqResp.setRootCause( event.getRootCause().name() );
        }
        if ( event.getType() != null ) {
            issueMqResp.setType( event.getType().name() );
        }
        if ( event.getTestMethod() != null ) {
            issueMqResp.setTestMethod( event.getTestMethod().name() );
        }
        if ( event.getFindStage() != null ) {
            issueMqResp.setFindStage( event.getFindStage().name() );
        }
        if ( event.getFindEnv() != null ) {
            issueMqResp.setFindEnv( event.getFindEnv().name() );
        }
        if ( event.getRepetitionRate() != null ) {
            issueMqResp.setRepetitionRate( event.getRepetitionRate().name() );
        }
        if ( event.getFindTime() != null ) {
            issueMqResp.setFindTime( event.getFindTime() );
        }
        if ( event.getUpdateTime() != null ) {
            issueMqResp.setUpdateTime( event.getUpdateTime() );
        }
        if ( event.getRequirementLevel() != null ) {
            issueMqResp.setRequirementLevel( event.getRequirementLevel().name() );
        }
        if ( event.getDescription() != null ) {
            issueMqResp.setDescription( event.getDescription() );
        }
        if ( event.getVersionConfirm() != null ) {
            issueMqResp.setVersionConfirm( event.getVersionConfirm() );
        }

        return issueMqResp;
    }

    @Override
    public IssueMqResp convert(IssueRemovedEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueMqResp issueMqResp = new IssueMqResp();

        if ( event.getCode() != null ) {
            issueMqResp.setCode( event.getCode() );
        }
        if ( event.getStatus() != null ) {
            issueMqResp.setStatus( event.getStatus().name() );
        }

        return issueMqResp;
    }

    @Override
    public IssueMqResp convert(IssueVO issueVO) {
        if ( issueVO == null ) {
            return null;
        }

        IssueMqResp issueMqResp = new IssueMqResp();

        if ( issueVO.getCreatorId() != null ) {
            issueMqResp.setCreatorId( issueVO.getCreatorId() );
        }
        if ( issueVO.getCreator() != null ) {
            issueMqResp.setCreator( issueVO.getCreator() );
        }
        if ( issueVO.getGmtModified() != null ) {
            issueMqResp.setGmtModified( issueVO.getGmtModified() );
        }
        if ( issueVO.getCode() != null ) {
            issueMqResp.setCode( issueVO.getCode() );
        }
        if ( issueVO.getTitle() != null ) {
            issueMqResp.setTitle( issueVO.getTitle() );
        }
        if ( issueVO.getStatus() != null ) {
            issueMqResp.setStatus( issueVO.getStatus().name() );
        }
        if ( issueVO.getPriority() != null ) {
            issueMqResp.setPriority( issueVO.getPriority().name() );
        }
        if ( issueVO.getRootCause() != null ) {
            issueMqResp.setRootCause( issueVO.getRootCause().name() );
        }
        if ( issueVO.getType() != null ) {
            issueMqResp.setType( issueVO.getType().name() );
        }
        if ( issueVO.getTestMethod() != null ) {
            issueMqResp.setTestMethod( issueVO.getTestMethod().name() );
        }
        if ( issueVO.getFindStage() != null ) {
            issueMqResp.setFindStage( issueVO.getFindStage().name() );
        }
        if ( issueVO.getFindEnv() != null ) {
            issueMqResp.setFindEnv( issueVO.getFindEnv().name() );
        }
        if ( issueVO.getRepetitionRate() != null ) {
            issueMqResp.setRepetitionRate( issueVO.getRepetitionRate().name() );
        }
        if ( issueVO.getGmtCreate() != null ) {
            issueMqResp.setGmtCreate( issueVO.getGmtCreate() );
        }
        if ( issueVO.getReopenTime() != null ) {
            issueMqResp.setReopenTime( issueVO.getReopenTime() );
        }
        if ( issueVO.getActualWorkingHours() != null ) {
            issueMqResp.setActualWorkingHours( issueVO.getActualWorkingHours() );
        }
        if ( issueVO.getStartFixTime() != null ) {
            issueMqResp.setStartFixTime( issueVO.getStartFixTime() );
        }
        if ( issueVO.getDelayFixTime() != null ) {
            issueMqResp.setDelayFixTime( issueVO.getDelayFixTime() );
        }
        if ( issueVO.getDeliverTime() != null ) {
            issueMqResp.setDeliverTime( issueVO.getDeliverTime() );
        }
        if ( issueVO.getRejectTime() != null ) {
            issueMqResp.setRejectTime( issueVO.getRejectTime() );
        }
        if ( issueVO.getCloseTime() != null ) {
            issueMqResp.setCloseTime( issueVO.getCloseTime() );
        }
        if ( issueVO.getFindTime() != null ) {
            issueMqResp.setFindTime( issueVO.getFindTime() );
        }
        if ( issueVO.getUpdateTime() != null ) {
            issueMqResp.setUpdateTime( issueVO.getUpdateTime() );
        }
        if ( issueVO.getProductCode() != null ) {
            issueMqResp.setProductCode( issueVO.getProductCode() );
        }
        if ( issueVO.getProductName() != null ) {
            issueMqResp.setProductName( issueVO.getProductName() );
        }
        if ( issueVO.getRequirementCode() != null ) {
            issueMqResp.setRequirementCode( issueVO.getRequirementCode() );
        }
        if ( issueVO.getRequirementName() != null ) {
            issueMqResp.setRequirementName( issueVO.getRequirementName() );
        }
        if ( issueVO.getRequirementLevel() != null ) {
            issueMqResp.setRequirementLevel( issueVO.getRequirementLevel().name() );
        }
        if ( issueVO.getFindVersionCode() != null ) {
            issueMqResp.setFindVersionCode( issueVO.getFindVersionCode() );
        }
        if ( issueVO.getFindVersionName() != null ) {
            issueMqResp.setFindVersionName( issueVO.getFindVersionName() );
        }
        if ( issueVO.getFixVersionCode() != null ) {
            issueMqResp.setFixVersionCode( issueVO.getFixVersionCode() );
        }
        if ( issueVO.getFixVersionName() != null ) {
            issueMqResp.setFixVersionName( issueVO.getFixVersionName() );
        }
        if ( issueVO.getSprintCode() != null ) {
            issueMqResp.setSprintCode( issueVO.getSprintCode() );
        }
        if ( issueVO.getSprintName() != null ) {
            issueMqResp.setSprintName( issueVO.getSprintName() );
        }
        if ( issueVO.getFindUserId() != null ) {
            issueMqResp.setFindUserId( issueVO.getFindUserId() );
        }
        if ( issueVO.getFindUserName() != null ) {
            issueMqResp.setFindUserName( issueVO.getFindUserName() );
        }
        if ( issueVO.getUpdateUserId() != null ) {
            issueMqResp.setUpdateUserId( issueVO.getUpdateUserId() );
        }
        if ( issueVO.getUpdateUserName() != null ) {
            issueMqResp.setUpdateUserName( issueVO.getUpdateUserName() );
        }
        if ( issueVO.getHandleUserName() != null ) {
            issueMqResp.setHandleUserName( issueVO.getHandleUserName() );
        }
        if ( issueVO.getHandleUserId() != null ) {
            issueMqResp.setHandleUserId( issueVO.getHandleUserId() );
        }
        if ( issueVO.getDevelopUserId() != null ) {
            issueMqResp.setDevelopUserId( issueVO.getDevelopUserId() );
        }
        if ( issueVO.getDevelopUserName() != null ) {
            issueMqResp.setDevelopUserName( issueVO.getDevelopUserName() );
        }
        if ( issueVO.getTestUserId() != null ) {
            issueMqResp.setTestUserId( issueVO.getTestUserId() );
        }
        if ( issueVO.getTestUserName() != null ) {
            issueMqResp.setTestUserName( issueVO.getTestUserName() );
        }
        if ( issueVO.getDescription() != null ) {
            issueMqResp.setDescription( issueVO.getDescription() );
        }
        if ( issueVO.getVersionConfirm() != null ) {
            issueMqResp.setVersionConfirm( issueVO.getVersionConfirm() );
        }
        if ( issueVO.getExamination() != null ) {
            issueMqResp.setExamination( issueVO.getExamination() );
        }
        if ( issueVO.getTestOmission() != null ) {
            issueMqResp.setTestOmission( issueVO.getTestOmission() );
        }
        if ( issueVO.getCodeDefect() != null ) {
            issueMqResp.setCodeDefect( issueVO.getCodeDefect() );
        }
        if ( issueVO.getTestOmissionVersion() != null ) {
            issueMqResp.setTestOmissionVersion( issueVO.getTestOmissionVersion() );
        }
        if ( issueVO.getCodeDefectVersion() != null ) {
            issueMqResp.setCodeDefectVersion( issueVO.getCodeDefectVersion() );
        }

        return issueMqResp;
    }

    @Override
    public void convert(IssueEditedEvent event, IssueMqResp resp) {
        if ( event == null ) {
            return;
        }

        Long userId = eventHandlerUserId1( event );
        if ( userId != null ) {
            resp.setHandleUserId( userId );
        }
        String userName = eventHandlerUserName1( event );
        if ( userName != null ) {
            resp.setHandleUserName( userName );
        }
        Long userId1 = eventDeveloperUserId1( event );
        if ( userId1 != null ) {
            resp.setDevelopUserId( userId1 );
        }
        String userName1 = eventDeveloperUserName1( event );
        if ( userName1 != null ) {
            resp.setDevelopUserName( userName1 );
        }
        Long userId2 = eventTesterUserId1( event );
        if ( userId2 != null ) {
            resp.setTestUserId( userId2 );
        }
        String userName2 = eventTesterUserName1( event );
        if ( userName2 != null ) {
            resp.setTestUserName( userName2 );
        }
        Long userId3 = eventTransactorUserId1( event );
        if ( userId3 != null ) {
            resp.setUpdateUserId( userId3 );
        }
        String userName3 = eventTransactorUserName1( event );
        if ( userName3 != null ) {
            resp.setUpdateUserName( userName3 );
        }
        String code = eventRequirementCode1( event );
        if ( code != null ) {
            resp.setRequirementCode( code );
        }
        String name = eventRequirementName1( event );
        if ( name != null ) {
            resp.setRequirementName( name );
        }
        String code1 = eventFindVersionCode1( event );
        if ( code1 != null ) {
            resp.setFindVersionCode( code1 );
        }
        String name1 = eventFindVersionName1( event );
        if ( name1 != null ) {
            resp.setFindVersionName( name1 );
        }
        String code2 = eventFixVersionCode1( event );
        if ( code2 != null ) {
            resp.setFixVersionCode( code2 );
        }
        String name2 = eventFixVersionName1( event );
        if ( name2 != null ) {
            resp.setFixVersionName( name2 );
        }
        String code3 = eventSprintCode1( event );
        if ( code3 != null ) {
            resp.setSprintCode( code3 );
        }
        if ( event.getCode() != null ) {
            resp.setCode( event.getCode() );
        }
        if ( event.getTitle() != null ) {
            resp.setTitle( event.getTitle() );
        }
        if ( event.getPriority() != null ) {
            resp.setPriority( event.getPriority().name() );
        }
        if ( event.getRootCause() != null ) {
            resp.setRootCause( event.getRootCause().name() );
        }
        if ( event.getType() != null ) {
            resp.setType( event.getType().name() );
        }
        if ( event.getTestMethod() != null ) {
            resp.setTestMethod( event.getTestMethod().name() );
        }
        if ( event.getFindStage() != null ) {
            resp.setFindStage( event.getFindStage().name() );
        }
        if ( event.getFindEnv() != null ) {
            resp.setFindEnv( event.getFindEnv().name() );
        }
        if ( event.getRepetitionRate() != null ) {
            resp.setRepetitionRate( event.getRepetitionRate().name() );
        }
        if ( event.getUpdateTime() != null ) {
            resp.setUpdateTime( event.getUpdateTime() );
        }
        if ( event.getRequirementLevel() != null ) {
            resp.setRequirementLevel( event.getRequirementLevel().name() );
        }
        if ( event.getDescription() != null ) {
            resp.setDescription( event.getDescription() );
        }
        if ( event.getVersionConfirm() != null ) {
            resp.setVersionConfirm( event.getVersionConfirm() );
        }
    }

    @Override
    public IssueMqResp convert(IssueRelatedRequirementEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueMqResp issueMqResp = new IssueMqResp();

        if ( event.getIssueCode() != null ) {
            issueMqResp.setCode( event.getIssueCode() );
        }
        if ( event.getRequirementCode() != null ) {
            issueMqResp.setRequirementCode( event.getRequirementCode() );
        }
        if ( event.getRequirementName() != null ) {
            issueMqResp.setRequirementName( event.getRequirementName() );
        }
        if ( event.getRequirementLevel() != null ) {
            issueMqResp.setRequirementLevel( event.getRequirementLevel().name() );
        }

        return issueMqResp;
    }

    @Override
    public List<IssueTagResp> convert(Collection<TagVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<IssueTagResp> list = new ArrayList<IssueTagResp>( vo.size() );
        for ( TagVO tagVO : vo ) {
            list.add( tagVOToIssueTagResp( tagVO ) );
        }

        return list;
    }

    private Long eventFinderUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User finder = issueAddedEvent.getFinder();
        if ( finder == null ) {
            return null;
        }
        Long userId = finder.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventFinderUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User finder = issueAddedEvent.getFinder();
        if ( finder == null ) {
            return null;
        }
        String userName = finder.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventHandlerUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User handler = issueAddedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        Long userId = handler.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventHandlerUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User handler = issueAddedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        String userName = handler.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventDeveloperUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User developer = issueAddedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        Long userId = developer.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventDeveloperUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User developer = issueAddedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        String userName = developer.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTesterUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User tester = issueAddedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        Long userId = tester.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTesterUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User tester = issueAddedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        String userName = tester.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User transactor = issueAddedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User transactor = issueAddedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private String eventProductCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Product product = issueAddedEvent.getProduct();
        if ( product == null ) {
            return null;
        }
        String code = product.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventProductName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Product product = issueAddedEvent.getProduct();
        if ( product == null ) {
            return null;
        }
        String name = product.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventRequirementCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Requirement requirement = issueAddedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String code = requirement.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventRequirementName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Requirement requirement = issueAddedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String name = requirement.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFindVersionCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Version findVersion = issueAddedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String code = findVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFindVersionName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Version findVersion = issueAddedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String name = findVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFixVersionCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Version fixVersion = issueAddedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String code = fixVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFixVersionName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Version fixVersion = issueAddedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String name = fixVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventSprintCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Sprint sprint = issueAddedEvent.getSprint();
        if ( sprint == null ) {
            return null;
        }
        String code = sprint.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventSprintName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Sprint sprint = issueAddedEvent.getSprint();
        if ( sprint == null ) {
            return null;
        }
        String name = sprint.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private Long eventHandlerUserId1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User handler = issueEditedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        Long userId = handler.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventHandlerUserName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User handler = issueEditedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        String userName = handler.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventDeveloperUserId1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User developer = issueEditedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        Long userId = developer.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventDeveloperUserName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User developer = issueEditedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        String userName = developer.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTesterUserId1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User tester = issueEditedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        Long userId = tester.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTesterUserName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User tester = issueEditedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        String userName = tester.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User transactor = issueEditedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User transactor = issueEditedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private String eventRequirementCode1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Requirement requirement = issueEditedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String code = requirement.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventRequirementName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Requirement requirement = issueEditedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String name = requirement.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFindVersionCode1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version findVersion = issueEditedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String code = findVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFindVersionName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version findVersion = issueEditedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String name = findVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFixVersionCode1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version fixVersion = issueEditedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String code = fixVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFixVersionName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version fixVersion = issueEditedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String name = fixVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventSprintCode1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Sprint sprint = issueEditedEvent.getSprint();
        if ( sprint == null ) {
            return null;
        }
        String code = sprint.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    protected IssueTagResp tagVOToIssueTagResp(TagVO tagVO) {
        if ( tagVO == null ) {
            return null;
        }

        IssueTagResp issueTagResp = new IssueTagResp();

        if ( tagVO.getCode() != null ) {
            issueTagResp.setCode( tagVO.getCode() );
        }
        if ( tagVO.getDomain() != null ) {
            issueTagResp.setDomain( tagVO.getDomain().name() );
        }
        if ( tagVO.getBusinessCode() != null ) {
            issueTagResp.setBusinessCode( tagVO.getBusinessCode() );
        }
        if ( tagVO.getType() != null ) {
            issueTagResp.setType( tagVO.getType().name() );
        }
        if ( tagVO.getTagAlias() != null ) {
            issueTagResp.setTagAlias( tagVO.getTagAlias() );
        }
        if ( tagVO.getTagCode() != null ) {
            issueTagResp.setTagCode( tagVO.getTagCode() );
        }
        if ( tagVO.getTagName() != null ) {
            issueTagResp.setTagName( tagVO.getTagName() );
        }
        if ( tagVO.getEnable() != null ) {
            issueTagResp.setEnable( tagVO.getEnable() );
        }
        if ( tagVO.getCreatorId() != null ) {
            issueTagResp.setCreatorId( tagVO.getCreatorId() );
        }

        return issueTagResp;
    }
}
