package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddAttachmentCommand;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AttachmentDomainConverterImpl implements AttachmentDomainConverter {

    @Override
    public AttachmentVO convert(AddAttachmentCommand command) {
        if ( command == null ) {
            return null;
        }

        AttachmentVO attachmentVO = new AttachmentVO();

        attachmentVO.setDomain( command.getDomain() );
        attachmentVO.setBusinessCode( command.getBusinessCode() );
        attachmentVO.setCode( command.getCode() );
        attachmentVO.setUrl( command.getUrl() );
        attachmentVO.setName( command.getName() );
        attachmentVO.setRemoteFileId( command.getRemoteFileId() );
        attachmentVO.setType( command.getType() );
        attachmentVO.setDocumentType( command.getDocumentType() );

        return attachmentVO;
    }
}
