package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AddAutomaticSchedulerCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AddSchedulerCasesCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AutomaticSchedulerExecutionCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.EditAutomaticSchedulerCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.RemoveSchedulerCasesCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.AutomaticSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AddSchedulerCasesEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticPreExecutionUpdateEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerAddedEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerEditedEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerExecutionEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.RemoveSchedulerCasesEvent;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSchedulerDomainConvertorImpl implements AutomaticSchedulerDomainConvertor {

    @Override
    public AutomaticSchedulerAddedEvent converter(AddAutomaticSchedulerCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticSchedulerAddedEvent automaticSchedulerAddedEvent = new AutomaticSchedulerAddedEvent();

        automaticSchedulerAddedEvent.setSchedulerCode( command.getAggregateId() );
        automaticSchedulerAddedEvent.setAggregateId( command.getAggregateId() );
        automaticSchedulerAddedEvent.setTransactor( command.getTransactor() );
        automaticSchedulerAddedEvent.setOccurred( command.getOccurred() );
        automaticSchedulerAddedEvent.setSchedulerName( command.getSchedulerName() );
        automaticSchedulerAddedEvent.setProductCode( command.getProductCode() );
        automaticSchedulerAddedEvent.setCrontab( command.getCrontab() );
        automaticSchedulerAddedEvent.setExecuteEnv( command.getExecuteEnv() );
        automaticSchedulerAddedEvent.setExecuteTag( command.getExecuteTag() );
        automaticSchedulerAddedEvent.setExecuteSpaceCode( command.getExecuteSpaceCode() );
        automaticSchedulerAddedEvent.setCoverageFlag( command.getCoverageFlag() );
        automaticSchedulerAddedEvent.setMessageFlag( command.getMessageFlag() );
        List<User> list = command.getCcList();
        if ( list != null ) {
            automaticSchedulerAddedEvent.setCcList( new ArrayList<User>( list ) );
        }

        return automaticSchedulerAddedEvent;
    }

    @Override
    public void domainConverter(AutomaticSchedulerAddedEvent event, AutomaticSchedulerVO domain) {
        if ( event == null ) {
            return;
        }

        domain.setSchedulerCode( event.getSchedulerCode() );
        domain.setSchedulerName( event.getSchedulerName() );
        domain.setCrontab( event.getCrontab() );
        domain.setExecuteEnv( event.getExecuteEnv() );
        domain.setExecuteTag( event.getExecuteTag() );
        domain.setExecuteSpaceCode( event.getExecuteSpaceCode() );
        domain.setMessageFlag( event.getMessageFlag() );
    }

    @Override
    public AutomaticSchedulerEditedEvent converter(EditAutomaticSchedulerCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticSchedulerEditedEvent automaticSchedulerEditedEvent = new AutomaticSchedulerEditedEvent();

        automaticSchedulerEditedEvent.setSchedulerCode( command.getAggregateId() );
        automaticSchedulerEditedEvent.setAggregateId( command.getAggregateId() );
        automaticSchedulerEditedEvent.setTransactor( command.getTransactor() );
        automaticSchedulerEditedEvent.setOccurred( command.getOccurred() );
        automaticSchedulerEditedEvent.setSchedulerName( command.getSchedulerName() );
        automaticSchedulerEditedEvent.setProductCode( command.getProductCode() );
        automaticSchedulerEditedEvent.setCrontab( command.getCrontab() );
        automaticSchedulerEditedEvent.setExecuteEnv( command.getExecuteEnv() );
        automaticSchedulerEditedEvent.setExecuteTag( command.getExecuteTag() );
        automaticSchedulerEditedEvent.setExecuteSpaceCode( command.getExecuteSpaceCode() );
        automaticSchedulerEditedEvent.setCoverageFlag( command.getCoverageFlag() );
        automaticSchedulerEditedEvent.setMessageFlag( command.getMessageFlag() );
        List<User> list = command.getCcList();
        if ( list != null ) {
            automaticSchedulerEditedEvent.setCcList( new ArrayList<User>( list ) );
        }
        automaticSchedulerEditedEvent.setSwitchFlag( command.getSwitchFlag() );

        return automaticSchedulerEditedEvent;
    }

    @Override
    public AddSchedulerCasesEvent converter(AddSchedulerCasesCommand command) {
        if ( command == null ) {
            return null;
        }

        AddSchedulerCasesEvent addSchedulerCasesEvent = new AddSchedulerCasesEvent();

        addSchedulerCasesEvent.setAggregateId( command.getAggregateId() );
        addSchedulerCasesEvent.setTransactor( command.getTransactor() );
        addSchedulerCasesEvent.setOccurred( command.getOccurred() );
        addSchedulerCasesEvent.setProductCode( command.getProductCode() );
        addSchedulerCasesEvent.setSchedulerCode( command.getSchedulerCode() );
        List<String> list = command.getCaseCodeList();
        if ( list != null ) {
            addSchedulerCasesEvent.setCaseCodeList( new ArrayList<String>( list ) );
        }

        return addSchedulerCasesEvent;
    }

    @Override
    public RemoveSchedulerCasesEvent converter(RemoveSchedulerCasesCommand command) {
        if ( command == null ) {
            return null;
        }

        RemoveSchedulerCasesEvent removeSchedulerCasesEvent = new RemoveSchedulerCasesEvent();

        removeSchedulerCasesEvent.setAggregateId( command.getAggregateId() );
        removeSchedulerCasesEvent.setTransactor( command.getTransactor() );
        removeSchedulerCasesEvent.setOccurred( command.getOccurred() );
        removeSchedulerCasesEvent.setSchedulerCode( command.getSchedulerCode() );
        List<String> list = command.getCaseCodeList();
        if ( list != null ) {
            removeSchedulerCasesEvent.setCaseCodeList( new ArrayList<String>( list ) );
        }

        return removeSchedulerCasesEvent;
    }

    @Override
    public AutomaticSchedulerExecutionEvent converter(AutomaticSchedulerExecutionCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticSchedulerExecutionEvent automaticSchedulerExecutionEvent = new AutomaticSchedulerExecutionEvent();

        automaticSchedulerExecutionEvent.setSchedulerCode( command.getAggregateId() );
        automaticSchedulerExecutionEvent.setAggregateId( command.getAggregateId() );
        automaticSchedulerExecutionEvent.setTransactor( command.getTransactor() );
        automaticSchedulerExecutionEvent.setOccurred( command.getOccurred() );
        automaticSchedulerExecutionEvent.setPreCode( command.getPreCode() );
        automaticSchedulerExecutionEvent.setTrigMode( command.getTrigMode() );

        return automaticSchedulerExecutionEvent;
    }

    @Override
    public AutomaticPreExecutionUpdateEvent convert(AutomaticSchedulerExecutionCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticPreExecutionUpdateEvent automaticPreExecutionUpdateEvent = new AutomaticPreExecutionUpdateEvent();

        automaticPreExecutionUpdateEvent.setSchedulerCode( command.getAggregateId() );
        automaticPreExecutionUpdateEvent.setAggregateId( command.getAggregateId() );
        automaticPreExecutionUpdateEvent.setTransactor( command.getTransactor() );
        automaticPreExecutionUpdateEvent.setOccurred( command.getOccurred() );
        automaticPreExecutionUpdateEvent.setPreCode( command.getPreCode() );

        return automaticPreExecutionUpdateEvent;
    }
}
