package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddRelevantUserCommand;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class RelevantUserVOConverterImpl implements RelevantUserVOConverter {

    @Override
    public RelevantUserVO convert(AddRelevantUserCommand command) {
        if ( command == null ) {
            return null;
        }

        RelevantUserVO relevantUserVO = new RelevantUserVO();

        relevantUserVO.setType( command.getType() );
        relevantUserVO.setBusinessCode( command.getBusinessCode() );
        relevantUserVO.setCode( command.getCode() );
        relevantUserVO.setDomain( command.getDomain() );

        return relevantUserVO;
    }
}
