package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AddAutomaticSchedulerCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AddSchedulerCasesCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.EditAutomaticSchedulerCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.RemoveSchedulerCasesCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.ListSchedulerCaseQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.PageSchedulerQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.ProductSchedulerQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.SchedulerCaseCodeListQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.SchedulerModuleListQuery;
import com.zto.devops.qc.client.service.testmanager.cases.model.AddAutomaticSchedulerReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.AddSchedulerCasesReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.EditAutomaticSchedulerReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListSchedulerCaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.PageSchedulerReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ProductSchedulerReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.RemoveSchedulerCasesReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.SchedulerCaseCodeListReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.SchedulerModuleListReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSchedulerAdapterConvertorImpl implements AutomaticSchedulerAdapterConvertor {

    @Override
    public void converter(AddAutomaticSchedulerReq req, AddAutomaticSchedulerCommand command) {
        if ( req == null ) {
            return;
        }

        command.setSchedulerName( req.getSchedulerName() );
        command.setProductCode( req.getProductCode() );
        command.setCrontab( req.getCrontab() );
        command.setExecuteEnv( req.getExecuteEnv() );
        command.setExecuteTag( req.getExecuteTag() );
        command.setExecuteSpaceCode( req.getExecuteSpaceCode() );
        command.setCoverageFlag( req.getCoverageFlag() );
        command.setMessageFlag( req.getMessageFlag() );
        if ( command.getCcList() != null ) {
            List<User> list = req.getCcList();
            if ( list != null ) {
                command.getCcList().clear();
                command.getCcList().addAll( list );
            }
            else {
                command.setCcList( null );
            }
        }
        else {
            List<User> list = req.getCcList();
            if ( list != null ) {
                command.setCcList( new ArrayList<User>( list ) );
            }
        }
    }

    @Override
    public void converter(EditAutomaticSchedulerReq req, EditAutomaticSchedulerCommand command) {
        if ( req == null ) {
            return;
        }

        command.setSchedulerName( req.getSchedulerName() );
        command.setProductCode( req.getProductCode() );
        command.setCrontab( req.getCrontab() );
        command.setExecuteEnv( req.getExecuteEnv() );
        command.setExecuteTag( req.getExecuteTag() );
        command.setExecuteSpaceCode( req.getExecuteSpaceCode() );
        command.setCoverageFlag( req.getCoverageFlag() );
        command.setMessageFlag( req.getMessageFlag() );
        if ( command.getCcList() != null ) {
            List<User> list = req.getCcList();
            if ( list != null ) {
                command.getCcList().clear();
                command.getCcList().addAll( list );
            }
            else {
                command.setCcList( null );
            }
        }
        else {
            List<User> list = req.getCcList();
            if ( list != null ) {
                command.setCcList( new ArrayList<User>( list ) );
            }
        }
        command.setSwitchFlag( req.getSwitchFlag() );
    }

    @Override
    public void converter(PageSchedulerReq req, PageSchedulerQuery query) {
        if ( req == null ) {
            return;
        }

        query.setTransactor( req.getTransactor() );
        query.setPage( req.getPage() );
        query.setSize( req.getSize() );
        query.setProductCode( req.getProductCode() );
        query.setSchedulerName( req.getSchedulerName() );
        if ( query.getResultList() != null ) {
            List<AutomaticStatusEnum> list = req.getResultList();
            if ( list != null ) {
                query.getResultList().clear();
                query.getResultList().addAll( list );
            }
            else {
                query.setResultList( null );
            }
        }
        else {
            List<AutomaticStatusEnum> list = req.getResultList();
            if ( list != null ) {
                query.setResultList( new ArrayList<AutomaticStatusEnum>( list ) );
            }
        }
        if ( query.getSwitchFlagList() != null ) {
            List<Boolean> list1 = req.getSwitchFlagList();
            if ( list1 != null ) {
                query.getSwitchFlagList().clear();
                query.getSwitchFlagList().addAll( list1 );
            }
            else {
                query.setSwitchFlagList( null );
            }
        }
        else {
            List<Boolean> list1 = req.getSwitchFlagList();
            if ( list1 != null ) {
                query.setSwitchFlagList( new ArrayList<Boolean>( list1 ) );
            }
        }
        if ( query.getEnvList() != null ) {
            List<String> list2 = req.getEnvList();
            if ( list2 != null ) {
                query.getEnvList().clear();
                query.getEnvList().addAll( list2 );
            }
            else {
                query.setEnvList( null );
            }
        }
        else {
            List<String> list2 = req.getEnvList();
            if ( list2 != null ) {
                query.setEnvList( new ArrayList<String>( list2 ) );
            }
        }
        query.setExecuteStartTime( req.getExecuteStartTime() );
        query.setExecuteEndTime( req.getExecuteEndTime() );
    }

    @Override
    public void convertor(AddSchedulerCasesReq req, AddSchedulerCasesCommand command) {
        if ( req == null ) {
            return;
        }

        command.setProductCode( req.getProductCode() );
        command.setSchedulerCode( req.getSchedulerCode() );
        if ( command.getCaseCodeList() != null ) {
            List<String> list = req.getCaseCodeList();
            if ( list != null ) {
                command.getCaseCodeList().clear();
                command.getCaseCodeList().addAll( list );
            }
            else {
                command.setCaseCodeList( null );
            }
        }
        else {
            List<String> list = req.getCaseCodeList();
            if ( list != null ) {
                command.setCaseCodeList( new ArrayList<String>( list ) );
            }
        }
    }

    @Override
    public void convertor(RemoveSchedulerCasesReq req, RemoveSchedulerCasesCommand command) {
        if ( req == null ) {
            return;
        }

        command.setSchedulerCode( req.getSchedulerCode() );
        if ( command.getCaseCodeList() != null ) {
            List<String> list = req.getCaseCodeList();
            if ( list != null ) {
                command.getCaseCodeList().clear();
                command.getCaseCodeList().addAll( list );
            }
            else {
                command.setCaseCodeList( null );
            }
        }
        else {
            List<String> list = req.getCaseCodeList();
            if ( list != null ) {
                command.setCaseCodeList( new ArrayList<String>( list ) );
            }
        }
    }

    @Override
    public SchedulerModuleListQuery converter(SchedulerModuleListReq req) {
        if ( req == null ) {
            return null;
        }

        SchedulerModuleListQuery schedulerModuleListQuery = new SchedulerModuleListQuery();

        schedulerModuleListQuery.setProductCode( req.getProductCode() );
        schedulerModuleListQuery.setSchedulerCode( req.getSchedulerCode() );

        return schedulerModuleListQuery;
    }

    @Override
    public ListSchedulerCaseQuery converter(ListSchedulerCaseReq req) {
        if ( req == null ) {
            return null;
        }

        ListSchedulerCaseQuery listSchedulerCaseQuery = new ListSchedulerCaseQuery();

        listSchedulerCaseQuery.setSearch( req.getCodeOrTitle() );
        listSchedulerCaseQuery.setProductCode( req.getProductCode() );
        listSchedulerCaseQuery.setSchedulerCode( req.getSchedulerCode() );
        listSchedulerCaseQuery.setParentCode( req.getParentCode() );
        List<TestPlanCaseStatusEnum> list = req.getStatusList();
        if ( list != null ) {
            listSchedulerCaseQuery.setStatusList( new ArrayList<TestPlanCaseStatusEnum>( list ) );
        }
        List<TestcasePriorityEnum> list1 = req.getPriorityList();
        if ( list1 != null ) {
            listSchedulerCaseQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list1 ) );
        }
        List<AutomaticNodeTypeEnum> list2 = req.getNodeTypeList();
        if ( list2 != null ) {
            listSchedulerCaseQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list2 ) );
        }
        listSchedulerCaseQuery.setType( req.getType() );
        List<AutomaticRecordTypeEnum> list3 = req.getAutomaticTypeList();
        if ( list3 != null ) {
            listSchedulerCaseQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list3 ) );
        }

        return listSchedulerCaseQuery;
    }

    @Override
    public SchedulerCaseCodeListQuery converter(SchedulerCaseCodeListReq req) {
        if ( req == null ) {
            return null;
        }

        SchedulerCaseCodeListQuery schedulerCaseCodeListQuery = new SchedulerCaseCodeListQuery();

        schedulerCaseCodeListQuery.setSearch( req.getCodeOrTitle() );
        schedulerCaseCodeListQuery.setProductCode( req.getProductCode() );
        schedulerCaseCodeListQuery.setSchedulerCode( req.getSchedulerCode() );
        schedulerCaseCodeListQuery.setParentCode( req.getParentCode() );
        List<TestcasePriorityEnum> list = req.getPriorityList();
        if ( list != null ) {
            schedulerCaseCodeListQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
        }
        List<AutomaticNodeTypeEnum> list1 = req.getNodeTypeList();
        if ( list1 != null ) {
            schedulerCaseCodeListQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list1 ) );
        }
        List<AutomaticRecordTypeEnum> list2 = req.getAutomaticTypeList();
        if ( list2 != null ) {
            schedulerCaseCodeListQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list2 ) );
        }

        return schedulerCaseCodeListQuery;
    }

    @Override
    public void converter(ProductSchedulerReq req, ProductSchedulerQuery query) {
        if ( req == null ) {
            return;
        }

        query.setProductCode( req.getProductCode() );
        query.setSchedulerName( req.getSchedulerName() );
    }
}
