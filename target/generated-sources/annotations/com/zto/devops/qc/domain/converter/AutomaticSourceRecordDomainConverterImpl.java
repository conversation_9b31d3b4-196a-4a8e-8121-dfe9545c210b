package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddAutomaticRecordLogCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.AutomaticSuccessCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditAutomaticRecordLogCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.SubmitAnalysisAutomaticCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddAutomaticRecordEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddAutomaticRecordLogEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticSuccessEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditAutomaticRecordEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditAutomaticRecordLogEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.SubmitAnalysisAutomaticEvent;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:05+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSourceRecordDomainConverterImpl implements AutomaticSourceRecordDomainConverter {

    @Override
    public AddAutomaticRecordEvent convert(AddAutomaticRecordCommand command) {
        if ( command == null ) {
            return null;
        }

        AddAutomaticRecordEvent addAutomaticRecordEvent = new AddAutomaticRecordEvent();

        addAutomaticRecordEvent.setCode( command.getAggregateId() );
        addAutomaticRecordEvent.setAggregateId( command.getAggregateId() );
        addAutomaticRecordEvent.setTransactor( command.getTransactor() );
        addAutomaticRecordEvent.setOccurred( command.getOccurred() );
        addAutomaticRecordEvent.setAutoAnalysisFlag( command.getAutoAnalysisFlag() );
        addAutomaticRecordEvent.setProductCode( command.getProductCode() );
        addAutomaticRecordEvent.setName( command.getName() );
        addAutomaticRecordEvent.setComment( command.getComment() );
        addAutomaticRecordEvent.setAddress( command.getAddress() );
        addAutomaticRecordEvent.setType( command.getType() );
        addAutomaticRecordEvent.setFileName( command.getFileName() );
        addAutomaticRecordEvent.setDataFileAddress( command.getDataFileAddress() );
        addAutomaticRecordEvent.setExtendJarAddress( command.getExtendJarAddress() );
        addAutomaticRecordEvent.setThirdJarAddress( command.getThirdJarAddress() );
        addAutomaticRecordEvent.setBucketName( command.getBucketName() );
        addAutomaticRecordEvent.setTestcaseCode( command.getTestcaseCode() );
        addAutomaticRecordEvent.setWorkSpace( command.getWorkSpace() );
        addAutomaticRecordEvent.setBranch( command.getBranch() );
        addAutomaticRecordEvent.setScanDirectory( command.getScanDirectory() );
        addAutomaticRecordEvent.setCommitId( command.getCommitId() );

        return addAutomaticRecordEvent;
    }

    @Override
    public EditAutomaticRecordEvent convert(EditAutomaticRecordCommand command) {
        if ( command == null ) {
            return null;
        }

        EditAutomaticRecordEvent editAutomaticRecordEvent = new EditAutomaticRecordEvent();

        editAutomaticRecordEvent.setAggregateId( command.getAggregateId() );
        editAutomaticRecordEvent.setTransactor( command.getTransactor() );
        editAutomaticRecordEvent.setOccurred( command.getOccurred() );
        editAutomaticRecordEvent.setCode( command.getCode() );
        editAutomaticRecordEvent.setStatus( command.getStatus() );
        editAutomaticRecordEvent.setAutoAnalysisFlag( command.getAutoAnalysisFlag() );
        editAutomaticRecordEvent.setProductCode( command.getProductCode() );
        editAutomaticRecordEvent.setName( command.getName() );
        editAutomaticRecordEvent.setComment( command.getComment() );
        editAutomaticRecordEvent.setAddress( command.getAddress() );
        editAutomaticRecordEvent.setType( command.getType() );
        editAutomaticRecordEvent.setFileName( command.getFileName() );
        editAutomaticRecordEvent.setDataFileAddress( command.getDataFileAddress() );
        editAutomaticRecordEvent.setExtendJarAddress( command.getExtendJarAddress() );
        editAutomaticRecordEvent.setThirdJarAddress( command.getThirdJarAddress() );
        editAutomaticRecordEvent.setBucketName( command.getBucketName() );
        editAutomaticRecordEvent.setTestcaseCode( command.getTestcaseCode() );
        editAutomaticRecordEvent.setPath( command.getPath() );
        editAutomaticRecordEvent.setWorkSpace( command.getWorkSpace() );
        editAutomaticRecordEvent.setBranch( command.getBranch() );
        editAutomaticRecordEvent.setScanDirectory( command.getScanDirectory() );
        editAutomaticRecordEvent.setCommitId( command.getCommitId() );

        return editAutomaticRecordEvent;
    }

    @Override
    public AddAutomaticRecordLogCommand convertCommand(EditAutomaticRecordCommand command) {
        if ( command == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = command.getAggregateId();

        AddAutomaticRecordLogCommand addAutomaticRecordLogCommand = new AddAutomaticRecordLogCommand( aggregateId );

        addAutomaticRecordLogCommand.setTransactor( command.getTransactor() );
        addAutomaticRecordLogCommand.setAutoAnalysisFlag( command.getAutoAnalysisFlag() );
        addAutomaticRecordLogCommand.setProductCode( command.getProductCode() );
        addAutomaticRecordLogCommand.setName( command.getName() );
        addAutomaticRecordLogCommand.setComment( command.getComment() );
        addAutomaticRecordLogCommand.setAddress( command.getAddress() );
        addAutomaticRecordLogCommand.setType( command.getType() );
        addAutomaticRecordLogCommand.setFileName( command.getFileName() );
        addAutomaticRecordLogCommand.setDataFileAddress( command.getDataFileAddress() );
        addAutomaticRecordLogCommand.setExtendJarAddress( command.getExtendJarAddress() );
        addAutomaticRecordLogCommand.setThirdJarAddress( command.getThirdJarAddress() );
        addAutomaticRecordLogCommand.setBucketName( command.getBucketName() );
        addAutomaticRecordLogCommand.setLastAutomaticSourceLogCode( command.getLastAutomaticSourceLogCode() );
        addAutomaticRecordLogCommand.setTestcaseCode( command.getTestcaseCode() );
        addAutomaticRecordLogCommand.setPath( command.getPath() );
        addAutomaticRecordLogCommand.setWorkSpace( command.getWorkSpace() );
        addAutomaticRecordLogCommand.setBranch( command.getBranch() );
        addAutomaticRecordLogCommand.setScanDirectory( command.getScanDirectory() );
        addAutomaticRecordLogCommand.setCommitId( command.getCommitId() );
        if ( command.getStatus() != null ) {
            addAutomaticRecordLogCommand.setStatus( command.getStatus().name() );
        }

        return addAutomaticRecordLogCommand;
    }

    @Override
    public EditAutomaticRecordCommand convertDO(AutomaticSourceRecordEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = entityDO.getCode();

        EditAutomaticRecordCommand editAutomaticRecordCommand = new EditAutomaticRecordCommand( aggregateId );

        editAutomaticRecordCommand.setAutoAnalysisFlag( entityDO.getAutoAnalysisFlag() );
        editAutomaticRecordCommand.setType( entityDO.getType() );
        editAutomaticRecordCommand.setProductCode( entityDO.getProductCode() );
        editAutomaticRecordCommand.setName( entityDO.getName() );
        editAutomaticRecordCommand.setComment( entityDO.getComment() );
        editAutomaticRecordCommand.setAddress( entityDO.getAddress() );
        editAutomaticRecordCommand.setFileName( entityDO.getFileName() );
        editAutomaticRecordCommand.setDataFileAddress( entityDO.getDataFileAddress() );
        editAutomaticRecordCommand.setExtendJarAddress( entityDO.getExtendJarAddress() );
        editAutomaticRecordCommand.setThirdJarAddress( entityDO.getThirdJarAddress() );
        editAutomaticRecordCommand.setBucketName( entityDO.getBucketName() );
        editAutomaticRecordCommand.setLastAutomaticSourceLogCode( entityDO.getLastAutomaticSourceLogCode() );
        editAutomaticRecordCommand.setTestcaseCode( entityDO.getTestcaseCode() );
        editAutomaticRecordCommand.setCode( entityDO.getCode() );
        editAutomaticRecordCommand.setStatus( entityDO.getStatus() );
        editAutomaticRecordCommand.setWorkSpace( entityDO.getWorkSpace() );
        editAutomaticRecordCommand.setBranch( entityDO.getBranch() );
        editAutomaticRecordCommand.setScanDirectory( entityDO.getScanDirectory() );
        editAutomaticRecordCommand.setCommitId( entityDO.getCommitId() );

        return editAutomaticRecordCommand;
    }

    @Override
    public AddAutomaticRecordLogEvent convert(AddAutomaticRecordLogCommand command) {
        if ( command == null ) {
            return null;
        }

        AddAutomaticRecordLogEvent addAutomaticRecordLogEvent = new AddAutomaticRecordLogEvent();

        addAutomaticRecordLogEvent.setCode( command.getAggregateId() );
        addAutomaticRecordLogEvent.setAggregateId( command.getAggregateId() );
        addAutomaticRecordLogEvent.setTransactor( command.getTransactor() );
        addAutomaticRecordLogEvent.setOccurred( command.getOccurred() );
        addAutomaticRecordLogEvent.setAnalyticMethod( command.getAnalyticMethod() );
        addAutomaticRecordLogEvent.setAutomaticSourceCode( command.getAutomaticSourceCode() );
        addAutomaticRecordLogEvent.setStatus( command.getStatus() );
        addAutomaticRecordLogEvent.setFailInformation( command.getFailInformation() );
        addAutomaticRecordLogEvent.setProductCode( command.getProductCode() );
        addAutomaticRecordLogEvent.setName( command.getName() );
        addAutomaticRecordLogEvent.setComment( command.getComment() );
        addAutomaticRecordLogEvent.setAddress( command.getAddress() );
        addAutomaticRecordLogEvent.setType( command.getType() );
        addAutomaticRecordLogEvent.setFileName( command.getFileName() );
        addAutomaticRecordLogEvent.setDataFileAddress( command.getDataFileAddress() );
        addAutomaticRecordLogEvent.setExtendJarAddress( command.getExtendJarAddress() );
        addAutomaticRecordLogEvent.setThirdJarAddress( command.getThirdJarAddress() );
        addAutomaticRecordLogEvent.setBucketName( command.getBucketName() );

        return addAutomaticRecordLogEvent;
    }

    @Override
    public EditAutomaticRecordLogEvent convert(EditAutomaticRecordLogCommand command) {
        if ( command == null ) {
            return null;
        }

        EditAutomaticRecordLogEvent editAutomaticRecordLogEvent = new EditAutomaticRecordLogEvent();

        editAutomaticRecordLogEvent.setCode( command.getAggregateId() );
        editAutomaticRecordLogEvent.setAggregateId( command.getAggregateId() );
        editAutomaticRecordLogEvent.setTransactor( command.getTransactor() );
        editAutomaticRecordLogEvent.setOccurred( command.getOccurred() );
        editAutomaticRecordLogEvent.setAutomaticSourceCode( command.getAutomaticSourceCode() );
        editAutomaticRecordLogEvent.setStatus( command.getStatus() );
        editAutomaticRecordLogEvent.setFailInformation( command.getFailInformation() );

        return editAutomaticRecordLogEvent;
    }

    @Override
    public AutomaticRecordVO convert(AutomaticSourceRecordEntityDO entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticRecordVO automaticRecordVO = new AutomaticRecordVO();

        automaticRecordVO.setCode( entity.getCode() );
        automaticRecordVO.setProductCode( entity.getProductCode() );
        automaticRecordVO.setName( entity.getName() );
        automaticRecordVO.setComment( entity.getComment() );
        automaticRecordVO.setAddress( entity.getAddress() );
        automaticRecordVO.setType( entity.getType() );
        automaticRecordVO.setCreatorId( entity.getCreatorId() );
        automaticRecordVO.setCreator( entity.getCreator() );
        automaticRecordVO.setGmtCreate( entity.getGmtCreate() );
        automaticRecordVO.setModifierId( entity.getModifierId() );
        automaticRecordVO.setModifier( entity.getModifier() );
        automaticRecordVO.setGmtModified( entity.getGmtModified() );
        automaticRecordVO.setFileName( entity.getFileName() );
        automaticRecordVO.setBucketName( entity.getBucketName() );
        automaticRecordVO.setDataFileAddress( entity.getDataFileAddress() );
        automaticRecordVO.setExtendJarAddress( entity.getExtendJarAddress() );
        automaticRecordVO.setThirdJarAddress( entity.getThirdJarAddress() );
        automaticRecordVO.setFailInformation( entity.getFailInformation() );
        automaticRecordVO.setStatus( entity.getStatus() );
        automaticRecordVO.setPersonLiableId( entity.getPersonLiableId() );
        automaticRecordVO.setPersonLiable( entity.getPersonLiable() );
        automaticRecordVO.setTestcaseCode( entity.getTestcaseCode() );
        automaticRecordVO.setWorkSpace( entity.getWorkSpace() );
        automaticRecordVO.setBranch( entity.getBranch() );
        automaticRecordVO.setCommitId( entity.getCommitId() );
        automaticRecordVO.setScanDirectory( entity.getScanDirectory() );
        automaticRecordVO.setErrorLogFile( entity.getErrorLogFile() );
        automaticRecordVO.setAutoAnalysisFlag( entity.getAutoAnalysisFlag() );

        return automaticRecordVO;
    }

    @Override
    public SubmitAnalysisAutomaticEvent convert(SubmitAnalysisAutomaticCommand command) {
        if ( command == null ) {
            return null;
        }

        SubmitAnalysisAutomaticEvent submitAnalysisAutomaticEvent = new SubmitAnalysisAutomaticEvent();

        submitAnalysisAutomaticEvent.setCode( command.getAggregateId() );
        submitAnalysisAutomaticEvent.setAggregateId( command.getAggregateId() );
        submitAnalysisAutomaticEvent.setTransactor( command.getTransactor() );
        submitAnalysisAutomaticEvent.setOccurred( command.getOccurred() );
        submitAnalysisAutomaticEvent.setAnalyticMethod( command.getAnalyticMethod() );
        submitAnalysisAutomaticEvent.setAutomaticSourceLogCode( command.getAutomaticSourceLogCode() );
        submitAnalysisAutomaticEvent.setFileName( command.getFileName() );
        submitAnalysisAutomaticEvent.setCommitId( command.getCommitId() );

        return submitAnalysisAutomaticEvent;
    }

    @Override
    public AutomaticSuccessEvent convert(AutomaticSuccessCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticSuccessEvent automaticSuccessEvent = new AutomaticSuccessEvent();

        automaticSuccessEvent.setCode( command.getAggregateId() );
        automaticSuccessEvent.setAggregateId( command.getAggregateId() );
        automaticSuccessEvent.setTransactor( command.getTransactor() );
        automaticSuccessEvent.setOccurred( command.getOccurred() );
        automaticSuccessEvent.setAddCaseNo( command.getAddCaseNo() );
        automaticSuccessEvent.setUpdateCaseNo( command.getUpdateCaseNo() );
        automaticSuccessEvent.setDeleteCaseNo( command.getDeleteCaseNo() );
        automaticSuccessEvent.setFileName( command.getFileName() );
        automaticSuccessEvent.setProductCode( command.getProductCode() );
        automaticSuccessEvent.setCommitId( command.getCommitId() );

        return automaticSuccessEvent;
    }

    @Override
    public AutomaticSourceRecordEntityDO convert(AutomaticSuccessEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSourceRecordEntityDO automaticSourceRecordEntityDO = new AutomaticSourceRecordEntityDO();

        automaticSourceRecordEntityDO.setCode( event.getCode() );
        automaticSourceRecordEntityDO.setProductCode( event.getProductCode() );
        automaticSourceRecordEntityDO.setAddCaseNo( event.getAddCaseNo() );
        automaticSourceRecordEntityDO.setUpdateCaseNo( event.getUpdateCaseNo() );
        automaticSourceRecordEntityDO.setDeleteCaseNo( event.getDeleteCaseNo() );
        automaticSourceRecordEntityDO.setFileName( event.getFileName() );
        automaticSourceRecordEntityDO.setCommitId( event.getCommitId() );

        return automaticSourceRecordEntityDO;
    }

    @Override
    public AutomaticSourceLogTestcaseVO convert(ListTestcaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        AutomaticSourceLogTestcaseVO automaticSourceLogTestcaseVO = new AutomaticSourceLogTestcaseVO();

        automaticSourceLogTestcaseVO.setTestcaseCode( vo.getCode() );
        if ( vo.getStatus() != null ) {
            automaticSourceLogTestcaseVO.setStatus( vo.getStatus().name() );
        }
        if ( vo.getAttribute() != null ) {
            automaticSourceLogTestcaseVO.setAttribute( vo.getAttribute().name() );
        }
        automaticSourceLogTestcaseVO.setCode( vo.getCode() );
        automaticSourceLogTestcaseVO.setProductCode( vo.getProductCode() );
        automaticSourceLogTestcaseVO.setParentCode( vo.getParentCode() );
        automaticSourceLogTestcaseVO.setName( vo.getName() );
        if ( vo.getType() != null ) {
            automaticSourceLogTestcaseVO.setType( vo.getType().name() );
        }
        if ( vo.getPriority() != null ) {
            automaticSourceLogTestcaseVO.setPriority( vo.getPriority().name() );
        }
        automaticSourceLogTestcaseVO.setPrecondition( vo.getPrecondition() );
        automaticSourceLogTestcaseVO.setDutyUserId( vo.getDutyUserId() );
        automaticSourceLogTestcaseVO.setDutyUser( vo.getDutyUser() );
        automaticSourceLogTestcaseVO.setComment( vo.getComment() );
        if ( vo.getNodeType() != null ) {
            automaticSourceLogTestcaseVO.setNodeType( vo.getNodeType().name() );
        }
        automaticSourceLogTestcaseVO.setSort( vo.getSort() );
        automaticSourceLogTestcaseVO.setLayer( vo.getLayer() );
        automaticSourceLogTestcaseVO.setPath( vo.getPath() );
        automaticSourceLogTestcaseVO.setCreatorId( vo.getCreatorId() );
        automaticSourceLogTestcaseVO.setCreator( vo.getCreator() );
        automaticSourceLogTestcaseVO.setGmtCreate( vo.getGmtCreate() );
        automaticSourceLogTestcaseVO.setModifierId( vo.getModifierId() );
        automaticSourceLogTestcaseVO.setModifier( vo.getModifier() );
        automaticSourceLogTestcaseVO.setGmtModified( vo.getGmtModified() );
        automaticSourceLogTestcaseVO.setNodeTypePath( vo.getNodeTypePath() );
        automaticSourceLogTestcaseVO.setStatusDesc( vo.getStatusDesc() );
        automaticSourceLogTestcaseVO.setHasChildren( vo.getHasChildren() );
        List<TmTestPlanVO> list = vo.getTestPlanList();
        if ( list != null ) {
            automaticSourceLogTestcaseVO.setTestPlanList( new ArrayList<TmTestPlanVO>( list ) );
        }
        automaticSourceLogTestcaseVO.setCount( vo.getCount() );
        automaticSourceLogTestcaseVO.setPlanName( vo.getPlanName() );
        automaticSourceLogTestcaseVO.setInterfaceName( vo.getInterfaceName() );
        automaticSourceLogTestcaseVO.setSonList( testcaseVOListToAutomaticSourceLogTestcaseVOList( vo.getSonList() ) );

        return automaticSourceLogTestcaseVO;
    }

    protected List<AutomaticSourceLogTestcaseVO> testcaseVOListToAutomaticSourceLogTestcaseVOList(List<TestcaseVO> list) {
        if ( list == null ) {
            return null;
        }

        List<AutomaticSourceLogTestcaseVO> list1 = new ArrayList<AutomaticSourceLogTestcaseVO>( list.size() );
        for ( TestcaseVO testcaseVO : list ) {
            list1.add( testcaseVOToAutomaticSourceLogTestcaseVO( testcaseVO ) );
        }

        return list1;
    }

    protected AutomaticSourceLogTestcaseVO testcaseVOToAutomaticSourceLogTestcaseVO(TestcaseVO testcaseVO) {
        if ( testcaseVO == null ) {
            return null;
        }

        AutomaticSourceLogTestcaseVO automaticSourceLogTestcaseVO = new AutomaticSourceLogTestcaseVO();

        if ( testcaseVO.getStatus() != null ) {
            automaticSourceLogTestcaseVO.setStatus( testcaseVO.getStatus().name() );
        }
        if ( testcaseVO.getAttribute() != null ) {
            automaticSourceLogTestcaseVO.setAttribute( testcaseVO.getAttribute().name() );
        }
        automaticSourceLogTestcaseVO.setCode( testcaseVO.getCode() );
        automaticSourceLogTestcaseVO.setProductCode( testcaseVO.getProductCode() );
        automaticSourceLogTestcaseVO.setParentCode( testcaseVO.getParentCode() );
        automaticSourceLogTestcaseVO.setName( testcaseVO.getName() );
        if ( testcaseVO.getType() != null ) {
            automaticSourceLogTestcaseVO.setType( testcaseVO.getType().name() );
        }
        if ( testcaseVO.getPriority() != null ) {
            automaticSourceLogTestcaseVO.setPriority( testcaseVO.getPriority().name() );
        }
        automaticSourceLogTestcaseVO.setPrecondition( testcaseVO.getPrecondition() );
        automaticSourceLogTestcaseVO.setDutyUserId( testcaseVO.getDutyUserId() );
        automaticSourceLogTestcaseVO.setDutyUser( testcaseVO.getDutyUser() );
        automaticSourceLogTestcaseVO.setComment( testcaseVO.getComment() );
        if ( testcaseVO.getNodeType() != null ) {
            automaticSourceLogTestcaseVO.setNodeType( testcaseVO.getNodeType().name() );
        }
        automaticSourceLogTestcaseVO.setSort( testcaseVO.getSort() );
        automaticSourceLogTestcaseVO.setLayer( testcaseVO.getLayer() );
        automaticSourceLogTestcaseVO.setPath( testcaseVO.getPath() );
        automaticSourceLogTestcaseVO.setCreatorId( testcaseVO.getCreatorId() );
        automaticSourceLogTestcaseVO.setCreator( testcaseVO.getCreator() );
        automaticSourceLogTestcaseVO.setGmtCreate( testcaseVO.getGmtCreate() );
        automaticSourceLogTestcaseVO.setModifierId( testcaseVO.getModifierId() );
        automaticSourceLogTestcaseVO.setModifier( testcaseVO.getModifier() );
        automaticSourceLogTestcaseVO.setGmtModified( testcaseVO.getGmtModified() );
        automaticSourceLogTestcaseVO.setNodeTypePath( testcaseVO.getNodeTypePath() );
        automaticSourceLogTestcaseVO.setStatusDesc( testcaseVO.getStatusDesc() );
        automaticSourceLogTestcaseVO.setHasChildren( testcaseVO.getHasChildren() );
        List<TmTestPlanVO> list = testcaseVO.getTestPlanList();
        if ( list != null ) {
            automaticSourceLogTestcaseVO.setTestPlanList( new ArrayList<TmTestPlanVO>( list ) );
        }
        automaticSourceLogTestcaseVO.setCount( testcaseVO.getCount() );
        automaticSourceLogTestcaseVO.setPlanName( testcaseVO.getPlanName() );
        automaticSourceLogTestcaseVO.setInterfaceName( testcaseVO.getInterfaceName() );
        automaticSourceLogTestcaseVO.setSonList( testcaseVOListToAutomaticSourceLogTestcaseVOList( testcaseVO.getSonList() ) );

        return automaticSourceLogTestcaseVO;
    }
}
