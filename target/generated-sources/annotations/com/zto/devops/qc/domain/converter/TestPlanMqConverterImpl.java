package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.testPlan.entity.TestPlanDataResp;
import com.zto.devops.qc.client.model.testPlan.event.AddSafetyTestPlanEvent;
import com.zto.devops.qc.client.model.testPlan.event.EditSafetyTestPlanEvent;
import com.zto.devops.qc.client.model.testmanager.plan.entity.SimpleTmTestPlanVO;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestPlanMqConverterImpl implements TestPlanMqConverter {

    @Override
    public TestPlanDataResp convert(AddSafetyTestPlanEvent event) {
        if ( event == null ) {
            return null;
        }

        TestPlanDataResp testPlanDataResp = new TestPlanDataResp();

        testPlanDataResp.setCode( event.getCode() );
        testPlanDataResp.setTestPlanMainCode( event.getTestPlanMainCode() );
        testPlanDataResp.setPlanName( event.getPlanName() );
        testPlanDataResp.setProductName( event.getProductName() );
        testPlanDataResp.setVersionName( event.getVersionName() );
        testPlanDataResp.setVersionCode( event.getVersionCode() );
        testPlanDataResp.setPermissionsTest( event.getPermissionsTest() );
        testPlanDataResp.setPriority( event.getPriority() );
        testPlanDataResp.setLastTestDate( event.getLastTestDate() );
        testPlanDataResp.setTestInformation( event.getTestInformation() );
        testPlanDataResp.setPriorityTestInformation( event.getPriorityTestInformation() );
        testPlanDataResp.setProductCode( event.getProductCode() );
        testPlanDataResp.setStatus( event.getStatus() );

        return testPlanDataResp;
    }

    @Override
    public TestPlanDataResp convert(EditSafetyTestPlanEvent event) {
        if ( event == null ) {
            return null;
        }

        TestPlanDataResp testPlanDataResp = new TestPlanDataResp();

        testPlanDataResp.setCode( event.getCode() );
        testPlanDataResp.setTestPlanMainCode( event.getTestPlanMainCode() );
        testPlanDataResp.setPlanName( event.getPlanName() );
        testPlanDataResp.setProductName( event.getProductName() );
        testPlanDataResp.setVersionName( event.getVersionName() );
        testPlanDataResp.setVersionCode( event.getVersionCode() );
        testPlanDataResp.setPermissionsTest( event.getPermissionsTest() );
        testPlanDataResp.setPriority( event.getPriority() );
        testPlanDataResp.setLastTestDate( event.getLastTestDate() );
        testPlanDataResp.setTestInformation( event.getTestInformation() );
        testPlanDataResp.setPriorityTestInformation( event.getPriorityTestInformation() );
        testPlanDataResp.setProductCode( event.getProductCode() );
        if ( event.getStatus() != null ) {
            testPlanDataResp.setStatus( event.getStatus().name() );
        }

        return testPlanDataResp;
    }

    @Override
    public TestPlanDataResp convert(SimpleTmTestPlanVO planVO) {
        if ( planVO == null ) {
            return null;
        }

        TestPlanDataResp testPlanDataResp = new TestPlanDataResp();

        testPlanDataResp.setCode( planVO.getCode() );
        testPlanDataResp.setPlanName( planVO.getPlanName() );
        testPlanDataResp.setProductName( planVO.getProductName() );
        testPlanDataResp.setVersionName( planVO.getVersionName() );
        testPlanDataResp.setVersionCode( planVO.getVersionCode() );
        testPlanDataResp.setPermissionsTest( planVO.getPermissionsTest() );
        if ( planVO.getPriority() != null ) {
            testPlanDataResp.setPriority( planVO.getPriority().name() );
        }
        testPlanDataResp.setLastTestDate( planVO.getLastTestDate() );
        testPlanDataResp.setTestInformation( planVO.getTestInformation() );
        testPlanDataResp.setProductCode( planVO.getProductCode() );
        if ( planVO.getStatus() != null ) {
            testPlanDataResp.setStatus( planVO.getStatus().name() );
        }

        return testPlanDataResp;
    }
}
