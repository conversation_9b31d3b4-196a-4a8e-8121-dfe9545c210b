package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.enums.issue.IssueApplicationType;
import com.zto.devops.qc.client.enums.issue.IssueFindEnv;
import com.zto.devops.qc.client.enums.issue.IssueFindStage;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueRepetitionRate;
import com.zto.devops.qc.client.enums.issue.IssueRootCause;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.IssueTestMethod;
import com.zto.devops.qc.client.enums.issue.IssueType;
import com.zto.devops.qc.client.enums.issue.RefuseReason;
import com.zto.devops.qc.client.enums.issue.RelatedToMeEnum;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.issue.command.AddVersionIssueCommand;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.query.ExpIssueQuery;
import com.zto.devops.qc.client.model.issue.query.IssueQuery;
import com.zto.devops.qc.client.model.issue.query.PageIssueQuery;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.rpc.project.RelatedItemVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueVOConverterImpl implements IssueVOConverter {

    @Override
    public IssueVO convert(IssueEntityDO entity) {
        if ( entity == null ) {
            return null;
        }

        IssueVO issueVO = new IssueVO();

        issueVO.setTagName( entity.getCreator() );
        issueVO.setUpdateUserId( entity.getModifierId() );
        issueVO.setUpdateUserName( entity.getModifier() );
        issueVO.setCode( entity.getCode() );
        issueVO.setTitle( entity.getTitle() );
        issueVO.setDescription( entity.getDescription() );
        issueVO.setReopenTime( entity.getReopenTime() );
        issueVO.setStartFixTime( entity.getStartFixTime() );
        issueVO.setDelayFixTime( entity.getDelayFixTime() );
        issueVO.setDeliverTime( entity.getDeliverTime() );
        issueVO.setRejectTime( entity.getRejectTime() );
        issueVO.setCloseTime( entity.getCloseTime() );
        issueVO.setGmtCreate( entity.getGmtCreate() );
        issueVO.setGmtModified( entity.getGmtModified() );
        issueVO.setUpdateTime( entity.getUpdateTime() );
        issueVO.setFindTime( entity.getFindTime() );
        issueVO.setStatus( entity.getStatus() );
        issueVO.setFindEnv( entity.getFindEnv() );
        issueVO.setFindStage( entity.getFindStage() );
        issueVO.setPriority( entity.getPriority() );
        issueVO.setRepetitionRate( entity.getRepetitionRate() );
        issueVO.setRootCause( entity.getRootCause() );
        issueVO.setTestMethod( entity.getTestMethod() );
        issueVO.setType( entity.getType() );
        issueVO.setProductCode( entity.getProductCode() );
        issueVO.setProductName( entity.getProductName() );
        issueVO.setSprintCode( entity.getSprintCode() );
        issueVO.setSprintName( entity.getSprintName() );
        issueVO.setRequirementCode( entity.getRequirementCode() );
        issueVO.setRequirementName( entity.getRequirementName() );
        issueVO.setRequirementLevel( entity.getRequirementLevel() );
        issueVO.setFindVersionCode( entity.getFindVersionCode() );
        issueVO.setFindVersionName( entity.getFindVersionName() );
        issueVO.setFindUserId( entity.getFindUserId() );
        issueVO.setFindUserName( entity.getFindUserName() );
        issueVO.setHandleUserId( entity.getHandleUserId() );
        issueVO.setHandleUserName( entity.getHandleUserName() );
        issueVO.setDevelopUserId( entity.getDevelopUserId() );
        issueVO.setDevelopUserName( entity.getDevelopUserName() );
        issueVO.setTestUserId( entity.getTestUserId() );
        issueVO.setTestUserName( entity.getTestUserName() );
        issueVO.setCreatorId( entity.getCreatorId() );
        issueVO.setCreator( entity.getCreator() );
        issueVO.setActualWorkingHours( entity.getActualWorkingHours() );
        issueVO.setVersionConfirm( entity.getVersionConfirm() );
        issueVO.setExamination( entity.getExamination() );
        issueVO.setTestOmission( entity.getTestOmission() );
        issueVO.setCodeDefect( entity.getCodeDefect() );
        issueVO.setTestOmissionVersion( entity.getTestOmissionVersion() );
        issueVO.setCodeDefectVersion( entity.getCodeDefectVersion() );
        issueVO.setApplicationType( entity.getApplicationType() );
        issueVO.setPlanStartDate( entity.getPlanStartDate() );
        issueVO.setPlanEndDate( entity.getPlanEndDate() );
        issueVO.setMsgCode( entity.getMsgCode() );
        issueVO.setReopen( entity.getReopen() );

        issueVO.setFixVersionCode( com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.isDefaultValue(entity.getFixVersionCode())?null:entity.getFixVersionCode() );
        issueVO.setFixVersionName( (com.zto.devops.framework.common.util.StringUtil.isNotBlank(entity.getFixVersionName()))?null: entity.getFixVersionName() );

        return issueVO;
    }

    @Override
    public List<IssueVO> convert(List<IssueEntityDO> entity) {
        if ( entity == null ) {
            return null;
        }

        List<IssueVO> list = new ArrayList<IssueVO>( entity.size() );
        for ( IssueEntityDO issueEntityDO : entity ) {
            list.add( convert( issueEntityDO ) );
        }

        return list;
    }

    @Override
    public IssueQueryParameter convert(PageIssueQuery query) {
        if ( query == null ) {
            return null;
        }

        IssueQueryParameter issueQueryParameter = new IssueQueryParameter();

        issueQueryParameter.setCodeOrTitle( query.getCodeOrTitle() );
        issueQueryParameter.setCurrentUserId( query.getCurrentUserId() );
        List<IssueStatus> list = query.getStatusList();
        if ( list != null ) {
            issueQueryParameter.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        List<IssuePriority> list1 = query.getPriorityList();
        if ( list1 != null ) {
            issueQueryParameter.setPriorityList( new ArrayList<IssuePriority>( list1 ) );
        }
        List<RelatedToMeEnum> list2 = query.getRelatedList();
        if ( list2 != null ) {
            issueQueryParameter.setRelatedList( new ArrayList<RelatedToMeEnum>( list2 ) );
        }
        List<Long> list3 = query.getHandleUserIdList();
        if ( list3 != null ) {
            issueQueryParameter.setHandleUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = query.getDevelopUserIdList();
        if ( list4 != null ) {
            issueQueryParameter.setDevelopUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = query.getTestUserIdList();
        if ( list5 != null ) {
            issueQueryParameter.setTestUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<String> list6 = query.getFindVersionList();
        if ( list6 != null ) {
            issueQueryParameter.setFindVersionList( new ArrayList<String>( list6 ) );
        }
        List<String> list7 = query.getFixVersionList();
        if ( list7 != null ) {
            issueQueryParameter.setFixVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = query.getFixOrFindVersionList();
        if ( list8 != null ) {
            issueQueryParameter.setFixOrFindVersionList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = query.getRelatedRequireList();
        if ( list9 != null ) {
            issueQueryParameter.setRelatedRequireList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = query.getRelatedProductList();
        if ( list10 != null ) {
            issueQueryParameter.setRelatedProductList( new ArrayList<String>( list10 ) );
        }
        List<String> list11 = query.getSprintCode();
        if ( list11 != null ) {
            issueQueryParameter.setSprintCode( new ArrayList<String>( list11 ) );
        }
        List<IssueRootCause> list12 = query.getRootCauseList();
        if ( list12 != null ) {
            issueQueryParameter.setRootCauseList( new ArrayList<IssueRootCause>( list12 ) );
        }
        List<IssueType> list13 = query.getIssueTypeList();
        if ( list13 != null ) {
            issueQueryParameter.setIssueTypeList( new ArrayList<IssueType>( list13 ) );
        }
        List<IssueTestMethod> list14 = query.getTestMethodList();
        if ( list14 != null ) {
            issueQueryParameter.setTestMethodList( new ArrayList<IssueTestMethod>( list14 ) );
        }
        List<IssueRepetitionRate> list15 = query.getRepetitionRateList();
        if ( list15 != null ) {
            issueQueryParameter.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list15 ) );
        }
        List<IssueFindStage> list16 = query.getFindStageList();
        if ( list16 != null ) {
            issueQueryParameter.setFindStageList( new ArrayList<IssueFindStage>( list16 ) );
        }
        List<IssueFindEnv> list17 = query.getFindEnvList();
        if ( list17 != null ) {
            issueQueryParameter.setFindEnvList( new ArrayList<IssueFindEnv>( list17 ) );
        }
        List<Long> list18 = query.getFindUserIdList();
        if ( list18 != null ) {
            issueQueryParameter.setFindUserIdList( new ArrayList<Long>( list18 ) );
        }
        List<String> list19 = query.getTagName();
        if ( list19 != null ) {
            issueQueryParameter.setTagName( new ArrayList<String>( list19 ) );
        }
        issueQueryParameter.setCreateTimeStart( query.getCreateTimeStart() );
        issueQueryParameter.setCreateTimeEnd( query.getCreateTimeEnd() );
        issueQueryParameter.setCloseTimeStart( query.getCloseTimeStart() );
        issueQueryParameter.setCloseTimeEnd( query.getCloseTimeEnd() );
        issueQueryParameter.setFixVersionIsNull( query.getFixVersionIsNull() );
        issueQueryParameter.setGmtModifiedStart( query.getGmtModifiedStart() );
        issueQueryParameter.setGmtModifiedEnd( query.getGmtModifiedEnd() );
        List<String> list20 = query.getVersionConfirm();
        if ( list20 != null ) {
            issueQueryParameter.setVersionConfirm( new ArrayList<String>( list20 ) );
        }
        List<String> list21 = query.getIssueCodeList();
        if ( list21 != null ) {
            issueQueryParameter.setIssueCodeList( new ArrayList<String>( list21 ) );
        }
        List<Boolean> list22 = query.getTestOmission();
        if ( list22 != null ) {
            issueQueryParameter.setTestOmission( new ArrayList<Boolean>( list22 ) );
        }
        List<Boolean> list23 = query.getCodeDefect();
        if ( list23 != null ) {
            issueQueryParameter.setCodeDefect( new ArrayList<Boolean>( list23 ) );
        }
        List<Boolean> list24 = query.getExamination();
        if ( list24 != null ) {
            issueQueryParameter.setExamination( new ArrayList<Boolean>( list24 ) );
        }
        List<RefuseReason> list25 = query.getRefuseReasonList();
        if ( list25 != null ) {
            issueQueryParameter.setRefuseReasonList( new ArrayList<RefuseReason>( list25 ) );
        }
        List<IssueApplicationType> list26 = query.getApplicationTypeList();
        if ( list26 != null ) {
            issueQueryParameter.setApplicationTypeList( new ArrayList<IssueApplicationType>( list26 ) );
        }
        issueQueryParameter.setVersionType( query.getVersionType() );
        issueQueryParameter.setCCUserId( query.getCCUserId() );
        issueQueryParameter.setType( query.getType() );
        issueQueryParameter.setOrderField( query.getOrderField() );
        issueQueryParameter.setOrderType( query.getOrderType() );
        List<Long> list27 = query.getCcUserIdList();
        if ( list27 != null ) {
            issueQueryParameter.setCcUserIdList( new ArrayList<Long>( list27 ) );
        }
        issueQueryParameter.setValidFlag( query.getValidFlag() );

        return issueQueryParameter;
    }

    @Override
    public IssueQueryParameter convert(IssueQuery query) {
        if ( query == null ) {
            return null;
        }

        IssueQueryParameter issueQueryParameter = new IssueQueryParameter();

        issueQueryParameter.setCodeOrTitle( query.getCodeOrTitle() );
        issueQueryParameter.setCurrentUserId( query.getCurrentUserId() );
        List<IssueStatus> list = query.getStatusList();
        if ( list != null ) {
            issueQueryParameter.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        List<IssuePriority> list1 = query.getPriorityList();
        if ( list1 != null ) {
            issueQueryParameter.setPriorityList( new ArrayList<IssuePriority>( list1 ) );
        }
        List<RelatedToMeEnum> list2 = query.getRelatedList();
        if ( list2 != null ) {
            issueQueryParameter.setRelatedList( new ArrayList<RelatedToMeEnum>( list2 ) );
        }
        List<Long> list3 = query.getHandleUserIdList();
        if ( list3 != null ) {
            issueQueryParameter.setHandleUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = query.getDevelopUserIdList();
        if ( list4 != null ) {
            issueQueryParameter.setDevelopUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = query.getTestUserIdList();
        if ( list5 != null ) {
            issueQueryParameter.setTestUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<String> list6 = query.getFindVersionList();
        if ( list6 != null ) {
            issueQueryParameter.setFindVersionList( new ArrayList<String>( list6 ) );
        }
        List<String> list7 = query.getFixVersionList();
        if ( list7 != null ) {
            issueQueryParameter.setFixVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = query.getFixOrFindVersionList();
        if ( list8 != null ) {
            issueQueryParameter.setFixOrFindVersionList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = query.getRelatedRequireList();
        if ( list9 != null ) {
            issueQueryParameter.setRelatedRequireList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = query.getRelatedProductList();
        if ( list10 != null ) {
            issueQueryParameter.setRelatedProductList( new ArrayList<String>( list10 ) );
        }
        List<String> list11 = query.getSprintCode();
        if ( list11 != null ) {
            issueQueryParameter.setSprintCode( new ArrayList<String>( list11 ) );
        }
        List<IssueRootCause> list12 = query.getRootCauseList();
        if ( list12 != null ) {
            issueQueryParameter.setRootCauseList( new ArrayList<IssueRootCause>( list12 ) );
        }
        List<IssueType> list13 = query.getIssueTypeList();
        if ( list13 != null ) {
            issueQueryParameter.setIssueTypeList( new ArrayList<IssueType>( list13 ) );
        }
        List<IssueTestMethod> list14 = query.getTestMethodList();
        if ( list14 != null ) {
            issueQueryParameter.setTestMethodList( new ArrayList<IssueTestMethod>( list14 ) );
        }
        List<IssueRepetitionRate> list15 = query.getRepetitionRateList();
        if ( list15 != null ) {
            issueQueryParameter.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list15 ) );
        }
        List<IssueFindStage> list16 = query.getFindStageList();
        if ( list16 != null ) {
            issueQueryParameter.setFindStageList( new ArrayList<IssueFindStage>( list16 ) );
        }
        List<IssueFindEnv> list17 = query.getFindEnvList();
        if ( list17 != null ) {
            issueQueryParameter.setFindEnvList( new ArrayList<IssueFindEnv>( list17 ) );
        }
        List<Long> list18 = query.getFindUserIdList();
        if ( list18 != null ) {
            issueQueryParameter.setFindUserIdList( new ArrayList<Long>( list18 ) );
        }
        issueQueryParameter.setCreateTimeStart( query.getCreateTimeStart() );
        issueQueryParameter.setCreateTimeEnd( query.getCreateTimeEnd() );
        issueQueryParameter.setCloseTimeStart( query.getCloseTimeStart() );
        issueQueryParameter.setCloseTimeEnd( query.getCloseTimeEnd() );
        issueQueryParameter.setFixVersionIsNull( query.getFixVersionIsNull() );
        issueQueryParameter.setGmtModifiedStart( query.getGmtModifiedStart() );
        issueQueryParameter.setGmtModifiedEnd( query.getGmtModifiedEnd() );
        List<String> list19 = query.getVersionConfirm();
        if ( list19 != null ) {
            issueQueryParameter.setVersionConfirm( new ArrayList<String>( list19 ) );
        }
        List<Long> list20 = query.getModifierId();
        if ( list20 != null ) {
            issueQueryParameter.setModifierId( new ArrayList<Long>( list20 ) );
        }

        return issueQueryParameter;
    }

    @Override
    public List<AddVersionIssueCommand> convertVersion(List<RelatedItemVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<AddVersionIssueCommand> list = new ArrayList<AddVersionIssueCommand>( vos.size() );
        for ( RelatedItemVO relatedItemVO : vos ) {
            list.add( relatedItemVOToAddVersionIssueCommand( relatedItemVO ) );
        }

        return list;
    }

    @Override
    public PageIssueQuery convertQuery(ExpIssueQuery query) {
        if ( query == null ) {
            return null;
        }

        PageIssueQuery pageIssueQuery = new PageIssueQuery();

        pageIssueQuery.setTransactor( query.getTransactor() );
        pageIssueQuery.setPage( query.getPage() );
        pageIssueQuery.setSize( query.getSize() );
        List<String> list = query.getIssueCodeList();
        if ( list != null ) {
            pageIssueQuery.setIssueCodeList( new ArrayList<String>( list ) );
        }
        pageIssueQuery.setCodeOrTitle( query.getCodeOrTitle() );
        pageIssueQuery.setCurrentUserId( query.getCurrentUserId() );
        List<IssueStatus> list1 = query.getStatusList();
        if ( list1 != null ) {
            pageIssueQuery.setStatusList( new ArrayList<IssueStatus>( list1 ) );
        }
        List<IssuePriority> list2 = query.getPriorityList();
        if ( list2 != null ) {
            pageIssueQuery.setPriorityList( new ArrayList<IssuePriority>( list2 ) );
        }
        List<RelatedToMeEnum> list3 = query.getRelatedList();
        if ( list3 != null ) {
            pageIssueQuery.setRelatedList( new ArrayList<RelatedToMeEnum>( list3 ) );
        }
        List<Long> list4 = query.getHandleUserIdList();
        if ( list4 != null ) {
            pageIssueQuery.setHandleUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = query.getDevelopUserIdList();
        if ( list5 != null ) {
            pageIssueQuery.setDevelopUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<Long> list6 = query.getTestUserIdList();
        if ( list6 != null ) {
            pageIssueQuery.setTestUserIdList( new ArrayList<Long>( list6 ) );
        }
        List<String> list7 = query.getFindVersionList();
        if ( list7 != null ) {
            pageIssueQuery.setFindVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = query.getFixVersionList();
        if ( list8 != null ) {
            pageIssueQuery.setFixVersionList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = query.getFixOrFindVersionList();
        if ( list9 != null ) {
            pageIssueQuery.setFixOrFindVersionList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = query.getRelatedRequireList();
        if ( list10 != null ) {
            pageIssueQuery.setRelatedRequireList( new ArrayList<String>( list10 ) );
        }
        List<String> list11 = query.getRelatedProductList();
        if ( list11 != null ) {
            pageIssueQuery.setRelatedProductList( new ArrayList<String>( list11 ) );
        }
        List<String> list12 = query.getSprintCode();
        if ( list12 != null ) {
            pageIssueQuery.setSprintCode( new ArrayList<String>( list12 ) );
        }
        List<IssueRootCause> list13 = query.getRootCauseList();
        if ( list13 != null ) {
            pageIssueQuery.setRootCauseList( new ArrayList<IssueRootCause>( list13 ) );
        }
        List<IssueType> list14 = query.getIssueTypeList();
        if ( list14 != null ) {
            pageIssueQuery.setIssueTypeList( new ArrayList<IssueType>( list14 ) );
        }
        List<IssueTestMethod> list15 = query.getTestMethodList();
        if ( list15 != null ) {
            pageIssueQuery.setTestMethodList( new ArrayList<IssueTestMethod>( list15 ) );
        }
        List<IssueRepetitionRate> list16 = query.getRepetitionRateList();
        if ( list16 != null ) {
            pageIssueQuery.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list16 ) );
        }
        List<IssueFindStage> list17 = query.getFindStageList();
        if ( list17 != null ) {
            pageIssueQuery.setFindStageList( new ArrayList<IssueFindStage>( list17 ) );
        }
        List<IssueFindEnv> list18 = query.getFindEnvList();
        if ( list18 != null ) {
            pageIssueQuery.setFindEnvList( new ArrayList<IssueFindEnv>( list18 ) );
        }
        List<Long> list19 = query.getFindUserIdList();
        if ( list19 != null ) {
            pageIssueQuery.setFindUserIdList( new ArrayList<Long>( list19 ) );
        }
        List<String> list20 = query.getTagName();
        if ( list20 != null ) {
            pageIssueQuery.setTagName( new ArrayList<String>( list20 ) );
        }
        pageIssueQuery.setCreateTimeStart( query.getCreateTimeStart() );
        pageIssueQuery.setCreateTimeEnd( query.getCreateTimeEnd() );
        pageIssueQuery.setCloseTimeStart( query.getCloseTimeStart() );
        pageIssueQuery.setCloseTimeEnd( query.getCloseTimeEnd() );
        pageIssueQuery.setFixVersionIsNull( query.getFixVersionIsNull() );
        pageIssueQuery.setGmtModifiedStart( query.getGmtModifiedStart() );
        pageIssueQuery.setGmtModifiedEnd( query.getGmtModifiedEnd() );
        List<String> list21 = query.getVersionConfirm();
        if ( list21 != null ) {
            pageIssueQuery.setVersionConfirm( new ArrayList<String>( list21 ) );
        }
        List<Boolean> list22 = query.getExamination();
        if ( list22 != null ) {
            pageIssueQuery.setExamination( new ArrayList<Boolean>( list22 ) );
        }
        List<Boolean> list23 = query.getTestOmission();
        if ( list23 != null ) {
            pageIssueQuery.setTestOmission( new ArrayList<Boolean>( list23 ) );
        }
        List<Boolean> list24 = query.getCodeDefect();
        if ( list24 != null ) {
            pageIssueQuery.setCodeDefect( new ArrayList<Boolean>( list24 ) );
        }
        List<IssueApplicationType> list25 = query.getApplicationTypeList();
        if ( list25 != null ) {
            pageIssueQuery.setApplicationTypeList( new ArrayList<IssueApplicationType>( list25 ) );
        }
        pageIssueQuery.setVersionType( query.getVersionType() );
        List<RefuseReason> list26 = query.getRefuseReasonList();
        if ( list26 != null ) {
            pageIssueQuery.setRefuseReasonList( new ArrayList<RefuseReason>( list26 ) );
        }
        pageIssueQuery.setCCUserId( query.getCCUserId() );
        pageIssueQuery.setType( query.getType() );
        pageIssueQuery.setOrderField( query.getOrderField() );
        pageIssueQuery.setOrderType( query.getOrderType() );
        List<Long> list27 = query.getCcUserIdList();
        if ( list27 != null ) {
            pageIssueQuery.setCcUserIdList( new ArrayList<Long>( list27 ) );
        }
        pageIssueQuery.setGroupId( query.getGroupId() );
        pageIssueQuery.setValidFlag( query.getValidFlag() );

        return pageIssueQuery;
    }

    protected AddVersionIssueCommand relatedItemVOToAddVersionIssueCommand(RelatedItemVO relatedItemVO) {
        if ( relatedItemVO == null ) {
            return null;
        }

        String aggregateId = null;

        AddVersionIssueCommand addVersionIssueCommand = new AddVersionIssueCommand( aggregateId );

        addVersionIssueCommand.setTitle( relatedItemVO.getTitle() );
        addVersionIssueCommand.setVersionConfirm( relatedItemVO.getVersionConfirm() );

        return addVersionIssueCommand;
    }
}
