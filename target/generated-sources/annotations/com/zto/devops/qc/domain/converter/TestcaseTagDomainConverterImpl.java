package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.BatchAddTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagBatchAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagRemovedEvent;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:03+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseTagDomainConverterImpl implements TestcaseTagDomainConverter {

    @Override
    public TestcaseTagAddedEvent converter(AddTestcaseTagCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseTagAddedEvent testcaseTagAddedEvent = new TestcaseTagAddedEvent();

        testcaseTagAddedEvent.setCode( command.getAggregateId() );
        testcaseTagAddedEvent.setAggregateId( command.getAggregateId() );
        testcaseTagAddedEvent.setTransactor( command.getTransactor() );
        testcaseTagAddedEvent.setOccurred( command.getOccurred() );
        testcaseTagAddedEvent.setDomain( command.getDomain() );
        testcaseTagAddedEvent.setBusinessCode( command.getBusinessCode() );
        testcaseTagAddedEvent.setType( command.getType() );
        testcaseTagAddedEvent.setTagName( command.getTagName() );

        return testcaseTagAddedEvent;
    }

    @Override
    public TestcaseTagBatchAddedEvent converter(BatchAddTestcaseTagCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseTagBatchAddedEvent testcaseTagBatchAddedEvent = new TestcaseTagBatchAddedEvent();

        testcaseTagBatchAddedEvent.setCode( command.getAggregateId() );
        testcaseTagBatchAddedEvent.setAggregateId( command.getAggregateId() );
        testcaseTagBatchAddedEvent.setTransactor( command.getTransactor() );
        testcaseTagBatchAddedEvent.setOccurred( command.getOccurred() );
        testcaseTagBatchAddedEvent.setDomain( command.getDomain() );
        List<String> list = command.getBusinessCodes();
        if ( list != null ) {
            testcaseTagBatchAddedEvent.setBusinessCodes( new ArrayList<String>( list ) );
        }
        testcaseTagBatchAddedEvent.setType( command.getType() );
        List<String> list1 = command.getTagNames();
        if ( list1 != null ) {
            testcaseTagBatchAddedEvent.setTagNames( new ArrayList<String>( list1 ) );
        }

        return testcaseTagBatchAddedEvent;
    }

    @Override
    public List<TagVO> convert(List<TagEntityDO> entity) {
        if ( entity == null ) {
            return null;
        }

        List<TagVO> list = new ArrayList<TagVO>( entity.size() );
        for ( TagEntityDO tagEntityDO : entity ) {
            list.add( tagEntityDOToTagVO( tagEntityDO ) );
        }

        return list;
    }

    @Override
    public TestcaseTagRemovedEvent converter(RemoveTestcaseTagCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseTagRemovedEvent testcaseTagRemovedEvent = new TestcaseTagRemovedEvent();

        testcaseTagRemovedEvent.setCode( command.getAggregateId() );
        testcaseTagRemovedEvent.setAggregateId( command.getAggregateId() );
        testcaseTagRemovedEvent.setTransactor( command.getTransactor() );
        testcaseTagRemovedEvent.setOccurred( command.getOccurred() );

        return testcaseTagRemovedEvent;
    }

    protected TagVO tagEntityDOToTagVO(TagEntityDO tagEntityDO) {
        if ( tagEntityDO == null ) {
            return null;
        }

        TagVO tagVO = new TagVO();

        tagVO.setDomain( tagEntityDO.getDomain() );
        tagVO.setBusinessCode( tagEntityDO.getBusinessCode() );
        tagVO.setCode( tagEntityDO.getCode() );
        tagVO.setType( tagEntityDO.getType() );
        tagVO.setTagAlias( tagEntityDO.getTagAlias() );
        tagVO.setTagCode( tagEntityDO.getTagCode() );
        tagVO.setTagName( tagEntityDO.getTagName() );
        tagVO.setEnable( tagEntityDO.getEnable() );
        tagVO.setCreatorId( tagEntityDO.getCreatorId() );

        return tagVO;
    }
}
