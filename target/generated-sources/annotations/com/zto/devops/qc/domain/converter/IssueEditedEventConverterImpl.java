package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.qc.client.model.issue.command.EditIssueCommand;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueEditedEventConverterImpl implements IssueEditedEventConverter {

    @Override
    public IssueEditedEvent convert(EditIssueCommand command) {
        if ( command == null ) {
            return null;
        }

        IssueEditedEvent issueEditedEvent = new IssueEditedEvent();

        issueEditedEvent.setCode( command.getAggregateId() );
        issueEditedEvent.setAggregateId( command.getAggregateId() );
        issueEditedEvent.setTransactor( command.getTransactor() );
        issueEditedEvent.setOccurred( command.getOccurred() );
        issueEditedEvent.setTitle( command.getTitle() );
        issueEditedEvent.setDescription( command.getDescription() );
        issueEditedEvent.setFindEnv( command.getFindEnv() );
        issueEditedEvent.setFindStage( command.getFindStage() );
        issueEditedEvent.setPriority( command.getPriority() );
        issueEditedEvent.setRepetitionRate( command.getRepetitionRate() );
        issueEditedEvent.setRootCause( command.getRootCause() );
        issueEditedEvent.setTestMethod( command.getTestMethod() );
        issueEditedEvent.setType( command.getType() );
        issueEditedEvent.setDeveloper( command.getDeveloper() );
        issueEditedEvent.setTester( command.getTester() );
        issueEditedEvent.setRequirement( requirementToRequirement( command.getRequirement() ) );
        issueEditedEvent.setRequirementLevel( command.getRequirementLevel() );
        issueEditedEvent.setFindVersion( command.getFindVersion() );
        issueEditedEvent.setFixVersion( command.getFixVersion() );
        issueEditedEvent.setSprint( sprintToSprint( command.getSprint() ) );
        issueEditedEvent.setVersionConfirm( command.getVersionConfirm() );
        issueEditedEvent.setPlanStartDate( command.getPlanStartDate() );
        issueEditedEvent.setPlanEndDate( command.getPlanEndDate() );
        issueEditedEvent.setApplicationType( command.getApplicationType() );

        return issueEditedEvent;
    }

    protected Requirement requirementToRequirement(Requirement requirement) {
        if ( requirement == null ) {
            return null;
        }

        Requirement requirement1 = new Requirement();

        requirement1.setName( requirement.getName() );

        requirement1.setCode( (requirement!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(requirement.getCode())) ?requirement.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.REQUIREMENT_VALUE.getValue() );

        return requirement1;
    }

    protected Sprint sprintToSprint(Sprint sprint) {
        if ( sprint == null ) {
            return null;
        }

        Sprint sprint1 = new Sprint();

        sprint1.setName( sprint.getName() );

        sprint1.setCode( (sprint!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(sprint.getCode())) ?sprint.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.SPRINT_VALUE.getValue() );

        return sprint1;
    }
}
