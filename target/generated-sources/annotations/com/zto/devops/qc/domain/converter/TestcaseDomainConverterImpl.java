package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.HeartCaseFilterEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseStepEntityDO;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.AssociateTestcaseDomainCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.ChangePlanCaseExecutorCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.ChangeTestcaseDutyUserCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.ChangeTestcaseStatusCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.ChangeVersionCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.DragTestcaseModuleCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditTestcaseCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditTestcaseTitleCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.MoveTestcaseCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.ReleaseTestcaseRelationCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.XmindCaseAddCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.XmindCaseEditCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseModuleVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListXmindDetailVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.DeleteTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditTestcaseTitleEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.MoveModuleEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.PlanCaseExecutorChangedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseChangeVersionEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseDomainAssociatedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseDutyUserChangedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseModuleDraggedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseMovedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseRelationReleasedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseStatusChangedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagRemovedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.XmindCaseAddEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.XmindCaseEditEvent;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseExpQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseNewQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageTestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.XmindFilterQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListXmindDetailResp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseDomainConverterImpl implements TestcaseDomainConverter {

    @Override
    public TestcaseMovedEvent converter(MoveTestcaseCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseMovedEvent testcaseMovedEvent = new TestcaseMovedEvent();

        testcaseMovedEvent.setAggregateId( command.getAggregateId() );
        testcaseMovedEvent.setTransactor( command.getTransactor() );
        testcaseMovedEvent.setOccurred( command.getOccurred() );
        testcaseMovedEvent.setParentCode( command.getParentCode() );
        testcaseMovedEvent.setSetCore( command.getSetCore() );

        return testcaseMovedEvent;
    }

    @Override
    public TestcaseDomainAssociatedEvent convertor(AssociateTestcaseDomainCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseDomainAssociatedEvent testcaseDomainAssociatedEvent = new TestcaseDomainAssociatedEvent();

        testcaseDomainAssociatedEvent.setCode( command.getAggregateId() );
        testcaseDomainAssociatedEvent.setAggregateId( command.getAggregateId() );
        testcaseDomainAssociatedEvent.setTransactor( command.getTransactor() );
        testcaseDomainAssociatedEvent.setOccurred( command.getOccurred() );
        testcaseDomainAssociatedEvent.setDomain( command.getDomain() );
        List<String> list = command.getCodeList();
        if ( list != null ) {
            testcaseDomainAssociatedEvent.setCodeList( new ArrayList<String>( list ) );
        }

        return testcaseDomainAssociatedEvent;
    }

    @Override
    public AddTestcaseEvent convert(AddTestcaseCommand command) {
        if ( command == null ) {
            return null;
        }

        AddTestcaseEvent addTestcaseEvent = new AddTestcaseEvent();

        addTestcaseEvent.setCode( command.getAggregateId() );
        addTestcaseEvent.setAggregateId( command.getAggregateId() );
        addTestcaseEvent.setTransactor( command.getTransactor() );
        addTestcaseEvent.setOccurred( command.getOccurred() );
        addTestcaseEvent.setProductCode( command.getProductCode() );
        addTestcaseEvent.setVersionCode( command.getVersionCode() );
        addTestcaseEvent.setParentCode( command.getParentCode() );
        addTestcaseEvent.setName( command.getName() );
        addTestcaseEvent.setAttribute( command.getAttribute() );
        addTestcaseEvent.setType( command.getType() );
        addTestcaseEvent.setPriority( command.getPriority() );
        addTestcaseEvent.setStatus( command.getStatus() );
        addTestcaseEvent.setPrecondition( command.getPrecondition() );
        addTestcaseEvent.setDutyUserId( command.getDutyUserId() );
        addTestcaseEvent.setDutyUser( command.getDutyUser() );
        addTestcaseEvent.setComment( command.getComment() );
        addTestcaseEvent.setSort( command.getSort() );
        addTestcaseEvent.setLayer( command.getLayer() );
        addTestcaseEvent.setPath( command.getPath() );
        addTestcaseEvent.setNodeType( command.getNodeType() );
        List<AttachmentVO> list = command.getAttachments();
        if ( list != null ) {
            addTestcaseEvent.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<TagVO> list1 = command.getTags();
        if ( list1 != null ) {
            addTestcaseEvent.setTags( new ArrayList<TagVO>( list1 ) );
        }
        List<TestcaseRelationVO> list2 = command.getVos();
        if ( list2 != null ) {
            addTestcaseEvent.setVos( new ArrayList<TestcaseRelationVO>( list2 ) );
        }
        List<TestcaseStepVO> list3 = command.getTestSteps();
        if ( list3 != null ) {
            addTestcaseEvent.setTestSteps( new ArrayList<TestcaseStepVO>( list3 ) );
        }
        addTestcaseEvent.setAutomaticSourceCode( command.getAutomaticSourceCode() );
        addTestcaseEvent.setNodeTypePath( command.getNodeTypePath() );
        addTestcaseEvent.setFlag( command.getFlag() );
        addTestcaseEvent.setTestcaseModulePath( command.getTestcaseModulePath() );
        addTestcaseEvent.setSetCore( command.getSetCore() );
        addTestcaseEvent.setInterfaceName( command.getInterfaceName() );

        return addTestcaseEvent;
    }

    @Override
    public List<AddTestcaseEvent> convertList(List<AddTestcaseCommand> list) {
        if ( list == null ) {
            return null;
        }

        List<AddTestcaseEvent> list1 = new ArrayList<AddTestcaseEvent>( list.size() );
        for ( AddTestcaseCommand addTestcaseCommand : list ) {
            list1.add( convert( addTestcaseCommand ) );
        }

        return list1;
    }

    @Override
    public AddTestcaseCommand convertVO(TestcaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = vo.getCode();

        AddTestcaseCommand addTestcaseCommand = new AddTestcaseCommand( aggregateId );

        addTestcaseCommand.setProductCode( vo.getProductCode() );
        addTestcaseCommand.setParentCode( vo.getParentCode() );
        addTestcaseCommand.setName( vo.getName() );
        addTestcaseCommand.setAttribute( vo.getAttribute() );
        addTestcaseCommand.setType( vo.getType() );
        addTestcaseCommand.setPriority( vo.getPriority() );
        addTestcaseCommand.setStatus( vo.getStatus() );
        addTestcaseCommand.setPrecondition( vo.getPrecondition() );
        addTestcaseCommand.setDutyUserId( vo.getDutyUserId() );
        addTestcaseCommand.setDutyUser( vo.getDutyUser() );
        addTestcaseCommand.setComment( vo.getComment() );
        addTestcaseCommand.setSort( vo.getSort() );
        addTestcaseCommand.setLayer( vo.getLayer() );
        addTestcaseCommand.setPath( vo.getPath() );
        List<TagVO> list = vo.getTags();
        if ( list != null ) {
            addTestcaseCommand.setTags( new ArrayList<TagVO>( list ) );
        }
        List<TestcaseStepVO> list1 = vo.getTestSteps();
        if ( list1 != null ) {
            addTestcaseCommand.setTestSteps( new ArrayList<TestcaseStepVO>( list1 ) );
        }
        addTestcaseCommand.setAutomaticSourceCode( vo.getAutomaticSourceCode() );
        addTestcaseCommand.setNodeType( vo.getNodeType() );
        addTestcaseCommand.setNodeTypePath( vo.getNodeTypePath() );
        List<TmTestPlanVO> list2 = vo.getTestPlanList();
        if ( list2 != null ) {
            addTestcaseCommand.setTestPlanList( new ArrayList<TmTestPlanVO>( list2 ) );
        }
        addTestcaseCommand.setTestcaseModulePath( vo.getTestcaseModulePath() );
        addTestcaseCommand.setPlanName( vo.getPlanName() );
        addTestcaseCommand.setInterfaceName( vo.getInterfaceName() );
        addTestcaseCommand.setSetCore( vo.getSetCore() );
        addTestcaseCommand.setVersionCode( vo.getVersionCode() );

        return addTestcaseCommand;
    }

    @Override
    public TestcaseVO convertVO(AddTestcaseCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseVO testcaseVO = new TestcaseVO();

        testcaseVO.setCode( command.getAggregateId() );
        testcaseVO.setPriority( command.getPriority() );
        testcaseVO.setStatus( command.getStatus() );
        testcaseVO.setType( command.getType() );
        testcaseVO.setProductCode( command.getProductCode() );
        testcaseVO.setParentCode( command.getParentCode() );
        testcaseVO.setName( command.getName() );
        testcaseVO.setAttribute( command.getAttribute() );
        testcaseVO.setPrecondition( command.getPrecondition() );
        testcaseVO.setDutyUserId( command.getDutyUserId() );
        testcaseVO.setDutyUser( command.getDutyUser() );
        testcaseVO.setComment( command.getComment() );
        testcaseVO.setSort( command.getSort() );
        testcaseVO.setLayer( command.getLayer() );
        testcaseVO.setPath( command.getPath() );
        testcaseVO.setNodeType( command.getNodeType() );
        List<TestcaseStepVO> list = command.getTestSteps();
        if ( list != null ) {
            testcaseVO.setTestSteps( new ArrayList<TestcaseStepVO>( list ) );
        }
        List<TagVO> list1 = command.getTags();
        if ( list1 != null ) {
            testcaseVO.setTags( new ArrayList<TagVO>( list1 ) );
        }
        testcaseVO.setAutomaticSourceCode( command.getAutomaticSourceCode() );
        testcaseVO.setInterfaceName( command.getInterfaceName() );
        List<TmTestPlanVO> list2 = command.getTestPlanList();
        if ( list2 != null ) {
            testcaseVO.setTestPlanList( new ArrayList<TmTestPlanVO>( list2 ) );
        }
        testcaseVO.setNodeTypePath( command.getNodeTypePath() );
        testcaseVO.setPlanName( command.getPlanName() );
        testcaseVO.setTestcaseModulePath( command.getTestcaseModulePath() );
        testcaseVO.setSetCore( command.getSetCore() );
        testcaseVO.setVersionCode( command.getVersionCode() );

        return testcaseVO;
    }

    @Override
    public AddTestcaseCommand convertSame(AddTestcaseCommand command) {
        if ( command == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = command.getAggregateId();

        AddTestcaseCommand addTestcaseCommand = new AddTestcaseCommand( aggregateId );

        addTestcaseCommand.setTransactor( command.getTransactor() );
        addTestcaseCommand.setProductCode( command.getProductCode() );
        addTestcaseCommand.setParentCode( command.getParentCode() );
        addTestcaseCommand.setName( command.getName() );
        addTestcaseCommand.setAttribute( command.getAttribute() );
        addTestcaseCommand.setType( command.getType() );
        addTestcaseCommand.setPriority( command.getPriority() );
        addTestcaseCommand.setStatus( command.getStatus() );
        addTestcaseCommand.setPrecondition( command.getPrecondition() );
        addTestcaseCommand.setDutyUserId( command.getDutyUserId() );
        addTestcaseCommand.setDutyUser( command.getDutyUser() );
        addTestcaseCommand.setComment( command.getComment() );
        addTestcaseCommand.setSort( command.getSort() );
        addTestcaseCommand.setLayer( command.getLayer() );
        addTestcaseCommand.setPath( command.getPath() );
        List<AttachmentVO> list = command.getAttachments();
        if ( list != null ) {
            addTestcaseCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<TagVO> list1 = command.getTags();
        if ( list1 != null ) {
            addTestcaseCommand.setTags( new ArrayList<TagVO>( list1 ) );
        }
        List<TestcaseRelationVO> list2 = command.getVos();
        if ( list2 != null ) {
            addTestcaseCommand.setVos( new ArrayList<TestcaseRelationVO>( list2 ) );
        }
        List<TestcaseStepVO> list3 = command.getTestSteps();
        if ( list3 != null ) {
            addTestcaseCommand.setTestSteps( new ArrayList<TestcaseStepVO>( list3 ) );
        }
        addTestcaseCommand.setAutomaticSourceCode( command.getAutomaticSourceCode() );
        addTestcaseCommand.setNodeType( command.getNodeType() );
        addTestcaseCommand.setNodeTypePath( command.getNodeTypePath() );
        addTestcaseCommand.setIfUpdateStatus( command.getIfUpdateStatus() );
        List<TmTestPlanVO> list4 = command.getTestPlanList();
        if ( list4 != null ) {
            addTestcaseCommand.setTestPlanList( new ArrayList<TmTestPlanVO>( list4 ) );
        }
        addTestcaseCommand.setFlag( command.getFlag() );
        addTestcaseCommand.setTestcaseModulePath( command.getTestcaseModulePath() );
        addTestcaseCommand.setPlanName( command.getPlanName() );
        addTestcaseCommand.setInterfaceName( command.getInterfaceName() );
        addTestcaseCommand.setSetCore( command.getSetCore() );
        addTestcaseCommand.setVersionCode( command.getVersionCode() );

        return addTestcaseCommand;
    }

    @Override
    public TestcaseVO convertVO(TestcaseEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TestcaseVO testcaseVO = new TestcaseVO();

        testcaseVO.setPriority( entityDO.getPriority() );
        testcaseVO.setStatus( entityDO.getStatus() );
        testcaseVO.setType( entityDO.getType() );
        testcaseVO.setCode( entityDO.getCode() );
        testcaseVO.setProductCode( entityDO.getProductCode() );
        testcaseVO.setParentCode( entityDO.getParentCode() );
        testcaseVO.setName( entityDO.getName() );
        testcaseVO.setAttribute( entityDO.getAttribute() );
        testcaseVO.setPrecondition( entityDO.getPrecondition() );
        testcaseVO.setDutyUserId( entityDO.getDutyUserId() );
        testcaseVO.setDutyUser( entityDO.getDutyUser() );
        testcaseVO.setComment( entityDO.getComment() );
        testcaseVO.setSort( entityDO.getSort() );
        testcaseVO.setLayer( entityDO.getLayer() );
        testcaseVO.setPath( entityDO.getPath() );
        testcaseVO.setNodeType( entityDO.getNodeType() );
        testcaseVO.setCreatorId( entityDO.getCreatorId() );
        testcaseVO.setCreator( entityDO.getCreator() );
        testcaseVO.setGmtCreate( entityDO.getGmtCreate() );
        testcaseVO.setModifierId( entityDO.getModifierId() );
        testcaseVO.setModifier( entityDO.getModifier() );
        testcaseVO.setGmtModified( entityDO.getGmtModified() );
        testcaseVO.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        testcaseVO.setInterfaceName( entityDO.getInterfaceName() );
        testcaseVO.setNodeTypePath( entityDO.getNodeTypePath() );
        testcaseVO.setTestcaseModulePath( entityDO.getTestcaseModulePath() );
        testcaseVO.setSetHeart( entityDO.getSetHeart() );
        testcaseVO.setSetCore( entityDO.getSetCore() );
        testcaseVO.setVersionCode( entityDO.getVersionCode() );

        return testcaseVO;
    }

    @Override
    public EditTestcaseCommand convertEdit(AutomaticSourceLogTestcaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        String aggregateId = null;

        EditTestcaseCommand editTestcaseCommand = new EditTestcaseCommand( aggregateId );

        editTestcaseCommand.setEnable( vo.getEnable() );
        editTestcaseCommand.setProductCode( vo.getProductCode() );
        editTestcaseCommand.setParentCode( vo.getParentCode() );
        editTestcaseCommand.setName( vo.getName() );
        if ( vo.getAttribute() != null ) {
            editTestcaseCommand.setAttribute( Enum.valueOf( TestcaseAttributeEnum.class, vo.getAttribute() ) );
        }
        if ( vo.getType() != null ) {
            editTestcaseCommand.setType( Enum.valueOf( TestcaseTypeEnum.class, vo.getType() ) );
        }
        if ( vo.getPriority() != null ) {
            editTestcaseCommand.setPriority( Enum.valueOf( TestcasePriorityEnum.class, vo.getPriority() ) );
        }
        if ( vo.getStatus() != null ) {
            editTestcaseCommand.setStatus( Enum.valueOf( TestcaseStatusEnum.class, vo.getStatus() ) );
        }
        editTestcaseCommand.setPrecondition( vo.getPrecondition() );
        editTestcaseCommand.setDutyUserId( vo.getDutyUserId() );
        editTestcaseCommand.setDutyUser( vo.getDutyUser() );
        editTestcaseCommand.setComment( vo.getComment() );
        editTestcaseCommand.setSort( vo.getSort() );
        editTestcaseCommand.setLayer( vo.getLayer() );
        editTestcaseCommand.setPath( vo.getPath() );
        if ( vo.getNodeType() != null ) {
            editTestcaseCommand.setNodeType( Enum.valueOf( AutomaticNodeTypeEnum.class, vo.getNodeType() ) );
        }
        editTestcaseCommand.setNodeTypePath( vo.getNodeTypePath() );
        List<TmTestPlanVO> list = vo.getTestPlanList();
        if ( list != null ) {
            editTestcaseCommand.setTestPlanList( new ArrayList<TmTestPlanVO>( list ) );
        }
        editTestcaseCommand.setFlag( vo.getFlag() );
        editTestcaseCommand.setPlanName( vo.getPlanName() );
        editTestcaseCommand.setInterfaceName( vo.getInterfaceName() );

        return editTestcaseCommand;
    }

    @Override
    public EditTestcaseEvent convert(EditTestcaseCommand command) {
        if ( command == null ) {
            return null;
        }

        EditTestcaseEvent editTestcaseEvent = new EditTestcaseEvent();

        editTestcaseEvent.setCode( command.getAggregateId() );
        editTestcaseEvent.setAggregateId( command.getAggregateId() );
        editTestcaseEvent.setTransactor( command.getTransactor() );
        editTestcaseEvent.setOccurred( command.getOccurred() );
        editTestcaseEvent.setProductCode( command.getProductCode() );
        editTestcaseEvent.setParentCode( command.getParentCode() );
        editTestcaseEvent.setName( command.getName() );
        editTestcaseEvent.setAttribute( command.getAttribute() );
        editTestcaseEvent.setType( command.getType() );
        editTestcaseEvent.setPriority( command.getPriority() );
        editTestcaseEvent.setStatus( command.getStatus() );
        editTestcaseEvent.setPrecondition( command.getPrecondition() );
        editTestcaseEvent.setDutyUserId( command.getDutyUserId() );
        editTestcaseEvent.setDutyUser( command.getDutyUser() );
        editTestcaseEvent.setComment( command.getComment() );
        editTestcaseEvent.setSort( command.getSort() );
        editTestcaseEvent.setLayer( command.getLayer() );
        editTestcaseEvent.setPath( command.getPath() );
        List<AttachmentVO> list = command.getAttachments();
        if ( list != null ) {
            editTestcaseEvent.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<TagVO> list1 = command.getTags();
        if ( list1 != null ) {
            editTestcaseEvent.setTags( new ArrayList<TagVO>( list1 ) );
        }
        List<TestcaseRelationVO> list2 = command.getVos();
        if ( list2 != null ) {
            editTestcaseEvent.setVos( new ArrayList<TestcaseRelationVO>( list2 ) );
        }
        List<TestcaseStepVO> list3 = command.getTestSteps();
        if ( list3 != null ) {
            editTestcaseEvent.setTestSteps( new ArrayList<TestcaseStepVO>( list3 ) );
        }
        editTestcaseEvent.setEnable( command.getEnable() );
        editTestcaseEvent.setIfUpdateStatus( command.getIfUpdateStatus() );

        return editTestcaseEvent;
    }

    @Override
    public EditTestcaseCommand convert(TestcaseEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        String aggregateId = null;

        EditTestcaseCommand editTestcaseCommand = new EditTestcaseCommand( aggregateId );

        editTestcaseCommand.setEnable( entityDO.getEnable() );
        editTestcaseCommand.setProductCode( entityDO.getProductCode() );
        editTestcaseCommand.setParentCode( entityDO.getParentCode() );
        editTestcaseCommand.setName( entityDO.getName() );
        editTestcaseCommand.setAttribute( entityDO.getAttribute() );
        editTestcaseCommand.setType( entityDO.getType() );
        editTestcaseCommand.setPriority( entityDO.getPriority() );
        editTestcaseCommand.setStatus( entityDO.getStatus() );
        editTestcaseCommand.setPrecondition( entityDO.getPrecondition() );
        editTestcaseCommand.setDutyUserId( entityDO.getDutyUserId() );
        editTestcaseCommand.setDutyUser( entityDO.getDutyUser() );
        editTestcaseCommand.setComment( entityDO.getComment() );
        editTestcaseCommand.setSort( entityDO.getSort() );
        editTestcaseCommand.setLayer( entityDO.getLayer() );
        editTestcaseCommand.setPath( entityDO.getPath() );
        editTestcaseCommand.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        editTestcaseCommand.setNodeType( entityDO.getNodeType() );
        editTestcaseCommand.setNodeTypePath( entityDO.getNodeTypePath() );
        editTestcaseCommand.setTestcaseModulePath( entityDO.getTestcaseModulePath() );
        editTestcaseCommand.setInterfaceName( entityDO.getInterfaceName() );
        editTestcaseCommand.setVersionCode( entityDO.getVersionCode() );

        return editTestcaseCommand;
    }

    @Override
    public AddTestcaseCommand convertAdd(AutomaticSourceLogTestcaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        String aggregateId = null;

        AddTestcaseCommand addTestcaseCommand = new AddTestcaseCommand( aggregateId );

        addTestcaseCommand.setProductCode( vo.getProductCode() );
        addTestcaseCommand.setParentCode( vo.getParentCode() );
        addTestcaseCommand.setName( vo.getName() );
        if ( vo.getAttribute() != null ) {
            addTestcaseCommand.setAttribute( Enum.valueOf( TestcaseAttributeEnum.class, vo.getAttribute() ) );
        }
        if ( vo.getType() != null ) {
            addTestcaseCommand.setType( Enum.valueOf( TestcaseTypeEnum.class, vo.getType() ) );
        }
        if ( vo.getPriority() != null ) {
            addTestcaseCommand.setPriority( Enum.valueOf( TestcasePriorityEnum.class, vo.getPriority() ) );
        }
        if ( vo.getStatus() != null ) {
            addTestcaseCommand.setStatus( Enum.valueOf( TestcaseStatusEnum.class, vo.getStatus() ) );
        }
        addTestcaseCommand.setPrecondition( vo.getPrecondition() );
        addTestcaseCommand.setDutyUserId( vo.getDutyUserId() );
        addTestcaseCommand.setDutyUser( vo.getDutyUser() );
        addTestcaseCommand.setComment( vo.getComment() );
        addTestcaseCommand.setSort( vo.getSort() );
        addTestcaseCommand.setLayer( vo.getLayer() );
        addTestcaseCommand.setPath( vo.getPath() );
        if ( vo.getNodeType() != null ) {
            addTestcaseCommand.setNodeType( Enum.valueOf( AutomaticNodeTypeEnum.class, vo.getNodeType() ) );
        }
        addTestcaseCommand.setNodeTypePath( vo.getNodeTypePath() );
        List<TmTestPlanVO> list = vo.getTestPlanList();
        if ( list != null ) {
            addTestcaseCommand.setTestPlanList( new ArrayList<TmTestPlanVO>( list ) );
        }
        addTestcaseCommand.setFlag( vo.getFlag() );
        addTestcaseCommand.setPlanName( vo.getPlanName() );
        addTestcaseCommand.setInterfaceName( vo.getInterfaceName() );

        return addTestcaseCommand;
    }

    @Override
    public TestcaseModuleDraggedEvent converter(DragTestcaseModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseModuleDraggedEvent testcaseModuleDraggedEvent = new TestcaseModuleDraggedEvent();

        testcaseModuleDraggedEvent.setCode( command.getAggregateId() );
        testcaseModuleDraggedEvent.setAggregateId( command.getAggregateId() );
        testcaseModuleDraggedEvent.setTransactor( command.getTransactor() );
        testcaseModuleDraggedEvent.setOccurred( command.getOccurred() );
        testcaseModuleDraggedEvent.setParentCode( command.getParentCode() );
        testcaseModuleDraggedEvent.setTargetCode( command.getTargetCode() );
        testcaseModuleDraggedEvent.setAction( command.getAction() );
        testcaseModuleDraggedEvent.setType( command.getType() );

        return testcaseModuleDraggedEvent;
    }

    @Override
    public ListTestcaseNewQuery convertor(ListTestcaseExpQuery query) {
        if ( query == null ) {
            return null;
        }

        ListTestcaseNewQuery listTestcaseNewQuery = new ListTestcaseNewQuery();

        listTestcaseNewQuery.setTransactor( query.getTransactor() );
        listTestcaseNewQuery.setPage( query.getPage() );
        listTestcaseNewQuery.setSize( query.getSize() );
        List<String> list = query.getExcludeFields();
        if ( list != null ) {
            listTestcaseNewQuery.setExcludeFields( new ArrayList<String>( list ) );
        }
        listTestcaseNewQuery.setGroupType( query.getGroupType() );
        listTestcaseNewQuery.setProductCode( query.getProductCode() );
        listTestcaseNewQuery.setType( query.getType() );
        listTestcaseNewQuery.setParentCode( query.getParentCode() );
        listTestcaseNewQuery.setCodeOrTitle( query.getCodeOrTitle() );
        List<TestcasePriorityEnum> list1 = query.getPriorityList();
        if ( list1 != null ) {
            listTestcaseNewQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list1 ) );
        }
        List<TestcaseStatusEnum> list2 = query.getStatusList();
        if ( list2 != null ) {
            listTestcaseNewQuery.setStatusList( new ArrayList<TestcaseStatusEnum>( list2 ) );
        }
        List<String> list3 = query.getTagList();
        if ( list3 != null ) {
            listTestcaseNewQuery.setTagList( new ArrayList<String>( list3 ) );
        }
        List<Long> list4 = query.getDutyUserList();
        if ( list4 != null ) {
            listTestcaseNewQuery.setDutyUserList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = query.getCreatorList();
        if ( list5 != null ) {
            listTestcaseNewQuery.setCreatorList( new ArrayList<Long>( list5 ) );
        }
        listTestcaseNewQuery.setCreateTimeBegin( query.getCreateTimeBegin() );
        listTestcaseNewQuery.setCreateTimeEnd( query.getCreateTimeEnd() );
        listTestcaseNewQuery.setModifyTimeBegin( query.getModifyTimeBegin() );
        listTestcaseNewQuery.setModifyTimeEnd( query.getModifyTimeEnd() );
        listTestcaseNewQuery.setTestStage( query.getTestStage() );
        listTestcaseNewQuery.setPlanCode( query.getPlanCode() );
        listTestcaseNewQuery.setPlanPattern( query.getPlanPattern() );
        listTestcaseNewQuery.setAutomaticSourceCode( query.getAutomaticSourceCode() );
        List<AutomaticNodeTypeEnum> list6 = query.getNodeTypeList();
        if ( list6 != null ) {
            listTestcaseNewQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list6 ) );
        }
        listTestcaseNewQuery.setTestcaseAttribute( query.getTestcaseAttribute() );
        listTestcaseNewQuery.setPath( query.getPath() );
        listTestcaseNewQuery.setSetCore( query.getSetCore() );
        listTestcaseNewQuery.setVersionCode( query.getVersionCode() );

        return listTestcaseNewQuery;
    }

    @Override
    public void converter(TestcaseEntityDO entity, ListTestcaseModuleVO vo) {
        if ( entity == null ) {
            return;
        }

        vo.setCode( entity.getCode() );
        vo.setName( entity.getName() );
        vo.setProductCode( entity.getProductCode() );
        vo.setVersionCode( entity.getVersionCode() );
        vo.setParentCode( entity.getParentCode() );
        vo.setType( entity.getType() );
        vo.setStatus( entity.getStatus() );
        vo.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        vo.setNodeType( entity.getNodeType() );
        vo.setSort( entity.getSort() );
        vo.setPath( entity.getPath() );
        vo.setLayer( entity.getLayer() );
        vo.setAttribute( entity.getAttribute() );
        vo.setComment( entity.getComment() );
        vo.setSetCore( entity.getSetCore() );
        vo.setId( entity.getId() );
        vo.setGmtCreate( entity.getGmtCreate() );
    }

    @Override
    public void converter(TestcaseEntityDO entity, ListTestcaseVO vo) {
        if ( entity == null ) {
            return;
        }

        vo.setPriority( entity.getPriority() );
        vo.setStatus( entity.getStatus() );
        vo.setType( entity.getType() );
        vo.setCode( entity.getCode() );
        vo.setProductCode( entity.getProductCode() );
        vo.setParentCode( entity.getParentCode() );
        vo.setName( entity.getName() );
        vo.setAttribute( entity.getAttribute() );
        vo.setPrecondition( entity.getPrecondition() );
        vo.setDutyUserId( entity.getDutyUserId() );
        vo.setDutyUser( entity.getDutyUser() );
        vo.setComment( entity.getComment() );
        vo.setSort( entity.getSort() );
        vo.setLayer( entity.getLayer() );
        vo.setPath( entity.getPath() );
        vo.setNodeType( entity.getNodeType() );
        vo.setCreatorId( entity.getCreatorId() );
        vo.setCreator( entity.getCreator() );
        vo.setGmtCreate( entity.getGmtCreate() );
        vo.setModifierId( entity.getModifierId() );
        vo.setModifier( entity.getModifier() );
        vo.setGmtModified( entity.getGmtModified() );
        vo.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        vo.setInterfaceName( entity.getInterfaceName() );
        vo.setNodeTypePath( entity.getNodeTypePath() );
        vo.setTestcaseModulePath( entity.getTestcaseModulePath() );
        vo.setSetHeart( entity.getSetHeart() );
        vo.setSetCore( entity.getSetCore() );
        vo.setVersionCode( entity.getVersionCode() );
        if ( entity.getId() != null ) {
            vo.setId( entity.getId() );
        }
        vo.setExecutionStatus( entity.getExecutionStatus() );
        vo.setExecutionStatusDesc( entity.getExecutionStatusDesc() );
    }

    @Override
    public XmindCaseEditEvent convert(XmindCaseEditCommand command) {
        if ( command == null ) {
            return null;
        }

        XmindCaseEditEvent xmindCaseEditEvent = new XmindCaseEditEvent();

        xmindCaseEditEvent.setAggregateId( command.getAggregateId() );
        xmindCaseEditEvent.setTransactor( command.getTransactor() );
        xmindCaseEditEvent.setOccurred( command.getOccurred() );
        xmindCaseEditEvent.setId( command.getId() );
        xmindCaseEditEvent.setTopic( command.getTopic() );
        xmindCaseEditEvent.setParentCode( command.getParentCode() );
        xmindCaseEditEvent.setTag( command.getTag() );
        xmindCaseEditEvent.setOperation( command.getOperation() );

        return xmindCaseEditEvent;
    }

    @Override
    public XmindCaseAddEvent convert(XmindCaseAddCommand command) {
        if ( command == null ) {
            return null;
        }

        XmindCaseAddEvent xmindCaseAddEvent = new XmindCaseAddEvent();

        xmindCaseAddEvent.setAggregateId( command.getAggregateId() );
        xmindCaseAddEvent.setTransactor( command.getTransactor() );
        xmindCaseAddEvent.setOccurred( command.getOccurred() );
        xmindCaseAddEvent.setAttribute( command.getAttribute() );
        xmindCaseAddEvent.setTopic( command.getTopic() );
        xmindCaseAddEvent.setLayer( command.getLayer() );
        xmindCaseAddEvent.setParentCode( command.getParentCode() );
        xmindCaseAddEvent.setProductCode( command.getProductCode() );
        xmindCaseAddEvent.setType( command.getType() );
        xmindCaseAddEvent.setPriority( command.getPriority() );

        return xmindCaseAddEvent;
    }

    @Override
    public List<ListXmindDetailVO> convertToList(List<ListTestcaseVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ListXmindDetailVO> list1 = new ArrayList<ListXmindDetailVO>( list.size() );
        for ( ListTestcaseVO listTestcaseVO : list ) {
            list1.add( converter( listTestcaseVO ) );
        }

        return list1;
    }

    @Override
    public ListXmindDetailVO converter(ListTestcaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        ListXmindDetailVO listXmindDetailVO = new ListXmindDetailVO();

        listXmindDetailVO.setIncId( vo.getId() );
        listXmindDetailVO.setId( vo.getCode() );
        listXmindDetailVO.setTopic( vo.getName() );
        listXmindDetailVO.setTestcaseStatus( vo.getStatus() );
        listXmindDetailVO.setVersionCode( vo.getVersionCode() );
        listXmindDetailVO.setParentCode( vo.getParentCode() );
        listXmindDetailVO.setTestcaseCount( vo.getTestcaseCount() );
        listXmindDetailVO.setChildren( convertToList( vo.getChildren() ) );
        listXmindDetailVO.setAttribute( vo.getAttribute() );
        listXmindDetailVO.setPriority( vo.getPriority() );
        listXmindDetailVO.setPriorityDesc( vo.getPriorityDesc() );
        listXmindDetailVO.setCreatorId( vo.getCreatorId() );
        listXmindDetailVO.setCreator( vo.getCreator() );
        listXmindDetailVO.setDutyUserId( vo.getDutyUserId() );
        listXmindDetailVO.setDutyUser( vo.getDutyUser() );
        listXmindDetailVO.setPath( vo.getPath() );
        listXmindDetailVO.setExecutionStatus( vo.getExecutionStatus() );
        listXmindDetailVO.setExecutionStatusDesc( vo.getExecutionStatusDesc() );

        return listXmindDetailVO;
    }

    @Override
    public TestcaseEntityDO convertorDeleteToEntity(DeleteTestcaseEvent event) {
        if ( event == null ) {
            return null;
        }

        TestcaseEntityDO testcaseEntityDO = new TestcaseEntityDO();

        testcaseEntityDO.setModifierId( eventTransactorUserId( event ) );
        testcaseEntityDO.setModifier( eventTransactorUserName( event ) );
        testcaseEntityDO.setGmtModified( event.getOccurred() );
        testcaseEntityDO.setCode( event.getCode() );
        testcaseEntityDO.setAttribute( event.getAttribute() );
        testcaseEntityDO.setType( event.getType() );

        return testcaseEntityDO;
    }

    @Override
    public MoveModuleEvent converter(XmindCaseEditCommand command) {
        if ( command == null ) {
            return null;
        }

        MoveModuleEvent moveModuleEvent = new MoveModuleEvent();

        moveModuleEvent.setCode( command.getId() );
        moveModuleEvent.setName( command.getTopic() );
        moveModuleEvent.setAggregateId( command.getAggregateId() );
        moveModuleEvent.setTransactor( command.getTransactor() );
        moveModuleEvent.setOccurred( command.getOccurred() );
        moveModuleEvent.setProductCode( command.getProductCode() );
        moveModuleEvent.setParentCode( command.getParentCode() );
        moveModuleEvent.setOldPath( command.getOldPath() );
        moveModuleEvent.setNewPath( command.getNewPath() );
        moveModuleEvent.setAttribute( command.getAttribute() );

        return moveModuleEvent;
    }

    @Override
    public PageTestcaseQuery convert(XmindFilterQuery filterQuery) {
        if ( filterQuery == null ) {
            return null;
        }

        PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();

        pageTestcaseQuery.setTransactor( filterQuery.getTransactor() );
        pageTestcaseQuery.setProductCode( filterQuery.getProductCode() );
        pageTestcaseQuery.setType( filterQuery.getType() );
        pageTestcaseQuery.setParentCode( filterQuery.getParentCode() );
        pageTestcaseQuery.setCodeOrTitle( filterQuery.getCodeOrTitle() );
        List<TestcasePriorityEnum> list = filterQuery.getPriorityList();
        if ( list != null ) {
            pageTestcaseQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
        }
        List<TestcaseStatusEnum> list1 = filterQuery.getStatusList();
        if ( list1 != null ) {
            pageTestcaseQuery.setStatusList( new ArrayList<TestcaseStatusEnum>( list1 ) );
        }
        List<String> list2 = filterQuery.getTagList();
        if ( list2 != null ) {
            pageTestcaseQuery.setTagList( new ArrayList<String>( list2 ) );
        }
        List<Long> list3 = filterQuery.getDutyUserList();
        if ( list3 != null ) {
            pageTestcaseQuery.setDutyUserList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = filterQuery.getCreatorList();
        if ( list4 != null ) {
            pageTestcaseQuery.setCreatorList( new ArrayList<Long>( list4 ) );
        }
        pageTestcaseQuery.setCreateTimeBegin( filterQuery.getCreateTimeBegin() );
        pageTestcaseQuery.setCreateTimeEnd( filterQuery.getCreateTimeEnd() );
        pageTestcaseQuery.setModifyTimeBegin( filterQuery.getModifyTimeBegin() );
        pageTestcaseQuery.setModifyTimeEnd( filterQuery.getModifyTimeEnd() );
        pageTestcaseQuery.setTestStage( filterQuery.getTestStage() );
        pageTestcaseQuery.setPlanCode( filterQuery.getPlanCode() );
        pageTestcaseQuery.setPlanPattern( filterQuery.getPlanPattern() );
        List<AutomaticNodeTypeEnum> list5 = filterQuery.getNodeTypeList();
        if ( list5 != null ) {
            pageTestcaseQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
        }
        List<HeartCaseFilterEnum> list6 = filterQuery.getIsHeart();
        if ( list6 != null ) {
            pageTestcaseQuery.setIsHeart( new ArrayList<HeartCaseFilterEnum>( list6 ) );
        }
        List<AutomaticRecordTypeEnum> list7 = filterQuery.getAutomaticTypeList();
        if ( list7 != null ) {
            pageTestcaseQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list7 ) );
        }
        pageTestcaseQuery.setSetCore( filterQuery.getSetCore() );
        pageTestcaseQuery.setVersionCode( filterQuery.getVersionCode() );

        return pageTestcaseQuery;
    }

    @Override
    public TestcaseVO converter(TestcaseEntityDO entity) {
        if ( entity == null ) {
            return null;
        }

        TestcaseVO testcaseVO = new TestcaseVO();

        testcaseVO.setPriority( entity.getPriority() );
        testcaseVO.setStatus( entity.getStatus() );
        testcaseVO.setType( entity.getType() );
        testcaseVO.setCode( entity.getCode() );
        testcaseVO.setProductCode( entity.getProductCode() );
        testcaseVO.setParentCode( entity.getParentCode() );
        testcaseVO.setName( entity.getName() );
        testcaseVO.setAttribute( entity.getAttribute() );
        testcaseVO.setPrecondition( entity.getPrecondition() );
        testcaseVO.setDutyUserId( entity.getDutyUserId() );
        testcaseVO.setDutyUser( entity.getDutyUser() );
        testcaseVO.setComment( entity.getComment() );
        testcaseVO.setSort( entity.getSort() );
        testcaseVO.setLayer( entity.getLayer() );
        testcaseVO.setPath( entity.getPath() );
        testcaseVO.setNodeType( entity.getNodeType() );
        testcaseVO.setCreatorId( entity.getCreatorId() );
        testcaseVO.setCreator( entity.getCreator() );
        testcaseVO.setGmtCreate( entity.getGmtCreate() );
        testcaseVO.setModifierId( entity.getModifierId() );
        testcaseVO.setModifier( entity.getModifier() );
        testcaseVO.setGmtModified( entity.getGmtModified() );
        testcaseVO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        testcaseVO.setInterfaceName( entity.getInterfaceName() );
        testcaseVO.setNodeTypePath( entity.getNodeTypePath() );
        testcaseVO.setTestcaseModulePath( entity.getTestcaseModulePath() );
        testcaseVO.setSetHeart( entity.getSetHeart() );
        testcaseVO.setSetCore( entity.getSetCore() );
        testcaseVO.setVersionCode( entity.getVersionCode() );

        return testcaseVO;
    }

    @Override
    public TestcaseStatusChangedEvent converter(ChangeTestcaseStatusCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseStatusChangedEvent testcaseStatusChangedEvent = new TestcaseStatusChangedEvent();

        testcaseStatusChangedEvent.setCode( command.getAggregateId() );
        testcaseStatusChangedEvent.setAggregateId( command.getAggregateId() );
        testcaseStatusChangedEvent.setTransactor( command.getTransactor() );
        testcaseStatusChangedEvent.setOccurred( command.getOccurred() );
        testcaseStatusChangedEvent.setStatus( command.getStatus() );
        testcaseStatusChangedEvent.setAbandonReason( command.getAbandonReason() );

        return testcaseStatusChangedEvent;
    }

    @Override
    public PlanCaseExecutorChangedEvent convertor(ChangePlanCaseExecutorCommand command) {
        if ( command == null ) {
            return null;
        }

        PlanCaseExecutorChangedEvent planCaseExecutorChangedEvent = new PlanCaseExecutorChangedEvent();

        planCaseExecutorChangedEvent.setCaseCode( command.getAggregateId() );
        planCaseExecutorChangedEvent.setAggregateId( command.getAggregateId() );
        planCaseExecutorChangedEvent.setTransactor( command.getTransactor() );
        planCaseExecutorChangedEvent.setOccurred( command.getOccurred() );
        planCaseExecutorChangedEvent.setPlanCode( command.getPlanCode() );
        planCaseExecutorChangedEvent.setTestStage( command.getTestStage() );
        planCaseExecutorChangedEvent.setExecutorId( command.getExecutorId() );
        planCaseExecutorChangedEvent.setExecutor( command.getExecutor() );
        planCaseExecutorChangedEvent.setOperateCaseCode( command.getOperateCaseCode() );

        return planCaseExecutorChangedEvent;
    }

    @Override
    public TestcaseDutyUserChangedEvent convertor(ChangeTestcaseDutyUserCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseDutyUserChangedEvent testcaseDutyUserChangedEvent = new TestcaseDutyUserChangedEvent();

        testcaseDutyUserChangedEvent.setCode( command.getAggregateId() );
        testcaseDutyUserChangedEvent.setAggregateId( command.getAggregateId() );
        testcaseDutyUserChangedEvent.setTransactor( command.getTransactor() );
        testcaseDutyUserChangedEvent.setOccurred( command.getOccurred() );
        testcaseDutyUserChangedEvent.setDutyUser( command.getDutyUser() );
        testcaseDutyUserChangedEvent.setDutyUserId( command.getDutyUserId() );

        return testcaseDutyUserChangedEvent;
    }

    @Override
    public TestcaseChangeVersionEvent converter(ChangeVersionCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseChangeVersionEvent testcaseChangeVersionEvent = new TestcaseChangeVersionEvent();

        testcaseChangeVersionEvent.setCode( command.getAggregateId() );
        testcaseChangeVersionEvent.setAggregateId( command.getAggregateId() );
        testcaseChangeVersionEvent.setTransactor( command.getTransactor() );
        testcaseChangeVersionEvent.setOccurred( command.getOccurred() );
        testcaseChangeVersionEvent.setTargetVersionCode( command.getTargetVersionCode() );
        testcaseChangeVersionEvent.setTargetParentCode( command.getTargetParentCode() );
        testcaseChangeVersionEvent.setUseOriginalGroup( command.getUseOriginalGroup() );

        return testcaseChangeVersionEvent;
    }

    @Override
    public TestcaseTagRemovedEvent converter(RemoveTestcaseTagCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseTagRemovedEvent testcaseTagRemovedEvent = new TestcaseTagRemovedEvent();

        testcaseTagRemovedEvent.setCode( command.getAggregateId() );
        testcaseTagRemovedEvent.setAggregateId( command.getAggregateId() );
        testcaseTagRemovedEvent.setTransactor( command.getTransactor() );
        testcaseTagRemovedEvent.setOccurred( command.getOccurred() );

        return testcaseTagRemovedEvent;
    }

    @Override
    public TestcaseTagAddedEvent converter(AddTestcaseTagCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseTagAddedEvent testcaseTagAddedEvent = new TestcaseTagAddedEvent();

        testcaseTagAddedEvent.setCode( command.getAggregateId() );
        testcaseTagAddedEvent.setAggregateId( command.getAggregateId() );
        testcaseTagAddedEvent.setTransactor( command.getTransactor() );
        testcaseTagAddedEvent.setOccurred( command.getOccurred() );
        testcaseTagAddedEvent.setDomain( command.getDomain() );
        testcaseTagAddedEvent.setBusinessCode( command.getBusinessCode() );
        testcaseTagAddedEvent.setType( command.getType() );
        testcaseTagAddedEvent.setTagName( command.getTagName() );

        return testcaseTagAddedEvent;
    }

    @Override
    public TestcaseEntityDO convertor(AddTestcaseEvent event) {
        if ( event == null ) {
            return null;
        }

        TestcaseEntityDO testcaseEntityDO = new TestcaseEntityDO();

        testcaseEntityDO.setCreatorId( eventTransactorUserId1( event ) );
        testcaseEntityDO.setCreator( eventTransactorUserName1( event ) );
        testcaseEntityDO.setGmtCreate( event.getOccurred() );
        testcaseEntityDO.setModifierId( eventTransactorUserId1( event ) );
        testcaseEntityDO.setModifier( eventTransactorUserName1( event ) );
        testcaseEntityDO.setGmtModified( event.getOccurred() );
        testcaseEntityDO.setCode( event.getCode() );
        testcaseEntityDO.setProductCode( event.getProductCode() );
        testcaseEntityDO.setVersionCode( event.getVersionCode() );
        testcaseEntityDO.setParentCode( event.getParentCode() );
        testcaseEntityDO.setName( event.getName() );
        testcaseEntityDO.setAttribute( event.getAttribute() );
        testcaseEntityDO.setType( event.getType() );
        testcaseEntityDO.setPriority( event.getPriority() );
        testcaseEntityDO.setStatus( event.getStatus() );
        testcaseEntityDO.setPrecondition( event.getPrecondition() );
        testcaseEntityDO.setDutyUserId( event.getDutyUserId() );
        testcaseEntityDO.setDutyUser( event.getDutyUser() );
        testcaseEntityDO.setComment( event.getComment() );
        testcaseEntityDO.setAutomaticSourceCode( event.getAutomaticSourceCode() );
        testcaseEntityDO.setNodeType( event.getNodeType() );
        testcaseEntityDO.setSort( event.getSort() );
        testcaseEntityDO.setLayer( event.getLayer() );
        testcaseEntityDO.setPath( event.getPath() );
        testcaseEntityDO.setNodeTypePath( event.getNodeTypePath() );
        testcaseEntityDO.setTestcaseModulePath( event.getTestcaseModulePath() );
        testcaseEntityDO.setInterfaceName( event.getInterfaceName() );
        testcaseEntityDO.setSetCore( event.getSetCore() );

        return testcaseEntityDO;
    }

    @Override
    public List<TagEntityDO> convert(List<TagVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<TagEntityDO> list = new ArrayList<TagEntityDO>( vo.size() );
        for ( TagVO tagVO : vo ) {
            list.add( tagVOToTagEntityDO( tagVO ) );
        }

        return list;
    }

    @Override
    public List<TestcaseStepEntityDO> convertToStepList(List<TestcaseStepVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<TestcaseStepEntityDO> list = new ArrayList<TestcaseStepEntityDO>( vo.size() );
        for ( TestcaseStepVO testcaseStepVO : vo ) {
            list.add( testcaseStepVOToTestcaseStepEntityDO( testcaseStepVO ) );
        }

        return list;
    }

    @Override
    public EditTestcaseTitleEvent convertor(EditTestcaseTitleCommand command) {
        if ( command == null ) {
            return null;
        }

        EditTestcaseTitleEvent editTestcaseTitleEvent = new EditTestcaseTitleEvent();

        editTestcaseTitleEvent.setAggregateId( command.getAggregateId() );
        editTestcaseTitleEvent.setTransactor( command.getTransactor() );
        editTestcaseTitleEvent.setOccurred( command.getOccurred() );
        editTestcaseTitleEvent.setCode( command.getCode() );
        editTestcaseTitleEvent.setName( command.getName() );
        editTestcaseTitleEvent.setPriority( command.getPriority() );
        editTestcaseTitleEvent.setVersionCode( command.getVersionCode() );

        return editTestcaseTitleEvent;
    }

    @Override
    public TestcaseRelationReleasedEvent convertor(ReleaseTestcaseRelationCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseRelationReleasedEvent testcaseRelationReleasedEvent = new TestcaseRelationReleasedEvent();

        testcaseRelationReleasedEvent.setCode( command.getAggregateId() );
        testcaseRelationReleasedEvent.setAggregateId( command.getAggregateId() );
        testcaseRelationReleasedEvent.setTransactor( command.getTransactor() );
        testcaseRelationReleasedEvent.setOccurred( command.getOccurred() );
        testcaseRelationReleasedEvent.setBusinessCode( command.getBusinessCode() );
        testcaseRelationReleasedEvent.setDomain( command.getDomain() );

        return testcaseRelationReleasedEvent;
    }

    @Override
    public List<ListXmindDetailResp> convertXmindList(List<ListXmindDetailVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ListXmindDetailResp> list1 = new ArrayList<ListXmindDetailResp>( list.size() );
        for ( ListXmindDetailVO listXmindDetailVO : list ) {
            list1.add( listXmindDetailVOToListXmindDetailResp( listXmindDetailVO ) );
        }

        return list1;
    }

    private Long eventTransactorUserId(DeleteTestcaseEvent deleteTestcaseEvent) {
        if ( deleteTestcaseEvent == null ) {
            return null;
        }
        User transactor = deleteTestcaseEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(DeleteTestcaseEvent deleteTestcaseEvent) {
        if ( deleteTestcaseEvent == null ) {
            return null;
        }
        User transactor = deleteTestcaseEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId1(AddTestcaseEvent addTestcaseEvent) {
        if ( addTestcaseEvent == null ) {
            return null;
        }
        User transactor = addTestcaseEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName1(AddTestcaseEvent addTestcaseEvent) {
        if ( addTestcaseEvent == null ) {
            return null;
        }
        User transactor = addTestcaseEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    protected TagEntityDO tagVOToTagEntityDO(TagVO tagVO) {
        if ( tagVO == null ) {
            return null;
        }

        TagEntityDO tagEntityDO = new TagEntityDO();

        tagEntityDO.setEnable( tagVO.getEnable() );
        tagEntityDO.setCreatorId( tagVO.getCreatorId() );
        tagEntityDO.setCode( tagVO.getCode() );
        tagEntityDO.setDomain( tagVO.getDomain() );
        tagEntityDO.setBusinessCode( tagVO.getBusinessCode() );
        tagEntityDO.setType( tagVO.getType() );
        tagEntityDO.setTagAlias( tagVO.getTagAlias() );
        tagEntityDO.setTagCode( tagVO.getTagCode() );
        tagEntityDO.setTagName( tagVO.getTagName() );

        return tagEntityDO;
    }

    protected TestcaseStepEntityDO testcaseStepVOToTestcaseStepEntityDO(TestcaseStepVO testcaseStepVO) {
        if ( testcaseStepVO == null ) {
            return null;
        }

        TestcaseStepEntityDO testcaseStepEntityDO = new TestcaseStepEntityDO();

        testcaseStepEntityDO.setTestcaseCode( testcaseStepVO.getTestcaseCode() );
        testcaseStepEntityDO.setStepDesc( testcaseStepVO.getStepDesc() );
        testcaseStepEntityDO.setExpectResult( testcaseStepVO.getExpectResult() );
        testcaseStepEntityDO.setSort( testcaseStepVO.getSort() );

        return testcaseStepEntityDO;
    }

    protected ListXmindDetailResp listXmindDetailVOToListXmindDetailResp(ListXmindDetailVO listXmindDetailVO) {
        if ( listXmindDetailVO == null ) {
            return null;
        }

        ListXmindDetailResp listXmindDetailResp = new ListXmindDetailResp();

        listXmindDetailResp.setId( listXmindDetailVO.getId() );
        listXmindDetailResp.setTopic( listXmindDetailVO.getTopic() );
        listXmindDetailResp.setParentCode( listXmindDetailVO.getParentCode() );
        listXmindDetailResp.setDirection( listXmindDetailVO.getDirection() );
        listXmindDetailResp.setDisabled( listXmindDetailVO.getDisabled() );
        listXmindDetailResp.setExpanded( listXmindDetailVO.getExpanded() );
        listXmindDetailResp.setTagName( listXmindDetailVO.getTagName() );
        listXmindDetailResp.setTagValue( listXmindDetailVO.getTagValue() );
        listXmindDetailResp.setTagEdit( listXmindDetailVO.getTagEdit() );
        List<ListXmindDetailVO> list = listXmindDetailVO.getChildren();
        if ( list != null ) {
            listXmindDetailResp.setChildren( new ArrayList<ListXmindDetailVO>( list ) );
        }
        listXmindDetailResp.setAttribute( listXmindDetailVO.getAttribute() );
        listXmindDetailResp.setHasChilds( listXmindDetailVO.getHasChilds() );
        listXmindDetailResp.setPriority( listXmindDetailVO.getPriority() );
        listXmindDetailResp.setPriorityDesc( listXmindDetailVO.getPriorityDesc() );
        listXmindDetailResp.setTestcaseCount( listXmindDetailVO.getTestcaseCount() );
        listXmindDetailResp.setPath( listXmindDetailVO.getPath() );
        listXmindDetailResp.setExecutionStatus( listXmindDetailVO.getExecutionStatus() );
        listXmindDetailResp.setExecutionStatusDesc( listXmindDetailVO.getExecutionStatusDesc() );
        listXmindDetailResp.setTestcaseStatus( listXmindDetailVO.getTestcaseStatus() );
        listXmindDetailResp.setTestcaseStatusDesc( listXmindDetailVO.getTestcaseStatusDesc() );

        return listXmindDetailResp;
    }
}
