package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.RelatedToMeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.PlanEventFieldEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.dto.TestFunctionPointEntityDO;
import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmEmailEntityDO;
import com.zto.devops.qc.client.model.dto.TmPlanIssueEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanListQuery;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ButtonVO;
import com.zto.devops.qc.client.model.testmanager.plan.command.ChangeCaseExecuteResultCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.ChangePlanCaseResultCommentCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.EditPlanStageStatusCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.EditPlanStatusCommand;
import com.zto.devops.qc.client.model.testmanager.plan.entity.RelatedPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.RelatedReportVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.SimpleTmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestFunctionPointVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanSendEmailVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.event.ChangeCaseExecuteResultEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.EditPlanStageStatusEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.EditPlanStatusEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.PlanCaseResultCommentChangedEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.SendTestPlanEvent;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseCodeQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PageListPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PagePlanIssueQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PageTestPlanQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PlanListQuery;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmTestPlanDomainConverterImpl implements TmTestPlanDomainConverter {

    @Override
    public TmTestPlanVO convertDO2VO(TmTestPlanEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TmTestPlanVO tmTestPlanVO = new TmTestPlanVO();

        tmTestPlanVO.setCode( entityDO.getCode() );
        tmTestPlanVO.setPlanName( entityDO.getPlanName() );
        tmTestPlanVO.setType( entityDO.getType() );
        tmTestPlanVO.setTestStrategy( entityDO.getTestStrategy() );
        tmTestPlanVO.setStatus( entityDO.getStatus() );
        tmTestPlanVO.setRelationPlanCode( entityDO.getRelationPlanCode() );
        tmTestPlanVO.setDeptId( entityDO.getDeptId() );
        tmTestPlanVO.setDeptName( entityDO.getDeptName() );
        tmTestPlanVO.setProductCode( entityDO.getProductCode() );
        tmTestPlanVO.setProductName( entityDO.getProductName() );
        tmTestPlanVO.setVersionCode( entityDO.getVersionCode() );
        tmTestPlanVO.setVersionName( entityDO.getVersionName() );
        tmTestPlanVO.setStartDate( entityDO.getStartDate() );
        tmTestPlanVO.setPublishDate( entityDO.getPublishDate() );
        tmTestPlanVO.setAccessDate( entityDO.getAccessDate() );
        tmTestPlanVO.setAccessDatePartition( entityDO.getAccessDatePartition() );
        tmTestPlanVO.setPermitDate( entityDO.getPermitDate() );
        tmTestPlanVO.setPermitDatePartition( entityDO.getPermitDatePartition() );
        tmTestPlanVO.setDeveloperNum( entityDO.getDeveloperNum() );
        tmTestPlanVO.setTesterNum( entityDO.getTesterNum() );
        tmTestPlanVO.setComment( entityDO.getComment() );
        tmTestPlanVO.setProductDirectorId( entityDO.getProductDirectorId() );
        tmTestPlanVO.setProductDirectorName( entityDO.getProductDirectorName() );
        tmTestPlanVO.setTestDirectorId( entityDO.getTestDirectorId() );
        tmTestPlanVO.setTestDirectorName( entityDO.getTestDirectorName() );
        tmTestPlanVO.setPlanDirectorId( entityDO.getPlanDirectorId() );
        tmTestPlanVO.setPlanDirectorName( entityDO.getPlanDirectorName() );
        tmTestPlanVO.setCreatorId( entityDO.getCreatorId() );
        tmTestPlanVO.setCreator( entityDO.getCreator() );
        tmTestPlanVO.setGmtCreate( entityDO.getGmtCreate() );
        tmTestPlanVO.setModifierId( entityDO.getModifierId() );
        tmTestPlanVO.setModifier( entityDO.getModifier() );
        tmTestPlanVO.setGmtModified( entityDO.getGmtModified() );
        tmTestPlanVO.setEditNo( entityDO.getEditNo() );
        tmTestPlanVO.setProductSource( entityDO.getProductSource() );
        Map<String, Object> map = entityDO.getStageStatus();
        if ( map != null ) {
            tmTestPlanVO.setStageStatus( new HashMap<String, Object>( map ) );
        }

        return tmTestPlanVO;
    }

    @Override
    public List<TestFunctionPointVO> convertPointVOList(List<TestFunctionPointEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<TestFunctionPointVO> list = new ArrayList<TestFunctionPointVO>( entityDOList.size() );
        for ( TestFunctionPointEntityDO testFunctionPointEntityDO : entityDOList ) {
            list.add( testFunctionPointEntityDOToTestFunctionPointVO( testFunctionPointEntityDO ) );
        }

        return list;
    }

    @Override
    public Button convertButtonVOS(PlanEventFieldEnum em) {
        if ( em == null ) {
            return null;
        }

        Button button = new Button();

        button.setName( em.getValue() );
        button.setCode( em.getFieldName() );

        return button;
    }

    @Override
    public List<Button> convertButtonVOS(LinkedHashSet<PlanEventFieldEnum> planEventFieldLists) {
        if ( planEventFieldLists == null ) {
            return null;
        }

        List<Button> list = new ArrayList<Button>( planEventFieldLists.size() );
        for ( PlanEventFieldEnum planEventFieldEnum : planEventFieldLists ) {
            list.add( convertButtonVOS( planEventFieldEnum ) );
        }

        return list;
    }

    @Override
    public List<RelatedPlanVO> convertPlanVOList(List<TmEmailEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<RelatedPlanVO> list = new ArrayList<RelatedPlanVO>( entityDOList.size() );
        for ( TmEmailEntityDO tmEmailEntityDO : entityDOList ) {
            list.add( convertPlanVO( tmEmailEntityDO ) );
        }

        return list;
    }

    @Override
    public RelatedPlanVO convertPlanVO(TmEmailEntityDO entity) {
        if ( entity == null ) {
            return null;
        }

        RelatedPlanVO relatedPlanVO = new RelatedPlanVO();

        relatedPlanVO.setPlanCode( entity.getBusinessCode() );
        if ( entity.getEmailType() != null ) {
            relatedPlanVO.setPlanType( entity.getEmailType().name() );
        }
        relatedPlanVO.setPlanName( entity.getBusinessName() );
        relatedPlanVO.setAccessDate( entity.getPlanPresentationDate() );
        relatedPlanVO.setPermitDate( entity.getPlanApprovalExitDate() );
        relatedPlanVO.setEmailCode( entity.getEmailCode() );
        relatedPlanVO.setName( entity.getEmailName() );
        relatedPlanVO.setVersionCode( entity.getVersionCode() );
        relatedPlanVO.setProductCode( entity.getProductCode() );
        relatedPlanVO.setSenderId( entity.getSenderId() );
        relatedPlanVO.setSender( entity.getSender() );
        relatedPlanVO.setSendDate( entity.getSendDate() );

        return relatedPlanVO;
    }

    @Override
    public List<RelatedReportVO> convertReportVOList(List<TmEmailEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<RelatedReportVO> list1 = new ArrayList<RelatedReportVO>( list.size() );
        for ( TmEmailEntityDO tmEmailEntityDO : list ) {
            list1.add( convertReportVO( tmEmailEntityDO ) );
        }

        return list1;
    }

    @Override
    public RelatedReportVO convertReportVO(TmEmailEntityDO entity) {
        if ( entity == null ) {
            return null;
        }

        RelatedReportVO relatedReportVO = new RelatedReportVO();

        relatedReportVO.setReportCode( entity.getBusinessCode() );
        if ( entity.getEmailType() != null ) {
            relatedReportVO.setReportType( entity.getEmailType().name() );
        }
        relatedReportVO.setReportName( entity.getBusinessName() );
        relatedReportVO.setEmailCode( entity.getEmailCode() );
        relatedReportVO.setName( entity.getEmailName() );
        relatedReportVO.setVersionCode( entity.getVersionCode() );
        relatedReportVO.setProductCode( entity.getProductCode() );
        relatedReportVO.setSenderId( entity.getSenderId() );
        relatedReportVO.setSender( entity.getSender() );
        relatedReportVO.setSendDate( entity.getSendDate() );

        return relatedReportVO;
    }

    @Override
    public TmPlanIssueEntityDO convert(PagePlanIssueQuery query) {
        if ( query == null ) {
            return null;
        }

        TmPlanIssueEntityDO tmPlanIssueEntityDO = new TmPlanIssueEntityDO();

        tmPlanIssueEntityDO.setPlanCode( query.getPlanCode() );
        tmPlanIssueEntityDO.setCodeOrTitle( query.getCodeOrTitle() );
        List<IssueStatus> list = query.getStatusList();
        if ( list != null ) {
            tmPlanIssueEntityDO.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        tmPlanIssueEntityDO.setCurrentUserId( query.getCurrentUserId() );
        List<Long> list1 = query.getHandleUserIdList();
        if ( list1 != null ) {
            tmPlanIssueEntityDO.setHandleUserIdList( new ArrayList<Long>( list1 ) );
        }
        List<Long> list2 = query.getDevelopUserIdList();
        if ( list2 != null ) {
            tmPlanIssueEntityDO.setDevelopUserIdList( new ArrayList<Long>( list2 ) );
        }
        List<Long> list3 = query.getTestUserIdList();
        if ( list3 != null ) {
            tmPlanIssueEntityDO.setTestUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<RelatedToMeEnum> list4 = query.getRelatedList();
        if ( list4 != null ) {
            tmPlanIssueEntityDO.setRelatedList( new ArrayList<RelatedToMeEnum>( list4 ) );
        }
        List<IssuePriority> list5 = query.getPriorityList();
        if ( list5 != null ) {
            tmPlanIssueEntityDO.setPriorityList( new ArrayList<IssuePriority>( list5 ) );
        }

        return tmPlanIssueEntityDO;
    }

    @Override
    public ListPlanCaseQuery convert(PageListPlanCaseQuery query) {
        if ( query == null ) {
            return null;
        }

        ListPlanCaseQuery listPlanCaseQuery = new ListPlanCaseQuery();

        listPlanCaseQuery.setTransactor( query.getTransactor() );
        listPlanCaseQuery.setProductCode( query.getProductCode() );
        listPlanCaseQuery.setVersionCode( query.getVersionCode() );
        listPlanCaseQuery.setPlanCode( query.getPlanCode() );
        List<TestPlanStageEnum> list = query.getTestPlanStageList();
        if ( list != null ) {
            listPlanCaseQuery.setTestPlanStageList( new ArrayList<TestPlanStageEnum>( list ) );
        }
        listPlanCaseQuery.setParentCode( query.getParentCode() );
        List<TestcaseTypeEnum> list1 = query.getCaseTypeList();
        if ( list1 != null ) {
            listPlanCaseQuery.setCaseTypeList( new ArrayList<TestcaseTypeEnum>( list1 ) );
        }
        List<String> list2 = query.getCaseCodeList();
        if ( list2 != null ) {
            listPlanCaseQuery.setCaseCodeList( new ArrayList<String>( list2 ) );
        }
        listPlanCaseQuery.setSearch( query.getSearch() );
        List<TestPlanCaseStatusEnum> list3 = query.getStatusList();
        if ( list3 != null ) {
            listPlanCaseQuery.setStatusList( new ArrayList<TestPlanCaseStatusEnum>( list3 ) );
        }
        List<Long> list4 = query.getExecutorIdList();
        if ( list4 != null ) {
            listPlanCaseQuery.setExecutorIdList( new ArrayList<Long>( list4 ) );
        }
        List<TestcasePriorityEnum> list5 = query.getPriorityList();
        if ( list5 != null ) {
            listPlanCaseQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list5 ) );
        }
        List<AutomaticNodeTypeEnum> list6 = query.getNodeTypeList();
        if ( list6 != null ) {
            listPlanCaseQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list6 ) );
        }
        List<AutomaticRecordTypeEnum> list7 = query.getAutomaticTypeList();
        if ( list7 != null ) {
            listPlanCaseQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list7 ) );
        }
        List<String> list8 = query.getTagList();
        if ( list8 != null ) {
            listPlanCaseQuery.setTagList( new ArrayList<String>( list8 ) );
        }
        List<Boolean> list9 = query.getSetCoreList();
        if ( list9 != null ) {
            listPlanCaseQuery.setSetCoreList( new ArrayList<Boolean>( list9 ) );
        }

        return listPlanCaseQuery;
    }

    @Override
    public TmTestPlanEntityDO convert(PageTestPlanQuery query) {
        if ( query == null ) {
            return null;
        }

        TmTestPlanEntityDO tmTestPlanEntityDO = new TmTestPlanEntityDO();

        tmTestPlanEntityDO.setPlanName( query.getPlanName() );
        tmTestPlanEntityDO.setProductCode( query.getProductCode() );

        return tmTestPlanEntityDO;
    }

    @Override
    public List<TmTestPlanVO> convertLists(List<TmTestPlanEntityDO> tmTestPlanEntityLists) {
        if ( tmTestPlanEntityLists == null ) {
            return null;
        }

        List<TmTestPlanVO> list = new ArrayList<TmTestPlanVO>( tmTestPlanEntityLists.size() );
        for ( TmTestPlanEntityDO tmTestPlanEntityDO : tmTestPlanEntityLists ) {
            list.add( convertDO2VO( tmTestPlanEntityDO ) );
        }

        return list;
    }

    @Override
    public EditPlanStageStatusEvent convert(EditPlanStageStatusCommand command) {
        if ( command == null ) {
            return null;
        }

        EditPlanStageStatusEvent editPlanStageStatusEvent = new EditPlanStageStatusEvent();

        editPlanStageStatusEvent.setAggregateId( command.getAggregateId() );
        editPlanStageStatusEvent.setTransactor( command.getTransactor() );
        editPlanStageStatusEvent.setOccurred( command.getOccurred() );
        editPlanStageStatusEvent.setPlanCode( command.getPlanCode() );
        editPlanStageStatusEvent.setType( command.getType() );
        editPlanStageStatusEvent.setStage( command.getStage() );

        return editPlanStageStatusEvent;
    }

    @Override
    public EditPlanStatusEvent convert(EditPlanStatusCommand command) {
        if ( command == null ) {
            return null;
        }

        EditPlanStatusEvent editPlanStatusEvent = new EditPlanStatusEvent();

        editPlanStatusEvent.setAggregateId( command.getAggregateId() );
        editPlanStatusEvent.setTransactor( command.getTransactor() );
        editPlanStatusEvent.setOccurred( command.getOccurred() );
        editPlanStatusEvent.setPlanCode( command.getPlanCode() );
        editPlanStatusEvent.setStatus( command.getStatus() );
        editPlanStatusEvent.setRelationPlanCode( command.getRelationPlanCode() );
        editPlanStatusEvent.setPlanType( command.getPlanType() );
        editPlanStatusEvent.setSecurityTestResult( command.getSecurityTestResult() );

        return editPlanStatusEvent;
    }

    @Override
    public TmTestPlanEntityDO convert(EditPlanStatusEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestPlanEntityDO tmTestPlanEntityDO = new TmTestPlanEntityDO();

        tmTestPlanEntityDO.setCode( event.getPlanCode() );
        tmTestPlanEntityDO.setType( event.getPlanType() );
        tmTestPlanEntityDO.setStatus( event.getStatus() );
        tmTestPlanEntityDO.setRelationPlanCode( event.getRelationPlanCode() );

        return tmTestPlanEntityDO;
    }

    @Override
    public SendTestPlanEvent convertor(TmTestPlanSendEmailVO vo) {
        if ( vo == null ) {
            return null;
        }

        SendTestPlanEvent sendTestPlanEvent = new SendTestPlanEvent();

        if ( vo.getEmailType() != null ) {
            sendTestPlanEvent.setEmailType( vo.getEmailType().name() );
        }
        sendTestPlanEvent.setBusinessCode( vo.getBusinessCode() );
        sendTestPlanEvent.setBusinessName( vo.getBusinessName() );
        sendTestPlanEvent.setProductCode( vo.getProductCode() );
        sendTestPlanEvent.setProductName( vo.getProductName() );
        sendTestPlanEvent.setRelationPlanCode( vo.getRelationPlanCode() );
        sendTestPlanEvent.setRelationPlanName( vo.getRelationPlanName() );
        sendTestPlanEvent.setVersionCode( vo.getVersionCode() );
        sendTestPlanEvent.setVersionName( vo.getVersionName() );
        List<SendUserInfoVO> list = vo.getReceiveUsers();
        if ( list != null ) {
            sendTestPlanEvent.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = vo.getCcUsers();
        if ( list1 != null ) {
            sendTestPlanEvent.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        sendTestPlanEvent.setPreview( vo.getPreview() );
        sendTestPlanEvent.setSendDate( vo.getSendDate() );
        sendTestPlanEvent.setSenderId( vo.getSenderId() );
        sendTestPlanEvent.setSender( vo.getSender() );

        sendTestPlanEvent.setAccessDate( com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanSendEmailVO.getAccessDate(vo) );
        sendTestPlanEvent.setPermitDate( com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanSendEmailVO.getPermitDate(vo) );

        return sendTestPlanEvent;
    }

    @Override
    public TmEmailEntityDO convert(SendTestPlanEvent event) {
        if ( event == null ) {
            return null;
        }

        TmEmailEntityDO tmEmailEntityDO = new TmEmailEntityDO();

        tmEmailEntityDO.setEmailName( event.getName() );
        tmEmailEntityDO.setPlanPresentationDate( event.getAccessDate() );
        tmEmailEntityDO.setPlanApprovalExitDate( event.getPermitDate() );
        tmEmailEntityDO.setRelatePlanCode( event.getRelationPlanCode() );
        tmEmailEntityDO.setRelatePlanName( event.getRelationPlanName() );
        tmEmailEntityDO.setEmailCode( event.getEmailCode() );
        if ( event.getEmailType() != null ) {
            tmEmailEntityDO.setEmailType( Enum.valueOf( EmailTypeEnum.class, event.getEmailType() ) );
        }
        tmEmailEntityDO.setBusinessCode( event.getBusinessCode() );
        tmEmailEntityDO.setBusinessName( event.getBusinessName() );
        tmEmailEntityDO.setProductCode( event.getProductCode() );
        tmEmailEntityDO.setProductName( event.getProductName() );
        tmEmailEntityDO.setVersionCode( event.getVersionCode() );
        tmEmailEntityDO.setVersionName( event.getVersionName() );
        tmEmailEntityDO.setPreview( event.getPreview() );
        tmEmailEntityDO.setSendDate( event.getSendDate() );
        tmEmailEntityDO.setSenderId( event.getSenderId() );
        tmEmailEntityDO.setSender( event.getSender() );

        return tmEmailEntityDO;
    }

    @Override
    public ChangeCaseExecuteResultEvent convert(ChangeCaseExecuteResultCommand command) {
        if ( command == null ) {
            return null;
        }

        ChangeCaseExecuteResultEvent changeCaseExecuteResultEvent = new ChangeCaseExecuteResultEvent();

        changeCaseExecuteResultEvent.setAggregateId( command.getAggregateId() );
        changeCaseExecuteResultEvent.setTransactor( command.getTransactor() );
        changeCaseExecuteResultEvent.setOccurred( command.getOccurred() );
        changeCaseExecuteResultEvent.setExecuteStatus( command.getExecuteStatus() );
        List<String> list = command.getCaseIds();
        if ( list != null ) {
            changeCaseExecuteResultEvent.setCaseIds( new ArrayList<String>( list ) );
        }
        changeCaseExecuteResultEvent.setTestStage( command.getTestStage() );
        changeCaseExecuteResultEvent.setPlanCode( command.getPlanCode() );

        return changeCaseExecuteResultEvent;
    }

    @Override
    public PlanCaseResultCommentChangedEvent convert(ChangePlanCaseResultCommentCommand command) {
        if ( command == null ) {
            return null;
        }

        PlanCaseResultCommentChangedEvent planCaseResultCommentChangedEvent = new PlanCaseResultCommentChangedEvent();

        planCaseResultCommentChangedEvent.setPlanCode( command.getAggregateId() );
        planCaseResultCommentChangedEvent.setAggregateId( command.getAggregateId() );
        planCaseResultCommentChangedEvent.setTransactor( command.getTransactor() );
        planCaseResultCommentChangedEvent.setOccurred( command.getOccurred() );
        planCaseResultCommentChangedEvent.setTestStage( command.getTestStage() );
        planCaseResultCommentChangedEvent.setCaseCode( command.getCaseCode() );
        planCaseResultCommentChangedEvent.setResultComment( command.getResultComment() );
        planCaseResultCommentChangedEvent.setOperateCaseCode( command.getOperateCaseCode() );

        return planCaseResultCommentChangedEvent;
    }

    @Override
    public ButtonVO convertButtonVOList(PlanEventFieldEnum em) {
        if ( em == null ) {
            return null;
        }

        ButtonVO buttonVO = new ButtonVO();

        buttonVO.setName( em.getValue() );
        buttonVO.setCode( em.getFieldName() );

        return buttonVO;
    }

    @Override
    public List<ButtonVO> convertButtonVOList(Collection<PlanEventFieldEnum> emList) {
        if ( emList == null ) {
            return null;
        }

        List<ButtonVO> list = new ArrayList<ButtonVO>( emList.size() );
        for ( PlanEventFieldEnum planEventFieldEnum : emList ) {
            list.add( convertButtonVOList( planEventFieldEnum ) );
        }

        return list;
    }

    @Override
    public PlanListQuery convertQuery(TestPlanListQuery query) {
        if ( query == null ) {
            return null;
        }

        PlanListQuery planListQuery = new PlanListQuery();

        List<String> list = query.getStatusList();
        if ( list != null ) {
            planListQuery.setStatus( new ArrayList<String>( list ) );
        }
        planListQuery.setTransactor( query.getTransactor() );
        planListQuery.setPage( query.getPage() );
        planListQuery.setSize( query.getSize() );
        planListQuery.setProductCode( query.getProductCode() );
        planListQuery.setCreateTimeStart( query.getCreateTimeStart() );
        planListQuery.setCreateTimeEnd( query.getCreateTimeEnd() );
        List<String> list1 = query.getProductCodes();
        if ( list1 != null ) {
            planListQuery.setProductCodes( new ArrayList<String>( list1 ) );
        }
        planListQuery.setVersionCode( query.getVersionCode() );
        planListQuery.setOrderField( query.getOrderField() );
        planListQuery.setOrderType( query.getOrderType() );

        return planListQuery;
    }

    @Override
    public List<TestPlanVO> convert(List<TestPlanEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<TestPlanVO> list1 = new ArrayList<TestPlanVO>( list.size() );
        for ( TestPlanEntityDO testPlanEntityDO : list ) {
            list1.add( testPlanEntityDOToTestPlanVO( testPlanEntityDO ) );
        }

        return list1;
    }

    @Override
    public SimpleTmTestPlanVO convertSimpleVO(TmTestPlanEntityDO vo) {
        if ( vo == null ) {
            return null;
        }

        SimpleTmTestPlanVO simpleTmTestPlanVO = new SimpleTmTestPlanVO();

        simpleTmTestPlanVO.setCode( vo.getCode() );
        simpleTmTestPlanVO.setPlanName( vo.getPlanName() );
        simpleTmTestPlanVO.setType( vo.getType() );
        simpleTmTestPlanVO.setTestStrategy( vo.getTestStrategy() );
        simpleTmTestPlanVO.setStatus( vo.getStatus() );
        simpleTmTestPlanVO.setRelationPlanCode( vo.getRelationPlanCode() );
        simpleTmTestPlanVO.setDeptId( vo.getDeptId() );
        simpleTmTestPlanVO.setDeptName( vo.getDeptName() );
        simpleTmTestPlanVO.setProductCode( vo.getProductCode() );
        simpleTmTestPlanVO.setProductName( vo.getProductName() );
        simpleTmTestPlanVO.setVersionCode( vo.getVersionCode() );
        simpleTmTestPlanVO.setVersionName( vo.getVersionName() );
        simpleTmTestPlanVO.setStartDate( vo.getStartDate() );
        simpleTmTestPlanVO.setPublishDate( vo.getPublishDate() );
        simpleTmTestPlanVO.setAccessDate( vo.getAccessDate() );
        simpleTmTestPlanVO.setAccessDatePartition( vo.getAccessDatePartition() );
        simpleTmTestPlanVO.setPermitDate( vo.getPermitDate() );
        simpleTmTestPlanVO.setPermitDatePartition( vo.getPermitDatePartition() );
        simpleTmTestPlanVO.setDeveloperNum( vo.getDeveloperNum() );
        simpleTmTestPlanVO.setTesterNum( vo.getTesterNum() );
        simpleTmTestPlanVO.setComment( vo.getComment() );
        simpleTmTestPlanVO.setProductDirectorId( vo.getProductDirectorId() );
        simpleTmTestPlanVO.setProductDirectorName( vo.getProductDirectorName() );
        simpleTmTestPlanVO.setTestDirectorId( vo.getTestDirectorId() );
        simpleTmTestPlanVO.setTestDirectorName( vo.getTestDirectorName() );
        simpleTmTestPlanVO.setPlanDirectorId( vo.getPlanDirectorId() );
        simpleTmTestPlanVO.setPlanDirectorName( vo.getPlanDirectorName() );
        simpleTmTestPlanVO.setCreatorId( vo.getCreatorId() );
        simpleTmTestPlanVO.setCreator( vo.getCreator() );
        simpleTmTestPlanVO.setGmtCreate( vo.getGmtCreate() );
        simpleTmTestPlanVO.setModifierId( vo.getModifierId() );
        simpleTmTestPlanVO.setModifier( vo.getModifier() );
        simpleTmTestPlanVO.setGmtModified( vo.getGmtModified() );
        simpleTmTestPlanVO.setEditNo( vo.getEditNo() );
        Map<String, Object> map = vo.getStageStatus();
        if ( map != null ) {
            simpleTmTestPlanVO.setStageStatus( new HashMap<String, Object>( map ) );
        }

        return simpleTmTestPlanVO;
    }

    @Override
    public ListPlanCaseQuery convert(ListPlanCaseCodeQuery query) {
        if ( query == null ) {
            return null;
        }

        ListPlanCaseQuery listPlanCaseQuery = new ListPlanCaseQuery();

        listPlanCaseQuery.setTransactor( query.getTransactor() );
        listPlanCaseQuery.setProductCode( query.getProductCode() );
        listPlanCaseQuery.setVersionCode( query.getVersionCode() );
        listPlanCaseQuery.setPlanCode( query.getPlanCode() );
        List<TestPlanStageEnum> list = query.getTestPlanStageList();
        if ( list != null ) {
            listPlanCaseQuery.setTestPlanStageList( new ArrayList<TestPlanStageEnum>( list ) );
        }
        listPlanCaseQuery.setParentCode( query.getParentCode() );
        List<TestcaseTypeEnum> list1 = query.getCaseTypeList();
        if ( list1 != null ) {
            listPlanCaseQuery.setCaseTypeList( new ArrayList<TestcaseTypeEnum>( list1 ) );
        }
        listPlanCaseQuery.setSearch( query.getSearch() );
        List<TestPlanCaseStatusEnum> list2 = query.getStatusList();
        if ( list2 != null ) {
            listPlanCaseQuery.setStatusList( new ArrayList<TestPlanCaseStatusEnum>( list2 ) );
        }
        List<Long> list3 = query.getExecutorIdList();
        if ( list3 != null ) {
            listPlanCaseQuery.setExecutorIdList( new ArrayList<Long>( list3 ) );
        }
        List<TestcasePriorityEnum> list4 = query.getPriorityList();
        if ( list4 != null ) {
            listPlanCaseQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list4 ) );
        }
        List<AutomaticNodeTypeEnum> list5 = query.getNodeTypeList();
        if ( list5 != null ) {
            listPlanCaseQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
        }
        List<AutomaticRecordTypeEnum> list6 = query.getAutomaticTypeList();
        if ( list6 != null ) {
            listPlanCaseQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list6 ) );
        }
        List<String> list7 = query.getTagList();
        if ( list7 != null ) {
            listPlanCaseQuery.setTagList( new ArrayList<String>( list7 ) );
        }
        List<Boolean> list8 = query.getSetCoreList();
        if ( list8 != null ) {
            listPlanCaseQuery.setSetCoreList( new ArrayList<Boolean>( list8 ) );
        }

        return listPlanCaseQuery;
    }

    protected TestFunctionPointVO testFunctionPointEntityDOToTestFunctionPointVO(TestFunctionPointEntityDO testFunctionPointEntityDO) {
        if ( testFunctionPointEntityDO == null ) {
            return null;
        }

        TestFunctionPointVO testFunctionPointVO = new TestFunctionPointVO();

        testFunctionPointVO.setCode( testFunctionPointEntityDO.getCode() );
        testFunctionPointVO.setType( testFunctionPointEntityDO.getType() );
        testFunctionPointVO.setFunctionPoint( testFunctionPointEntityDO.getFunctionPoint() );
        testFunctionPointVO.setDirectorId( testFunctionPointEntityDO.getDirectorId() );
        testFunctionPointVO.setDirectorName( testFunctionPointEntityDO.getDirectorName() );
        testFunctionPointVO.setNumber( testFunctionPointEntityDO.getNumber() );

        return testFunctionPointVO;
    }

    protected TestPlanVO testPlanEntityDOToTestPlanVO(TestPlanEntityDO testPlanEntityDO) {
        if ( testPlanEntityDO == null ) {
            return null;
        }

        TestPlanVO testPlanVO = new TestPlanVO();

        testPlanVO.setCode( testPlanEntityDO.getCode() );
        testPlanVO.setPlanName( testPlanEntityDO.getPlanName() );
        testPlanVO.setProductCode( testPlanEntityDO.getProductCode() );
        testPlanVO.setProductName( testPlanEntityDO.getProductName() );
        if ( testPlanEntityDO.getType() != null ) {
            testPlanVO.setType( testPlanEntityDO.getType().name() );
        }
        testPlanVO.setTypeDesc( testPlanEntityDO.getTypeDesc() );
        testPlanVO.setPlanCode( testPlanEntityDO.getPlanCode() );
        testPlanVO.setTestDirectorId( testPlanEntityDO.getTestDirectorId() );
        testPlanVO.setTestDirectorName( testPlanEntityDO.getTestDirectorName() );
        testPlanVO.setCreatorId( testPlanEntityDO.getCreatorId() );
        testPlanVO.setCreator( testPlanEntityDO.getCreator() );
        testPlanVO.setGmtCreate( testPlanEntityDO.getGmtCreate() );
        testPlanVO.setModifierId( testPlanEntityDO.getModifierId() );
        testPlanVO.setModifier( testPlanEntityDO.getModifier() );
        testPlanVO.setGmtModified( testPlanEntityDO.getGmtModified() );
        testPlanVO.setEnable( testPlanEntityDO.getEnable() );
        testPlanVO.setEditNo( testPlanEntityDO.getEditNo() );
        if ( testPlanEntityDO.getStatus() != null ) {
            testPlanVO.setStatus( testPlanEntityDO.getStatus().name() );
        }
        testPlanVO.setPreview( testPlanEntityDO.getPreview() );
        testPlanVO.setVersionName( testPlanEntityDO.getVersionName() );
        testPlanVO.setVersionCode( testPlanEntityDO.getVersionCode() );
        testPlanVO.setDeptId( testPlanEntityDO.getDeptId() );
        testPlanVO.setDeptName( testPlanEntityDO.getDeptName() );
        testPlanVO.setProductDirectorId( testPlanEntityDO.getProductDirectorId() );
        testPlanVO.setProductDirectorName( testPlanEntityDO.getProductDirectorName() );

        return testPlanVO;
    }
}
