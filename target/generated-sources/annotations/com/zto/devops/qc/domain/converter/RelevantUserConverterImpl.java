package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.issue.entity.CurrentHandlerVO;
import com.zto.devops.qc.client.model.issue.entity.CurrentHandlerVO.CurrentHandlerVOBuilder;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.parameter.TaskBaseParameter;
import com.zto.devops.qc.client.model.parameter.TaskResultParameter;
import com.zto.devops.qc.client.model.relevantUser.query.MyIssueQuery;
import com.zto.devops.qc.client.model.relevantUser.query.MyTaskVO;
import com.zto.devops.qc.domain.model.RelevantUser;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class RelevantUserConverterImpl implements RelevantUserConverter {

    @Override
    public RelevantUser convert(RelevantUserVO vo) {
        if ( vo == null ) {
            return null;
        }

        RelevantUser relevantUser = new RelevantUser();

        relevantUser.setBusinessCode( vo.getBusinessCode() );
        relevantUser.setCode( vo.getCode() );
        relevantUser.setUserId( vo.getUserId() );
        relevantUser.setUserName( vo.getUserName() );
        relevantUser.setDomain( vo.getDomain() );
        relevantUser.setType( vo.getType() );

        return relevantUser;
    }

    @Override
    public List<RelevantUser> convert(Collection<RelevantUserVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<RelevantUser> list = new ArrayList<RelevantUser>( vo.size() );
        for ( RelevantUserVO relevantUserVO : vo ) {
            list.add( convert( relevantUserVO ) );
        }

        return list;
    }

    @Override
    public CurrentHandlerVO convertCurrentHandlerVO(User user) {
        if ( user == null ) {
            return null;
        }

        CurrentHandlerVOBuilder currentHandlerVO = CurrentHandlerVO.builder();

        if ( user.getUserId() != null ) {
            currentHandlerVO.userId( String.valueOf( user.getUserId() ) );
        }
        currentHandlerVO.userName( user.getUserName() );

        return currentHandlerVO.build();
    }

    @Override
    public CurrentHandlerVO convertCurrentHandlerVO(RelevantUser currentHandlerVO) {
        if ( currentHandlerVO == null ) {
            return null;
        }

        CurrentHandlerVOBuilder currentHandlerVO1 = CurrentHandlerVO.builder();

        if ( currentHandlerVO.getType() != null ) {
            currentHandlerVO1.type( currentHandlerVO.getType().name() );
        }
        currentHandlerVO1.businessCode( currentHandlerVO.getBusinessCode() );
        if ( currentHandlerVO.getUserId() != null ) {
            currentHandlerVO1.userId( String.valueOf( currentHandlerVO.getUserId() ) );
        }
        currentHandlerVO1.userName( currentHandlerVO.getUserName() );
        if ( currentHandlerVO.getDomain() != null ) {
            currentHandlerVO1.domain( currentHandlerVO.getDomain().name() );
        }
        currentHandlerVO1.code( currentHandlerVO.getCode() );

        return currentHandlerVO1.build();
    }

    @Override
    public Set<CurrentHandlerVO> convertCurrentHandlerVO(Collection<RelevantUser> currentHandlerVO) {
        if ( currentHandlerVO == null ) {
            return null;
        }

        Set<CurrentHandlerVO> set = new HashSet<CurrentHandlerVO>( Math.max( (int) ( currentHandlerVO.size() / .75f ) + 1, 16 ) );
        for ( RelevantUser relevantUser : currentHandlerVO ) {
            set.add( convertCurrentHandlerVO( relevantUser ) );
        }

        return set;
    }

    @Override
    public TaskBaseParameter convertTask(MyIssueQuery taskBaseParameter) {
        if ( taskBaseParameter == null ) {
            return null;
        }

        TaskBaseParameter taskBaseParameter1 = new TaskBaseParameter();

        taskBaseParameter1.setRelevantUserTypes( relevantUserTypeEnumListToStringList( taskBaseParameter.getRelevantUserTypes() ) );
        if ( taskBaseParameter.getDomain() != null ) {
            taskBaseParameter1.setDomain( taskBaseParameter.getDomain().name() );
        }
        taskBaseParameter1.setOrderField( taskBaseParameter.getOrderField() );
        taskBaseParameter1.setOrderType( taskBaseParameter.getOrderType() );
        taskBaseParameter1.setUserId( taskBaseParameter.getUserId() );
        taskBaseParameter1.setModifierId( taskBaseParameter.getModifierId() );
        List<String> list1 = taskBaseParameter.getStatus();
        if ( list1 != null ) {
            taskBaseParameter1.setStatus( new ArrayList<String>( list1 ) );
        }
        taskBaseParameter1.setName( taskBaseParameter.getName() );
        taskBaseParameter1.setBusinessCode( taskBaseParameter.getBusinessCode() );
        List<String> list2 = taskBaseParameter.getProjectLevel();
        if ( list2 != null ) {
            taskBaseParameter1.setProjectLevel( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = taskBaseParameter.getProjectScale();
        if ( list3 != null ) {
            taskBaseParameter1.setProjectScale( new ArrayList<String>( list3 ) );
        }
        List<String> list4 = taskBaseParameter.getWorthLevel();
        if ( list4 != null ) {
            taskBaseParameter1.setWorthLevel( new ArrayList<String>( list4 ) );
        }
        List<String> list5 = taskBaseParameter.getVersionCodeList();
        if ( list5 != null ) {
            taskBaseParameter1.setVersionCodeList( new ArrayList<String>( list5 ) );
        }

        return taskBaseParameter1;
    }

    @Override
    public MyTaskVO convertIssue(TaskResultParameter taskResultParameter) {
        if ( taskResultParameter == null ) {
            return null;
        }

        MyTaskVO myTaskVO = new MyTaskVO();

        myTaskVO.setFindVersion( taskResultParameterToVersion( taskResultParameter ) );
        myTaskVO.setDeveloper( taskResultParameterToUser( taskResultParameter ) );
        myTaskVO.setTester( taskResultParameterToUser1( taskResultParameter ) );
        myTaskVO.setProduct( taskResultParameterToProduct( taskResultParameter ) );
        myTaskVO.setHandlerId( taskResultParameter.getHandleUserId() );
        myTaskVO.setHandler( taskResultParameter.getHandleUserName() );
        myTaskVO.setFindUserId( taskResultParameter.getFindUserId() );
        myTaskVO.setStatus( taskResultParameter.getStatus() );
        myTaskVO.setName( taskResultParameter.getName() );
        myTaskVO.setPriority( taskResultParameter.getPriority() );
        myTaskVO.setRelevantUserType( taskResultParameter.getRelevantUserType() );
        myTaskVO.setWarn( taskResultParameter.getWarn() );
        myTaskVO.setWarnDay( taskResultParameter.getWarnDay() );
        myTaskVO.setProductCode( taskResultParameter.getProductCode() );
        myTaskVO.setBusinessCode( taskResultParameter.getBusinessCode() );
        myTaskVO.setGmtCreate( taskResultParameter.getGmtCreate() );

        myTaskVO.setStatusDesc( com.zto.devops.qc.client.enums.issue.IssueStatus.getValueByName(taskResultParameter.getStatus()) );
        myTaskVO.setPriorityDesc( com.zto.devops.qc.client.enums.issue.IssuePriority.getValueByName(taskResultParameter.getPriority()) );
        myTaskVO.setDomainEnum( com.zto.devops.framework.client.enums.DomainEnum.ISSUE );

        return myTaskVO;
    }

    @Override
    public Set<RelevantUserVO> convert(List<RelevantUserVO> relevantUserEntities) {
        if ( relevantUserEntities == null ) {
            return null;
        }

        Set<RelevantUserVO> set = new HashSet<RelevantUserVO>( Math.max( (int) ( relevantUserEntities.size() / .75f ) + 1, 16 ) );
        for ( RelevantUserVO relevantUserVO : relevantUserEntities ) {
            set.add( relevantUserVO );
        }

        return set;
    }

    protected List<String> relevantUserTypeEnumListToStringList(List<RelevantUserTypeEnum> list) {
        if ( list == null ) {
            return null;
        }

        List<String> list1 = new ArrayList<String>( list.size() );
        for ( RelevantUserTypeEnum relevantUserTypeEnum : list ) {
            list1.add( relevantUserTypeEnum.name() );
        }

        return list1;
    }

    protected Version taskResultParameterToVersion(TaskResultParameter taskResultParameter) {
        if ( taskResultParameter == null ) {
            return null;
        }

        Version version = new Version();

        version.setCode( taskResultParameter.getFindVersionCode() );
        version.setName( taskResultParameter.getFindVersionName() );

        return version;
    }

    protected User taskResultParameterToUser(TaskResultParameter taskResultParameter) {
        if ( taskResultParameter == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( taskResultParameter.getDevelopUserId() );
        user.setUserName( taskResultParameter.getDevelopUserName() );

        return user;
    }

    protected User taskResultParameterToUser1(TaskResultParameter taskResultParameter) {
        if ( taskResultParameter == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( taskResultParameter.getTestUserId() );
        user.setUserName( taskResultParameter.getTestUserName() );

        return user;
    }

    protected Product taskResultParameterToProduct(TaskResultParameter taskResultParameter) {
        if ( taskResultParameter == null ) {
            return null;
        }

        Product product = new Product();

        product.setCode( taskResultParameter.getProductCode() );
        product.setName( taskResultParameter.getProductName() );

        return product;
    }
}
