package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.DragAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.MoveAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.MoveModuleCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticRecordDraggedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.MoveModuleEvent;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:05+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseModuleDomainConverterImpl implements TestcaseModuleDomainConverter {

    @Override
    public AddTestcaseEvent convertor(AddTestcaseCommand command) {
        if ( command == null ) {
            return null;
        }

        AddTestcaseEvent addTestcaseEvent = new AddTestcaseEvent();

        addTestcaseEvent.setCode( command.getAggregateId() );
        addTestcaseEvent.setAggregateId( command.getAggregateId() );
        addTestcaseEvent.setTransactor( command.getTransactor() );
        addTestcaseEvent.setOccurred( command.getOccurred() );
        addTestcaseEvent.setProductCode( command.getProductCode() );
        addTestcaseEvent.setVersionCode( command.getVersionCode() );
        addTestcaseEvent.setParentCode( command.getParentCode() );
        addTestcaseEvent.setName( command.getName() );
        addTestcaseEvent.setAttribute( command.getAttribute() );
        addTestcaseEvent.setType( command.getType() );
        addTestcaseEvent.setPriority( command.getPriority() );
        addTestcaseEvent.setStatus( command.getStatus() );
        addTestcaseEvent.setPrecondition( command.getPrecondition() );
        addTestcaseEvent.setDutyUserId( command.getDutyUserId() );
        addTestcaseEvent.setDutyUser( command.getDutyUser() );
        addTestcaseEvent.setComment( command.getComment() );
        addTestcaseEvent.setSort( command.getSort() );
        addTestcaseEvent.setLayer( command.getLayer() );
        addTestcaseEvent.setPath( command.getPath() );
        addTestcaseEvent.setNodeType( command.getNodeType() );
        List<AttachmentVO> list = command.getAttachments();
        if ( list != null ) {
            addTestcaseEvent.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<TagVO> list1 = command.getTags();
        if ( list1 != null ) {
            addTestcaseEvent.setTags( new ArrayList<TagVO>( list1 ) );
        }
        List<TestcaseRelationVO> list2 = command.getVos();
        if ( list2 != null ) {
            addTestcaseEvent.setVos( new ArrayList<TestcaseRelationVO>( list2 ) );
        }
        List<TestcaseStepVO> list3 = command.getTestSteps();
        if ( list3 != null ) {
            addTestcaseEvent.setTestSteps( new ArrayList<TestcaseStepVO>( list3 ) );
        }
        addTestcaseEvent.setAutomaticSourceCode( command.getAutomaticSourceCode() );
        addTestcaseEvent.setNodeTypePath( command.getNodeTypePath() );
        addTestcaseEvent.setFlag( command.getFlag() );
        addTestcaseEvent.setTestcaseModulePath( command.getTestcaseModulePath() );
        addTestcaseEvent.setSetCore( command.getSetCore() );
        addTestcaseEvent.setInterfaceName( command.getInterfaceName() );

        return addTestcaseEvent;
    }

    @Override
    public AutomaticRecordDraggedEvent convertor(DragAutomaticRecordCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticRecordDraggedEvent automaticRecordDraggedEvent = new AutomaticRecordDraggedEvent();

        automaticRecordDraggedEvent.setCode( command.getAggregateId() );
        automaticRecordDraggedEvent.setAggregateId( command.getAggregateId() );
        automaticRecordDraggedEvent.setTransactor( command.getTransactor() );
        automaticRecordDraggedEvent.setOccurred( command.getOccurred() );
        automaticRecordDraggedEvent.setParentCode( command.getParentCode() );
        automaticRecordDraggedEvent.setTargetCode( command.getTargetCode() );
        automaticRecordDraggedEvent.setAction( command.getAction() );

        return automaticRecordDraggedEvent;
    }

    @Override
    public MoveModuleEvent converter(MoveAutomaticRecordCommand command) {
        if ( command == null ) {
            return null;
        }

        MoveModuleEvent moveModuleEvent = new MoveModuleEvent();

        moveModuleEvent.setAggregateId( command.getAggregateId() );
        moveModuleEvent.setTransactor( command.getTransactor() );
        moveModuleEvent.setOccurred( command.getOccurred() );
        moveModuleEvent.setCode( command.getCode() );
        moveModuleEvent.setName( command.getName() );
        moveModuleEvent.setProductCode( command.getProductCode() );
        moveModuleEvent.setParentCode( command.getParentCode() );
        moveModuleEvent.setParentName( command.getParentName() );
        moveModuleEvent.setLayer( command.getLayer() );
        moveModuleEvent.setOldPath( command.getOldPath() );
        moveModuleEvent.setNewPath( command.getNewPath() );
        moveModuleEvent.setType( command.getType() );
        moveModuleEvent.setAttribute( command.getAttribute() );

        return moveModuleEvent;
    }

    @Override
    public MoveModuleEvent converter(MoveModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        MoveModuleEvent moveModuleEvent = new MoveModuleEvent();

        moveModuleEvent.setAggregateId( command.getAggregateId() );
        moveModuleEvent.setTransactor( command.getTransactor() );
        moveModuleEvent.setOccurred( command.getOccurred() );
        moveModuleEvent.setCode( command.getCode() );
        moveModuleEvent.setName( command.getName() );
        moveModuleEvent.setProductCode( command.getProductCode() );
        moveModuleEvent.setParentCode( command.getParentCode() );
        moveModuleEvent.setParentName( command.getParentName() );
        moveModuleEvent.setLayer( command.getLayer() );
        moveModuleEvent.setOldPath( command.getOldPath() );
        moveModuleEvent.setNewPath( command.getNewPath() );
        moveModuleEvent.setType( command.getType() );
        moveModuleEvent.setAttribute( command.getAttribute() );

        return moveModuleEvent;
    }
}
