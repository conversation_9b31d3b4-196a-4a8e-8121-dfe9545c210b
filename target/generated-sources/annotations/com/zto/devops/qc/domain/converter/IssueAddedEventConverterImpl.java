package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.model.issue.command.AddIssueCommand;
import com.zto.devops.qc.client.model.issue.command.AddVersionIssueCommand;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:05+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueAddedEventConverterImpl implements IssueAddedEventConverter {

    @Override
    public IssueAddedEvent convert(AddIssueCommand command) {
        if ( command == null ) {
            return null;
        }

        IssueAddedEvent issueAddedEvent = new IssueAddedEvent();

        issueAddedEvent.setFinder( command.getTransactor() );
        issueAddedEvent.setCode( command.getAggregateId() );
        issueAddedEvent.setAggregateId( command.getAggregateId() );
        issueAddedEvent.setTransactor( command.getTransactor() );
        issueAddedEvent.setTitle( command.getTitle() );
        issueAddedEvent.setDescription( command.getDescription() );
        issueAddedEvent.setStatus( command.getStatus() );
        issueAddedEvent.setFindEnv( command.getFindEnv() );
        issueAddedEvent.setFindStage( command.getFindStage() );
        issueAddedEvent.setPriority( command.getPriority() );
        issueAddedEvent.setRepetitionRate( command.getRepetitionRate() );
        issueAddedEvent.setRootCause( command.getRootCause() );
        issueAddedEvent.setTestMethod( command.getTestMethod() );
        issueAddedEvent.setType( command.getType() );
        issueAddedEvent.setRequirementLevel( command.getRequirementLevel() );
        issueAddedEvent.setHandler( command.getHandler() );
        issueAddedEvent.setDeveloper( command.getDeveloper() );
        issueAddedEvent.setTester( command.getTester() );
        issueAddedEvent.setProduct( command.getProduct() );
        issueAddedEvent.setRequirement( requirementToRequirement( command.getRequirement() ) );
        issueAddedEvent.setFindVersion( command.getFindVersion() );
        issueAddedEvent.setSprint( sprintToSprint( command.getSprint() ) );
        issueAddedEvent.setFixVersion( versionToVersion( command.getFixVersion() ) );
        issueAddedEvent.setVersionConfirm( command.getVersionConfirm() );
        List<AttachmentVO> list = command.getAttachments();
        if ( list != null ) {
            issueAddedEvent.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<TagVO> list1 = command.getTags();
        if ( list1 != null ) {
            issueAddedEvent.setTags( new ArrayList<TagVO>( list1 ) );
        }
        issueAddedEvent.setApplicationType( command.getApplicationType() );
        issueAddedEvent.setMsgCode( command.getMsgCode() );

        issueAddedEvent.setOccurred( new java.util.Date() );

        return issueAddedEvent;
    }

    @Override
    public IssueAddedEvent convert(AddVersionIssueCommand command) {
        if ( command == null ) {
            return null;
        }

        IssueAddedEvent issueAddedEvent = new IssueAddedEvent();

        issueAddedEvent.setFinder( command.getTransactor() );
        issueAddedEvent.setTester( command.getTransactor() );
        issueAddedEvent.setCode( command.getAggregateId() );
        issueAddedEvent.setAggregateId( command.getAggregateId() );
        issueAddedEvent.setTransactor( command.getTransactor() );
        issueAddedEvent.setTitle( command.getTitle() );
        issueAddedEvent.setDescription( command.getDescription() );
        issueAddedEvent.setStatus( command.getStatus() );
        issueAddedEvent.setFindEnv( command.getFindEnv() );
        issueAddedEvent.setFindStage( command.getFindStage() );
        issueAddedEvent.setPriority( command.getPriority() );
        issueAddedEvent.setRepetitionRate( command.getRepetitionRate() );
        issueAddedEvent.setRootCause( command.getRootCause() );
        issueAddedEvent.setTestMethod( command.getTestMethod() );
        issueAddedEvent.setType( command.getType() );
        issueAddedEvent.setRequirementLevel( command.getRequirementLevel() );
        issueAddedEvent.setHandler( command.getHandler() );
        issueAddedEvent.setDeveloper( command.getDeveloper() );
        issueAddedEvent.setProduct( command.getProduct() );
        issueAddedEvent.setRequirement( requirementToRequirement1( command.getRequirement() ) );
        issueAddedEvent.setFindVersion( command.getFindVersion() );
        issueAddedEvent.setSprint( sprintToSprint1( command.getSprint() ) );
        issueAddedEvent.setFixVersion( versionToVersion1( command.getFixVersion() ) );
        issueAddedEvent.setVersionConfirm( command.getVersionConfirm() );
        List<AttachmentVO> list = command.getAttachments();
        if ( list != null ) {
            issueAddedEvent.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<TagVO> list1 = command.getTags();
        if ( list1 != null ) {
            issueAddedEvent.setTags( new ArrayList<TagVO>( list1 ) );
        }

        issueAddedEvent.setOccurred( new java.util.Date() );

        return issueAddedEvent;
    }

    protected Requirement requirementToRequirement(Requirement requirement) {
        if ( requirement == null ) {
            return null;
        }

        Requirement requirement1 = new Requirement();

        requirement1.setName( requirement.getName() );

        requirement1.setCode( (requirement!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(requirement.getCode())) ?requirement.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.REQUIREMENT_VALUE.getValue() );

        return requirement1;
    }

    protected Sprint sprintToSprint(Sprint sprint) {
        if ( sprint == null ) {
            return null;
        }

        Sprint sprint1 = new Sprint();

        sprint1.setName( sprint.getName() );

        sprint1.setCode( (sprint!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(sprint.getCode())) ?sprint.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.SPRINT_VALUE.getValue() );

        return sprint1;
    }

    protected Version versionToVersion(Version version) {
        if ( version == null ) {
            return null;
        }

        Version version1 = new Version();

        version1.setName( version.getName() );

        version1.setCode( (version!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(version.getCode())) ?version.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.getValue() );

        return version1;
    }

    protected Requirement requirementToRequirement1(Requirement requirement) {
        if ( requirement == null ) {
            return null;
        }

        Requirement requirement1 = new Requirement();

        requirement1.setName( requirement.getName() );

        requirement1.setCode( (requirement!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(requirement.getCode())) ?requirement.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.REQUIREMENT_VALUE.getValue() );

        return requirement1;
    }

    protected Sprint sprintToSprint1(Sprint sprint) {
        if ( sprint == null ) {
            return null;
        }

        Sprint sprint1 = new Sprint();

        sprint1.setName( sprint.getName() );

        sprint1.setCode( (sprint!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(sprint.getCode())) ?sprint.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.SPRINT_VALUE.getValue() );

        return sprint1;
    }

    protected Version versionToVersion1(Version version) {
        if ( version == null ) {
            return null;
        }

        Version version1 = new Version();

        version1.setName( version.getName() );

        version1.setCode( (version!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(version.getCode())) ?version.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.getValue() );

        return version1;
    }
}
