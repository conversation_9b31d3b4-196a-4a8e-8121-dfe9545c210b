package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseSourceTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTagEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.dto.ApiTestCaseEntityDO;
import com.zto.devops.qc.client.model.dto.ApiTestEntityDO;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddApiTestCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddApiTestVariableCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddPreDataModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddSceneModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.ApiTestRevokeCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.BatchMoveSceneCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.DbAuthorizeCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.DeleteApiTestVariableCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditApiTestCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditApiTestVariableCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditPreDataInfoCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditPreDataModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditSceneInfoCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditSceneModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.MovePreDataModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.MoveSceneModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.PublishApiTestCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SharePreDataCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.UpdateApiTestVariableStatusCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiSampleCaseVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeDbVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeProductVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.QueryPreDataVariableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneIndexVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneModuleQueryVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneTagVO;
import com.zto.devops.qc.client.model.testmanager.apitest.event.AddApiTestCaseEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.AddApiTestVariableEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.AddPreDataModuleEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.AddSceneModuleEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.ApiTestRevokeEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.BatchMoveSceneEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.ChangeSceneStatusEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.DbAuthorizeEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.DeleteApiTestVariableEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.EditApiTestCaseEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.EditApiTestVariableEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.EditPreDataModuleEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.EditSceneModuleEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.MovePreDataModuleEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.MoveSceneModuleEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.PreDataSharedEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.PublishApiTestCaseEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.SceneInfoPublishedEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.UpdateApiTestVariableStatusEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.query.ApiTestCaseExecuteDetailQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiCaseQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiExceptionCaseQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiTestCaseChildrenQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiTestCaseQuery;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ApiCaseDetailResp;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BaseApiTestVariableReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BaseApiTestVariableResp;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiCaseReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiInfoResp;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiTestCaseChildrenReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiTestCaseReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.QueryApiCaseExceptionReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneInfoResp;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneModuleQueryResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.ApiTestCaseExecuteDetailReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class ApiTestDomainConverterImpl implements ApiTestDomainConverter {

    @Override
    public AddApiTestVariableEvent convert(AddApiTestVariableCommand command) {
        if ( command == null ) {
            return null;
        }

        AddApiTestVariableEvent addApiTestVariableEvent = new AddApiTestVariableEvent();

        addApiTestVariableEvent.setVariableCode( command.getAggregateId() );
        addApiTestVariableEvent.setAggregateId( command.getAggregateId() );
        addApiTestVariableEvent.setTransactor( command.getTransactor() );
        addApiTestVariableEvent.setOccurred( command.getOccurred() );
        addApiTestVariableEvent.setProductCode( command.getProductCode() );
        addApiTestVariableEvent.setLinkCode( command.getLinkCode() );
        addApiTestVariableEvent.setVariableName( command.getVariableName() );
        addApiTestVariableEvent.setVariableKey( command.getVariableKey() );
        addApiTestVariableEvent.setVariableValue( command.getVariableValue() );
        addApiTestVariableEvent.setType( command.getType() );
        addApiTestVariableEvent.setVariableStatus( command.getVariableStatus() );
        addApiTestVariableEvent.setSceneType( command.getSceneType() );
        addApiTestVariableEvent.setSubVariableType( command.getSubVariableType() );
        addApiTestVariableEvent.setLoginValidTime( command.getLoginValidTime() );

        return addApiTestVariableEvent;
    }

    @Override
    public EditApiTestVariableEvent convert(EditApiTestVariableCommand command) {
        if ( command == null ) {
            return null;
        }

        EditApiTestVariableEvent editApiTestVariableEvent = new EditApiTestVariableEvent();

        editApiTestVariableEvent.setAggregateId( command.getAggregateId() );
        editApiTestVariableEvent.setTransactor( command.getTransactor() );
        editApiTestVariableEvent.setOccurred( command.getOccurred() );
        editApiTestVariableEvent.setProductCode( command.getProductCode() );
        editApiTestVariableEvent.setProductName( command.getProductName() );
        editApiTestVariableEvent.setLinkCode( command.getLinkCode() );
        editApiTestVariableEvent.setVariableCode( command.getVariableCode() );
        editApiTestVariableEvent.setVariableName( command.getVariableName() );
        editApiTestVariableEvent.setVariableKey( command.getVariableKey() );
        editApiTestVariableEvent.setVariableValue( command.getVariableValue() );
        editApiTestVariableEvent.setType( command.getType() );
        editApiTestVariableEvent.setVariableStatus( command.getVariableStatus() );
        editApiTestVariableEvent.setSubVariableType( command.getSubVariableType() );
        editApiTestVariableEvent.setLoginValidTime( command.getLoginValidTime() );

        return editApiTestVariableEvent;
    }

    @Override
    public UpdateApiTestVariableStatusEvent convert(UpdateApiTestVariableStatusCommand command) {
        if ( command == null ) {
            return null;
        }

        UpdateApiTestVariableStatusEvent updateApiTestVariableStatusEvent = new UpdateApiTestVariableStatusEvent();

        updateApiTestVariableStatusEvent.setAggregateId( command.getAggregateId() );
        updateApiTestVariableStatusEvent.setTransactor( command.getTransactor() );
        updateApiTestVariableStatusEvent.setOccurred( command.getOccurred() );
        updateApiTestVariableStatusEvent.setVariableCode( command.getVariableCode() );
        updateApiTestVariableStatusEvent.setVariableStatus( command.getVariableStatus() );

        return updateApiTestVariableStatusEvent;
    }

    @Override
    public DeleteApiTestVariableEvent convert(DeleteApiTestVariableCommand command) {
        if ( command == null ) {
            return null;
        }

        DeleteApiTestVariableEvent deleteApiTestVariableEvent = new DeleteApiTestVariableEvent();

        deleteApiTestVariableEvent.setVariableCode( command.getAggregateId() );
        deleteApiTestVariableEvent.setAggregateId( command.getAggregateId() );
        deleteApiTestVariableEvent.setTransactor( command.getTransactor() );
        deleteApiTestVariableEvent.setOccurred( command.getOccurred() );

        return deleteApiTestVariableEvent;
    }

    @Override
    public PageApiCaseQuery convert(PageApiCaseReq req) {
        if ( req == null ) {
            return null;
        }

        PageApiCaseQuery pageApiCaseQuery = new PageApiCaseQuery();

        pageApiCaseQuery.setTransactor( req.getTransactor() );
        pageApiCaseQuery.setPage( req.getPage() );
        pageApiCaseQuery.setSize( req.getSize() );
        List<Integer> list = req.getEnableNumList();
        if ( list != null ) {
            pageApiCaseQuery.setEnableNumList( new ArrayList<Integer>( list ) );
        }
        pageApiCaseQuery.setProductCode( req.getProductCode() );
        List<String> list1 = req.getApiNameList();
        if ( list1 != null ) {
            pageApiCaseQuery.setApiNameList( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = req.getApiAddressList();
        if ( list2 != null ) {
            pageApiCaseQuery.setApiAddressList( new ArrayList<String>( list2 ) );
        }
        pageApiCaseQuery.setApiCode( req.getApiCode() );
        List<ApiCaseSourceTypeEnum> list3 = req.getTypeList();
        if ( list3 != null ) {
            pageApiCaseQuery.setTypeList( new ArrayList<ApiCaseSourceTypeEnum>( list3 ) );
        }
        List<TestPlanCaseStatusEnum> list4 = req.getTestResultList();
        if ( list4 != null ) {
            pageApiCaseQuery.setTestResultList( new ArrayList<TestPlanCaseStatusEnum>( list4 ) );
        }
        pageApiCaseQuery.setCaseName( req.getCaseName() );
        pageApiCaseQuery.setStartTime( req.getStartTime() );
        pageApiCaseQuery.setEndTime( req.getEndTime() );
        List<String> list5 = req.getDocVersionList();
        if ( list5 != null ) {
            pageApiCaseQuery.setDocVersionList( new ArrayList<String>( list5 ) );
        }

        return pageApiCaseQuery;
    }

    @Override
    public SceneInfoPublishedEvent convert(SceneInfoEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneInfoPublishedEvent sceneInfoPublishedEvent = new SceneInfoPublishedEvent();

        sceneInfoPublishedEvent.setTransactor( sceneInfoEntityDOToUser( entityDO ) );
        sceneInfoPublishedEvent.setOccurred( entityDO.getGmtModified() );
        sceneInfoPublishedEvent.setSceneCode( entityDO.getSceneCode() );
        sceneInfoPublishedEvent.setSceneVersion( entityDO.getSceneVersion() );
        sceneInfoPublishedEvent.setSceneType( entityDO.getSceneType() );

        return sceneInfoPublishedEvent;
    }

    @Override
    public SceneInfoResp convert2SceneInfoResp(SceneInfoEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneInfoResp sceneInfoResp = new SceneInfoResp();

        sceneInfoResp.setId( entityDO.getId() );
        sceneInfoResp.setProductCode( entityDO.getProductCode() );
        sceneInfoResp.setSceneCode( entityDO.getSceneCode() );
        sceneInfoResp.setSceneName( entityDO.getSceneName() );
        sceneInfoResp.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        if ( entityDO.getSceneVersion() != null ) {
            sceneInfoResp.setSceneVersion( String.valueOf( entityDO.getSceneVersion() ) );
        }
        sceneInfoResp.setSceneInfoDesc( entityDO.getSceneInfoDesc() );
        sceneInfoResp.setSceneOssPath( entityDO.getSceneOssPath() );
        sceneInfoResp.setSceneOssFile( entityDO.getSceneOssFile() );
        sceneInfoResp.setStepRecord( entityDO.getStepRecord() );
        sceneInfoResp.setStatus( entityDO.getStatus() );
        sceneInfoResp.setEnable( entityDO.getEnable() );
        sceneInfoResp.setModifier( entityDO.getModifier() );
        sceneInfoResp.setGmtModified( entityDO.getGmtModified() );
        sceneInfoResp.setEnableDesc( entityDO.getEnableDesc() );
        sceneInfoResp.setIsModified( entityDO.getIsModified() );
        sceneInfoResp.setPublishErrorMsg( entityDO.getPublishErrorMsg() );
        sceneInfoResp.setModifierId( entityDO.getModifierId() );
        sceneInfoResp.setSceneFrontData( entityDO.getSceneFrontData() );
        sceneInfoResp.setSceneIndexType( entityDO.getSceneIndexType() );
        sceneInfoResp.setParentCode( entityDO.getParentCode() );
        sceneInfoResp.setCreator( entityDO.getCreator() );
        List<SceneTagVO> list = entityDO.getTagVOList();
        if ( list != null ) {
            sceneInfoResp.setTagVOList( new ArrayList<SceneTagVO>( list ) );
        }
        List<Button> list1 = entityDO.getButtonList();
        if ( list1 != null ) {
            sceneInfoResp.setButtonList( new ArrayList<Button>( list1 ) );
        }
        List<Button> list2 = entityDO.getCollapseButtonList();
        if ( list2 != null ) {
            sceneInfoResp.setCollapseButtonList( new ArrayList<Button>( list2 ) );
        }
        sceneInfoResp.setIsCheck( entityDO.getIsCheck() );
        sceneInfoResp.setShareStatus( entityDO.getShareStatus() );
        sceneInfoResp.setScenePath( entityDO.getScenePath() );

        return sceneInfoResp;
    }

    @Override
    public SceneInfoEntityDO copyConvert(SceneInfoEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneInfoEntityDO sceneInfoEntityDO = new SceneInfoEntityDO();

        sceneInfoEntityDO.setId( entityDO.getId() );
        sceneInfoEntityDO.setSceneCode( entityDO.getSceneCode() );
        sceneInfoEntityDO.setSceneName( entityDO.getSceneName() );
        sceneInfoEntityDO.setProductCode( entityDO.getProductCode() );
        sceneInfoEntityDO.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        sceneInfoEntityDO.setSceneVersion( entityDO.getSceneVersion() );
        sceneInfoEntityDO.setSceneInfoDesc( entityDO.getSceneInfoDesc() );
        sceneInfoEntityDO.setSceneOssPath( entityDO.getSceneOssPath() );
        sceneInfoEntityDO.setSceneOssFile( entityDO.getSceneOssFile() );
        sceneInfoEntityDO.setSceneBackDataMd5( entityDO.getSceneBackDataMd5() );
        sceneInfoEntityDO.setStatus( entityDO.getStatus() );
        sceneInfoEntityDO.setEnable( entityDO.getEnable() );
        sceneInfoEntityDO.setEnableDesc( entityDO.getEnableDesc() );
        sceneInfoEntityDO.setSceneType( entityDO.getSceneType() );
        sceneInfoEntityDO.setStepRecord( entityDO.getStepRecord() );
        sceneInfoEntityDO.setIsModified( entityDO.getIsModified() );
        sceneInfoEntityDO.setPublishErrorMsg( entityDO.getPublishErrorMsg() );
        sceneInfoEntityDO.setCreatorId( entityDO.getCreatorId() );
        sceneInfoEntityDO.setCreator( entityDO.getCreator() );
        sceneInfoEntityDO.setGmtCreate( entityDO.getGmtCreate() );
        sceneInfoEntityDO.setModifierId( entityDO.getModifierId() );
        sceneInfoEntityDO.setModifier( entityDO.getModifier() );
        sceneInfoEntityDO.setGmtModified( entityDO.getGmtModified() );
        sceneInfoEntityDO.setSceneFrontData( entityDO.getSceneFrontData() );
        sceneInfoEntityDO.setSceneBackData( entityDO.getSceneBackData() );
        sceneInfoEntityDO.setSceneIndexType( entityDO.getSceneIndexType() );
        sceneInfoEntityDO.setParentCode( entityDO.getParentCode() );
        sceneInfoEntityDO.setParentName( entityDO.getParentName() );
        sceneInfoEntityDO.setTagName( entityDO.getTagName() );
        List<SceneTagVO> list = entityDO.getTagVOList();
        if ( list != null ) {
            sceneInfoEntityDO.setTagVOList( new ArrayList<SceneTagVO>( list ) );
        }
        List<Button> list1 = entityDO.getButtonList();
        if ( list1 != null ) {
            sceneInfoEntityDO.setButtonList( new ArrayList<Button>( list1 ) );
        }
        List<Button> list2 = entityDO.getCollapseButtonList();
        if ( list2 != null ) {
            sceneInfoEntityDO.setCollapseButtonList( new ArrayList<Button>( list2 ) );
        }
        sceneInfoEntityDO.setIsCheck( entityDO.getIsCheck() );
        sceneInfoEntityDO.setShareStatus( entityDO.getShareStatus() );
        sceneInfoEntityDO.setIsCollect( entityDO.getIsCollect() );
        sceneInfoEntityDO.setSceneTagData( entityDO.getSceneTagData() );
        sceneInfoEntityDO.setScenePath( entityDO.getScenePath() );

        return sceneInfoEntityDO;
    }

    @Override
    public ChangeSceneStatusEvent convertToChangeStatusEvent(SceneInfoEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        ChangeSceneStatusEvent changeSceneStatusEvent = new ChangeSceneStatusEvent();

        changeSceneStatusEvent.setTransactor( sceneInfoEntityDOToUser1( entityDO ) );
        changeSceneStatusEvent.setOccurred( entityDO.getGmtModified() );
        changeSceneStatusEvent.setSceneCode( entityDO.getSceneCode() );
        changeSceneStatusEvent.setEnable( entityDO.getEnable() );

        return changeSceneStatusEvent;
    }

    @Override
    public SceneModuleQueryResp converter(SceneModuleQueryVO vo) {
        if ( vo == null ) {
            return null;
        }

        SceneModuleQueryResp sceneModuleQueryResp = new SceneModuleQueryResp();

        sceneModuleQueryResp.setId( vo.getId() );
        sceneModuleQueryResp.setCode( vo.getCode() );
        sceneModuleQueryResp.setName( vo.getName() );
        sceneModuleQueryResp.setDesc( vo.getDesc() );
        sceneModuleQueryResp.setSceneCount( vo.getSceneCount() );
        sceneModuleQueryResp.setSceneEnable( vo.getSceneEnable() );
        sceneModuleQueryResp.setSceneEnableDesc( vo.getSceneEnableDesc() );
        sceneModuleQueryResp.setType( vo.getType() );
        sceneModuleQueryResp.setParentCode( vo.getParentCode() );
        List<SceneModuleQueryVO> list = vo.getChildren();
        if ( list != null ) {
            sceneModuleQueryResp.setChildren( new ArrayList<SceneModuleQueryVO>( list ) );
        }
        sceneModuleQueryResp.setIsLeaf( vo.getIsLeaf() );
        sceneModuleQueryResp.setShareStatus( vo.getShareStatus() );
        List<Button> list1 = vo.getPermissionList();
        if ( list1 != null ) {
            sceneModuleQueryResp.setPermissionList( new ArrayList<Button>( list1 ) );
        }

        return sceneModuleQueryResp;
    }

    @Override
    public List<SceneModuleQueryResp> converterList(List<SceneModuleQueryVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<SceneModuleQueryResp> list = new ArrayList<SceneModuleQueryResp>( vo.size() );
        for ( SceneModuleQueryVO sceneModuleQueryVO : vo ) {
            list.add( converter( sceneModuleQueryVO ) );
        }

        return list;
    }

    @Override
    public AddSceneModuleEvent convert(AddSceneModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        AddSceneModuleEvent addSceneModuleEvent = new AddSceneModuleEvent();

        addSceneModuleEvent.setAggregateId( command.getAggregateId() );
        addSceneModuleEvent.setTransactor( command.getTransactor() );
        addSceneModuleEvent.setOccurred( command.getOccurred() );
        addSceneModuleEvent.setProductCode( command.getProductCode() );
        addSceneModuleEvent.setParentCode( command.getParentCode() );
        addSceneModuleEvent.setSceneIndexName( command.getSceneIndexName() );
        addSceneModuleEvent.setSceneType( command.getSceneType() );

        return addSceneModuleEvent;
    }

    @Override
    public AddPreDataModuleEvent convert(AddPreDataModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        AddPreDataModuleEvent addPreDataModuleEvent = new AddPreDataModuleEvent();

        addPreDataModuleEvent.setAggregateId( command.getAggregateId() );
        addPreDataModuleEvent.setTransactor( command.getTransactor() );
        addPreDataModuleEvent.setOccurred( command.getOccurred() );
        addPreDataModuleEvent.setProductCode( command.getProductCode() );
        addPreDataModuleEvent.setParentCode( command.getParentCode() );
        addPreDataModuleEvent.setSceneIndexName( command.getSceneIndexName() );
        addPreDataModuleEvent.setSceneType( command.getSceneType() );

        return addPreDataModuleEvent;
    }

    @Override
    public SceneIndexVO convert(AddSceneModuleEvent event) {
        if ( event == null ) {
            return null;
        }

        SceneIndexVO sceneIndexVO = new SceneIndexVO();

        sceneIndexVO.setGmtCreate( event.getOccurred() );
        sceneIndexVO.setGmtModified( event.getOccurred() );
        sceneIndexVO.setCreator( eventTransactorUserName( event ) );
        sceneIndexVO.setCreatorId( eventTransactorUserId( event ) );
        sceneIndexVO.setModifierId( eventTransactorUserId( event ) );
        sceneIndexVO.setModifier( eventTransactorUserName( event ) );
        sceneIndexVO.setSceneIndexCode( event.getAggregateId() );
        sceneIndexVO.setSceneIndexName( event.getSceneIndexName() );
        sceneIndexVO.setProductCode( event.getProductCode() );
        sceneIndexVO.setParentCode( event.getParentCode() );

        sceneIndexVO.setSceneType( null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode() );

        return sceneIndexVO;
    }

    @Override
    public SceneIndexVO convert(AddPreDataModuleEvent event) {
        if ( event == null ) {
            return null;
        }

        SceneIndexVO sceneIndexVO = new SceneIndexVO();

        sceneIndexVO.setGmtCreate( event.getOccurred() );
        sceneIndexVO.setGmtModified( event.getOccurred() );
        sceneIndexVO.setCreator( eventTransactorUserName1( event ) );
        sceneIndexVO.setCreatorId( eventTransactorUserId1( event ) );
        sceneIndexVO.setModifierId( eventTransactorUserId1( event ) );
        sceneIndexVO.setModifier( eventTransactorUserName1( event ) );
        sceneIndexVO.setSceneIndexCode( event.getAggregateId() );
        sceneIndexVO.setSceneIndexName( event.getSceneIndexName() );
        sceneIndexVO.setProductCode( event.getProductCode() );
        sceneIndexVO.setParentCode( event.getParentCode() );

        sceneIndexVO.setSceneType( null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode() );

        return sceneIndexVO;
    }

    @Override
    public EditSceneModuleEvent convert(EditSceneModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        EditSceneModuleEvent editSceneModuleEvent = new EditSceneModuleEvent();

        editSceneModuleEvent.setAggregateId( command.getAggregateId() );
        editSceneModuleEvent.setTransactor( command.getTransactor() );
        editSceneModuleEvent.setOccurred( command.getOccurred() );
        editSceneModuleEvent.setProductCode( command.getProductCode() );
        editSceneModuleEvent.setCode( command.getCode() );
        editSceneModuleEvent.setSceneIndexName( command.getSceneIndexName() );
        editSceneModuleEvent.setSceneType( command.getSceneType() );

        return editSceneModuleEvent;
    }

    @Override
    public EditPreDataModuleEvent convert(EditPreDataModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        EditPreDataModuleEvent editPreDataModuleEvent = new EditPreDataModuleEvent();

        editPreDataModuleEvent.setAggregateId( command.getAggregateId() );
        editPreDataModuleEvent.setTransactor( command.getTransactor() );
        editPreDataModuleEvent.setOccurred( command.getOccurred() );
        editPreDataModuleEvent.setProductCode( command.getProductCode() );
        editPreDataModuleEvent.setCode( command.getCode() );
        editPreDataModuleEvent.setSceneIndexName( command.getSceneIndexName() );
        editPreDataModuleEvent.setSceneType( command.getSceneType() );

        return editPreDataModuleEvent;
    }

    @Override
    public SceneIndexVO convert(EditSceneModuleEvent event) {
        if ( event == null ) {
            return null;
        }

        SceneIndexVO sceneIndexVO = new SceneIndexVO();

        sceneIndexVO.setGmtModified( event.getOccurred() );
        sceneIndexVO.setModifierId( eventTransactorUserId2( event ) );
        sceneIndexVO.setModifier( eventTransactorUserName2( event ) );
        sceneIndexVO.setSceneIndexCode( event.getCode() );
        sceneIndexVO.setSceneIndexName( event.getSceneIndexName() );
        sceneIndexVO.setProductCode( event.getProductCode() );

        sceneIndexVO.setSceneType( null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode() );

        return sceneIndexVO;
    }

    @Override
    public SceneIndexVO convert(EditPreDataModuleEvent event) {
        if ( event == null ) {
            return null;
        }

        SceneIndexVO sceneIndexVO = new SceneIndexVO();

        sceneIndexVO.setGmtModified( event.getOccurred() );
        sceneIndexVO.setModifierId( eventTransactorUserId3( event ) );
        sceneIndexVO.setModifier( eventTransactorUserName3( event ) );
        sceneIndexVO.setSceneIndexCode( event.getCode() );
        sceneIndexVO.setSceneIndexName( event.getSceneIndexName() );
        sceneIndexVO.setProductCode( event.getProductCode() );

        sceneIndexVO.setSceneType( null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode() );

        return sceneIndexVO;
    }

    @Override
    public MoveSceneModuleEvent convert(MoveSceneModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        MoveSceneModuleEvent moveSceneModuleEvent = new MoveSceneModuleEvent();

        moveSceneModuleEvent.setAggregateId( command.getAggregateId() );
        moveSceneModuleEvent.setTransactor( command.getTransactor() );
        moveSceneModuleEvent.setOccurred( command.getOccurred() );
        moveSceneModuleEvent.setProductCode( command.getProductCode() );
        moveSceneModuleEvent.setCode( command.getCode() );
        moveSceneModuleEvent.setParentCode( command.getParentCode() );
        moveSceneModuleEvent.setSceneType( command.getSceneType() );

        return moveSceneModuleEvent;
    }

    @Override
    public MovePreDataModuleEvent convert(MovePreDataModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        MovePreDataModuleEvent movePreDataModuleEvent = new MovePreDataModuleEvent();

        movePreDataModuleEvent.setAggregateId( command.getAggregateId() );
        movePreDataModuleEvent.setTransactor( command.getTransactor() );
        movePreDataModuleEvent.setOccurred( command.getOccurred() );
        movePreDataModuleEvent.setProductCode( command.getProductCode() );
        movePreDataModuleEvent.setCode( command.getCode() );
        movePreDataModuleEvent.setParentCode( command.getParentCode() );
        movePreDataModuleEvent.setSceneType( command.getSceneType() );

        return movePreDataModuleEvent;
    }

    @Override
    public BatchMoveSceneEvent convert(BatchMoveSceneCommand command) {
        if ( command == null ) {
            return null;
        }

        BatchMoveSceneEvent batchMoveSceneEvent = new BatchMoveSceneEvent();

        batchMoveSceneEvent.setAggregateId( command.getAggregateId() );
        batchMoveSceneEvent.setTransactor( command.getTransactor() );
        batchMoveSceneEvent.setOccurred( command.getOccurred() );
        batchMoveSceneEvent.setProductCode( command.getProductCode() );
        List<String> list = command.getSceneCodeList();
        if ( list != null ) {
            batchMoveSceneEvent.setSceneCodeList( new ArrayList<String>( list ) );
        }
        batchMoveSceneEvent.setParentCode( command.getParentCode() );

        return batchMoveSceneEvent;
    }

    @Override
    public SceneIndexVO convert(MoveSceneModuleEvent event) {
        if ( event == null ) {
            return null;
        }

        SceneIndexVO sceneIndexVO = new SceneIndexVO();

        sceneIndexVO.setGmtModified( event.getOccurred() );
        sceneIndexVO.setModifierId( eventTransactorUserId4( event ) );
        sceneIndexVO.setModifier( eventTransactorUserName4( event ) );
        sceneIndexVO.setSceneIndexCode( event.getCode() );
        sceneIndexVO.setProductCode( event.getProductCode() );
        sceneIndexVO.setParentCode( event.getParentCode() );
        sceneIndexVO.setSceneIndexType( event.getSceneIndexType() );
        sceneIndexVO.setTestcaseCode( event.getTestcaseCode() );

        sceneIndexVO.setSceneType( com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) );

        return sceneIndexVO;
    }

    @Override
    public SceneIndexVO convert(MovePreDataModuleEvent event) {
        if ( event == null ) {
            return null;
        }

        SceneIndexVO sceneIndexVO = new SceneIndexVO();

        sceneIndexVO.setGmtModified( event.getOccurred() );
        sceneIndexVO.setModifierId( eventTransactorUserId5( event ) );
        sceneIndexVO.setModifier( eventTransactorUserName5( event ) );
        sceneIndexVO.setSceneIndexCode( event.getCode() );
        sceneIndexVO.setProductCode( event.getProductCode() );
        sceneIndexVO.setParentCode( event.getParentCode() );
        sceneIndexVO.setSceneIndexType( event.getSceneIndexType() );
        sceneIndexVO.setTestcaseCode( event.getTestcaseCode() );

        sceneIndexVO.setSceneType( null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode() );

        return sceneIndexVO;
    }

    @Override
    public MoveSceneModuleCommand convert(EditSceneInfoCommand command) {
        if ( command == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = command.getAggregateId();

        MoveSceneModuleCommand moveSceneModuleCommand = new MoveSceneModuleCommand( aggregateId );

        moveSceneModuleCommand.setCode( command.getAggregateId() );
        moveSceneModuleCommand.setTransactor( command.getTransactor() );
        moveSceneModuleCommand.setProductCode( command.getProductCode() );
        moveSceneModuleCommand.setParentCode( command.getParentCode() );
        moveSceneModuleCommand.setSceneType( command.getSceneType() );

        return moveSceneModuleCommand;
    }

    @Override
    public MovePreDataModuleCommand convert(EditPreDataInfoCommand command) {
        if ( command == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = command.getAggregateId();

        MovePreDataModuleCommand movePreDataModuleCommand = new MovePreDataModuleCommand( aggregateId );

        movePreDataModuleCommand.setCode( command.getAggregateId() );
        movePreDataModuleCommand.setTransactor( command.getTransactor() );
        movePreDataModuleCommand.setProductCode( command.getProductCode() );
        movePreDataModuleCommand.setParentCode( command.getParentCode() );
        movePreDataModuleCommand.setSceneType( command.getSceneType() );

        return movePreDataModuleCommand;
    }

    @Override
    public EditSceneInfoCommand convertEditSceneInfo(EditSceneModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = command.getCode();

        EditSceneInfoCommand editSceneInfoCommand = new EditSceneInfoCommand( aggregateId );

        editSceneInfoCommand.setSceneName( command.getSceneIndexName() );
        editSceneInfoCommand.setTransactor( command.getTransactor() );
        editSceneInfoCommand.setProductCode( command.getProductCode() );
        editSceneInfoCommand.setSceneType( command.getSceneType() );

        return editSceneInfoCommand;
    }

    @Override
    public EditPreDataInfoCommand convertEditPreDataInfo(EditPreDataModuleCommand command) {
        if ( command == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = command.getCode();

        EditPreDataInfoCommand editPreDataInfoCommand = new EditPreDataInfoCommand( aggregateId );

        editPreDataInfoCommand.setSceneName( command.getSceneIndexName() );
        editPreDataInfoCommand.setTransactor( command.getTransactor() );
        editPreDataInfoCommand.setProductCode( command.getProductCode() );
        editPreDataInfoCommand.setSceneType( command.getSceneType() );

        return editPreDataInfoCommand;
    }

    @Override
    public List<BaseApiTestVariableResp> converter(List<ApiTestVariableVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<BaseApiTestVariableResp> list = new ArrayList<BaseApiTestVariableResp>( vo.size() );
        for ( ApiTestVariableVO apiTestVariableVO : vo ) {
            list.add( apiTestVariableVOToBaseApiTestVariableResp( apiTestVariableVO ) );
        }

        return list;
    }

    @Override
    public List<QueryPreDataVariableVO> convertVOList(List<ApiTestVariableVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<QueryPreDataVariableVO> list = new ArrayList<QueryPreDataVariableVO>( vo.size() );
        for ( ApiTestVariableVO apiTestVariableVO : vo ) {
            list.add( apiTestVariableVOToQueryPreDataVariableVO( apiTestVariableVO ) );
        }

        return list;
    }

    @Override
    public PageApiInfoResp convert(ApiSampleCaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        PageApiInfoResp pageApiInfoResp = new PageApiInfoResp();

        pageApiInfoResp.setApiCode( vo.getApiCode() );
        pageApiInfoResp.setApiName( vo.getApiName() );
        pageApiInfoResp.setReqMethod( vo.getReqMethod() );
        pageApiInfoResp.setDocProductCode( vo.getDocProductCode() );
        pageApiInfoResp.setDocId( vo.getDocId() );
        pageApiInfoResp.setAppId( vo.getAppId() );
        pageApiInfoResp.setProductCode( vo.getProductCode() );
        pageApiInfoResp.setApiType( vo.getApiType() );
        pageApiInfoResp.setDocVersion( vo.getDocVersion() );

        return pageApiInfoResp;
    }

    @Override
    public PreDataSharedEvent convert(SharePreDataCommand command) {
        if ( command == null ) {
            return null;
        }

        PreDataSharedEvent preDataSharedEvent = new PreDataSharedEvent();

        preDataSharedEvent.setSceneCode( command.getAggregateId() );
        preDataSharedEvent.setAggregateId( command.getAggregateId() );
        preDataSharedEvent.setTransactor( command.getTransactor() );
        preDataSharedEvent.setOccurred( command.getOccurred() );
        if ( command.getShareStatus() != null ) {
            preDataSharedEvent.setShareStatus( command.getShareStatus() );
        }

        return preDataSharedEvent;
    }

    @Override
    public List<BaseApiTestVariableReq> convertList(List<BaseApiTestVariableResp> list) {
        if ( list == null ) {
            return null;
        }

        List<BaseApiTestVariableReq> list1 = new ArrayList<BaseApiTestVariableReq>( list.size() );
        for ( BaseApiTestVariableResp baseApiTestVariableResp : list ) {
            list1.add( baseApiTestVariableRespToBaseApiTestVariableReq( baseApiTestVariableResp ) );
        }

        return list1;
    }

    @Override
    public DbAuthorizeEvent convert(DbAuthorizeCommand command) {
        if ( command == null ) {
            return null;
        }

        DbAuthorizeEvent dbAuthorizeEvent = new DbAuthorizeEvent();

        dbAuthorizeEvent.setAggregateId( command.getAggregateId() );
        dbAuthorizeEvent.setTransactor( command.getTransactor() );
        dbAuthorizeEvent.setOccurred( command.getOccurred() );
        dbAuthorizeEvent.setProductCode( command.getProductCode() );
        List<ApiTestAuthorizeProductVO> list = command.getAuthorizeProductList();
        if ( list != null ) {
            dbAuthorizeEvent.setAuthorizeProductList( new ArrayList<ApiTestAuthorizeProductVO>( list ) );
        }
        List<ApiTestAuthorizeDbVO> list1 = command.getAuthorizeDbList();
        if ( list1 != null ) {
            dbAuthorizeEvent.setAuthorizeDbList( new ArrayList<ApiTestAuthorizeDbVO>( list1 ) );
        }

        return dbAuthorizeEvent;
    }

    @Override
    public ApiTestRevokeEvent convert(ApiTestRevokeCommand command) {
        if ( command == null ) {
            return null;
        }

        ApiTestRevokeEvent apiTestRevokeEvent = new ApiTestRevokeEvent();

        apiTestRevokeEvent.setAggregateId( command.getAggregateId() );
        apiTestRevokeEvent.setTransactor( command.getTransactor() );
        apiTestRevokeEvent.setOccurred( command.getOccurred() );
        apiTestRevokeEvent.setProductCode( command.getProductCode() );
        apiTestRevokeEvent.setAuthorizeProductCode( command.getAuthorizeProductCode() );

        return apiTestRevokeEvent;
    }

    @Override
    public AddApiTestCaseEvent convert(AddApiTestCaseCommand command) {
        if ( command == null ) {
            return null;
        }

        AddApiTestCaseEvent addApiTestCaseEvent = new AddApiTestCaseEvent();

        addApiTestCaseEvent.setCaseCode( command.getAggregateId() );
        addApiTestCaseEvent.setAggregateId( command.getAggregateId() );
        addApiTestCaseEvent.setTransactor( command.getTransactor() );
        addApiTestCaseEvent.setOccurred( command.getOccurred() );
        addApiTestCaseEvent.setProductCode( command.getProductCode() );
        addApiTestCaseEvent.setCaseName( command.getCaseName() );
        addApiTestCaseEvent.setApiCode( command.getApiCode() );
        addApiTestCaseEvent.setStatus( command.getStatus() );
        addApiTestCaseEvent.setCaseType( command.getCaseType() );
        addApiTestCaseEvent.setCaseReqData( command.getCaseReqData() );
        List<Integer> list = command.getDbIds();
        if ( list != null ) {
            addApiTestCaseEvent.setDbIds( new ArrayList<Integer>( list ) );
        }

        return addApiTestCaseEvent;
    }

    @Override
    public EditApiTestCaseEvent convert(EditApiTestCaseCommand command) {
        if ( command == null ) {
            return null;
        }

        EditApiTestCaseEvent editApiTestCaseEvent = new EditApiTestCaseEvent();

        editApiTestCaseEvent.setAggregateId( command.getCaseCode() );
        editApiTestCaseEvent.setTransactor( command.getTransactor() );
        editApiTestCaseEvent.setOccurred( command.getOccurred() );
        editApiTestCaseEvent.setProductCode( command.getProductCode() );
        editApiTestCaseEvent.setCaseCode( command.getCaseCode() );
        editApiTestCaseEvent.setCaseName( command.getCaseName() );
        editApiTestCaseEvent.setApiCode( command.getApiCode() );
        editApiTestCaseEvent.setStatus( command.getStatus() );
        editApiTestCaseEvent.setCaseType( command.getCaseType() );
        editApiTestCaseEvent.setCaseReqData( command.getCaseReqData() );
        List<Integer> list = command.getDbIds();
        if ( list != null ) {
            editApiTestCaseEvent.setDbIds( new ArrayList<Integer>( list ) );
        }

        return editApiTestCaseEvent;
    }

    @Override
    public PublishApiTestCaseEvent convert(PublishApiTestCaseCommand command) {
        if ( command == null ) {
            return null;
        }

        PublishApiTestCaseEvent publishApiTestCaseEvent = new PublishApiTestCaseEvent();

        publishApiTestCaseEvent.setAggregateId( command.getCaseCode() );
        publishApiTestCaseEvent.setTransactor( command.getTransactor() );
        publishApiTestCaseEvent.setOccurred( command.getOccurred() );
        publishApiTestCaseEvent.setProductCode( command.getProductCode() );
        publishApiTestCaseEvent.setCaseCode( command.getCaseCode() );

        return publishApiTestCaseEvent;
    }

    @Override
    public AddApiTestCaseEvent convert(ApiTestCaseEntityDO apiTestCaseDO) {
        if ( apiTestCaseDO == null ) {
            return null;
        }

        AddApiTestCaseEvent addApiTestCaseEvent = new AddApiTestCaseEvent();

        addApiTestCaseEvent.setCaseCode( apiTestCaseDO.getCaseCode() );
        addApiTestCaseEvent.setProductCode( apiTestCaseDO.getProductCode() );
        addApiTestCaseEvent.setCaseName( apiTestCaseDO.getCaseName() );
        addApiTestCaseEvent.setApiCode( apiTestCaseDO.getApiCode() );
        addApiTestCaseEvent.setStatus( apiTestCaseDO.getStatus() );
        addApiTestCaseEvent.setCaseType( apiTestCaseDO.getCaseType() );
        addApiTestCaseEvent.setCaseReqData( apiTestCaseDO.getCaseReqData() );
        addApiTestCaseEvent.setLatestTaskId( apiTestCaseDO.getLatestTaskId() );
        addApiTestCaseEvent.setEnable( apiTestCaseDO.getEnable() );
        addApiTestCaseEvent.setAutomaticSourceCode( apiTestCaseDO.getAutomaticSourceCode() );
        addApiTestCaseEvent.setRelatedApiAddress( apiTestCaseDO.getRelatedApiAddress() );
        addApiTestCaseEvent.setRelatedApiName( apiTestCaseDO.getRelatedApiName() );
        addApiTestCaseEvent.setApiType( apiTestCaseDO.getApiType() );
        addApiTestCaseEvent.setDocVersion( apiTestCaseDO.getDocVersion() );

        return addApiTestCaseEvent;
    }

    @Override
    public EditApiTestCaseEvent convertToEditApiTestCaseEvent(ApiTestCaseEntityDO apiTestCaseDO) {
        if ( apiTestCaseDO == null ) {
            return null;
        }

        EditApiTestCaseEvent editApiTestCaseEvent = new EditApiTestCaseEvent();

        editApiTestCaseEvent.setProductCode( apiTestCaseDO.getProductCode() );
        editApiTestCaseEvent.setCaseCode( apiTestCaseDO.getCaseCode() );
        editApiTestCaseEvent.setCaseName( apiTestCaseDO.getCaseName() );
        editApiTestCaseEvent.setApiCode( apiTestCaseDO.getApiCode() );
        editApiTestCaseEvent.setStatus( apiTestCaseDO.getStatus() );
        editApiTestCaseEvent.setCaseType( apiTestCaseDO.getCaseType() );
        editApiTestCaseEvent.setCaseReqData( apiTestCaseDO.getCaseReqData() );
        editApiTestCaseEvent.setEnable( apiTestCaseDO.getEnable() );
        editApiTestCaseEvent.setAutomaticSourceCode( apiTestCaseDO.getAutomaticSourceCode() );
        editApiTestCaseEvent.setRelatedApiAddress( apiTestCaseDO.getRelatedApiAddress() );
        editApiTestCaseEvent.setRelatedApiName( apiTestCaseDO.getRelatedApiName() );
        editApiTestCaseEvent.setApiType( apiTestCaseDO.getApiType() );
        editApiTestCaseEvent.setDocVersion( apiTestCaseDO.getDocVersion() );

        return editApiTestCaseEvent;
    }

    @Override
    public ApiCaseDetailResp convertApiCaseDetailResp(ApiTestCaseEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        ApiCaseDetailResp apiCaseDetailResp = new ApiCaseDetailResp();

        apiCaseDetailResp.setCaseReqData( entityDO.getCaseReqData() );
        apiCaseDetailResp.setCaseType( entityDO.getCaseType() );
        apiCaseDetailResp.setCaseCode( entityDO.getCaseCode() );
        apiCaseDetailResp.setCaseName( entityDO.getCaseName() );
        apiCaseDetailResp.setProductCode( entityDO.getProductCode() );
        apiCaseDetailResp.setApiCode( entityDO.getApiCode() );
        apiCaseDetailResp.setStatus( entityDO.getStatus() );
        apiCaseDetailResp.setTagValue( entityDO.getTagValue() );

        return apiCaseDetailResp;
    }

    @Override
    public PageApiTestCaseQuery convert(PageApiTestCaseReq req) {
        if ( req == null ) {
            return null;
        }

        PageApiTestCaseQuery pageApiTestCaseQuery = new PageApiTestCaseQuery();

        pageApiTestCaseQuery.setTransactor( req.getTransactor() );
        pageApiTestCaseQuery.setPage( req.getPage() );
        pageApiTestCaseQuery.setSize( req.getSize() );
        pageApiTestCaseQuery.setProductCode( req.getProductCode() );
        List<Integer> list = req.getEnableNumList();
        if ( list != null ) {
            pageApiTestCaseQuery.setEnableNumList( new ArrayList<Integer>( list ) );
        }
        List<String> list1 = req.getApiNameList();
        if ( list1 != null ) {
            pageApiTestCaseQuery.setApiNameList( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = req.getApiAddressList();
        if ( list2 != null ) {
            pageApiTestCaseQuery.setApiAddressList( new ArrayList<String>( list2 ) );
        }
        pageApiTestCaseQuery.setApiCode( req.getApiCode() );
        List<ApiCaseTypeEnum> list3 = req.getTypeList();
        if ( list3 != null ) {
            pageApiTestCaseQuery.setTypeList( new ArrayList<ApiCaseTypeEnum>( list3 ) );
        }
        List<TestPlanCaseStatusEnum> list4 = req.getTestResultList();
        if ( list4 != null ) {
            pageApiTestCaseQuery.setTestResultList( new ArrayList<TestPlanCaseStatusEnum>( list4 ) );
        }
        pageApiTestCaseQuery.setCaseName( req.getCaseName() );
        pageApiTestCaseQuery.setStartTime( req.getStartTime() );
        pageApiTestCaseQuery.setEndTime( req.getEndTime() );
        List<String> list5 = req.getDocVersionList();
        if ( list5 != null ) {
            pageApiTestCaseQuery.setDocVersionList( new ArrayList<String>( list5 ) );
        }
        List<ApiCaseTagEnum> list6 = req.getTagList();
        if ( list6 != null ) {
            pageApiTestCaseQuery.setTagList( new ArrayList<ApiCaseTagEnum>( list6 ) );
        }
        List<ApiTypeEnum> list7 = req.getApiTypeList();
        if ( list7 != null ) {
            pageApiTestCaseQuery.setApiTypeList( new ArrayList<ApiTypeEnum>( list7 ) );
        }

        return pageApiTestCaseQuery;
    }

    @Override
    public ApiTestCaseExecuteDetailQuery convert(ApiTestCaseExecuteDetailReq req) {
        if ( req == null ) {
            return null;
        }

        ApiTestCaseExecuteDetailQuery apiTestCaseExecuteDetailQuery = new ApiTestCaseExecuteDetailQuery();

        apiTestCaseExecuteDetailQuery.setTransactor( req.getTransactor() );
        List<String> list = req.getExecutorIdList();
        if ( list != null ) {
            apiTestCaseExecuteDetailQuery.setExecutorIdList( new ArrayList<String>( list ) );
        }
        List<String> list1 = req.getTaskCodeList();
        if ( list1 != null ) {
            apiTestCaseExecuteDetailQuery.setTaskCodeList( new ArrayList<String>( list1 ) );
        }
        apiTestCaseExecuteDetailQuery.setProductCode( req.getProductCode() );
        List<TestPlanCaseStatusEnum> list2 = req.getStatus();
        if ( list2 != null ) {
            apiTestCaseExecuteDetailQuery.setStatus( new ArrayList<TestPlanCaseStatusEnum>( list2 ) );
        }
        apiTestCaseExecuteDetailQuery.setStartTimeBegin( req.getStartTimeBegin() );
        apiTestCaseExecuteDetailQuery.setStartTimeEnd( req.getStartTimeEnd() );
        apiTestCaseExecuteDetailQuery.setFinishTimeBegin( req.getFinishTimeBegin() );
        apiTestCaseExecuteDetailQuery.setFinishTimeEnd( req.getFinishTimeEnd() );

        return apiTestCaseExecuteDetailQuery;
    }

    @Override
    public PageApiTestCaseChildrenQuery convert(PageApiTestCaseChildrenReq req) {
        if ( req == null ) {
            return null;
        }

        PageApiTestCaseChildrenQuery pageApiTestCaseChildrenQuery = new PageApiTestCaseChildrenQuery();

        pageApiTestCaseChildrenQuery.setTransactor( req.getTransactor() );
        pageApiTestCaseChildrenQuery.setPage( req.getPage() );
        pageApiTestCaseChildrenQuery.setSize( req.getSize() );
        pageApiTestCaseChildrenQuery.setProductCode( req.getProductCode() );
        pageApiTestCaseChildrenQuery.setParentCaseCode( req.getParentCaseCode() );
        List<ApiCaseTypeEnum> list = req.getTypeList();
        if ( list != null ) {
            pageApiTestCaseChildrenQuery.setTypeList( new ArrayList<ApiCaseTypeEnum>( list ) );
        }
        List<TestPlanCaseStatusEnum> list1 = req.getTestResultList();
        if ( list1 != null ) {
            pageApiTestCaseChildrenQuery.setTestResultList( new ArrayList<TestPlanCaseStatusEnum>( list1 ) );
        }
        pageApiTestCaseChildrenQuery.setCaseName( req.getCaseName() );
        pageApiTestCaseChildrenQuery.setStartTime( req.getStartTime() );
        pageApiTestCaseChildrenQuery.setEndTime( req.getEndTime() );
        List<ApiCaseTagEnum> list2 = req.getTagList();
        if ( list2 != null ) {
            pageApiTestCaseChildrenQuery.setTagList( new ArrayList<ApiCaseTagEnum>( list2 ) );
        }

        return pageApiTestCaseChildrenQuery;
    }

    @Override
    public PageApiExceptionCaseQuery convert(QueryApiCaseExceptionReq req) {
        if ( req == null ) {
            return null;
        }

        PageApiExceptionCaseQuery pageApiExceptionCaseQuery = new PageApiExceptionCaseQuery();

        pageApiExceptionCaseQuery.setPage( req.getPage() );
        pageApiExceptionCaseQuery.setSize( req.getSize() );
        pageApiExceptionCaseQuery.setParentCaseCode( req.getParentCaseCode() );
        pageApiExceptionCaseQuery.setCaseCode( req.getCaseCode() );
        List<Integer> list = req.getGenerateRulesList();
        if ( list != null ) {
            pageApiExceptionCaseQuery.setGenerateRulesList( new ArrayList<Integer>( list ) );
        }
        pageApiExceptionCaseQuery.setStatus( req.getStatus() );
        pageApiExceptionCaseQuery.setKey( req.getKey() );
        pageApiExceptionCaseQuery.setOrderField( req.getOrderField() );
        pageApiExceptionCaseQuery.setOrderType( req.getOrderType() );

        return pageApiExceptionCaseQuery;
    }

    @Override
    public ApiSampleCaseVO convertApiSampleCaseVO(ApiTestEntityDO entity) {
        if ( entity == null ) {
            return null;
        }

        ApiSampleCaseVO apiSampleCaseVO = new ApiSampleCaseVO();

        apiSampleCaseVO.setApiCode( entity.getMainApiCode() );
        apiSampleCaseVO.setApiName( entity.getApiName() );
        apiSampleCaseVO.setReqMethod( entity.getReqMethod() );
        apiSampleCaseVO.setApiData( entity.getApiData() );
        apiSampleCaseVO.setDocId( entity.getDocId() );
        apiSampleCaseVO.setDocProductCode( entity.getDocProductCode() );
        apiSampleCaseVO.setProductCode( entity.getProductCode() );
        apiSampleCaseVO.setAppId( entity.getAppId() );
        apiSampleCaseVO.setApiType( entity.getApiType() );
        apiSampleCaseVO.setApiAddress( entity.getApiAddress() );
        apiSampleCaseVO.setDocVersion( entity.getDocVersion() );

        return apiSampleCaseVO;
    }

    @Override
    public List<ApiSampleCaseVO> convertApiSampleCaseVO(List<ApiTestEntityDO> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiSampleCaseVO> list = new ArrayList<ApiSampleCaseVO>( entityList.size() );
        for ( ApiTestEntityDO apiTestEntityDO : entityList ) {
            list.add( convertApiSampleCaseVO( apiTestEntityDO ) );
        }

        return list;
    }

    protected User sceneInfoEntityDOToUser(SceneInfoEntityDO sceneInfoEntityDO) {
        if ( sceneInfoEntityDO == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( sceneInfoEntityDO.getModifierId() );
        user.setUserName( sceneInfoEntityDO.getModifier() );

        return user;
    }

    protected User sceneInfoEntityDOToUser1(SceneInfoEntityDO sceneInfoEntityDO) {
        if ( sceneInfoEntityDO == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( sceneInfoEntityDO.getModifierId() );
        user.setUserName( sceneInfoEntityDO.getModifier() );

        return user;
    }

    private String eventTransactorUserName(AddSceneModuleEvent addSceneModuleEvent) {
        if ( addSceneModuleEvent == null ) {
            return null;
        }
        User transactor = addSceneModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId(AddSceneModuleEvent addSceneModuleEvent) {
        if ( addSceneModuleEvent == null ) {
            return null;
        }
        User transactor = addSceneModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName1(AddPreDataModuleEvent addPreDataModuleEvent) {
        if ( addPreDataModuleEvent == null ) {
            return null;
        }
        User transactor = addPreDataModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId1(AddPreDataModuleEvent addPreDataModuleEvent) {
        if ( addPreDataModuleEvent == null ) {
            return null;
        }
        User transactor = addPreDataModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private Long eventTransactorUserId2(EditSceneModuleEvent editSceneModuleEvent) {
        if ( editSceneModuleEvent == null ) {
            return null;
        }
        User transactor = editSceneModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName2(EditSceneModuleEvent editSceneModuleEvent) {
        if ( editSceneModuleEvent == null ) {
            return null;
        }
        User transactor = editSceneModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId3(EditPreDataModuleEvent editPreDataModuleEvent) {
        if ( editPreDataModuleEvent == null ) {
            return null;
        }
        User transactor = editPreDataModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName3(EditPreDataModuleEvent editPreDataModuleEvent) {
        if ( editPreDataModuleEvent == null ) {
            return null;
        }
        User transactor = editPreDataModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId4(MoveSceneModuleEvent moveSceneModuleEvent) {
        if ( moveSceneModuleEvent == null ) {
            return null;
        }
        User transactor = moveSceneModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName4(MoveSceneModuleEvent moveSceneModuleEvent) {
        if ( moveSceneModuleEvent == null ) {
            return null;
        }
        User transactor = moveSceneModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId5(MovePreDataModuleEvent movePreDataModuleEvent) {
        if ( movePreDataModuleEvent == null ) {
            return null;
        }
        User transactor = movePreDataModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName5(MovePreDataModuleEvent movePreDataModuleEvent) {
        if ( movePreDataModuleEvent == null ) {
            return null;
        }
        User transactor = movePreDataModuleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    protected BaseApiTestVariableResp apiTestVariableVOToBaseApiTestVariableResp(ApiTestVariableVO apiTestVariableVO) {
        if ( apiTestVariableVO == null ) {
            return null;
        }

        BaseApiTestVariableResp baseApiTestVariableResp = new BaseApiTestVariableResp();

        baseApiTestVariableResp.setVariableKey( apiTestVariableVO.getVariableKey() );
        baseApiTestVariableResp.setVariableValue( apiTestVariableVO.getVariableValue() );
        baseApiTestVariableResp.setVariableDesc( apiTestVariableVO.getVariableDesc() );

        return baseApiTestVariableResp;
    }

    protected QueryPreDataVariableVO apiTestVariableVOToQueryPreDataVariableVO(ApiTestVariableVO apiTestVariableVO) {
        if ( apiTestVariableVO == null ) {
            return null;
        }

        QueryPreDataVariableVO queryPreDataVariableVO = new QueryPreDataVariableVO();

        queryPreDataVariableVO.setVariableCode( apiTestVariableVO.getVariableCode() );
        queryPreDataVariableVO.setVariableName( apiTestVariableVO.getVariableName() );
        queryPreDataVariableVO.setVariableKey( apiTestVariableVO.getVariableKey() );
        queryPreDataVariableVO.setVariableValue( apiTestVariableVO.getVariableValue() );
        queryPreDataVariableVO.setType( apiTestVariableVO.getType() );
        queryPreDataVariableVO.setUsageType( apiTestVariableVO.getUsageType() );
        queryPreDataVariableVO.setRequiredStatus( apiTestVariableVO.getRequiredStatus() );
        if ( apiTestVariableVO.getVariableStatus() != null ) {
            queryPreDataVariableVO.setVariableStatus( String.valueOf( apiTestVariableVO.getVariableStatus() ) );
        }
        queryPreDataVariableVO.setSceneType( apiTestVariableVO.getSceneType() );

        return queryPreDataVariableVO;
    }

    protected BaseApiTestVariableReq baseApiTestVariableRespToBaseApiTestVariableReq(BaseApiTestVariableResp baseApiTestVariableResp) {
        if ( baseApiTestVariableResp == null ) {
            return null;
        }

        BaseApiTestVariableReq baseApiTestVariableReq = new BaseApiTestVariableReq();

        baseApiTestVariableReq.setVariableKey( baseApiTestVariableResp.getVariableKey() );
        baseApiTestVariableReq.setVariableValue( baseApiTestVariableResp.getVariableValue() );
        baseApiTestVariableReq.setVariableDesc( baseApiTestVariableResp.getVariableDesc() );

        return baseApiTestVariableReq;
    }
}
