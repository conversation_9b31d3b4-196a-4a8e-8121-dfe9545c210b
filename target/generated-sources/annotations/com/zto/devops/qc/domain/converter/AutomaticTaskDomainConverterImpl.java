package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteAutomaticTaskCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskExecutedEvent;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticTaskDomainConverterImpl implements AutomaticTaskDomainConverter {

    @Override
    public AutomaticTaskExecutedEvent converter(ExecuteAutomaticTaskCommand command) {
        if ( command == null ) {
            return null;
        }

        AutomaticTaskExecutedEvent automaticTaskExecutedEvent = new AutomaticTaskExecutedEvent();

        automaticTaskExecutedEvent.setCode( command.getAggregateId() );
        automaticTaskExecutedEvent.setAggregateId( command.getAggregateId() );
        automaticTaskExecutedEvent.setTransactor( command.getTransactor() );
        automaticTaskExecutedEvent.setOccurred( command.getOccurred() );
        automaticTaskExecutedEvent.setTaskId( command.getTaskId() );
        automaticTaskExecutedEvent.setAutomaticSourceCode( command.getAutomaticSourceCode() );
        automaticTaskExecutedEvent.setEnv( command.getEnv() );
        automaticTaskExecutedEvent.setTag( command.getTag() );
        List<String> list = command.getTestcaseCodeList();
        if ( list != null ) {
            automaticTaskExecutedEvent.setTestcaseCodeList( new ArrayList<String>( list ) );
        }
        automaticTaskExecutedEvent.setTestPlanCode( command.getTestPlanCode() );
        automaticTaskExecutedEvent.setTestStage( command.getTestStage() );
        automaticTaskExecutedEvent.setVersionCode( command.getVersionCode() );
        automaticTaskExecutedEvent.setTrigMode( command.getTrigMode() );
        automaticTaskExecutedEvent.setCoverageFlag( command.getCoverageFlag() );
        automaticTaskExecutedEvent.setSchedulerCode( command.getSchedulerCode() );
        automaticTaskExecutedEvent.setProductCode( command.getProductCode() );

        return automaticTaskExecutedEvent;
    }

    @Override
    public AutomaticTaskEntityDO converter(AutomaticTaskExecutedEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticTaskEntityDO automaticTaskEntityDO = new AutomaticTaskEntityDO();

        automaticTaskEntityDO.setCode( event.getCode() );
        automaticTaskEntityDO.setTaskId( event.getTaskId() );
        automaticTaskEntityDO.setAutomaticSourceCode( event.getAutomaticSourceCode() );
        automaticTaskEntityDO.setProductCode( event.getProductCode() );
        automaticTaskEntityDO.setSourceAddress( event.getSourceAddress() );
        automaticTaskEntityDO.setFilename( event.getFilename() );
        automaticTaskEntityDO.setType( event.getType() );
        automaticTaskEntityDO.setEnv( event.getEnv() );
        automaticTaskEntityDO.setTestPlanCode( event.getTestPlanCode() );
        automaticTaskEntityDO.setTestStage( event.getTestStage() );
        automaticTaskEntityDO.setVersionCode( event.getVersionCode() );
        automaticTaskEntityDO.setTrigMode( event.getTrigMode() );
        automaticTaskEntityDO.setBranchName( event.getBranchName() );
        automaticTaskEntityDO.setWorkDir( event.getWorkDir() );
        automaticTaskEntityDO.setCommitId( event.getCommitId() );
        automaticTaskEntityDO.setCoverageFlag( event.getCoverageFlag() );
        automaticTaskEntityDO.setSchedulerCode( event.getSchedulerCode() );

        return automaticTaskEntityDO;
    }

    @Override
    public void converter(AutomaticSourceRecordEntityDO entityDO, AutomaticTaskExecutedEvent event) {
        if ( entityDO == null ) {
            return;
        }

        event.setSourceAddress( entityDO.getAddress() );
        event.setFilename( entityDO.getFileName() );
        event.setBranchName( entityDO.getBranch() );
        event.setWorkDir( entityDO.getWorkSpace() );
        event.setType( entityDO.getType() );
        event.setCommitId( entityDO.getCommitId() );
        event.setProductCode( entityDO.getProductCode() );
    }

    @Override
    public TestcaseExecuteRecordEntityDO converter(AutomaticTaskEntityDO entity) {
        if ( entity == null ) {
            return null;
        }

        TestcaseExecuteRecordEntityDO testcaseExecuteRecordEntityDO = new TestcaseExecuteRecordEntityDO();

        testcaseExecuteRecordEntityDO.setAutomaticTaskCode( entity.getCode() );
        testcaseExecuteRecordEntityDO.setEnable( entity.getEnable() );
        testcaseExecuteRecordEntityDO.setCreatorId( entity.getCreatorId() );
        testcaseExecuteRecordEntityDO.setCreator( entity.getCreator() );
        testcaseExecuteRecordEntityDO.setGmtCreate( entity.getGmtCreate() );
        testcaseExecuteRecordEntityDO.setModifierId( entity.getModifierId() );
        testcaseExecuteRecordEntityDO.setModifier( entity.getModifier() );
        testcaseExecuteRecordEntityDO.setGmtModified( entity.getGmtModified() );
        testcaseExecuteRecordEntityDO.setId( entity.getId() );
        testcaseExecuteRecordEntityDO.setTestPlanCode( entity.getTestPlanCode() );
        testcaseExecuteRecordEntityDO.setTestStage( entity.getTestStage() );
        testcaseExecuteRecordEntityDO.setStartTime( entity.getStartTime() );
        testcaseExecuteRecordEntityDO.setFinishTime( entity.getFinishTime() );
        testcaseExecuteRecordEntityDO.setResultFile( entity.getResultFile() );
        testcaseExecuteRecordEntityDO.setExecLogFile( entity.getExecLogFile() );
        testcaseExecuteRecordEntityDO.setReportFile( entity.getReportFile() );

        return testcaseExecuteRecordEntityDO;
    }
}
