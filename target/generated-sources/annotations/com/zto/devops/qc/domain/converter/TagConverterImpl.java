package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddTagCommand;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.DeleteTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagDeleteEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagRemovedEvent;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TagConverterImpl implements TagConverter {

    @Override
    public TagVO convert(AddTagCommand command) {
        if ( command == null ) {
            return null;
        }

        TagVO tagVO = new TagVO();

        tagVO.setDomain( command.getDomain() );
        tagVO.setBusinessCode( command.getBusinessCode() );
        tagVO.setCode( command.getCode() );
        tagVO.setType( command.getType() );
        tagVO.setTagAlias( command.getTagAlias() );
        tagVO.setTagCode( command.getTagCode() );
        tagVO.setTagName( command.getTagName() );

        return tagVO;
    }

    @Override
    public TestcaseTagDeleteEvent converter(DeleteTestcaseTagCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseTagDeleteEvent testcaseTagDeleteEvent = new TestcaseTagDeleteEvent();

        testcaseTagDeleteEvent.setCode( command.getAggregateId() );
        testcaseTagDeleteEvent.setAggregateId( command.getAggregateId() );
        testcaseTagDeleteEvent.setTransactor( command.getTransactor() );
        testcaseTagDeleteEvent.setOccurred( command.getOccurred() );

        return testcaseTagDeleteEvent;
    }

    @Override
    public TestcaseTagRemovedEvent converter(RemoveTestcaseTagCommand command) {
        if ( command == null ) {
            return null;
        }

        TestcaseTagRemovedEvent testcaseTagRemovedEvent = new TestcaseTagRemovedEvent();

        testcaseTagRemovedEvent.setCode( command.getAggregateId() );
        testcaseTagRemovedEvent.setAggregateId( command.getAggregateId() );
        testcaseTagRemovedEvent.setTransactor( command.getTransactor() );
        testcaseTagRemovedEvent.setOccurred( command.getOccurred() );

        return testcaseTagRemovedEvent;
    }
}
