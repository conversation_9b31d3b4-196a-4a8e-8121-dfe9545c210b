package com.zto.devops.qc.adapter.converter;

import com.zto.devops.project.client.model.version.entity.RelatedItemVO;
import com.zto.devops.project.client.model.version.event.VersionAddedEvent;
import com.zto.devops.project.client.model.version.event.VersionEditedEvent;
import com.zto.devops.qc.client.model.issue.command.AddVersionIssueCommand;
import com.zto.devops.qc.client.model.rpc.project.VersionBaseEvent;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:12+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AddVersionIssueConvertorImpl implements AddVersionIssueConvertor {

    @Override
    public List<AddVersionIssueCommand> convertVersion(List<RelatedItemVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<AddVersionIssueCommand> list = new ArrayList<AddVersionIssueCommand>( vos.size() );
        for ( RelatedItemVO relatedItemVO : vos ) {
            list.add( relatedItemVOToAddVersionIssueCommand( relatedItemVO ) );
        }

        return list;
    }

    @Override
    public VersionBaseEvent convertVersionBaseEvent(VersionAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        VersionBaseEvent versionBaseEvent = new VersionBaseEvent();

        versionBaseEvent.setAggregateId( event.getAggregateId() );
        versionBaseEvent.setEventType( event.getEventType() );
        versionBaseEvent.setTransactor( event.getTransactor() );
        versionBaseEvent.setOccurred( event.getOccurred() );
        versionBaseEvent.setCode( event.getCode() );
        versionBaseEvent.setProductCode( event.getProductCode() );
        versionBaseEvent.setProductName( event.getProductName() );
        versionBaseEvent.setProjectCode( event.getProjectCode() );
        versionBaseEvent.setIsConfirm( event.getIsConfirm() );
        versionBaseEvent.setName( event.getName() );
        versionBaseEvent.setVersionNum( event.getVersionNum() );
        versionBaseEvent.setVersionDesc( event.getVersionDesc() );
        versionBaseEvent.setStartDate( event.getStartDate() );
        versionBaseEvent.setPresentationDate( event.getPresentationDate() );
        versionBaseEvent.setApprovalExitDate( event.getApprovalExitDate() );
        versionBaseEvent.setPublishDate( event.getPublishDate() );
        versionBaseEvent.setCodeReview( event.getCodeReview() );
        versionBaseEvent.setRelatedList( relatedItemVOListToRelatedItemVOList( event.getRelatedList() ) );

        return versionBaseEvent;
    }

    @Override
    public VersionBaseEvent convertVersionBaseEvent(VersionEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        VersionBaseEvent versionBaseEvent = new VersionBaseEvent();

        versionBaseEvent.setAggregateId( event.getAggregateId() );
        versionBaseEvent.setEventType( event.getEventType() );
        versionBaseEvent.setTransactor( event.getTransactor() );
        versionBaseEvent.setOccurred( event.getOccurred() );
        versionBaseEvent.setCode( event.getCode() );
        versionBaseEvent.setProductCode( event.getProductCode() );
        versionBaseEvent.setProductName( event.getProductName() );
        versionBaseEvent.setProjectCode( event.getProjectCode() );
        versionBaseEvent.setIsConfirm( event.getIsConfirm() );
        versionBaseEvent.setName( event.getName() );
        versionBaseEvent.setVersionNum( event.getVersionNum() );
        versionBaseEvent.setVersionDesc( event.getVersionDesc() );
        versionBaseEvent.setStartDate( event.getStartDate() );
        versionBaseEvent.setPresentationDate( event.getPresentationDate() );
        versionBaseEvent.setApprovalExitDate( event.getApprovalExitDate() );
        versionBaseEvent.setPublishDate( event.getPublishDate() );
        versionBaseEvent.setCodeReview( event.getCodeReview() );
        versionBaseEvent.setRelatedList( relatedItemVOListToRelatedItemVOList( event.getRelatedList() ) );
        versionBaseEvent.setOldRelatedList( relatedItemVOListToRelatedItemVOList( event.getOldRelatedList() ) );

        return versionBaseEvent;
    }

    protected AddVersionIssueCommand relatedItemVOToAddVersionIssueCommand(RelatedItemVO relatedItemVO) {
        if ( relatedItemVO == null ) {
            return null;
        }

        String aggregateId = null;

        AddVersionIssueCommand addVersionIssueCommand = new AddVersionIssueCommand( aggregateId );

        addVersionIssueCommand.setTitle( relatedItemVO.getTitle() );
        addVersionIssueCommand.setVersionConfirm( relatedItemVO.getVersionConfirm() );

        return addVersionIssueCommand;
    }

    protected com.zto.devops.qc.client.model.rpc.project.RelatedItemVO relatedItemVOToRelatedItemVO(RelatedItemVO relatedItemVO) {
        if ( relatedItemVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.RelatedItemVO relatedItemVO1 = new com.zto.devops.qc.client.model.rpc.project.RelatedItemVO();

        relatedItemVO1.setCode( relatedItemVO.getCode() );
        relatedItemVO1.setTitle( relatedItemVO.getTitle() );
        relatedItemVO1.setDomain( relatedItemVO.getDomain() );
        relatedItemVO1.setVersionConfirm( relatedItemVO.getVersionConfirm() );

        return relatedItemVO1;
    }

    protected List<com.zto.devops.qc.client.model.rpc.project.RelatedItemVO> relatedItemVOListToRelatedItemVOList(List<RelatedItemVO> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.project.RelatedItemVO> list1 = new ArrayList<com.zto.devops.qc.client.model.rpc.project.RelatedItemVO>( list.size() );
        for ( RelatedItemVO relatedItemVO : list ) {
            list1.add( relatedItemVOToRelatedItemVO( relatedItemVO ) );
        }

        return list1;
    }
}
