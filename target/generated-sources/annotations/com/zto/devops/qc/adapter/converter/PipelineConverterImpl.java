package com.zto.devops.qc.adapter.converter;

import com.zto.devops.pipeline.client.enums.FlowEventEnum;
import com.zto.devops.pipeline.client.enums.FlowStatusEnum;
import com.zto.devops.pipeline.client.model.application.event.ApplicationEditedEvent;
import com.zto.devops.pipeline.client.model.flow.event.CreateBranchQcEvent;
import com.zto.devops.pipeline.client.model.flow.event.CreateBranchQcEvent.Version;
import com.zto.devops.pipeline.client.model.plan.event.ReleasedQcEvent;
import com.zto.devops.qc.client.enums.rpc.FlowBusinessTypeEnum;
import com.zto.devops.qc.client.model.rpc.pipeline.AutomaticTaskCDExecutedEvent;
import com.zto.devops.qc.client.model.rpc.pipeline.BaseApplicationDO;
import com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentAcceptedReportEvent;
import com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentBackedEvent;
import com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentDelayAcceptedEvent;
import com.zto.devops.qc.client.model.rpc.pipeline.event.VersionStatusChangedEvent;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:12+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class PipelineConverterImpl implements PipelineConverter {

    @Override
    public List<BaseApplicationDO> convertBaseApplicationDOList(List<com.zto.devops.pipeline.client.model.flow.entity.BaseApplicationDO> appIds) {
        if ( appIds == null ) {
            return null;
        }

        List<BaseApplicationDO> list = new ArrayList<BaseApplicationDO>( appIds.size() );
        for ( com.zto.devops.pipeline.client.model.flow.entity.BaseApplicationDO baseApplicationDO : appIds ) {
            list.add( baseApplicationDOToBaseApplicationDO( baseApplicationDO ) );
        }

        return list;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent convert(CreateBranchQcEvent event) {
        if ( event == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent createBranchQcEvent = new com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent();

        createBranchQcEvent.setAggregateId( event.getAggregateId() );
        createBranchQcEvent.setEventType( event.getEventType() );
        createBranchQcEvent.setTransactor( event.getTransactor() );
        createBranchQcEvent.setOccurred( event.getOccurred() );
        createBranchQcEvent.setBranchName( event.getBranchName() );
        createBranchQcEvent.setReleaseBranchName( event.getReleaseBranchName() );
        createBranchQcEvent.setBasicBranchName( event.getBasicBranchName() );
        createBranchQcEvent.setVersions( versionListToVersionList( event.getVersions() ) );

        return createBranchQcEvent;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.pipeline.event.ReleasedQcEvent convert(ReleasedQcEvent event) {
        if ( event == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.event.ReleasedQcEvent releasedQcEvent = new com.zto.devops.qc.client.model.rpc.pipeline.event.ReleasedQcEvent();

        releasedQcEvent.setAggregateId( event.getAggregateId() );
        releasedQcEvent.setEventType( event.getEventType() );
        releasedQcEvent.setTransactor( event.getTransactor() );
        releasedQcEvent.setOccurred( event.getOccurred() );
        releasedQcEvent.setPlanCode( event.getPlanCode() );
        List<String> list = event.getVersionCodes();
        if ( list != null ) {
            releasedQcEvent.setVersionCodes( new ArrayList<String>( list ) );
        }
        releasedQcEvent.setBranchName( event.getBranchName() );
        List<String> list1 = event.getAppIds();
        if ( list1 != null ) {
            releasedQcEvent.setAppIds( new ArrayList<String>( list1 ) );
        }

        return releasedQcEvent;
    }

    @Override
    public AutomaticTaskCDExecutedEvent convert(com.zto.devops.pipeline.client.model.rpc.qc.event.AutomaticTaskCDExecutedEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticTaskCDExecutedEvent automaticTaskCDExecutedEvent = new AutomaticTaskCDExecutedEvent();

        automaticTaskCDExecutedEvent.setAggregateId( event.getAggregateId() );
        automaticTaskCDExecutedEvent.setEventType( event.getEventType() );
        automaticTaskCDExecutedEvent.setTransactor( event.getTransactor() );
        automaticTaskCDExecutedEvent.setOccurred( event.getOccurred() );
        automaticTaskCDExecutedEvent.setProductCode( event.getProductCode() );
        automaticTaskCDExecutedEvent.setTaskId( event.getTaskId() );
        automaticTaskCDExecutedEvent.setSpaceName( event.getSpaceName() );
        automaticTaskCDExecutedEvent.setSpaceTag( event.getSpaceTag() );
        List<String> list = event.getVersionCodeList();
        if ( list != null ) {
            automaticTaskCDExecutedEvent.setVersionCodeList( new ArrayList<String>( list ) );
        }
        automaticTaskCDExecutedEvent.setFlowCode( event.getFlowCode() );
        automaticTaskCDExecutedEvent.setExecuteType( event.getExecuteType() );
        automaticTaskCDExecutedEvent.setDubboTag( event.getDubboTag() );
        automaticTaskCDExecutedEvent.setNodeCode( event.getNodeCode() );
        List<String> list1 = event.getTaskCodeList();
        if ( list1 != null ) {
            automaticTaskCDExecutedEvent.setTaskCodeList( new ArrayList<String>( list1 ) );
        }

        return automaticTaskCDExecutedEvent;
    }

    @Override
    public DeploymentBackedEvent convert(com.zto.devops.pipeline.client.model.flow.event.deployment.DeploymentBackedEvent event) {
        if ( event == null ) {
            return null;
        }

        DeploymentBackedEvent deploymentBackedEvent = new DeploymentBackedEvent();

        deploymentBackedEvent.setAggregateId( event.getAggregateId() );
        deploymentBackedEvent.setEventType( event.getEventType() );
        deploymentBackedEvent.setTransactor( event.getTransactor() );
        deploymentBackedEvent.setOccurred( event.getOccurred() );
        deploymentBackedEvent.setFlowEvent( flowEventEnumToFlowEventEnum( event.getFlowEvent() ) );
        deploymentBackedEvent.setType( flowBusinessTypeEnumToFlowBusinessTypeEnum( event.getType() ) );
        deploymentBackedEvent.setCode( event.getCode() );
        deploymentBackedEvent.setStatus( flowStatusEnumToFlowStatusEnum( event.getStatus() ) );
        deploymentBackedEvent.setPreStatus( flowStatusEnumToFlowStatusEnum( event.getPreStatus() ) );

        return deploymentBackedEvent;
    }

    @Override
    public VersionStatusChangedEvent convert(com.zto.devops.pipeline.client.model.flow.event.VersionStatusChangedEvent event) {
        if ( event == null ) {
            return null;
        }

        VersionStatusChangedEvent versionStatusChangedEvent = new VersionStatusChangedEvent();

        versionStatusChangedEvent.setAggregateId( event.getAggregateId() );
        versionStatusChangedEvent.setEventType( event.getEventType() );
        versionStatusChangedEvent.setTransactor( event.getTransactor() );
        versionStatusChangedEvent.setOccurred( event.getOccurred() );
        List<String> list = event.getVersionCodes();
        if ( list != null ) {
            versionStatusChangedEvent.setVersionCodes( new ArrayList<String>( list ) );
        }
        versionStatusChangedEvent.setStatus( flowStatusEnumToFlowStatusEnum( event.getStatus() ) );
        versionStatusChangedEvent.setPreStatus( flowStatusEnumToFlowStatusEnum( event.getPreStatus() ) );

        return versionStatusChangedEvent;
    }

    @Override
    public DeploymentDelayAcceptedEvent convert(com.zto.devops.pipeline.client.model.flow.event.deployment.DeploymentDelayAcceptedEvent event) {
        if ( event == null ) {
            return null;
        }

        DeploymentDelayAcceptedEvent deploymentDelayAcceptedEvent = new DeploymentDelayAcceptedEvent();

        deploymentDelayAcceptedEvent.setAggregateId( event.getAggregateId() );
        deploymentDelayAcceptedEvent.setEventType( event.getEventType() );
        deploymentDelayAcceptedEvent.setTransactor( event.getTransactor() );
        deploymentDelayAcceptedEvent.setOccurred( event.getOccurred() );
        deploymentDelayAcceptedEvent.setFlowEvent( flowEventEnumToFlowEventEnum( event.getFlowEvent() ) );
        deploymentDelayAcceptedEvent.setType( flowBusinessTypeEnumToFlowBusinessTypeEnum( event.getType() ) );
        deploymentDelayAcceptedEvent.setCode( event.getCode() );
        deploymentDelayAcceptedEvent.setStatus( flowStatusEnumToFlowStatusEnum( event.getStatus() ) );
        deploymentDelayAcceptedEvent.setPreStatus( flowStatusEnumToFlowStatusEnum( event.getPreStatus() ) );
        deploymentDelayAcceptedEvent.setAcceptRemark( event.getAcceptRemark() );

        return deploymentDelayAcceptedEvent;
    }

    @Override
    public DeploymentAcceptedReportEvent convert(com.zto.devops.pipeline.client.model.flow.event.deployment.DeploymentAcceptedReportEvent event) {
        if ( event == null ) {
            return null;
        }

        DeploymentAcceptedReportEvent deploymentAcceptedReportEvent = new DeploymentAcceptedReportEvent();

        deploymentAcceptedReportEvent.setAggregateId( event.getAggregateId() );
        deploymentAcceptedReportEvent.setEventType( event.getEventType() );
        deploymentAcceptedReportEvent.setTransactor( event.getTransactor() );
        deploymentAcceptedReportEvent.setOccurred( event.getOccurred() );
        deploymentAcceptedReportEvent.setFlowEvent( flowEventEnumToFlowEventEnum( event.getFlowEvent() ) );
        deploymentAcceptedReportEvent.setType( flowBusinessTypeEnumToFlowBusinessTypeEnum( event.getType() ) );
        deploymentAcceptedReportEvent.setCode( event.getCode() );
        deploymentAcceptedReportEvent.setStatus( flowStatusEnumToFlowStatusEnum( event.getStatus() ) );
        deploymentAcceptedReportEvent.setPreStatus( flowStatusEnumToFlowStatusEnum( event.getPreStatus() ) );
        deploymentAcceptedReportEvent.setAcceptRemark( event.getAcceptRemark() );
        deploymentAcceptedReportEvent.setContinueRelease( event.getContinueRelease() );

        return deploymentAcceptedReportEvent;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.pipeline.event.ApplicationEditedEvent convert(ApplicationEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.event.ApplicationEditedEvent applicationEditedEvent = new com.zto.devops.qc.client.model.rpc.pipeline.event.ApplicationEditedEvent();

        applicationEditedEvent.setAggregateId( event.getAggregateId() );
        applicationEditedEvent.setEventType( event.getEventType() );
        applicationEditedEvent.setTransactor( event.getTransactor() );
        applicationEditedEvent.setOccurred( event.getOccurred() );
        applicationEditedEvent.setGitProjectUrl( event.getGitProjectUrl() );
        applicationEditedEvent.setGitProjectId( event.getGitProjectId() );
        applicationEditedEvent.setCode( event.getCode() );
        applicationEditedEvent.setBaseImage( event.getBaseImage() );
        applicationEditedEvent.setProduct( event.getProduct() );
        applicationEditedEvent.setName( event.getName() );
        applicationEditedEvent.setDescription( event.getDescription() );
        applicationEditedEvent.setAppName( event.getAppName() );
        applicationEditedEvent.setAppId( event.getAppId() );
        applicationEditedEvent.setApolloAppId( event.getApolloAppId() );
        applicationEditedEvent.setEnable( event.getEnable() );
        applicationEditedEvent.setCoverageStandardValue( event.getCoverageStandardValue() );
        applicationEditedEvent.setWhiteList( event.getWhiteList() );
        applicationEditedEvent.setWhiteListReason( event.getWhiteListReason() );
        applicationEditedEvent.setOldFirstAlerter( event.getOldFirstAlerter() );
        applicationEditedEvent.setOldSecondAlerter( event.getOldSecondAlerter() );
        applicationEditedEvent.setCoreApplication( event.getCoreApplication() );

        return applicationEditedEvent;
    }

    protected BaseApplicationDO baseApplicationDOToBaseApplicationDO(com.zto.devops.pipeline.client.model.flow.entity.BaseApplicationDO baseApplicationDO) {
        if ( baseApplicationDO == null ) {
            return null;
        }

        BaseApplicationDO baseApplicationDO1 = new BaseApplicationDO();

        baseApplicationDO1.setCode( baseApplicationDO.getCode() );
        baseApplicationDO1.setName( baseApplicationDO.getName() );
        baseApplicationDO1.setProductCode( baseApplicationDO.getProductCode() );
        baseApplicationDO1.setProductName( baseApplicationDO.getProductName() );
        baseApplicationDO1.setAppId( baseApplicationDO.getAppId() );
        baseApplicationDO1.setApolloAppId( baseApplicationDO.getApolloAppId() );
        baseApplicationDO1.setGitProjectId( baseApplicationDO.getGitProjectId() );
        baseApplicationDO1.setTypeCode( baseApplicationDO.getTypeCode() );
        baseApplicationDO1.setTypeName( baseApplicationDO.getTypeName() );
        baseApplicationDO1.setDeployType( baseApplicationDO.getDeployType() );
        baseApplicationDO1.setWebUrl( baseApplicationDO.getWebUrl() );

        return baseApplicationDO1;
    }

    protected com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent.Version versionToVersion(Version version) {
        if ( version == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent.Version version1 = new com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent.Version();

        version1.setCode( version.getCode() );
        version1.setName( version.getName() );
        version1.setApps( convertBaseApplicationDOList( version.getApps() ) );

        return version1;
    }

    protected List<com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent.Version> versionListToVersionList(List<Version> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent.Version> list1 = new ArrayList<com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent.Version>( list.size() );
        for ( Version version : list ) {
            list1.add( versionToVersion( version ) );
        }

        return list1;
    }

    protected com.zto.devops.qc.client.enums.rpc.FlowEventEnum flowEventEnumToFlowEventEnum(FlowEventEnum flowEventEnum) {
        if ( flowEventEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.FlowEventEnum flowEventEnum1;

        switch ( flowEventEnum ) {
            case CREATE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.CREATE;
            break;
            case DEPLOY_DEVELOPER_TESTER: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.DEPLOY_DEVELOPER_TESTER;
            break;
            case DEPLOY_DEVELOPER: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.DEPLOY_DEVELOPER;
            break;
            case DEPLOY_TESTER: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.DEPLOY_TESTER;
            break;
            case DEPLOY: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.DEPLOY;
            break;
            case SUBMIT_TEST: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.SUBMIT_TEST;
            break;
            case HOT_DM_SUBMIT_TEST: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.HOT_DM_SUBMIT_TEST;
            break;
            case SELF_TEST: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.SELF_TEST;
            break;
            case RELATE_APPLICATION: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELATE_APPLICATION;
            break;
            case BACK: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACK;
            break;
            case BACK_BY_DEVELOP_OWNER: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACK_BY_DEVELOP_OWNER;
            break;
            case REBUILD_BRANCH: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REBUILD_BRANCH;
            break;
            case TRUNK_BRANCH_CHANGE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.TRUNK_BRANCH_CHANGE;
            break;
            case MERGE_CODE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CODE;
            break;
            case MERGE_FEATURE_TO_TEST: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_FEATURE_TO_TEST;
            break;
            case MERGE_FEATURE_TO_PROD: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_FEATURE_TO_PROD;
            break;
            case MERGE_TO_MASTER: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_TO_MASTER;
            break;
            case MERGE_FROM_MASTER: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_FROM_MASTER;
            break;
            case MERGE_CODE_FAIL: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CODE_FAIL;
            break;
            case MERGE_CODE_SUCCESS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CODE_SUCCESS;
            break;
            case MERGE_FEATURE_TO_TEST_SUCCESS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_FEATURE_TO_TEST_SUCCESS;
            break;
            case MERGE_FEATURE_TO_PROD_SUCCESS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_FEATURE_TO_PROD_SUCCESS;
            break;
            case MERGE_TO_MASTER_SUCCESS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_TO_MASTER_SUCCESS;
            break;
            case MERGE_FROM_MASTER_SUCCESS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_FROM_MASTER_SUCCESS;
            break;
            case MERGE_CODE_SUCCESS_DEVELOP: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CODE_SUCCESS_DEVELOP;
            break;
            case MERGE_CODE_SUCCESS_SMOKING: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CODE_SUCCESS_SMOKING;
            break;
            case MERGE_CODE_SUCCESS_TESTING: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CODE_SUCCESS_TESTING;
            break;
            case MERGE_CODE_SUCCESS_REGRESSING: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CODE_SUCCESS_REGRESSING;
            break;
            case MERGE_CODE_SUCCESS_ACCEPT: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CODE_SUCCESS_ACCEPT;
            break;
            case MERGE_CONFLICT_LIST: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.MERGE_CONFLICT_LIST;
            break;
            case SMOKE_REPORT: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.SMOKE_REPORT;
            break;
            case SMOKE_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.SMOKE_PASS;
            break;
            case SMOKE_NOT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.SMOKE_NOT_PASS;
            break;
            case TEST_REPORT: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.TEST_REPORT;
            break;
            case TEST_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.TEST_PASS;
            break;
            case HOT_DM_TEST_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.HOT_DM_TEST_PASS;
            break;
            case HOT_DM_TEST_NOT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.HOT_DM_TEST_NOT_PASS;
            break;
            case TEST_NOT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.TEST_NOT_PASS;
            break;
            case REGRESS_REPORT: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESS_REPORT;
            break;
            case REGRESS_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESS_PASS;
            break;
            case REGRESS_NOT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESS_NOT_PASS;
            break;
            case REGRESSING_AUDIT_SUBMIT: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESSING_AUDIT_SUBMIT;
            break;
            case REGRESSING_AUDIT_CANCEL: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESSING_AUDIT_CANCEL;
            break;
            case REGRESSING_AUDIT: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESSING_AUDIT;
            break;
            case REGRESSING_AUDIT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESSING_AUDIT_PASS;
            break;
            case REGRESSING_AUDIT_NOT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESSING_AUDIT_NOT_PASS;
            break;
            case REGRESS_TEST: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.REGRESS_TEST;
            break;
            case APPLY_RELEASE_SPECIAL: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.APPLY_RELEASE_SPECIAL;
            break;
            case APPLY_RELEASE_ROUTINE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.APPLY_RELEASE_ROUTINE;
            break;
            case APPLY_RELEASE_ROUTINE_WITH_PLAN: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.APPLY_RELEASE_ROUTINE_WITH_PLAN;
            break;
            case APPLY_RELEASE_SPECIAL_WITH_PLAN: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.APPLY_RELEASE_SPECIAL_WITH_PLAN;
            break;
            case CANCEL_APPLY_RELEASE_WITH_PLAN: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.CANCEL_APPLY_RELEASE_WITH_PLAN;
            break;
            case CANCEL_RELEASE_WITH_PLAN: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.CANCEL_RELEASE_WITH_PLAN;
            break;
            case RELEASE_WITH_PLAN: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE_WITH_PLAN;
            break;
            case RELEASE_RETRY_WITH_PLAN: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE_RETRY_WITH_PLAN;
            break;
            case BACK_WITH_PLAN: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACK_WITH_PLAN;
            break;
            case BACK_BY_DEVELOP_OWNER_WITH_PLAN: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACK_BY_DEVELOP_OWNER_WITH_PLAN;
            break;
            case ACCEPTED_APPLY_RELEASE_SPECIAL: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.ACCEPTED_APPLY_RELEASE_SPECIAL;
            break;
            case ACCEPTED_APPLY_RELEASE_ROUTINE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.ACCEPTED_APPLY_RELEASE_ROUTINE;
            break;
            case BACKUP_APPLY_RELEASE_ROUTINE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACKUP_APPLY_RELEASE_ROUTINE;
            break;
            case ACCEPTED_CANCEL_RELEASE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.ACCEPTED_CANCEL_RELEASE;
            break;
            case CANCEL_APPLY_RELEASE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.CANCEL_APPLY_RELEASE;
            break;
            case EDIT_APPLY_RELEASE_ROUTINE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.EDIT_APPLY_RELEASE_ROUTINE;
            break;
            case EDIT_APPLY_RELEASE_SPECIAL: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.EDIT_APPLY_RELEASE_SPECIAL;
            break;
            case AUDIT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.AUDIT_PASS;
            break;
            case AUDIT_NOT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.AUDIT_NOT_PASS;
            break;
            case RELEASE_PRE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE_PRE;
            break;
            case RELEASE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE;
            break;
            case RELEASE_FAILED: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE_FAILED;
            break;
            case RELEASE_SUCCEED: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE_SUCCEED;
            break;
            case RELEASE_SUCCEED_CUSTOM: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE_SUCCEED_CUSTOM;
            break;
            case RELEASE_SUCCEED_BACKUP: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE_SUCCEED_BACKUP;
            break;
            case RELEASE_RETRY: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.RELEASE_RETRY;
            break;
            case BACKUP_RELEASE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACKUP_RELEASE;
            break;
            case BACKUP_RELEASE_FAILED: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACKUP_RELEASE_FAILED;
            break;
            case BACKUP_RELEASE_SUCCEED: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACKUP_RELEASE_SUCCEED;
            break;
            case BACKUP_RELEASE_SUCCEED_CUSTOM: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.BACKUP_RELEASE_SUCCEED_CUSTOM;
            break;
            case ACCEPT_REPORT: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.ACCEPT_REPORT;
            break;
            case ACCEPT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.ACCEPT_PASS;
            break;
            case DELAY_ACCEPT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.DELAY_ACCEPT_PASS;
            break;
            case CONTINUE_RELEASE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.CONTINUE_RELEASE;
            break;
            case ACCEPT_NOT_PASS: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.ACCEPT_NOT_PASS;
            break;
            case ARCHIVE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.ARCHIVE;
            break;
            case CLOSE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.CLOSE;
            break;
            case CHANGE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.CHANGE;
            break;
            case EDIT: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.EDIT;
            break;
            case CONFIRM: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.CONFIRM;
            break;
            case VERSION_MERGE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.VERSION_MERGE;
            break;
            case VERSION_MERGE_DETAIL: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.VERSION_MERGE_DETAIL;
            break;
            case VERSION_MERGE_FAILED: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.VERSION_MERGE_FAILED;
            break;
            case VERSION_MERGE_SUCCEED: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.VERSION_MERGE_SUCCEED;
            break;
            case VERSION_MERGE_CODE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.VERSION_MERGE_CODE;
            break;
            case VERSION_CODE_ONLY_MERGE: flowEventEnum1 = com.zto.devops.qc.client.enums.rpc.FlowEventEnum.VERSION_CODE_ONLY_MERGE;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + flowEventEnum );
        }

        return flowEventEnum1;
    }

    protected FlowBusinessTypeEnum flowBusinessTypeEnumToFlowBusinessTypeEnum(com.zto.devops.pipeline.client.enums.FlowBusinessTypeEnum flowBusinessTypeEnum) {
        if ( flowBusinessTypeEnum == null ) {
            return null;
        }

        FlowBusinessTypeEnum flowBusinessTypeEnum1;

        switch ( flowBusinessTypeEnum ) {
            case INTEGRATION: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.INTEGRATION;
            break;
            case DELIVERY: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.DELIVERY;
            break;
            case INT_DEPLOYMENT: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.INT_DEPLOYMENT;
            break;
            case HOT_DEPLOYMENT: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.HOT_DEPLOYMENT;
            break;
            case HOTFIX: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.HOTFIX;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + flowBusinessTypeEnum );
        }

        return flowBusinessTypeEnum1;
    }

    protected com.zto.devops.qc.client.enums.rpc.FlowStatusEnum flowStatusEnumToFlowStatusEnum(FlowStatusEnum flowStatusEnum) {
        if ( flowStatusEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.FlowStatusEnum flowStatusEnum1;

        switch ( flowStatusEnum ) {
            case START: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.START;
            break;
            case END: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.END;
            break;
            case SCHEDULING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.SCHEDULING;
            break;
            case VERSION_MERGING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.VERSION_MERGING;
            break;
            case VERSION_MERGE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.VERSION_MERGE_FAILED;
            break;
            case DEVELOPING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.DEVELOPING;
            break;
            case WAIT_MERGE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_MERGE;
            break;
            case MERGE_TO_TEST: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_TEST;
            break;
            case MERGE_TO_PROD: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_PROD;
            break;
            case MERGE_TO_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_RELEASE;
            break;
            case MERGE_TRUNK: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TRUNK;
            break;
            case SMOKING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.SMOKING;
            break;
            case TESTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.TESTING;
            break;
            case WAIT_REGRESS: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_REGRESS;
            break;
            case ITERATING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ITERATING;
            break;
            case REGRESSING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSING;
            break;
            case REGRESSING_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSING_AUDIT;
            break;
            case REGRESSED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSED;
            break;
            case REGRESS_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESS_FAILED;
            break;
            case WAIT_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_AUDIT;
            break;
            case AUDIT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.AUDIT_FAILED;
            break;
            case WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_RELEASE;
            break;
            case RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.RELEASING;
            break;
            case RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.RELEASE_FAILED;
            break;
            case BACKUP_APPLY_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_APPLY_RELEASE;
            break;
            case BACKUP_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_WAIT_RELEASE;
            break;
            case BACKUP_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_RELEASING;
            break;
            case BACKUP_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_RELEASE_FAILED;
            break;
            case ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTING;
            break;
            case DELAY_ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.DELAY_ACCEPTING;
            break;
            case ACCEPTED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED;
            break;
            case ACCEPTED_WAIT_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_WAIT_AUDIT;
            break;
            case ACCEPTED_AUDIT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_AUDIT_FAILED;
            break;
            case ACCEPTED_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_WAIT_RELEASE;
            break;
            case ACCEPTED_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_RELEASING;
            break;
            case ACCEPTED_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_RELEASE_FAILED;
            break;
            case BACKUP_ACCEPTED_APPLY_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_APPLY_RELEASE;
            break;
            case BACKUP_ACCEPTED_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_WAIT_RELEASE;
            break;
            case BACKUP_ACCEPTED_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_RELEASING;
            break;
            case BACKUP_ACCEPTED_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_RELEASE_FAILED;
            break;
            case ACCEPTED_ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_ACCEPTING;
            break;
            case ARCHIVED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ARCHIVED;
            break;
            case CLOSED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.CLOSED;
            break;
            case BACKED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKED;
            break;
            case ACCEPT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPT_FAILED;
            break;
            case APPLICATION_CHECK: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.APPLICATION_CHECK;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + flowStatusEnum );
        }

        return flowStatusEnum1;
    }
}
