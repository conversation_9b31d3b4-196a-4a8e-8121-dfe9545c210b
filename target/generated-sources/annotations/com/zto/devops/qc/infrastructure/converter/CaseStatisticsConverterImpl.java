package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.report.entity.CaseStatisticsVO;
import com.zto.devops.qc.infrastructure.dao.entity.CaseStatisticsEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class CaseStatisticsConverterImpl implements CaseStatisticsConverter {

    @Override
    public List<CaseStatisticsVO> convertCaseStatic(List<CaseStatisticsEntity> caseStatisticsEntities) {
        if ( caseStatisticsEntities == null ) {
            return null;
        }

        List<CaseStatisticsVO> list = new ArrayList<CaseStatisticsVO>( caseStatisticsEntities.size() );
        for ( CaseStatisticsEntity caseStatisticsEntity : caseStatisticsEntities ) {
            list.add( caseStatisticsEntityToCaseStatisticsVO( caseStatisticsEntity ) );
        }

        return list;
    }

    protected CaseStatisticsVO caseStatisticsEntityToCaseStatisticsVO(CaseStatisticsEntity caseStatisticsEntity) {
        if ( caseStatisticsEntity == null ) {
            return null;
        }

        CaseStatisticsVO caseStatisticsVO = new CaseStatisticsVO();

        caseStatisticsVO.setReportCode( caseStatisticsEntity.getReportCode() );
        caseStatisticsVO.setCode( caseStatisticsEntity.getCode() );
        caseStatisticsVO.setFrogPlanCode( caseStatisticsEntity.getFrogPlanCode() );
        caseStatisticsVO.setFrogPlanName( caseStatisticsEntity.getFrogPlanName() );
        caseStatisticsVO.setCaseCount( caseStatisticsEntity.getCaseCount() );
        caseStatisticsVO.setPlanSmokeCaseCount( caseStatisticsEntity.getPlanSmokeCaseCount() );
        caseStatisticsVO.setSmokeAccessCount( caseStatisticsEntity.getSmokeAccessCount() );

        return caseStatisticsVO;
    }
}
