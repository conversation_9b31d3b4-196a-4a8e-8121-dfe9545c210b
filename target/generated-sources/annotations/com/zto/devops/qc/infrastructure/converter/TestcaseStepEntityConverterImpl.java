package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestcaseStepEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseStepEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseStepEntityConverterImpl implements TestcaseStepEntityConverter {

    @Override
    public List<TestcaseStepVO> converter(List<TestcaseStepEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<TestcaseStepVO> list1 = new ArrayList<TestcaseStepVO>( list.size() );
        for ( TestcaseStepEntity testcaseStepEntity : list ) {
            list1.add( testcaseStepEntityToTestcaseStepVO( testcaseStepEntity ) );
        }

        return list1;
    }

    @Override
    public TestcaseStepEntityDO convert(TestcaseStepEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TestcaseStepEntityDO testcaseStepEntityDO = new TestcaseStepEntityDO();

        testcaseStepEntityDO.setEnable( entity.getEnable() );
        testcaseStepEntityDO.setCreatorId( entity.getCreatorId() );
        testcaseStepEntityDO.setCreator( entity.getCreator() );
        testcaseStepEntityDO.setGmtCreate( entity.getGmtCreate() );
        testcaseStepEntityDO.setModifierId( entity.getModifierId() );
        testcaseStepEntityDO.setModifier( entity.getModifier() );
        testcaseStepEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            testcaseStepEntityDO.setId( entity.getId() );
        }
        testcaseStepEntityDO.setTestcaseCode( entity.getTestcaseCode() );
        testcaseStepEntityDO.setStepDesc( entity.getStepDesc() );
        testcaseStepEntityDO.setExpectResult( entity.getExpectResult() );
        testcaseStepEntityDO.setSort( entity.getSort() );

        return testcaseStepEntityDO;
    }

    @Override
    public List<TestcaseStepEntityDO> convertList(List<TestcaseStepEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TestcaseStepEntityDO> list = new ArrayList<TestcaseStepEntityDO>( entityList.size() );
        for ( TestcaseStepEntity testcaseStepEntity : entityList ) {
            list.add( convert( testcaseStepEntity ) );
        }

        return list;
    }

    @Override
    public TestcaseStepEntity convert2(TestcaseStepEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TestcaseStepEntity testcaseStepEntity = new TestcaseStepEntity();

        testcaseStepEntity.setEnable( entityDO.getEnable() );
        testcaseStepEntity.setCreatorId( entityDO.getCreatorId() );
        testcaseStepEntity.setCreator( entityDO.getCreator() );
        testcaseStepEntity.setGmtCreate( entityDO.getGmtCreate() );
        testcaseStepEntity.setModifierId( entityDO.getModifierId() );
        testcaseStepEntity.setModifier( entityDO.getModifier() );
        testcaseStepEntity.setGmtModified( entityDO.getGmtModified() );
        testcaseStepEntity.setId( entityDO.getId() );
        testcaseStepEntity.setTestcaseCode( entityDO.getTestcaseCode() );
        testcaseStepEntity.setStepDesc( entityDO.getStepDesc() );
        testcaseStepEntity.setExpectResult( entityDO.getExpectResult() );
        testcaseStepEntity.setSort( entityDO.getSort() );

        return testcaseStepEntity;
    }

    @Override
    public List<TestcaseStepEntity> convert2List(List<TestcaseStepEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<TestcaseStepEntity> list = new ArrayList<TestcaseStepEntity>( entityDOList.size() );
        for ( TestcaseStepEntityDO testcaseStepEntityDO : entityDOList ) {
            list.add( convert2( testcaseStepEntityDO ) );
        }

        return list;
    }

    @Override
    public TestcaseStepEntity convertVO(TestcaseStepVO vo) {
        if ( vo == null ) {
            return null;
        }

        TestcaseStepEntity testcaseStepEntity = new TestcaseStepEntity();

        testcaseStepEntity.setTestcaseCode( vo.getTestcaseCode() );
        testcaseStepEntity.setStepDesc( vo.getStepDesc() );
        testcaseStepEntity.setExpectResult( vo.getExpectResult() );
        testcaseStepEntity.setSort( vo.getSort() );

        return testcaseStepEntity;
    }

    @Override
    public List<TestcaseStepEntity> convertVOList(List<TestcaseStepVO> list) {
        if ( list == null ) {
            return null;
        }

        List<TestcaseStepEntity> list1 = new ArrayList<TestcaseStepEntity>( list.size() );
        for ( TestcaseStepVO testcaseStepVO : list ) {
            list1.add( convertVO( testcaseStepVO ) );
        }

        return list1;
    }

    protected TestcaseStepVO testcaseStepEntityToTestcaseStepVO(TestcaseStepEntity testcaseStepEntity) {
        if ( testcaseStepEntity == null ) {
            return null;
        }

        TestcaseStepVO testcaseStepVO = new TestcaseStepVO();

        testcaseStepVO.setTestcaseCode( testcaseStepEntity.getTestcaseCode() );
        testcaseStepVO.setStepDesc( testcaseStepEntity.getStepDesc() );
        testcaseStepVO.setExpectResult( testcaseStepEntity.getExpectResult() );
        testcaseStepVO.setSort( testcaseStepEntity.getSort() );

        return testcaseStepVO;
    }
}
