package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.SceneLinkInfoEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.SceneLinkInfoEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class SceneLinkInfoEntityConverterImpl implements SceneLinkInfoEntityConverter {

    @Override
    public List<SceneLinkInfoEntity> convertSceneLinkInfoEntity(List<SceneLinkInfoEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<SceneLinkInfoEntity> list1 = new ArrayList<SceneLinkInfoEntity>( list.size() );
        for ( SceneLinkInfoEntityDO sceneLinkInfoEntityDO : list ) {
            list1.add( convertSceneLinkInfoEntity( sceneLinkInfoEntityDO ) );
        }

        return list1;
    }

    @Override
    public SceneLinkInfoEntity convertSceneLinkInfoEntity(SceneLinkInfoEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneLinkInfoEntity sceneLinkInfoEntity = new SceneLinkInfoEntity();

        sceneLinkInfoEntity.setProductCode( entityDO.getProductCode() );
        sceneLinkInfoEntity.setSceneCode( entityDO.getSceneCode() );
        sceneLinkInfoEntity.setSceneVersion( entityDO.getSceneVersion() );
        sceneLinkInfoEntity.setLinkMapCode( entityDO.getLinkMapCode() );
        sceneLinkInfoEntity.setEnable( entityDO.getEnable() );
        sceneLinkInfoEntity.setLinkComponentCode( entityDO.getLinkComponentCode() );
        sceneLinkInfoEntity.setLinkComponentName( entityDO.getLinkComponentName() );
        sceneLinkInfoEntity.setLinkComponentType( entityDO.getLinkComponentType() );
        sceneLinkInfoEntity.setSequenceNumber( entityDO.getSequenceNumber() );
        sceneLinkInfoEntity.setCreatorId( entityDO.getCreatorId() );
        sceneLinkInfoEntity.setCreator( entityDO.getCreator() );
        sceneLinkInfoEntity.setGmtCreate( entityDO.getGmtCreate() );
        sceneLinkInfoEntity.setModifierId( entityDO.getModifierId() );
        sceneLinkInfoEntity.setModifier( entityDO.getModifier() );
        sceneLinkInfoEntity.setGmtModified( entityDO.getGmtModified() );

        return sceneLinkInfoEntity;
    }

    @Override
    public List<SceneLinkInfoEntityDO> convertSceneLinkInfoEntityDO(List<SceneLinkInfoEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<SceneLinkInfoEntityDO> list1 = new ArrayList<SceneLinkInfoEntityDO>( list.size() );
        for ( SceneLinkInfoEntity sceneLinkInfoEntity : list ) {
            list1.add( convertSceneLinkInfoEntityDO( sceneLinkInfoEntity ) );
        }

        return list1;
    }

    @Override
    public SceneLinkInfoEntityDO convertSceneLinkInfoEntityDO(SceneLinkInfoEntity entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneLinkInfoEntityDO sceneLinkInfoEntityDO = new SceneLinkInfoEntityDO();

        sceneLinkInfoEntityDO.setProductCode( entityDO.getProductCode() );
        sceneLinkInfoEntityDO.setSceneCode( entityDO.getSceneCode() );
        sceneLinkInfoEntityDO.setSceneVersion( entityDO.getSceneVersion() );
        sceneLinkInfoEntityDO.setLinkMapCode( entityDO.getLinkMapCode() );
        sceneLinkInfoEntityDO.setEnable( entityDO.getEnable() );
        sceneLinkInfoEntityDO.setLinkComponentCode( entityDO.getLinkComponentCode() );
        sceneLinkInfoEntityDO.setLinkComponentName( entityDO.getLinkComponentName() );
        sceneLinkInfoEntityDO.setLinkComponentType( entityDO.getLinkComponentType() );
        sceneLinkInfoEntityDO.setSequenceNumber( entityDO.getSequenceNumber() );
        sceneLinkInfoEntityDO.setCreatorId( entityDO.getCreatorId() );
        sceneLinkInfoEntityDO.setCreator( entityDO.getCreator() );
        sceneLinkInfoEntityDO.setGmtCreate( entityDO.getGmtCreate() );
        sceneLinkInfoEntityDO.setModifierId( entityDO.getModifierId() );
        sceneLinkInfoEntityDO.setModifier( entityDO.getModifier() );
        sceneLinkInfoEntityDO.setGmtModified( entityDO.getGmtModified() );

        return sceneLinkInfoEntityDO;
    }
}
