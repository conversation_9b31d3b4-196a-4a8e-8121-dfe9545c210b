package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestcaseRelationEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseRelationEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:27+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseRelationEntityConverterImpl implements TestcaseRelationEntityConverter {

    @Override
    public TestcaseRelationEntity convert(TestcaseRelationVO vo) {
        if ( vo == null ) {
            return null;
        }

        TestcaseRelationEntity testcaseRelationEntity = new TestcaseRelationEntity();

        testcaseRelationEntity.setTestcaseCode( vo.getTestcaseCode() );
        testcaseRelationEntity.setBusinessCode( vo.getBusinessCode() );
        testcaseRelationEntity.setDomain( vo.getDomain() );

        return testcaseRelationEntity;
    }

    @Override
    public List<TestcaseRelationEntity> convertList(List<TestcaseRelationVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<TestcaseRelationEntity> list = new ArrayList<TestcaseRelationEntity>( vo.size() );
        for ( TestcaseRelationVO testcaseRelationVO : vo ) {
            list.add( convert( testcaseRelationVO ) );
        }

        return list;
    }

    @Override
    public TestcaseRelationEntityDO convertDO(TestcaseRelationEntity relationEntity) {
        if ( relationEntity == null ) {
            return null;
        }

        TestcaseRelationEntityDO testcaseRelationEntityDO = new TestcaseRelationEntityDO();

        testcaseRelationEntityDO.setEnable( relationEntity.getEnable() );
        testcaseRelationEntityDO.setCreatorId( relationEntity.getCreatorId() );
        testcaseRelationEntityDO.setCreator( relationEntity.getCreator() );
        testcaseRelationEntityDO.setGmtCreate( relationEntity.getGmtCreate() );
        testcaseRelationEntityDO.setModifierId( relationEntity.getModifierId() );
        testcaseRelationEntityDO.setModifier( relationEntity.getModifier() );
        testcaseRelationEntityDO.setGmtModified( relationEntity.getGmtModified() );
        if ( relationEntity.hasId() ) {
            testcaseRelationEntityDO.setId( relationEntity.getId() );
        }
        testcaseRelationEntityDO.setTestcaseCode( relationEntity.getTestcaseCode() );
        testcaseRelationEntityDO.setBusinessCode( relationEntity.getBusinessCode() );
        testcaseRelationEntityDO.setDomain( relationEntity.getDomain() );

        return testcaseRelationEntityDO;
    }

    @Override
    public List<TestcaseRelationEntityDO> convertDOList(List<TestcaseRelationEntity> relationEntityList) {
        if ( relationEntityList == null ) {
            return null;
        }

        List<TestcaseRelationEntityDO> list = new ArrayList<TestcaseRelationEntityDO>( relationEntityList.size() );
        for ( TestcaseRelationEntity testcaseRelationEntity : relationEntityList ) {
            list.add( convertDO( testcaseRelationEntity ) );
        }

        return list;
    }
}
