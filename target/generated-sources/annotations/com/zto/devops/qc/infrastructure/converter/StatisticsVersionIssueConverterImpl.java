package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.StatisticsVersionIssueEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class StatisticsVersionIssueConverterImpl implements StatisticsVersionIssueConverter {

    @Override
    public List<StatisticsVersionIssueEntityDO> convert2DO(List<StatisticsVersionIssueEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<StatisticsVersionIssueEntityDO> list = new ArrayList<StatisticsVersionIssueEntityDO>( entity.size() );
        for ( StatisticsVersionIssueEntity statisticsVersionIssueEntity : entity ) {
            list.add( statisticsVersionIssueEntityToStatisticsVersionIssueEntityDO( statisticsVersionIssueEntity ) );
        }

        return list;
    }

    protected StatisticsVersionIssueEntityDO statisticsVersionIssueEntityToStatisticsVersionIssueEntityDO(StatisticsVersionIssueEntity statisticsVersionIssueEntity) {
        if ( statisticsVersionIssueEntity == null ) {
            return null;
        }

        StatisticsVersionIssueEntityDO statisticsVersionIssueEntityDO = new StatisticsVersionIssueEntityDO();

        statisticsVersionIssueEntityDO.setVersionCode( statisticsVersionIssueEntity.getVersionCode() );
        statisticsVersionIssueEntityDO.setName( statisticsVersionIssueEntity.getName() );
        statisticsVersionIssueEntityDO.setStatus( statisticsVersionIssueEntity.getStatus() );
        statisticsVersionIssueEntityDO.setType( statisticsVersionIssueEntity.getType() );
        statisticsVersionIssueEntityDO.setActualPublishDate( statisticsVersionIssueEntity.getActualPublishDate() );
        statisticsVersionIssueEntityDO.setProductCode( statisticsVersionIssueEntity.getProductCode() );
        statisticsVersionIssueEntityDO.setDeptId( statisticsVersionIssueEntity.getDeptId() );
        statisticsVersionIssueEntityDO.setDeptNo( statisticsVersionIssueEntity.getDeptNo() );
        statisticsVersionIssueEntityDO.setUrgencyRepairTime( statisticsVersionIssueEntity.getUrgencyRepairTime() );
        statisticsVersionIssueEntityDO.setHighRepairTime( statisticsVersionIssueEntity.getHighRepairTime() );
        statisticsVersionIssueEntityDO.setMiddleRepairTime( statisticsVersionIssueEntity.getMiddleRepairTime() );
        statisticsVersionIssueEntityDO.setLowRepairTime( statisticsVersionIssueEntity.getLowRepairTime() );
        statisticsVersionIssueEntityDO.setUrgencyNum( statisticsVersionIssueEntity.getUrgencyNum() );
        statisticsVersionIssueEntityDO.setHighNum( statisticsVersionIssueEntity.getHighNum() );
        statisticsVersionIssueEntityDO.setMiddleNum( statisticsVersionIssueEntity.getMiddleNum() );
        statisticsVersionIssueEntityDO.setLowNum( statisticsVersionIssueEntity.getLowNum() );
        statisticsVersionIssueEntityDO.setUrgencyPunctualNum( statisticsVersionIssueEntity.getUrgencyPunctualNum() );
        statisticsVersionIssueEntityDO.setHighPunctualNum( statisticsVersionIssueEntity.getHighPunctualNum() );
        statisticsVersionIssueEntityDO.setMiddlePunctualNum( statisticsVersionIssueEntity.getMiddlePunctualNum() );
        statisticsVersionIssueEntityDO.setUrgencyReopenNum( statisticsVersionIssueEntity.getUrgencyReopenNum() );
        statisticsVersionIssueEntityDO.setHighReopenNum( statisticsVersionIssueEntity.getHighReopenNum() );
        statisticsVersionIssueEntityDO.setMiddleReopenNum( statisticsVersionIssueEntity.getMiddleReopenNum() );
        statisticsVersionIssueEntityDO.setLowReopenNum( statisticsVersionIssueEntity.getLowReopenNum() );
        statisticsVersionIssueEntityDO.setUrgencyEffectiveNum( statisticsVersionIssueEntity.getUrgencyEffectiveNum() );
        statisticsVersionIssueEntityDO.setHighEffectiveNum( statisticsVersionIssueEntity.getHighEffectiveNum() );
        statisticsVersionIssueEntityDO.setMiddleEffectiveNum( statisticsVersionIssueEntity.getMiddleEffectiveNum() );
        statisticsVersionIssueEntityDO.setLowEffectiveNum( statisticsVersionIssueEntity.getLowEffectiveNum() );
        statisticsVersionIssueEntityDO.setRequirementIssueNum( statisticsVersionIssueEntity.getRequirementIssueNum() );
        statisticsVersionIssueEntityDO.setVersionGmtCreate( statisticsVersionIssueEntity.getVersionGmtCreate() );
        statisticsVersionIssueEntityDO.setGmtModified( statisticsVersionIssueEntity.getGmtModified() );

        return statisticsVersionIssueEntityDO;
    }
}
