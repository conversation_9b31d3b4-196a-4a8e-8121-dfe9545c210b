package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmDelayAcceptRecordEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TmDelayAcceptRecordEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmDelayAcceptRecordEntityConverterImpl implements TmDelayAcceptRecordEntityConverter {

    @Override
    public TmDelayAcceptRecordEntityDO convert2DO(TmDelayAcceptRecordEntity tmDelayAcceptRecordEntity) {
        if ( tmDelayAcceptRecordEntity == null ) {
            return null;
        }

        TmDelayAcceptRecordEntityDO tmDelayAcceptRecordEntityDO = new TmDelayAcceptRecordEntityDO();

        tmDelayAcceptRecordEntityDO.setEnable( tmDelayAcceptRecordEntity.getEnable() );
        tmDelayAcceptRecordEntityDO.setCreatorId( tmDelayAcceptRecordEntity.getCreatorId() );
        tmDelayAcceptRecordEntityDO.setCreator( tmDelayAcceptRecordEntity.getCreator() );
        tmDelayAcceptRecordEntityDO.setGmtCreate( tmDelayAcceptRecordEntity.getGmtCreate() );
        tmDelayAcceptRecordEntityDO.setModifierId( tmDelayAcceptRecordEntity.getModifierId() );
        tmDelayAcceptRecordEntityDO.setModifier( tmDelayAcceptRecordEntity.getModifier() );
        tmDelayAcceptRecordEntityDO.setGmtModified( tmDelayAcceptRecordEntity.getGmtModified() );
        if ( tmDelayAcceptRecordEntity.hasId() ) {
            tmDelayAcceptRecordEntityDO.setId( tmDelayAcceptRecordEntity.getId() );
        }
        tmDelayAcceptRecordEntityDO.setProductCode( tmDelayAcceptRecordEntity.getProductCode() );
        tmDelayAcceptRecordEntityDO.setFlowCode( tmDelayAcceptRecordEntity.getFlowCode() );
        tmDelayAcceptRecordEntityDO.setVersionCode( tmDelayAcceptRecordEntity.getVersionCode() );
        tmDelayAcceptRecordEntityDO.setVersionName( tmDelayAcceptRecordEntity.getVersionName() );
        tmDelayAcceptRecordEntityDO.setAcceptRemark( tmDelayAcceptRecordEntity.getAcceptRemark() );
        tmDelayAcceptRecordEntityDO.setIsSend( tmDelayAcceptRecordEntity.getIsSend() );
        tmDelayAcceptRecordEntityDO.setSendTime( tmDelayAcceptRecordEntity.getSendTime() );
        tmDelayAcceptRecordEntityDO.setApprover( tmDelayAcceptRecordEntity.getApprover() );
        tmDelayAcceptRecordEntityDO.setAuditFlowCode( tmDelayAcceptRecordEntity.getAuditFlowCode() );

        return tmDelayAcceptRecordEntityDO;
    }

    @Override
    public List<TmDelayAcceptRecordEntityDO> convert2DO(List<TmDelayAcceptRecordEntity> tmDelayAcceptRecordEntity) {
        if ( tmDelayAcceptRecordEntity == null ) {
            return null;
        }

        List<TmDelayAcceptRecordEntityDO> list = new ArrayList<TmDelayAcceptRecordEntityDO>( tmDelayAcceptRecordEntity.size() );
        for ( TmDelayAcceptRecordEntity tmDelayAcceptRecordEntity1 : tmDelayAcceptRecordEntity ) {
            list.add( convert2DO( tmDelayAcceptRecordEntity1 ) );
        }

        return list;
    }

    @Override
    public TmDelayAcceptRecordEntity convert2Entity(TmDelayAcceptRecordEntityDO tmDelayAcceptRecordEntityDO) {
        if ( tmDelayAcceptRecordEntityDO == null ) {
            return null;
        }

        TmDelayAcceptRecordEntity tmDelayAcceptRecordEntity = new TmDelayAcceptRecordEntity();

        tmDelayAcceptRecordEntity.setEnable( tmDelayAcceptRecordEntityDO.getEnable() );
        tmDelayAcceptRecordEntity.setCreatorId( tmDelayAcceptRecordEntityDO.getCreatorId() );
        tmDelayAcceptRecordEntity.setCreator( tmDelayAcceptRecordEntityDO.getCreator() );
        tmDelayAcceptRecordEntity.setGmtCreate( tmDelayAcceptRecordEntityDO.getGmtCreate() );
        tmDelayAcceptRecordEntity.setModifierId( tmDelayAcceptRecordEntityDO.getModifierId() );
        tmDelayAcceptRecordEntity.setModifier( tmDelayAcceptRecordEntityDO.getModifier() );
        tmDelayAcceptRecordEntity.setGmtModified( tmDelayAcceptRecordEntityDO.getGmtModified() );
        tmDelayAcceptRecordEntity.setId( tmDelayAcceptRecordEntityDO.getId() );
        tmDelayAcceptRecordEntity.setFlowCode( tmDelayAcceptRecordEntityDO.getFlowCode() );
        tmDelayAcceptRecordEntity.setProductCode( tmDelayAcceptRecordEntityDO.getProductCode() );
        tmDelayAcceptRecordEntity.setVersionCode( tmDelayAcceptRecordEntityDO.getVersionCode() );
        tmDelayAcceptRecordEntity.setVersionName( tmDelayAcceptRecordEntityDO.getVersionName() );
        tmDelayAcceptRecordEntity.setAcceptRemark( tmDelayAcceptRecordEntityDO.getAcceptRemark() );
        tmDelayAcceptRecordEntity.setIsSend( tmDelayAcceptRecordEntityDO.getIsSend() );
        tmDelayAcceptRecordEntity.setSendTime( tmDelayAcceptRecordEntityDO.getSendTime() );
        tmDelayAcceptRecordEntity.setApprover( tmDelayAcceptRecordEntityDO.getApprover() );
        tmDelayAcceptRecordEntity.setAuditFlowCode( tmDelayAcceptRecordEntityDO.getAuditFlowCode() );

        return tmDelayAcceptRecordEntity;
    }
}
