package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.AttachmentEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentAddedEvent;
import com.zto.devops.qc.domain.model.TestcaseAttachment;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseAttachmentConverterImpl implements TestcaseAttachmentConverter {

    @Override
    public AttachmentEntity converter(TestcaseAttachmentAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        AttachmentEntity attachmentEntity = new AttachmentEntity();

        attachmentEntity.setCreatorId( eventTransactorUserId( event ) );
        attachmentEntity.setCreator( eventTransactorUserName( event ) );
        attachmentEntity.setGmtCreate( event.getOccurred() );
        attachmentEntity.setModifierId( eventTransactorUserId( event ) );
        attachmentEntity.setModifier( eventTransactorUserName( event ) );
        attachmentEntity.setGmtModified( event.getOccurred() );
        attachmentEntity.setCode( event.getCode() );
        attachmentEntity.setDomain( event.getDomain() );
        attachmentEntity.setBusinessCode( event.getBusinessCode() );
        attachmentEntity.setDocumentType( event.getDocumentType() );
        attachmentEntity.setType( event.getType() );
        attachmentEntity.setName( event.getName() );
        attachmentEntity.setRemoteFileId( event.getRemoteFileId() );
        attachmentEntity.setUrl( event.getUrl() );
        attachmentEntity.setFileType( event.getFileType() );
        attachmentEntity.setSize( event.getSize() );

        return attachmentEntity;
    }

    @Override
    public TestcaseAttachment domainConverter(AttachmentEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TestcaseAttachment testcaseAttachment = new TestcaseAttachment();

        testcaseAttachment.setCode( entity.getCode() );
        testcaseAttachment.setDomain( entity.getDomain() );
        testcaseAttachment.setBusinessCode( entity.getBusinessCode() );
        testcaseAttachment.setUrl( entity.getUrl() );
        testcaseAttachment.setName( entity.getName() );
        testcaseAttachment.setType( entity.getType() );
        testcaseAttachment.setDocumentType( entity.getDocumentType() );
        testcaseAttachment.setSize( entity.getSize() );
        testcaseAttachment.setFileType( entity.getFileType() );

        return testcaseAttachment;
    }

    @Override
    public AttachmentEntityDO convert(AttachmentEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AttachmentEntityDO attachmentEntityDO = new AttachmentEntityDO();

        attachmentEntityDO.setEnable( entity.getEnable() );
        attachmentEntityDO.setCreatorId( entity.getCreatorId() );
        attachmentEntityDO.setCreator( entity.getCreator() );
        attachmentEntityDO.setGmtCreate( entity.getGmtCreate() );
        attachmentEntityDO.setModifierId( entity.getModifierId() );
        attachmentEntityDO.setModifier( entity.getModifier() );
        attachmentEntityDO.setGmtModified( entity.getGmtModified() );
        attachmentEntityDO.setCode( entity.getCode() );
        attachmentEntityDO.setDomain( entity.getDomain() );
        attachmentEntityDO.setBusinessCode( entity.getBusinessCode() );
        attachmentEntityDO.setDocumentType( entity.getDocumentType() );
        attachmentEntityDO.setType( entity.getType() );
        attachmentEntityDO.setName( entity.getName() );
        attachmentEntityDO.setUrl( entity.getUrl() );
        attachmentEntityDO.setFileType( entity.getFileType() );
        attachmentEntityDO.setSize( entity.getSize() );

        return attachmentEntityDO;
    }

    @Override
    public List<AttachmentEntityDO> convertList(List<AttachmentEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AttachmentEntityDO> list = new ArrayList<AttachmentEntityDO>( entityList.size() );
        for ( AttachmentEntity attachmentEntity : entityList ) {
            list.add( convert( attachmentEntity ) );
        }

        return list;
    }

    @Override
    public AttachmentEntity convert2(AttachmentEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        AttachmentEntity attachmentEntity = new AttachmentEntity();

        attachmentEntity.setEnable( entityDO.getEnable() );
        attachmentEntity.setCreatorId( entityDO.getCreatorId() );
        attachmentEntity.setCreator( entityDO.getCreator() );
        attachmentEntity.setGmtCreate( entityDO.getGmtCreate() );
        attachmentEntity.setModifierId( entityDO.getModifierId() );
        attachmentEntity.setModifier( entityDO.getModifier() );
        attachmentEntity.setGmtModified( entityDO.getGmtModified() );
        attachmentEntity.setCode( entityDO.getCode() );
        attachmentEntity.setDomain( entityDO.getDomain() );
        attachmentEntity.setBusinessCode( entityDO.getBusinessCode() );
        attachmentEntity.setDocumentType( entityDO.getDocumentType() );
        attachmentEntity.setType( entityDO.getType() );
        attachmentEntity.setName( entityDO.getName() );
        attachmentEntity.setUrl( entityDO.getUrl() );
        attachmentEntity.setFileType( entityDO.getFileType() );
        attachmentEntity.setSize( entityDO.getSize() );

        return attachmentEntity;
    }

    private Long eventTransactorUserId(TestcaseAttachmentAddedEvent testcaseAttachmentAddedEvent) {
        if ( testcaseAttachmentAddedEvent == null ) {
            return null;
        }
        User transactor = testcaseAttachmentAddedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(TestcaseAttachmentAddedEvent testcaseAttachmentAddedEvent) {
        if ( testcaseAttachmentAddedEvent == null ) {
            return null;
        }
        User transactor = testcaseAttachmentAddedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }
}
