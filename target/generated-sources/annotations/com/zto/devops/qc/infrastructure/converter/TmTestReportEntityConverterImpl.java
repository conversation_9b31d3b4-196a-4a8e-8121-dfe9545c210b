package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.enums.report.AutoExecuteResult;
import com.zto.devops.qc.client.enums.report.CheckType;
import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmTestReportVO;
import com.zto.devops.qc.client.model.testmanager.report.event.TmAccessReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmAccessReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmOnlineSmokeReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmOnlineSmokeReportEditEvent;
import com.zto.devops.qc.infrastructure.dao.entity.ModuleTestEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestFunctionPointEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestReportEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmTestReportEntityConverterImpl implements TmTestReportEntityConverter {

    @Override
    public List<TmTestReportEntityDO> convert2DOList(List<TmTestReportEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmTestReportEntityDO> list = new ArrayList<TmTestReportEntityDO>( entityList.size() );
        for ( TmTestReportEntity tmTestReportEntity : entityList ) {
            list.add( convert2DO( tmTestReportEntity ) );
        }

        return list;
    }

    @Override
    public List<TmTestReportVO> convertVOList(List<TmTestReportEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmTestReportVO> list = new ArrayList<TmTestReportVO>( entityList.size() );
        for ( TmTestReportEntity tmTestReportEntity : entityList ) {
            list.add( tmTestReportEntityToTmTestReportVO( tmTestReportEntity ) );
        }

        return list;
    }

    @Override
    public List<TmModuleTestVO> convertModuleTest(List<ModuleTestEntity> moduleTestEntities) {
        if ( moduleTestEntities == null ) {
            return null;
        }

        List<TmModuleTestVO> list = new ArrayList<TmModuleTestVO>( moduleTestEntities.size() );
        for ( ModuleTestEntity moduleTestEntity : moduleTestEntities ) {
            list.add( moduleTestEntityToTmModuleTestVO( moduleTestEntity ) );
        }

        return list;
    }

    @Override
    public List<TmModuleTestVO> convertByFunctionPoint(List<TestFunctionPointEntity> functionPointEntityList) {
        if ( functionPointEntityList == null ) {
            return null;
        }

        List<TmModuleTestVO> list = new ArrayList<TmModuleTestVO>( functionPointEntityList.size() );
        for ( TestFunctionPointEntity testFunctionPointEntity : functionPointEntityList ) {
            list.add( testFunctionPointEntityToTmModuleTestVO( testFunctionPointEntity ) );
        }

        return list;
    }

    @Override
    public TmTestReportEntityDO convert2DO(TmTestReportEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TmTestReportEntityDO tmTestReportEntityDO = new TmTestReportEntityDO();

        tmTestReportEntityDO.setEnable( entity.getEnable() );
        tmTestReportEntityDO.setCreatorId( entity.getCreatorId() );
        tmTestReportEntityDO.setCreator( entity.getCreator() );
        tmTestReportEntityDO.setGmtCreate( entity.getGmtCreate() );
        tmTestReportEntityDO.setModifierId( entity.getModifierId() );
        tmTestReportEntityDO.setModifier( entity.getModifier() );
        tmTestReportEntityDO.setGmtModified( entity.getGmtModified() );
        tmTestReportEntityDO.setReportCode( entity.getReportCode() );
        tmTestReportEntityDO.setReportName( entity.getReportName() );
        tmTestReportEntityDO.setPlanCode( entity.getPlanCode() );
        tmTestReportEntityDO.setReportType( entity.getReportType() );
        tmTestReportEntityDO.setVersionCode( entity.getVersionCode() );
        tmTestReportEntityDO.setVersionName( entity.getVersionName() );
        tmTestReportEntityDO.setTestResult( entity.getTestResult() );
        tmTestReportEntityDO.setActualPresentationDate( entity.getActualPresentationDate() );
        tmTestReportEntityDO.setActualApprovalExitDate( entity.getActualApprovalExitDate() );
        tmTestReportEntityDO.setActualOnlineDate( entity.getActualOnlineDate() );
        tmTestReportEntityDO.setActualTestStart( entity.getActualTestStart() );
        tmTestReportEntityDO.setActualTestEnd( entity.getActualTestEnd() );
        tmTestReportEntityDO.setCheckStartDate( entity.getCheckStartDate() );
        tmTestReportEntityDO.setCheckEndDate( entity.getCheckEndDate() );
        tmTestReportEntityDO.setUpdateTestResultDate( entity.getUpdateTestResultDate() );
        tmTestReportEntityDO.setAutoTestResult( entity.getAutoTestResult() );
        tmTestReportEntityDO.setSecurityUserId( entity.getSecurityUserId() );
        tmTestReportEntityDO.setSecurityUserName( entity.getSecurityUserName() );
        tmTestReportEntityDO.setSecurityTestResult( entity.getSecurityTestResult() );
        tmTestReportEntityDO.setCheckType( entity.getCheckType() );
        tmTestReportEntityDO.setDeveloperCount( entity.getDeveloperCount() );
        tmTestReportEntityDO.setTesterCount( entity.getTesterCount() );
        tmTestReportEntityDO.setTestCaseNum( entity.getTestCaseNum() );
        tmTestReportEntityDO.setPlanCaseNum( entity.getPlanCaseNum() );
        tmTestReportEntityDO.setPermitNum( entity.getPermitNum() );
        tmTestReportEntityDO.setAsPlanedOnline( entity.getAsPlanedOnline() );
        tmTestReportEntityDO.setDelay( entity.getDelay() );
        tmTestReportEntityDO.setSummary( entity.getSummary() );
        tmTestReportEntityDO.setStatus( entity.getStatus() );
        tmTestReportEntityDO.setCodeCoverResult( entity.getCodeCoverResult() );
        tmTestReportEntityDO.setCodeCoverReason( entity.getCodeCoverReason() );
        tmTestReportEntityDO.setPlanPresentationDate( entity.getPlanPresentationDate() );
        tmTestReportEntityDO.setPlanApprovalExitDate( entity.getPlanApprovalExitDate() );
        tmTestReportEntityDO.setPlanOnlineDate( entity.getPlanOnlineDate() );
        tmTestReportEntityDO.setZuiTestResult( entity.getZuiTestResult() );
        tmTestReportEntityDO.setUiTestResult( entity.getUiTestResult() );

        return tmTestReportEntityDO;
    }

    @Override
    public TmTestReportEntity converter(TmAccessReportAddEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );

        return tmTestReportEntity;
    }

    @Override
    public TmTestReportEntity converter(TmAccessReportEditEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );

        return tmTestReportEntity;
    }

    @Override
    public TmTestReportEntity converter(TmOnlineSmokeReportAddEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );
        tmTestReportEntity.setZuiTestResult( event.getZuiTestResult() );

        return tmTestReportEntity;
    }

    @Override
    public TmTestReportEntity converter(TmOnlineSmokeReportEditEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );
        tmTestReportEntity.setZuiTestResult( event.getZuiTestResult() );

        return tmTestReportEntity;
    }

    protected TmTestReportVO tmTestReportEntityToTmTestReportVO(TmTestReportEntity tmTestReportEntity) {
        if ( tmTestReportEntity == null ) {
            return null;
        }

        TmTestReportVO tmTestReportVO = new TmTestReportVO();

        tmTestReportVO.setReportCode( tmTestReportEntity.getReportCode() );
        tmTestReportVO.setReportName( tmTestReportEntity.getReportName() );
        tmTestReportVO.setPlanCode( tmTestReportEntity.getPlanCode() );
        tmTestReportVO.setReportType( tmTestReportEntity.getReportType() );
        tmTestReportVO.setVersionCode( tmTestReportEntity.getVersionCode() );
        tmTestReportVO.setVersionName( tmTestReportEntity.getVersionName() );
        tmTestReportVO.setTestResult( tmTestReportEntity.getTestResult() );
        tmTestReportVO.setActualPresentationDate( tmTestReportEntity.getActualPresentationDate() );
        tmTestReportVO.setActualApprovalExitDate( tmTestReportEntity.getActualApprovalExitDate() );
        tmTestReportVO.setActualOnlineDate( tmTestReportEntity.getActualOnlineDate() );
        tmTestReportVO.setActualTestStart( tmTestReportEntity.getActualTestStart() );
        tmTestReportVO.setActualTestEnd( tmTestReportEntity.getActualTestEnd() );
        tmTestReportVO.setCheckStartDate( tmTestReportEntity.getCheckStartDate() );
        tmTestReportVO.setCheckEndDate( tmTestReportEntity.getCheckEndDate() );
        tmTestReportVO.setUpdateTestResultDate( tmTestReportEntity.getUpdateTestResultDate() );
        tmTestReportVO.setAutoTestResult( tmTestReportEntity.getAutoTestResult() );
        tmTestReportVO.setSecurityUserId( tmTestReportEntity.getSecurityUserId() );
        tmTestReportVO.setSecurityUserName( tmTestReportEntity.getSecurityUserName() );
        tmTestReportVO.setSecurityTestResult( tmTestReportEntity.getSecurityTestResult() );
        tmTestReportVO.setCheckType( tmTestReportEntity.getCheckType() );
        tmTestReportVO.setDeveloperCount( tmTestReportEntity.getDeveloperCount() );
        tmTestReportVO.setTesterCount( tmTestReportEntity.getTesterCount() );
        tmTestReportVO.setTestCaseNum( tmTestReportEntity.getTestCaseNum() );
        tmTestReportVO.setPlanCaseNum( tmTestReportEntity.getPlanCaseNum() );
        tmTestReportVO.setPermitNum( tmTestReportEntity.getPermitNum() );
        tmTestReportVO.setAsPlanedOnline( tmTestReportEntity.getAsPlanedOnline() );
        tmTestReportVO.setDelay( tmTestReportEntity.getDelay() );
        tmTestReportVO.setSummary( tmTestReportEntity.getSummary() );
        tmTestReportVO.setStatus( tmTestReportEntity.getStatus() );
        tmTestReportVO.setCodeCoverResult( tmTestReportEntity.getCodeCoverResult() );
        tmTestReportVO.setCodeCoverReason( tmTestReportEntity.getCodeCoverReason() );
        tmTestReportVO.setPlanPresentationDate( tmTestReportEntity.getPlanPresentationDate() );
        tmTestReportVO.setPlanApprovalExitDate( tmTestReportEntity.getPlanApprovalExitDate() );
        tmTestReportVO.setPlanOnlineDate( tmTestReportEntity.getPlanOnlineDate() );

        return tmTestReportVO;
    }

    protected TmModuleTestVO moduleTestEntityToTmModuleTestVO(ModuleTestEntity moduleTestEntity) {
        if ( moduleTestEntity == null ) {
            return null;
        }

        TmModuleTestVO tmModuleTestVO = new TmModuleTestVO();

        tmModuleTestVO.setCode( moduleTestEntity.getCode() );
        tmModuleTestVO.setReportCode( moduleTestEntity.getReportCode() );
        tmModuleTestVO.setTestType( moduleTestEntity.getTestType() );
        tmModuleTestVO.setTestResult( moduleTestEntity.getTestResult() );
        tmModuleTestVO.setValidIssueCount( moduleTestEntity.getValidIssueCount() );
        tmModuleTestVO.setLegacyIssueCount( moduleTestEntity.getLegacyIssueCount() );
        tmModuleTestVO.setReportUserId( moduleTestEntity.getReportUserId() );
        tmModuleTestVO.setReportUserName( moduleTestEntity.getReportUserName() );

        return tmModuleTestVO;
    }

    protected TmModuleTestVO testFunctionPointEntityToTmModuleTestVO(TestFunctionPointEntity testFunctionPointEntity) {
        if ( testFunctionPointEntity == null ) {
            return null;
        }

        TmModuleTestVO tmModuleTestVO = new TmModuleTestVO();

        tmModuleTestVO.setCode( testFunctionPointEntity.getCode() );

        return tmModuleTestVO;
    }

    protected com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult autoExecuteResultToAutoExecuteResult(AutoExecuteResult autoExecuteResult) {
        if ( autoExecuteResult == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult autoExecuteResult1;

        switch ( autoExecuteResult ) {
            case UNKNOWN: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.UNKNOWN;
            break;
            case WAIT_EXECUTED: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.WAIT_EXECUTED;
            break;
            case EXECUTING: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.EXECUTING;
            break;
            case NO_EXECUTE: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.NO_EXECUTE;
            break;
            case PASS_BY: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.PASS_BY;
            break;
            case NO_PASS: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.NO_PASS;
            break;
            case FAILURE: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.FAILURE;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + autoExecuteResult );
        }

        return autoExecuteResult1;
    }

    protected com.zto.devops.qc.client.enums.testmanager.report.CheckType checkTypeToCheckType(CheckType checkType) {
        if ( checkType == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.testmanager.report.CheckType checkType1;

        switch ( checkType ) {
            case UNKNOWN: checkType1 = com.zto.devops.qc.client.enums.testmanager.report.CheckType.UNKNOWN;
            break;
            case FOREIGN: checkType1 = com.zto.devops.qc.client.enums.testmanager.report.CheckType.FOREIGN;
            break;
            case INTERNAL: checkType1 = com.zto.devops.qc.client.enums.testmanager.report.CheckType.INTERNAL;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + checkType );
        }

        return checkType1;
    }
}
