package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmSceneDebugRecordEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TmSceneDebugRecordEntity;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmSceneDebugRecordEntityConverterImpl implements TmSceneDebugRecordEntityConverter {

    @Override
    public TmSceneDebugRecordEntityDO convert(TmSceneDebugRecordEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TmSceneDebugRecordEntityDO tmSceneDebugRecordEntityDO = new TmSceneDebugRecordEntityDO();

        tmSceneDebugRecordEntityDO.setId( entity.getId() );
        tmSceneDebugRecordEntityDO.setRecordCode( entity.getRecordCode() );
        tmSceneDebugRecordEntityDO.setTaskId( entity.getTaskId() );
        tmSceneDebugRecordEntityDO.setProductCode( entity.getProductCode() );
        tmSceneDebugRecordEntityDO.setSceneCode( entity.getSceneCode() );
        tmSceneDebugRecordEntityDO.setBucketName( entity.getBucketName() );
        tmSceneDebugRecordEntityDO.setLogOssPath( entity.getLogOssPath() );
        tmSceneDebugRecordEntityDO.setEnable( entity.getEnable() );
        tmSceneDebugRecordEntityDO.setCreator( entity.getCreator() );
        tmSceneDebugRecordEntityDO.setCreatorId( entity.getCreatorId() );
        tmSceneDebugRecordEntityDO.setModifier( entity.getModifier() );
        tmSceneDebugRecordEntityDO.setModifierId( entity.getModifierId() );
        tmSceneDebugRecordEntityDO.setGmtCreate( entity.getGmtCreate() );
        tmSceneDebugRecordEntityDO.setGmtModified( entity.getGmtModified() );

        return tmSceneDebugRecordEntityDO;
    }
}
