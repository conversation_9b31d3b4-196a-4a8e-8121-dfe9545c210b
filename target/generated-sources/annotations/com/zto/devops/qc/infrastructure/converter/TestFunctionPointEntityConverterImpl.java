package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestFunctionPointEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TestFunctionPointEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestFunctionPointEntityConverterImpl implements TestFunctionPointEntityConverter {

    @Override
    public List<TestFunctionPointEntityDO> convert2DOList(List<TestFunctionPointEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TestFunctionPointEntityDO> list = new ArrayList<TestFunctionPointEntityDO>( entityList.size() );
        for ( TestFunctionPointEntity testFunctionPointEntity : entityList ) {
            list.add( testFunctionPointEntityToTestFunctionPointEntityDO( testFunctionPointEntity ) );
        }

        return list;
    }

    protected TestFunctionPointEntityDO testFunctionPointEntityToTestFunctionPointEntityDO(TestFunctionPointEntity testFunctionPointEntity) {
        if ( testFunctionPointEntity == null ) {
            return null;
        }

        TestFunctionPointEntityDO testFunctionPointEntityDO = new TestFunctionPointEntityDO();

        if ( testFunctionPointEntity.hasId() ) {
            testFunctionPointEntityDO.setId( testFunctionPointEntity.getId() );
        }
        testFunctionPointEntityDO.setCode( testFunctionPointEntity.getCode() );
        testFunctionPointEntityDO.setBusinessCode( testFunctionPointEntity.getBusinessCode() );
        testFunctionPointEntityDO.setType( testFunctionPointEntity.getType() );
        testFunctionPointEntityDO.setFunctionPoint( testFunctionPointEntity.getFunctionPoint() );
        testFunctionPointEntityDO.setDirectorId( testFunctionPointEntity.getDirectorId() );
        testFunctionPointEntityDO.setDirectorName( testFunctionPointEntity.getDirectorName() );
        testFunctionPointEntityDO.setCreatorId( testFunctionPointEntity.getCreatorId() );
        testFunctionPointEntityDO.setCreator( testFunctionPointEntity.getCreator() );
        testFunctionPointEntityDO.setGmtCreate( testFunctionPointEntity.getGmtCreate() );
        testFunctionPointEntityDO.setModifierId( testFunctionPointEntity.getModifierId() );
        testFunctionPointEntityDO.setModifier( testFunctionPointEntity.getModifier() );
        testFunctionPointEntityDO.setGmtModified( testFunctionPointEntity.getGmtModified() );
        testFunctionPointEntityDO.setEnable( testFunctionPointEntity.getEnable() );
        testFunctionPointEntityDO.setNumber( testFunctionPointEntity.getNumber() );

        return testFunctionPointEntityDO;
    }
}
