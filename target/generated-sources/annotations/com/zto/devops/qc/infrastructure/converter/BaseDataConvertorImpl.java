package com.zto.devops.qc.infrastructure.converter;

import com.zto.agent.base.api.customer.response.GetCompanyInfoResponse;
import com.zto.base.bean.response.UserResp;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreBrandVO;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreNameVO;
import com.zto.devops.qc.client.model.rpc.outlet.ItemTypeVO;
import com.zto.devops.qc.client.model.rpc.pipeline.UserRespVO;
import com.zto.devops.qc.client.model.rpc.waybill.LabelNameVO;
import com.zto.huiyan.terminal.facade.manage.AgentManageInfoFO;
import com.zto.outlet.bean.response.TDictionaryResult;
import com.zto.waybill.tag.dto.LabelAndValueCO;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class BaseDataConvertorImpl implements BaseDataConvertor {

    @Override
    public UserRespVO convert(UserResp resp) {
        if ( resp == null ) {
            return null;
        }

        UserRespVO userRespVO = new UserRespVO();

        userRespVO.setId( resp.getId() );
        userRespVO.setCode( resp.getCode() );
        userRespVO.setUserName( resp.getUserName() );
        userRespVO.setRealName( resp.getRealName() );
        userRespVO.setNickName( resp.getNickName() );
        userRespVO.setQuickQuery( resp.getQuickQuery() );
        userRespVO.setCompanyId( resp.getCompanyId() );
        userRespVO.setCompanyName( resp.getCompanyName() );
        userRespVO.setSubcompanyId( resp.getSubcompanyId() );
        userRespVO.setSubcompanyName( resp.getSubcompanyName() );
        userRespVO.setDepartmentId( resp.getDepartmentId() );
        userRespVO.setDepartmentName( resp.getDepartmentName() );
        userRespVO.setDepartmentCode( resp.getDepartmentCode() );
        userRespVO.setWorkgroupId( resp.getWorkgroupId() );
        userRespVO.setWorkgroupName( resp.getWorkgroupName() );
        userRespVO.setWorkCategory( resp.getWorkCategory() );
        userRespVO.setSecurityLevel( resp.getSecurityLevel() );
        userRespVO.setSignature( resp.getSignature() );
        userRespVO.setDuty( resp.getDuty() );
        userRespVO.setTitle( resp.getTitle() );
        userRespVO.setGender( resp.getGender() );
        userRespVO.setBirthday( resp.getBirthday() );
        userRespVO.setLang( resp.getLang() );
        userRespVO.setTheme( resp.getTheme() );
        userRespVO.setScore( resp.getScore() );
        userRespVO.setFans( resp.getFans() );
        userRespVO.setHomeAddress( resp.getHomeAddress() );
        userRespVO.setIsStaff( resp.getIsStaff() );
        userRespVO.setIsVisible( resp.getIsVisible() );
        userRespVO.setEnabled( resp.getEnabled() );
        userRespVO.setAuditStatus( resp.getAuditStatus() );
        userRespVO.setDeletionstatecode( resp.getDeletionstatecode() );
        userRespVO.setSortCode( resp.getSortCode() );
        userRespVO.setDescription( resp.getDescription() );
        userRespVO.setSubdepartmentId( resp.getSubdepartmentId() );
        userRespVO.setSubdepartmentName( resp.getSubdepartmentName() );
        userRespVO.setProvince( resp.getProvince() );
        userRespVO.setCity( resp.getCity() );
        userRespVO.setDistrict( resp.getDistrict() );
        userRespVO.setUserFrom( resp.getUserFrom() );
        userRespVO.setSimpleSpelling( resp.getSimpleSpelling() );
        userRespVO.setIdCard( resp.getIdCard() );
        userRespVO.setManagerId( resp.getManagerId() );
        userRespVO.setIsAdministrator( resp.getIsAdministrator() );
        userRespVO.setManagerAuditStatus( resp.getManagerAuditStatus() );
        userRespVO.setManagerAuditDate( resp.getManagerAuditDate() );
        userRespVO.setIscheckBalance( resp.getIscheckBalance() );
        userRespVO.setCreateon( resp.getCreateon() );
        userRespVO.setCreateUserId( resp.getCreateUserId() );
        userRespVO.setCreateby( resp.getCreateby() );
        userRespVO.setModifiedon( resp.getModifiedon() );
        userRespVO.setModifiedUserId( resp.getModifiedUserId() );
        userRespVO.setModifiedby( resp.getModifiedby() );
        userRespVO.setMobile( resp.getMobile() );
        userRespVO.setTelephone( resp.getTelephone() );
        userRespVO.setMobileValiated( resp.getMobileValiated() );
        userRespVO.setOrgCode( resp.getOrgCode() );

        return userRespVO;
    }

    @Override
    public LabelNameVO convert(LabelAndValueCO<String> valueCO) {
        if ( valueCO == null ) {
            return null;
        }

        LabelNameVO labelNameVO = new LabelNameVO();

        labelNameVO.setLabel( valueCO.getLabel() );
        labelNameVO.setValue( valueCO.getValue() );

        return labelNameVO;
    }

    @Override
    public ItemTypeVO convert(TDictionaryResult result) {
        if ( result == null ) {
            return null;
        }

        ItemTypeVO itemTypeVO = new ItemTypeVO();

        itemTypeVO.setCode( result.getCode() );
        itemTypeVO.setName( result.getName() );

        return itemTypeVO;
    }

    @Override
    public StoreBrandVO convert(GetCompanyInfoResponse response) {
        if ( response == null ) {
            return null;
        }

        StoreBrandVO storeBrandVO = new StoreBrandVO();

        storeBrandVO.setCompanyName( response.getComName() );
        storeBrandVO.setCompanyCode( response.getCompanyCode() );

        return storeBrandVO;
    }

    @Override
    public StoreNameVO nameConvert(AgentManageInfoFO infoFO) {
        if ( infoFO == null ) {
            return null;
        }

        StoreNameVO storeNameVO = new StoreNameVO();

        storeNameVO.setAgentType( infoFO.getAgentType() );
        storeNameVO.setAgentTypeDesc( infoFO.getAgentTypeDesc() );
        storeNameVO.setAgentCode( infoFO.getAgentCode() );
        storeNameVO.setAgentName( infoFO.getAgentName() );

        return storeNameVO;
    }
}
