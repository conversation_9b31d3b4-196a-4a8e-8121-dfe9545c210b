package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.domain.model.RelevantUser;
import com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class RelevantUserEntityConverterImpl implements RelevantUserEntityConverter {

    @Override
    public List<RelevantUserEntity> convert(Collection<RelevantUserVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<RelevantUserEntity> list = new ArrayList<RelevantUserEntity>( vo.size() );
        for ( RelevantUserVO relevantUserVO : vo ) {
            list.add( convert( relevantUserVO ) );
        }

        return list;
    }

    @Override
    public RelevantUserEntity convert(RelevantUserVO vo) {
        if ( vo == null ) {
            return null;
        }

        RelevantUserEntity relevantUserEntity = new RelevantUserEntity();

        relevantUserEntity.setCreatorId( vo.getCreatorId() );
        relevantUserEntity.setCreator( vo.getCreator() );
        relevantUserEntity.setCode( vo.getCode() );
        if ( vo.getUserId() != null ) {
            relevantUserEntity.setUserId( String.valueOf( vo.getUserId() ) );
        }
        relevantUserEntity.setUserName( vo.getUserName() );
        relevantUserEntity.setType( vo.getType() );
        relevantUserEntity.setBusinessCode( vo.getBusinessCode() );
        relevantUserEntity.setDomain( vo.getDomain() );

        return relevantUserEntity;
    }

    @Override
    public RelevantUserVO convert(RelevantUserEntity entity) {
        if ( entity == null ) {
            return null;
        }

        RelevantUserVO relevantUserVO = new RelevantUserVO();

        relevantUserVO.setType( entity.getType() );
        relevantUserVO.setBusinessCode( entity.getBusinessCode() );
        relevantUserVO.setCode( entity.getCode() );
        if ( entity.getUserId() != null ) {
            relevantUserVO.setUserId( Long.parseLong( entity.getUserId() ) );
        }
        relevantUserVO.setUserName( entity.getUserName() );
        relevantUserVO.setDomain( entity.getDomain() );
        relevantUserVO.setCreatorId( entity.getCreatorId() );
        relevantUserVO.setCreator( entity.getCreator() );

        return relevantUserVO;
    }

    @Override
    public List<RelevantUserVO> convert(List<RelevantUserEntity> vo) {
        if ( vo == null ) {
            return null;
        }

        List<RelevantUserVO> list = new ArrayList<RelevantUserVO>( vo.size() );
        for ( RelevantUserEntity relevantUserEntity : vo ) {
            list.add( convert( relevantUserEntity ) );
        }

        return list;
    }

    @Override
    public Set<RelevantUser> convertList(Collection<RelevantUserEntity> relevantUserEntities) {
        if ( relevantUserEntities == null ) {
            return null;
        }

        Set<RelevantUser> set = new HashSet<RelevantUser>( Math.max( (int) ( relevantUserEntities.size() / .75f ) + 1, 16 ) );
        for ( RelevantUserEntity relevantUserEntity : relevantUserEntities ) {
            set.add( relevantUserEntityToRelevantUser( relevantUserEntity ) );
        }

        return set;
    }

    protected RelevantUser relevantUserEntityToRelevantUser(RelevantUserEntity relevantUserEntity) {
        if ( relevantUserEntity == null ) {
            return null;
        }

        RelevantUser relevantUser = new RelevantUser();

        relevantUser.setBusinessCode( relevantUserEntity.getBusinessCode() );
        relevantUser.setCode( relevantUserEntity.getCode() );
        if ( relevantUserEntity.getUserId() != null ) {
            relevantUser.setUserId( Long.parseLong( relevantUserEntity.getUserId() ) );
        }
        relevantUser.setUserName( relevantUserEntity.getUserName() );
        relevantUser.setDomain( relevantUserEntity.getDomain() );
        relevantUser.setType( relevantUserEntity.getType() );

        return relevantUser;
    }
}
