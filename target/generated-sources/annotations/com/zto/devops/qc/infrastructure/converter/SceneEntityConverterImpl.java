package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.SceneInfoEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class SceneEntityConverterImpl implements SceneEntityConverter {

    @Override
    public List<SceneInfoEntityDO> converterToSceneInfoEntityList(List<SceneInfoEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SceneInfoEntityDO> list = new ArrayList<SceneInfoEntityDO>( entityList.size() );
        for ( SceneInfoEntity sceneInfoEntity : entityList ) {
            list.add( sceneInfoEntityToSceneInfoEntityDO( sceneInfoEntity ) );
        }

        return list;
    }

    protected SceneInfoEntityDO sceneInfoEntityToSceneInfoEntityDO(SceneInfoEntity sceneInfoEntity) {
        if ( sceneInfoEntity == null ) {
            return null;
        }

        SceneInfoEntityDO sceneInfoEntityDO = new SceneInfoEntityDO();

        sceneInfoEntityDO.setId( sceneInfoEntity.getId() );
        sceneInfoEntityDO.setSceneCode( sceneInfoEntity.getSceneCode() );
        sceneInfoEntityDO.setSceneName( sceneInfoEntity.getSceneName() );
        sceneInfoEntityDO.setProductCode( sceneInfoEntity.getProductCode() );
        sceneInfoEntityDO.setAutomaticSourceCode( sceneInfoEntity.getAutomaticSourceCode() );
        sceneInfoEntityDO.setSceneVersion( sceneInfoEntity.getSceneVersion() );
        sceneInfoEntityDO.setSceneInfoDesc( sceneInfoEntity.getSceneInfoDesc() );
        sceneInfoEntityDO.setSceneOssPath( sceneInfoEntity.getSceneOssPath() );
        sceneInfoEntityDO.setSceneOssFile( sceneInfoEntity.getSceneOssFile() );
        sceneInfoEntityDO.setSceneBackDataMd5( sceneInfoEntity.getSceneBackDataMd5() );
        sceneInfoEntityDO.setStatus( sceneInfoEntity.getStatus() );
        sceneInfoEntityDO.setEnable( sceneInfoEntity.getEnable() );
        sceneInfoEntityDO.setSceneType( sceneInfoEntity.getSceneType() );
        sceneInfoEntityDO.setStepRecord( sceneInfoEntity.getStepRecord() );
        sceneInfoEntityDO.setCreatorId( sceneInfoEntity.getCreatorId() );
        sceneInfoEntityDO.setCreator( sceneInfoEntity.getCreator() );
        sceneInfoEntityDO.setGmtCreate( sceneInfoEntity.getGmtCreate() );
        sceneInfoEntityDO.setModifierId( sceneInfoEntity.getModifierId() );
        sceneInfoEntityDO.setModifier( sceneInfoEntity.getModifier() );
        sceneInfoEntityDO.setGmtModified( sceneInfoEntity.getGmtModified() );
        sceneInfoEntityDO.setSceneFrontData( sceneInfoEntity.getSceneFrontData() );
        sceneInfoEntityDO.setSceneBackData( sceneInfoEntity.getSceneBackData() );
        sceneInfoEntityDO.setShareStatus( sceneInfoEntity.getShareStatus() );
        sceneInfoEntityDO.setIsCollect( sceneInfoEntity.getIsCollect() );
        sceneInfoEntityDO.setSceneTagData( sceneInfoEntity.getSceneTagData() );

        return sceneInfoEntityDO;
    }
}
