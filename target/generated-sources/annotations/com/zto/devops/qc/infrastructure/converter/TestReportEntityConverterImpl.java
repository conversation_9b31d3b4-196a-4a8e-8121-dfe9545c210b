package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.enums.report.AutoExecuteResult;
import com.zto.devops.qc.client.enums.report.CheckType;
import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.devops.qc.client.enums.report.ReviewType;
import com.zto.devops.qc.client.enums.report.TestResultEunm;
import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.model.dto.ReviewInfoEntityDO;
import com.zto.devops.qc.client.model.dto.TestReportEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoDTO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewRenewalVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.devops.qc.client.model.testmanager.report.event.ExternalReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ExternalReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.MobileTestReportAddedEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.MobileTestReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ReportBaseEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ReviewReportAddedEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ReviewReportEditedEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.SimpleReportEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmPermitReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmPermitReportEditedEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import com.zto.devops.qc.infrastructure.dao.entity.ModuleTestEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestReportEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmReviewInfoEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmReviewOpinionEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmReviewRenewalEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestReportEntity;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestReportEntityConverterImpl implements TestReportEntityConverter {

    @Override
    public TestReportEntityDO convert(TestReportEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TestReportEntityDO testReportEntityDO = new TestReportEntityDO();

        testReportEntityDO.setEnable( entity.getEnable() );
        testReportEntityDO.setCreatorId( entity.getCreatorId() );
        testReportEntityDO.setCreator( entity.getCreator() );
        testReportEntityDO.setGmtCreate( entity.getGmtCreate() );
        testReportEntityDO.setModifierId( entity.getModifierId() );
        testReportEntityDO.setModifier( entity.getModifier() );
        testReportEntityDO.setGmtModified( entity.getGmtModified() );
        testReportEntityDO.setCode( entity.getCode() );
        testReportEntityDO.setName( entity.getName() );
        testReportEntityDO.setPlanCode( entity.getPlanCode() );
        testReportEntityDO.setReportType( entity.getReportType() );
        testReportEntityDO.setVersionCode( entity.getVersionCode() );
        testReportEntityDO.setVersionName( entity.getVersionName() );
        testReportEntityDO.setTestResult( entity.getTestResult() );
        testReportEntityDO.setActualPresentationDate( entity.getActualPresentationDate() );
        testReportEntityDO.setActualApprovalExitDate( entity.getActualApprovalExitDate() );
        testReportEntityDO.setActualPublishDate( entity.getActualPublishDate() );
        testReportEntityDO.setActualTestStart( entity.getActualTestStart() );
        testReportEntityDO.setActualTestEnd( entity.getActualTestEnd() );
        testReportEntityDO.setCheckStartDate( entity.getCheckStartDate() );
        testReportEntityDO.setCheckEndDate( entity.getCheckEndDate() );
        testReportEntityDO.setUpdateTestResultDate( entity.getUpdateTestResultDate() );
        testReportEntityDO.setAutoTestResult( entity.getAutoTestResult() );
        testReportEntityDO.setSecurityUserId( entity.getSecurityUserId() );
        testReportEntityDO.setSecurityUserName( entity.getSecurityUserName() );
        testReportEntityDO.setSecurityTestResult( entity.getSecurityTestResult() );
        testReportEntityDO.setCheckType( entity.getCheckType() );
        testReportEntityDO.setDeveloperCount( entity.getDeveloperCount() );
        testReportEntityDO.setTesterCount( entity.getTesterCount() );
        testReportEntityDO.setPlanSmokeCase( entity.getPlanSmokeCase() );
        testReportEntityDO.setFirstPermitSmoke( entity.getFirstPermitSmoke() );
        testReportEntityDO.setAsPlanedOnline( entity.getAsPlanedOnline() );
        testReportEntityDO.setDelay( entity.getDelay() );
        testReportEntityDO.setSummary( entity.getSummary() );
        testReportEntityDO.setStatus( entity.getStatus() );
        testReportEntityDO.setPreview( entity.getPreview() );
        testReportEntityDO.setCodeCoverResult( entity.getCodeCoverResult() );
        List<Map<String, String>> list = entity.getCodeCoverReason();
        if ( list != null ) {
            testReportEntityDO.setCodeCoverReason( new ArrayList<Map<String, String>>( list ) );
        }

        return testReportEntityDO;
    }

    @Override
    public List<TestReportEntityDO> convertList(List<TestReportEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TestReportEntityDO> list = new ArrayList<TestReportEntityDO>( entityList.size() );
        for ( TestReportEntity testReportEntity : entityList ) {
            list.add( convert( testReportEntity ) );
        }

        return list;
    }

    @Override
    public AttachmentEntity convertAttachments(AttachmentVO attachments) {
        if ( attachments == null ) {
            return null;
        }

        AttachmentEntity attachmentEntity = new AttachmentEntity();

        attachmentEntity.setCreatorId( attachments.getCreatorId() );
        attachmentEntity.setCreator( attachments.getCreator() );
        attachmentEntity.setGmtCreate( attachments.getGmtCreate() );
        attachmentEntity.setModifierId( attachments.getModifierId() );
        attachmentEntity.setModifier( attachments.getModifier() );
        attachmentEntity.setGmtModified( attachments.getGmtModified() );
        attachmentEntity.setDomain( attachments.getDomain() );
        attachmentEntity.setBusinessCode( attachments.getBusinessCode() );
        attachmentEntity.setDocumentType( attachments.getDocumentType() );
        attachmentEntity.setType( attachments.getType() );
        attachmentEntity.setName( attachments.getName() );
        attachmentEntity.setRemoteFileId( attachments.getRemoteFileId() );
        attachmentEntity.setUrl( attachments.getUrl() );
        attachmentEntity.setFileType( attachments.getFileType() );
        attachmentEntity.setSize( attachments.getSize() );

        attachmentEntity.setCode( com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.SNOWFLAKE) );

        return attachmentEntity;
    }

    @Override
    public List<AttachmentEntity> convertAttachments(List<AttachmentVO> attachments) {
        if ( attachments == null ) {
            return null;
        }

        List<AttachmentEntity> list = new ArrayList<AttachmentEntity>( attachments.size() );
        for ( AttachmentVO attachmentVO : attachments ) {
            list.add( convertAttachments( attachmentVO ) );
        }

        return list;
    }

    @Override
    public TmReviewInfoEntity convertReviewInfo(ReviewInfoDTO reviewInfo) {
        if ( reviewInfo == null ) {
            return null;
        }

        TmReviewInfoEntity tmReviewInfoEntity = new TmReviewInfoEntity();

        tmReviewInfoEntity.setCode( reviewInfo.getCode() );
        tmReviewInfoEntity.setReportCode( reviewInfo.getReportCode() );
        tmReviewInfoEntity.setReviewDate( reviewInfo.getReviewDate() );
        tmReviewInfoEntity.setReviewType( reviewTypeToReviewType( reviewInfo.getReviewType() ) );
        tmReviewInfoEntity.setRecordUserId( reviewInfo.getRecordUserId() );
        tmReviewInfoEntity.setRecordUserName( reviewInfo.getRecordUserName() );
        tmReviewInfoEntity.setProductReviewUsers( reviewInfo.getProductReviewUsers() );
        tmReviewInfoEntity.setDevelopReviewUsers( reviewInfo.getDevelopReviewUsers() );
        tmReviewInfoEntity.setTestReviewUsers( reviewInfo.getTestReviewUsers() );

        return tmReviewInfoEntity;
    }

    @Override
    public List<TmReviewRenewalEntity> convertReviewRenwals(List<ReviewRenewalVO> reviewRenewals) {
        if ( reviewRenewals == null ) {
            return null;
        }

        List<TmReviewRenewalEntity> list = new ArrayList<TmReviewRenewalEntity>( reviewRenewals.size() );
        for ( ReviewRenewalVO reviewRenewalVO : reviewRenewals ) {
            list.add( reviewRenewalVOToTmReviewRenewalEntity( reviewRenewalVO ) );
        }

        return list;
    }

    @Override
    public List<TmReviewOpinionEntity> convertReviewOpinions(List<ReviewOpinionVO> reviewOpinions) {
        if ( reviewOpinions == null ) {
            return null;
        }

        List<TmReviewOpinionEntity> list = new ArrayList<TmReviewOpinionEntity>( reviewOpinions.size() );
        for ( ReviewOpinionVO reviewOpinionVO : reviewOpinions ) {
            list.add( reviewOpinionVOToTmReviewOpinionEntity( reviewOpinionVO ) );
        }

        return list;
    }

    @Override
    public TmTestReportEntity convert(TmPermitReportAddEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setCodeCoverResult( event.getCoverageResult() );
        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );
        tmTestReportEntity.setZuiTestResult( event.getZuiTestResult() );
        tmTestReportEntity.setUiTestResult( event.getUiTestResult() );

        tmTestReportEntity.setCodeCoverReason( com.zto.devops.framework.common.util.CollectionUtil.isNotEmpty(event.getCoverageReasonVOS()) ? com.alibaba.fastjson.JSON.toJSONString(event.getCoverageReasonVOS()) : "" );

        return tmTestReportEntity;
    }

    @Override
    public TmTestReportEntity convert(TmPermitReportEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setCodeCoverResult( event.getCoverageResult() );
        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );
        tmTestReportEntity.setZuiTestResult( event.getZuiTestResult() );
        tmTestReportEntity.setUiTestResult( event.getUiTestResult() );

        tmTestReportEntity.setCodeCoverReason( com.zto.devops.framework.common.util.CollectionUtil.isNotEmpty(event.getCoverageReasonVOS()) ? com.alibaba.fastjson.JSON.toJSONString(event.getCoverageReasonVOS()) : "" );

        return tmTestReportEntity;
    }

    @Override
    public List<ModuleTestEntity> convertModuleTests(List<TmModuleTestVO> moduleTests) {
        if ( moduleTests == null ) {
            return null;
        }

        List<ModuleTestEntity> list = new ArrayList<ModuleTestEntity>( moduleTests.size() );
        for ( TmModuleTestVO tmModuleTestVO : moduleTests ) {
            list.add( convert( tmModuleTestVO ) );
        }

        return list;
    }

    @Override
    public ModuleTestEntity convert(TmModuleTestVO moduleTest) {
        if ( moduleTest == null ) {
            return null;
        }

        ModuleTestEntity moduleTestEntity = new ModuleTestEntity();

        moduleTestEntity.setReportCode( moduleTest.getReportCode() );
        moduleTestEntity.setTestType( moduleTest.getTestType() );
        moduleTestEntity.setTestResult( moduleTest.getTestResult() );
        moduleTestEntity.setValidIssueCount( moduleTest.getValidIssueCount() );
        moduleTestEntity.setLegacyIssueCount( moduleTest.getLegacyIssueCount() );
        moduleTestEntity.setReportUserId( moduleTest.getReportUserId() );
        moduleTestEntity.setReportUserName( moduleTest.getReportUserName() );

        moduleTestEntity.setCode( com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.SNOWFLAKE) );

        return moduleTestEntity;
    }

    @Override
    public TmTestReportEntity convert(SimpleReportEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setCodeCoverResult( event.getCoverageResult() );
        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );
        tmTestReportEntity.setZuiTestResult( event.getZuiTestResult() );
        tmTestReportEntity.setUiTestResult( event.getUiTestResult() );

        tmTestReportEntity.setCodeCoverReason( com.zto.devops.framework.common.util.CollectionUtil.isNotEmpty(event.getCoverageReasonVOS()) ? com.alibaba.fastjson.JSON.toJSONString(event.getCoverageReasonVOS()) : "" );

        return tmTestReportEntity;
    }

    @Override
    public ReportBaseEvent convertReportBaseEvent(SimpleReportEvent event) {
        if ( event == null ) {
            return null;
        }

        ReportBaseEvent reportBaseEvent = new ReportBaseEvent();

        reportBaseEvent.setAggregateId( event.getAggregateId() );
        reportBaseEvent.setEventType( event.getEventType() );
        reportBaseEvent.setTransactor( event.getTransactor() );
        reportBaseEvent.setOccurred( event.getOccurred() );
        reportBaseEvent.setReportCode( event.getReportCode() );
        reportBaseEvent.setReportName( event.getReportName() );
        reportBaseEvent.setPlanCode( event.getPlanCode() );
        reportBaseEvent.setVersionCode( event.getVersionCode() );
        reportBaseEvent.setVersionName( event.getVersionName() );
        reportBaseEvent.setReportType( reportTypeToReportType( event.getReportType() ) );
        reportBaseEvent.setTestResult( tmTestResultEnumToTestResultEunm( event.getTestResult() ) );
        reportBaseEvent.setActualPresentationDate( event.getActualPresentationDate() );
        reportBaseEvent.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        reportBaseEvent.setActualTestStart( event.getActualTestStart() );
        reportBaseEvent.setActualTestEnd( event.getActualTestEnd() );
        reportBaseEvent.setCheckStartDate( event.getCheckStartDate() );
        reportBaseEvent.setCheckEndDate( event.getCheckEndDate() );
        reportBaseEvent.setSummary( event.getSummary() );

        return reportBaseEvent;
    }

    @Override
    public ReviewInfoEntityDO converter(TmReviewInfoEntity tmReviewInfoEntity) {
        if ( tmReviewInfoEntity == null ) {
            return null;
        }

        ReviewInfoEntityDO reviewInfoEntityDO = new ReviewInfoEntityDO();

        reviewInfoEntityDO.setEnable( tmReviewInfoEntity.getEnable() );
        reviewInfoEntityDO.setCreatorId( tmReviewInfoEntity.getCreatorId() );
        reviewInfoEntityDO.setCreator( tmReviewInfoEntity.getCreator() );
        reviewInfoEntityDO.setGmtCreate( tmReviewInfoEntity.getGmtCreate() );
        reviewInfoEntityDO.setModifierId( tmReviewInfoEntity.getModifierId() );
        reviewInfoEntityDO.setModifier( tmReviewInfoEntity.getModifier() );
        reviewInfoEntityDO.setGmtModified( tmReviewInfoEntity.getGmtModified() );
        reviewInfoEntityDO.setCode( tmReviewInfoEntity.getCode() );
        reviewInfoEntityDO.setReportCode( tmReviewInfoEntity.getReportCode() );
        reviewInfoEntityDO.setReviewDate( tmReviewInfoEntity.getReviewDate() );
        reviewInfoEntityDO.setReviewType( tmReviewInfoEntity.getReviewType() );
        reviewInfoEntityDO.setRecordUserId( tmReviewInfoEntity.getRecordUserId() );
        reviewInfoEntityDO.setRecordUserName( tmReviewInfoEntity.getRecordUserName() );
        reviewInfoEntityDO.setProductReviewUsers( tmReviewInfoEntity.getProductReviewUsers() );
        reviewInfoEntityDO.setDevelopReviewUsers( tmReviewInfoEntity.getDevelopReviewUsers() );
        reviewInfoEntityDO.setTestReviewUsers( tmReviewInfoEntity.getTestReviewUsers() );

        return reviewInfoEntityDO;
    }

    @Override
    public TmTestReportEntityDO converter(TmTestReportEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TmTestReportEntityDO tmTestReportEntityDO = new TmTestReportEntityDO();

        tmTestReportEntityDO.setEnable( entity.getEnable() );
        tmTestReportEntityDO.setCreatorId( entity.getCreatorId() );
        tmTestReportEntityDO.setCreator( entity.getCreator() );
        tmTestReportEntityDO.setGmtCreate( entity.getGmtCreate() );
        tmTestReportEntityDO.setModifierId( entity.getModifierId() );
        tmTestReportEntityDO.setModifier( entity.getModifier() );
        tmTestReportEntityDO.setGmtModified( entity.getGmtModified() );
        tmTestReportEntityDO.setReportCode( entity.getReportCode() );
        tmTestReportEntityDO.setReportName( entity.getReportName() );
        tmTestReportEntityDO.setPlanCode( entity.getPlanCode() );
        tmTestReportEntityDO.setReportType( entity.getReportType() );
        tmTestReportEntityDO.setVersionCode( entity.getVersionCode() );
        tmTestReportEntityDO.setVersionName( entity.getVersionName() );
        tmTestReportEntityDO.setTestResult( entity.getTestResult() );
        tmTestReportEntityDO.setActualPresentationDate( entity.getActualPresentationDate() );
        tmTestReportEntityDO.setActualApprovalExitDate( entity.getActualApprovalExitDate() );
        tmTestReportEntityDO.setActualOnlineDate( entity.getActualOnlineDate() );
        tmTestReportEntityDO.setActualTestStart( entity.getActualTestStart() );
        tmTestReportEntityDO.setActualTestEnd( entity.getActualTestEnd() );
        tmTestReportEntityDO.setCheckStartDate( entity.getCheckStartDate() );
        tmTestReportEntityDO.setCheckEndDate( entity.getCheckEndDate() );
        tmTestReportEntityDO.setUpdateTestResultDate( entity.getUpdateTestResultDate() );
        tmTestReportEntityDO.setAutoTestResult( entity.getAutoTestResult() );
        tmTestReportEntityDO.setSecurityUserId( entity.getSecurityUserId() );
        tmTestReportEntityDO.setSecurityUserName( entity.getSecurityUserName() );
        tmTestReportEntityDO.setSecurityTestResult( entity.getSecurityTestResult() );
        tmTestReportEntityDO.setCheckType( entity.getCheckType() );
        tmTestReportEntityDO.setDeveloperCount( entity.getDeveloperCount() );
        tmTestReportEntityDO.setTesterCount( entity.getTesterCount() );
        tmTestReportEntityDO.setTestCaseNum( entity.getTestCaseNum() );
        tmTestReportEntityDO.setPlanCaseNum( entity.getPlanCaseNum() );
        tmTestReportEntityDO.setPermitNum( entity.getPermitNum() );
        tmTestReportEntityDO.setAsPlanedOnline( entity.getAsPlanedOnline() );
        tmTestReportEntityDO.setDelay( entity.getDelay() );
        tmTestReportEntityDO.setSummary( entity.getSummary() );
        tmTestReportEntityDO.setStatus( entity.getStatus() );
        tmTestReportEntityDO.setCodeCoverResult( entity.getCodeCoverResult() );
        tmTestReportEntityDO.setCodeCoverReason( entity.getCodeCoverReason() );
        tmTestReportEntityDO.setPlanPresentationDate( entity.getPlanPresentationDate() );
        tmTestReportEntityDO.setPlanApprovalExitDate( entity.getPlanApprovalExitDate() );
        tmTestReportEntityDO.setPlanOnlineDate( entity.getPlanOnlineDate() );
        tmTestReportEntityDO.setZuiTestResult( entity.getZuiTestResult() );
        tmTestReportEntityDO.setUiTestResult( entity.getUiTestResult() );

        return tmTestReportEntityDO;
    }

    @Override
    public List<ReviewOpinionVO> convertReviewOpinion(List<TmReviewOpinionEntity> tmReviewOpinionEntities) {
        if ( tmReviewOpinionEntities == null ) {
            return null;
        }

        List<ReviewOpinionVO> list = new ArrayList<ReviewOpinionVO>( tmReviewOpinionEntities.size() );
        for ( TmReviewOpinionEntity tmReviewOpinionEntity : tmReviewOpinionEntities ) {
            list.add( tmReviewOpinionEntityToReviewOpinionVO( tmReviewOpinionEntity ) );
        }

        return list;
    }

    @Override
    public List<ReviewRenewalVO> convertReviewRenewal(List<TmReviewRenewalEntity> tmReviewRenewalEntities) {
        if ( tmReviewRenewalEntities == null ) {
            return null;
        }

        List<ReviewRenewalVO> list = new ArrayList<ReviewRenewalVO>( tmReviewRenewalEntities.size() );
        for ( TmReviewRenewalEntity tmReviewRenewalEntity : tmReviewRenewalEntities ) {
            list.add( tmReviewRenewalEntityToReviewRenewalVO( tmReviewRenewalEntity ) );
        }

        return list;
    }

    @Override
    public TmTestReportEntity convert(ReviewReportAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );

        return tmTestReportEntity;
    }

    @Override
    public ReportBaseEvent convertReportBaseEvent(ReviewReportAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        ReportBaseEvent reportBaseEvent = new ReportBaseEvent();

        reportBaseEvent.setAggregateId( event.getAggregateId() );
        reportBaseEvent.setEventType( event.getEventType() );
        reportBaseEvent.setTransactor( event.getTransactor() );
        reportBaseEvent.setOccurred( event.getOccurred() );
        reportBaseEvent.setReportCode( event.getReportCode() );
        reportBaseEvent.setReportName( event.getReportName() );
        reportBaseEvent.setPlanCode( event.getPlanCode() );
        reportBaseEvent.setVersionCode( event.getVersionCode() );
        reportBaseEvent.setVersionName( event.getVersionName() );
        reportBaseEvent.setReportType( reportTypeToReportType( event.getReportType() ) );
        reportBaseEvent.setTestResult( tmTestResultEnumToTestResultEunm( event.getTestResult() ) );
        reportBaseEvent.setActualPresentationDate( event.getActualPresentationDate() );
        reportBaseEvent.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        reportBaseEvent.setActualTestStart( event.getActualTestStart() );
        reportBaseEvent.setActualTestEnd( event.getActualTestEnd() );
        reportBaseEvent.setCheckStartDate( event.getCheckStartDate() );
        reportBaseEvent.setCheckEndDate( event.getCheckEndDate() );
        reportBaseEvent.setSummary( event.getSummary() );

        return reportBaseEvent;
    }

    @Override
    public TmTestReportEntity convert(ReviewReportEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );

        return tmTestReportEntity;
    }

    @Override
    public ReportBaseEvent convertReportBaseEvent(ReviewReportEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        ReportBaseEvent reportBaseEvent = new ReportBaseEvent();

        reportBaseEvent.setAggregateId( event.getAggregateId() );
        reportBaseEvent.setEventType( event.getEventType() );
        reportBaseEvent.setTransactor( event.getTransactor() );
        reportBaseEvent.setOccurred( event.getOccurred() );
        reportBaseEvent.setReportCode( event.getReportCode() );
        reportBaseEvent.setReportName( event.getReportName() );
        reportBaseEvent.setPlanCode( event.getPlanCode() );

        return reportBaseEvent;
    }

    @Override
    public TmTestReportEntity convert(ExternalReportAddEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );
        tmTestReportEntity.setZuiTestResult( event.getZuiTestResult() );
        tmTestReportEntity.setUiTestResult( event.getUiTestResult() );

        return tmTestReportEntity;
    }

    @Override
    public ReportBaseEvent convertReportBaseEvent(ExternalReportAddEvent event) {
        if ( event == null ) {
            return null;
        }

        ReportBaseEvent reportBaseEvent = new ReportBaseEvent();

        reportBaseEvent.setAggregateId( event.getAggregateId() );
        reportBaseEvent.setEventType( event.getEventType() );
        reportBaseEvent.setTransactor( event.getTransactor() );
        reportBaseEvent.setOccurred( event.getOccurred() );
        reportBaseEvent.setReportCode( event.getReportCode() );
        reportBaseEvent.setReportName( event.getReportName() );
        reportBaseEvent.setPlanCode( event.getPlanCode() );
        reportBaseEvent.setVersionCode( event.getVersionCode() );
        reportBaseEvent.setVersionName( event.getVersionName() );
        reportBaseEvent.setReportType( reportTypeToReportType( event.getReportType() ) );
        reportBaseEvent.setTestResult( tmTestResultEnumToTestResultEunm( event.getTestResult() ) );
        reportBaseEvent.setPresentationDate( event.getPresentationDate() );
        reportBaseEvent.setActualPresentationDate( event.getActualPresentationDate() );
        reportBaseEvent.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        reportBaseEvent.setActualPublishDate( event.getActualPublishDate() );
        reportBaseEvent.setActualTestStart( event.getActualTestStart() );
        reportBaseEvent.setActualTestEnd( event.getActualTestEnd() );
        reportBaseEvent.setCheckStartDate( event.getCheckStartDate() );
        reportBaseEvent.setCheckEndDate( event.getCheckEndDate() );
        reportBaseEvent.setSummary( event.getSummary() );

        return reportBaseEvent;
    }

    @Override
    public TmTestReportEntity convert(ExternalReportEditEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );
        tmTestReportEntity.setZuiTestResult( event.getZuiTestResult() );
        tmTestReportEntity.setUiTestResult( event.getUiTestResult() );

        return tmTestReportEntity;
    }

    @Override
    public ReportBaseEvent convertReportBaseEvent(ExternalReportEditEvent event) {
        if ( event == null ) {
            return null;
        }

        ReportBaseEvent reportBaseEvent = new ReportBaseEvent();

        reportBaseEvent.setAggregateId( event.getAggregateId() );
        reportBaseEvent.setEventType( event.getEventType() );
        reportBaseEvent.setTransactor( event.getTransactor() );
        reportBaseEvent.setOccurred( event.getOccurred() );
        reportBaseEvent.setReportCode( event.getReportCode() );
        reportBaseEvent.setReportName( event.getReportName() );
        reportBaseEvent.setPlanCode( event.getPlanCode() );
        reportBaseEvent.setVersionCode( event.getVersionCode() );
        reportBaseEvent.setVersionName( event.getVersionName() );
        reportBaseEvent.setReportType( reportTypeToReportType( event.getReportType() ) );
        reportBaseEvent.setTestResult( tmTestResultEnumToTestResultEunm( event.getTestResult() ) );
        reportBaseEvent.setPresentationDate( event.getPresentationDate() );
        reportBaseEvent.setActualPresentationDate( event.getActualPresentationDate() );
        reportBaseEvent.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        reportBaseEvent.setActualPublishDate( event.getActualPublishDate() );
        reportBaseEvent.setActualTestStart( event.getActualTestStart() );
        reportBaseEvent.setActualTestEnd( event.getActualTestEnd() );
        reportBaseEvent.setCheckStartDate( event.getCheckStartDate() );
        reportBaseEvent.setCheckEndDate( event.getCheckEndDate() );
        reportBaseEvent.setSummary( event.getSummary() );

        return reportBaseEvent;
    }

    @Override
    public TmTestReportEntity convert(MobileTestReportAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );

        return tmTestReportEntity;
    }

    @Override
    public ReportBaseEvent convertReportBaseEvent(MobileTestReportAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        ReportBaseEvent reportBaseEvent = new ReportBaseEvent();

        reportBaseEvent.setAggregateId( event.getAggregateId() );
        reportBaseEvent.setEventType( event.getEventType() );
        reportBaseEvent.setTransactor( event.getTransactor() );
        reportBaseEvent.setOccurred( event.getOccurred() );
        reportBaseEvent.setReportCode( event.getReportCode() );
        reportBaseEvent.setReportName( event.getReportName() );
        reportBaseEvent.setPlanCode( event.getPlanCode() );
        reportBaseEvent.setVersionCode( event.getVersionCode() );
        reportBaseEvent.setVersionName( event.getVersionName() );
        reportBaseEvent.setReportType( reportTypeToReportType( event.getReportType() ) );
        reportBaseEvent.setTestResult( tmTestResultEnumToTestResultEunm( event.getTestResult() ) );
        reportBaseEvent.setActualPresentationDate( event.getActualPresentationDate() );
        reportBaseEvent.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        reportBaseEvent.setActualTestStart( event.getActualTestStart() );
        reportBaseEvent.setActualTestEnd( event.getActualTestEnd() );
        reportBaseEvent.setCheckStartDate( event.getCheckStartDate() );
        reportBaseEvent.setCheckEndDate( event.getCheckEndDate() );
        reportBaseEvent.setSummary( event.getSummary() );

        return reportBaseEvent;
    }

    @Override
    public TmTestReportEntity convert(MobileTestReportEditEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestReportEntity tmTestReportEntity = new TmTestReportEntity();

        tmTestReportEntity.setReportCode( event.getReportCode() );
        tmTestReportEntity.setReportName( event.getReportName() );
        tmTestReportEntity.setPlanCode( event.getPlanCode() );
        tmTestReportEntity.setReportType( event.getReportType() );
        tmTestReportEntity.setVersionCode( event.getVersionCode() );
        tmTestReportEntity.setVersionName( event.getVersionName() );
        tmTestReportEntity.setTestResult( event.getTestResult() );
        tmTestReportEntity.setActualPresentationDate( event.getActualPresentationDate() );
        tmTestReportEntity.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        tmTestReportEntity.setActualOnlineDate( event.getActualOnlineDate() );
        tmTestReportEntity.setActualTestStart( event.getActualTestStart() );
        tmTestReportEntity.setActualTestEnd( event.getActualTestEnd() );
        tmTestReportEntity.setCheckStartDate( event.getCheckStartDate() );
        tmTestReportEntity.setCheckEndDate( event.getCheckEndDate() );
        tmTestReportEntity.setUpdateTestResultDate( event.getUpdateTestResultDate() );
        tmTestReportEntity.setAutoTestResult( autoExecuteResultToAutoExecuteResult( event.getAutoTestResult() ) );
        tmTestReportEntity.setSecurityUserId( event.getSecurityUserId() );
        tmTestReportEntity.setSecurityUserName( event.getSecurityUserName() );
        tmTestReportEntity.setSecurityTestResult( event.getSecurityTestResult() );
        tmTestReportEntity.setCheckType( checkTypeToCheckType( event.getCheckType() ) );
        tmTestReportEntity.setDeveloperCount( event.getDeveloperCount() );
        tmTestReportEntity.setTesterCount( event.getTesterCount() );
        tmTestReportEntity.setTestCaseNum( event.getTestCaseNum() );
        tmTestReportEntity.setPlanCaseNum( event.getPlanCaseNum() );
        tmTestReportEntity.setPermitNum( event.getPermitNum() );
        tmTestReportEntity.setAsPlanedOnline( event.getAsPlanedOnline() );
        tmTestReportEntity.setDelay( event.getDelay() );
        tmTestReportEntity.setSummary( event.getSummary() );
        tmTestReportEntity.setStatus( event.getStatus() );
        tmTestReportEntity.setPlanPresentationDate( event.getPlanPresentationDate() );
        tmTestReportEntity.setPlanApprovalExitDate( event.getPlanApprovalExitDate() );
        tmTestReportEntity.setPlanOnlineDate( event.getPlanOnlineDate() );

        return tmTestReportEntity;
    }

    @Override
    public ReportBaseEvent convertReportBaseEvent(MobileTestReportEditEvent event) {
        if ( event == null ) {
            return null;
        }

        ReportBaseEvent reportBaseEvent = new ReportBaseEvent();

        reportBaseEvent.setAggregateId( event.getAggregateId() );
        reportBaseEvent.setEventType( event.getEventType() );
        reportBaseEvent.setTransactor( event.getTransactor() );
        reportBaseEvent.setOccurred( event.getOccurred() );
        reportBaseEvent.setReportCode( event.getReportCode() );
        reportBaseEvent.setReportName( event.getReportName() );
        reportBaseEvent.setPlanCode( event.getPlanCode() );
        reportBaseEvent.setVersionCode( event.getVersionCode() );
        reportBaseEvent.setVersionName( event.getVersionName() );
        reportBaseEvent.setReportType( reportTypeToReportType( event.getReportType() ) );
        reportBaseEvent.setTestResult( tmTestResultEnumToTestResultEunm( event.getTestResult() ) );
        reportBaseEvent.setActualPresentationDate( event.getActualPresentationDate() );
        reportBaseEvent.setActualApprovalExitDate( event.getActualApprovalExitDate() );
        reportBaseEvent.setActualTestStart( event.getActualTestStart() );
        reportBaseEvent.setActualTestEnd( event.getActualTestEnd() );
        reportBaseEvent.setCheckStartDate( event.getCheckStartDate() );
        reportBaseEvent.setCheckEndDate( event.getCheckEndDate() );
        reportBaseEvent.setSummary( event.getSummary() );

        return reportBaseEvent;
    }

    @Override
    public List<TmTestReportEntityDO> convert2DOS(List<TmTestReportEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<TmTestReportEntityDO> list = new ArrayList<TmTestReportEntityDO>( entity.size() );
        for ( TmTestReportEntity tmTestReportEntity : entity ) {
            list.add( converter( tmTestReportEntity ) );
        }

        return list;
    }

    protected com.zto.devops.qc.client.enums.testmanager.report.ReviewType reviewTypeToReviewType(ReviewType reviewType) {
        if ( reviewType == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.testmanager.report.ReviewType reviewType1;

        switch ( reviewType ) {
            case UNKNOWN: reviewType1 = com.zto.devops.qc.client.enums.testmanager.report.ReviewType.UNKNOWN;
            break;
            case MEET_REVIEW: reviewType1 = com.zto.devops.qc.client.enums.testmanager.report.ReviewType.MEET_REVIEW;
            break;
            case MAIL_REVIEW: reviewType1 = com.zto.devops.qc.client.enums.testmanager.report.ReviewType.MAIL_REVIEW;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + reviewType );
        }

        return reviewType1;
    }

    protected TmReviewRenewalEntity reviewRenewalVOToTmReviewRenewalEntity(ReviewRenewalVO reviewRenewalVO) {
        if ( reviewRenewalVO == null ) {
            return null;
        }

        TmReviewRenewalEntity tmReviewRenewalEntity = new TmReviewRenewalEntity();

        tmReviewRenewalEntity.setReportCode( reviewRenewalVO.getReportCode() );
        tmReviewRenewalEntity.setSerialId( reviewRenewalVO.getSerialId() );
        tmReviewRenewalEntity.setCode( reviewRenewalVO.getCode() );
        tmReviewRenewalEntity.setReviewBefore( reviewRenewalVO.getReviewBefore() );
        tmReviewRenewalEntity.setReviewAfter( reviewRenewalVO.getReviewAfter() );
        tmReviewRenewalEntity.setRenewalContent( reviewRenewalVO.getRenewalContent() );

        return tmReviewRenewalEntity;
    }

    protected TmReviewOpinionEntity reviewOpinionVOToTmReviewOpinionEntity(ReviewOpinionVO reviewOpinionVO) {
        if ( reviewOpinionVO == null ) {
            return null;
        }

        TmReviewOpinionEntity tmReviewOpinionEntity = new TmReviewOpinionEntity();

        tmReviewOpinionEntity.setReportCode( reviewOpinionVO.getReportCode() );
        tmReviewOpinionEntity.setCode( reviewOpinionVO.getCode() );
        tmReviewOpinionEntity.setDescription( reviewOpinionVO.getDescription() );
        tmReviewOpinionEntity.setSerialId( reviewOpinionVO.getSerialId() );
        tmReviewOpinionEntity.setOwnerUserId( reviewOpinionVO.getOwnerUserId() );
        tmReviewOpinionEntity.setOwnerUserName( reviewOpinionVO.getOwnerUserName() );
        tmReviewOpinionEntity.setDeadLineDate( reviewOpinionVO.getDeadLineDate() );

        return tmReviewOpinionEntity;
    }

    protected com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult autoExecuteResultToAutoExecuteResult(AutoExecuteResult autoExecuteResult) {
        if ( autoExecuteResult == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult autoExecuteResult1;

        switch ( autoExecuteResult ) {
            case UNKNOWN: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.UNKNOWN;
            break;
            case WAIT_EXECUTED: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.WAIT_EXECUTED;
            break;
            case EXECUTING: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.EXECUTING;
            break;
            case NO_EXECUTE: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.NO_EXECUTE;
            break;
            case PASS_BY: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.PASS_BY;
            break;
            case NO_PASS: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.NO_PASS;
            break;
            case FAILURE: autoExecuteResult1 = com.zto.devops.qc.client.enums.testmanager.report.AutoExecuteResult.FAILURE;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + autoExecuteResult );
        }

        return autoExecuteResult1;
    }

    protected com.zto.devops.qc.client.enums.testmanager.report.CheckType checkTypeToCheckType(CheckType checkType) {
        if ( checkType == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.testmanager.report.CheckType checkType1;

        switch ( checkType ) {
            case UNKNOWN: checkType1 = com.zto.devops.qc.client.enums.testmanager.report.CheckType.UNKNOWN;
            break;
            case FOREIGN: checkType1 = com.zto.devops.qc.client.enums.testmanager.report.CheckType.FOREIGN;
            break;
            case INTERNAL: checkType1 = com.zto.devops.qc.client.enums.testmanager.report.CheckType.INTERNAL;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + checkType );
        }

        return checkType1;
    }

    protected ReportType reportTypeToReportType(com.zto.devops.qc.client.enums.testmanager.report.ReportType reportType) {
        if ( reportType == null ) {
            return null;
        }

        ReportType reportType1;

        switch ( reportType ) {
            case NULL: reportType1 = ReportType.NULL;
            break;
            case UNKNOWN: reportType1 = ReportType.UNKNOWN;
            break;
            case TEST_ACCESS: reportType1 = ReportType.TEST_ACCESS;
            break;
            case TEST_PERMIT: reportType1 = ReportType.TEST_PERMIT;
            break;
            case SPECIAL_MOBILE: reportType1 = ReportType.SPECIAL_MOBILE;
            break;
            case INTEGRATION_TEST: reportType1 = ReportType.INTEGRATION_TEST;
            break;
            case ONLINE_SMOKE: reportType1 = ReportType.ONLINE_SMOKE;
            break;
            case CHECED_TEST: reportType1 = ReportType.CHECED_TEST;
            break;
            case SIMPLE_PROCESS: reportType1 = ReportType.SIMPLE_PROCESS;
            break;
            case CASE_REVIEW: reportType1 = ReportType.CASE_REVIEW;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + reportType );
        }

        return reportType1;
    }

    protected TestResultEunm tmTestResultEnumToTestResultEunm(TmTestResultEnum tmTestResultEnum) {
        if ( tmTestResultEnum == null ) {
            return null;
        }

        TestResultEunm testResultEunm;

        switch ( tmTestResultEnum ) {
            case PASS: testResultEunm = TestResultEunm.PASS;
            break;
            case ONCE_SUCCESS: testResultEunm = TestResultEunm.ONCE_SUCCESS;
            break;
            case FILTER_SMOKE_SUCCESS: testResultEunm = TestResultEunm.FILTER_SMOKE_SUCCESS;
            break;
            case MORE_SUCCESS: testResultEunm = TestResultEunm.MORE_SUCCESS;
            break;
            case NO_PASS: testResultEunm = TestResultEunm.NO_PASS;
            break;
            case OTHER: testResultEunm = TestResultEunm.OTHER;
            break;
            case UNKNOWN: testResultEunm = TestResultEunm.UNKNOWN;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + tmTestResultEnum );
        }

        return testResultEunm;
    }

    protected ReviewOpinionVO tmReviewOpinionEntityToReviewOpinionVO(TmReviewOpinionEntity tmReviewOpinionEntity) {
        if ( tmReviewOpinionEntity == null ) {
            return null;
        }

        ReviewOpinionVO reviewOpinionVO = new ReviewOpinionVO();

        reviewOpinionVO.setReportCode( tmReviewOpinionEntity.getReportCode() );
        reviewOpinionVO.setSerialId( tmReviewOpinionEntity.getSerialId() );
        reviewOpinionVO.setCode( tmReviewOpinionEntity.getCode() );
        reviewOpinionVO.setDescription( tmReviewOpinionEntity.getDescription() );
        reviewOpinionVO.setOwnerUserId( tmReviewOpinionEntity.getOwnerUserId() );
        reviewOpinionVO.setOwnerUserName( tmReviewOpinionEntity.getOwnerUserName() );
        reviewOpinionVO.setDeadLineDate( tmReviewOpinionEntity.getDeadLineDate() );

        return reviewOpinionVO;
    }

    protected ReviewRenewalVO tmReviewRenewalEntityToReviewRenewalVO(TmReviewRenewalEntity tmReviewRenewalEntity) {
        if ( tmReviewRenewalEntity == null ) {
            return null;
        }

        ReviewRenewalVO reviewRenewalVO = new ReviewRenewalVO();

        reviewRenewalVO.setReportCode( tmReviewRenewalEntity.getReportCode() );
        reviewRenewalVO.setSerialId( tmReviewRenewalEntity.getSerialId() );
        reviewRenewalVO.setCode( tmReviewRenewalEntity.getCode() );
        reviewRenewalVO.setReviewBefore( tmReviewRenewalEntity.getReviewBefore() );
        reviewRenewalVO.setReviewAfter( tmReviewRenewalEntity.getReviewAfter() );
        reviewRenewalVO.setRenewalContent( tmReviewRenewalEntity.getRenewalContent() );

        return reviewRenewalVO;
    }
}
