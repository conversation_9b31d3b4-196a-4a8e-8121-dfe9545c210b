package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.SceneCollectionEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.SceneCollectionEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:27+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmSceneCollectionEntityConverterImpl implements TmSceneCollectionEntityConverter {

    @Override
    public SceneCollectionEntity convertSceneCollectionEntity(SceneCollectionEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneCollectionEntity sceneCollectionEntity = new SceneCollectionEntity();

        sceneCollectionEntity.setId( entityDO.getId() );
        sceneCollectionEntity.setSceneIndexCode( entityDO.getSceneIndexCode() );
        sceneCollectionEntity.setProductCode( entityDO.getProductCode() );
        sceneCollectionEntity.setFavoriteType( entityDO.getFavoriteType() );
        sceneCollectionEntity.setCreatorId( entityDO.getCreatorId() );
        sceneCollectionEntity.setCreator( entityDO.getCreator() );
        sceneCollectionEntity.setGmtCreate( entityDO.getGmtCreate() );
        sceneCollectionEntity.setModifierId( entityDO.getModifierId() );
        sceneCollectionEntity.setModifier( entityDO.getModifier() );
        sceneCollectionEntity.setGmtModified( entityDO.getGmtModified() );

        return sceneCollectionEntity;
    }

    @Override
    public SceneCollectionEntityDO convertSceneCollectionEntityDO(SceneCollectionEntity entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneCollectionEntityDO sceneCollectionEntityDO = new SceneCollectionEntityDO();

        sceneCollectionEntityDO.setId( entityDO.getId() );
        sceneCollectionEntityDO.setSceneIndexCode( entityDO.getSceneIndexCode() );
        sceneCollectionEntityDO.setProductCode( entityDO.getProductCode() );
        sceneCollectionEntityDO.setFavoriteType( entityDO.getFavoriteType() );
        sceneCollectionEntityDO.setCreatorId( entityDO.getCreatorId() );
        sceneCollectionEntityDO.setCreator( entityDO.getCreator() );
        sceneCollectionEntityDO.setGmtCreate( entityDO.getGmtCreate() );
        sceneCollectionEntityDO.setModifierId( entityDO.getModifierId() );
        sceneCollectionEntityDO.setModifier( entityDO.getModifier() );
        sceneCollectionEntityDO.setGmtModified( entityDO.getGmtModified() );

        return sceneCollectionEntityDO;
    }

    @Override
    public List<SceneCollectionEntity> convertSceneCollectionEntityList(List<SceneCollectionEntityDO> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SceneCollectionEntity> list = new ArrayList<SceneCollectionEntity>( entityList.size() );
        for ( SceneCollectionEntityDO sceneCollectionEntityDO : entityList ) {
            list.add( convertSceneCollectionEntity( sceneCollectionEntityDO ) );
        }

        return list;
    }

    @Override
    public List<SceneCollectionEntityDO> convertSceneCollectionEntityDOList(List<SceneCollectionEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SceneCollectionEntityDO> list = new ArrayList<SceneCollectionEntityDO>( entityList.size() );
        for ( SceneCollectionEntity sceneCollectionEntity : entityList ) {
            list.add( convertSceneCollectionEntityDO( sceneCollectionEntity ) );
        }

        return list;
    }
}
