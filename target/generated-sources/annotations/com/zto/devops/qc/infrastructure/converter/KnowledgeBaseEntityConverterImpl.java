package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.KnowledgeBaseEntityDO;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseVO;
import com.zto.devops.qc.infrastructure.dao.entity.KnowledgeBaseEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class KnowledgeBaseEntityConverterImpl implements KnowledgeBaseEntityConverter {

    @Override
    public KnowledgeBaseVO convert(KnowledgeBaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        KnowledgeBaseVO knowledgeBaseVO = new KnowledgeBaseVO();

        if ( entity.hasId() ) {
            knowledgeBaseVO.setId( entity.getId() );
        }
        knowledgeBaseVO.setSpaceId( entity.getSpaceId() );
        knowledgeBaseVO.setProductCode( entity.getProductCode() );
        knowledgeBaseVO.setUrl( entity.getUrl() );
        knowledgeBaseVO.setCreatorId( entity.getCreatorId() );
        knowledgeBaseVO.setCreator( entity.getCreator() );

        return knowledgeBaseVO;
    }

    @Override
    public KnowledgeBaseEntity convert(KnowledgeBaseEntityDO entity) {
        if ( entity == null ) {
            return null;
        }

        KnowledgeBaseEntity knowledgeBaseEntity = new KnowledgeBaseEntity();

        knowledgeBaseEntity.setEnable( entity.getEnable() );
        knowledgeBaseEntity.setCreatorId( entity.getCreatorId() );
        knowledgeBaseEntity.setCreator( entity.getCreator() );
        knowledgeBaseEntity.setGmtCreate( entity.getGmtCreate() );
        knowledgeBaseEntity.setModifierId( entity.getModifierId() );
        knowledgeBaseEntity.setModifier( entity.getModifier() );
        knowledgeBaseEntity.setGmtModified( entity.getGmtModified() );
        knowledgeBaseEntity.setId( entity.getId() );
        knowledgeBaseEntity.setSpaceId( entity.getSpaceId() );
        knowledgeBaseEntity.setProductCode( entity.getProductCode() );
        knowledgeBaseEntity.setUrl( entity.getUrl() );

        return knowledgeBaseEntity;
    }

    @Override
    public List<KnowledgeBaseEntity> convert(List<KnowledgeBaseEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<KnowledgeBaseEntity> list = new ArrayList<KnowledgeBaseEntity>( entityDOList.size() );
        for ( KnowledgeBaseEntityDO knowledgeBaseEntityDO : entityDOList ) {
            list.add( convert( knowledgeBaseEntityDO ) );
        }

        return list;
    }
}
