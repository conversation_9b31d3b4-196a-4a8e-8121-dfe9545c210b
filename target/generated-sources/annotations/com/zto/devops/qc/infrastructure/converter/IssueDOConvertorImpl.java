package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.IssueApplicationType;
import com.zto.devops.qc.client.enums.issue.IssueFindEnv;
import com.zto.devops.qc.client.enums.issue.IssueFindStage;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueRepetitionRate;
import com.zto.devops.qc.client.enums.issue.IssueRootCause;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.IssueTestMethod;
import com.zto.devops.qc.client.enums.issue.IssueType;
import com.zto.devops.qc.client.enums.issue.RefuseReason;
import com.zto.devops.qc.client.enums.issue.RelatedToMeEnum;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.issue.entity.IssueBaseVO;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.entity.RelatedMatterStatusCountVO;
import com.zto.devops.qc.client.model.issue.query.PageIssueQuery;
import com.zto.devops.qc.client.model.issue.query.PageIssueThingQuery;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.domain.model.Attachment;
import com.zto.devops.qc.domain.model.Comment;
import com.zto.devops.qc.domain.model.Issue;
import com.zto.devops.qc.domain.model.RelevantUser;
import com.zto.devops.qc.domain.model.Tag;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CommentEntity;
import com.zto.devops.qc.infrastructure.dao.entity.IssueEntity;
import com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueDOConvertorImpl implements IssueDOConvertor {

    @Override
    public IssueQueryParameter convert(PageIssueQuery query) {
        if ( query == null ) {
            return null;
        }

        IssueQueryParameter issueQueryParameter = new IssueQueryParameter();

        issueQueryParameter.setCodeOrTitle( query.getCodeOrTitle() );
        issueQueryParameter.setCurrentUserId( query.getCurrentUserId() );
        List<IssueStatus> list = query.getStatusList();
        if ( list != null ) {
            issueQueryParameter.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        List<IssuePriority> list1 = query.getPriorityList();
        if ( list1 != null ) {
            issueQueryParameter.setPriorityList( new ArrayList<IssuePriority>( list1 ) );
        }
        List<RelatedToMeEnum> list2 = query.getRelatedList();
        if ( list2 != null ) {
            issueQueryParameter.setRelatedList( new ArrayList<RelatedToMeEnum>( list2 ) );
        }
        List<Long> list3 = query.getHandleUserIdList();
        if ( list3 != null ) {
            issueQueryParameter.setHandleUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = query.getDevelopUserIdList();
        if ( list4 != null ) {
            issueQueryParameter.setDevelopUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = query.getTestUserIdList();
        if ( list5 != null ) {
            issueQueryParameter.setTestUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<String> list6 = query.getFindVersionList();
        if ( list6 != null ) {
            issueQueryParameter.setFindVersionList( new ArrayList<String>( list6 ) );
        }
        List<String> list7 = query.getFixVersionList();
        if ( list7 != null ) {
            issueQueryParameter.setFixVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = query.getFixOrFindVersionList();
        if ( list8 != null ) {
            issueQueryParameter.setFixOrFindVersionList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = query.getRelatedRequireList();
        if ( list9 != null ) {
            issueQueryParameter.setRelatedRequireList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = query.getRelatedProductList();
        if ( list10 != null ) {
            issueQueryParameter.setRelatedProductList( new ArrayList<String>( list10 ) );
        }
        List<String> list11 = query.getSprintCode();
        if ( list11 != null ) {
            issueQueryParameter.setSprintCode( new ArrayList<String>( list11 ) );
        }
        List<IssueRootCause> list12 = query.getRootCauseList();
        if ( list12 != null ) {
            issueQueryParameter.setRootCauseList( new ArrayList<IssueRootCause>( list12 ) );
        }
        List<IssueType> list13 = query.getIssueTypeList();
        if ( list13 != null ) {
            issueQueryParameter.setIssueTypeList( new ArrayList<IssueType>( list13 ) );
        }
        List<IssueTestMethod> list14 = query.getTestMethodList();
        if ( list14 != null ) {
            issueQueryParameter.setTestMethodList( new ArrayList<IssueTestMethod>( list14 ) );
        }
        List<IssueRepetitionRate> list15 = query.getRepetitionRateList();
        if ( list15 != null ) {
            issueQueryParameter.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list15 ) );
        }
        List<IssueFindStage> list16 = query.getFindStageList();
        if ( list16 != null ) {
            issueQueryParameter.setFindStageList( new ArrayList<IssueFindStage>( list16 ) );
        }
        List<IssueFindEnv> list17 = query.getFindEnvList();
        if ( list17 != null ) {
            issueQueryParameter.setFindEnvList( new ArrayList<IssueFindEnv>( list17 ) );
        }
        List<Long> list18 = query.getFindUserIdList();
        if ( list18 != null ) {
            issueQueryParameter.setFindUserIdList( new ArrayList<Long>( list18 ) );
        }
        List<String> list19 = query.getTagName();
        if ( list19 != null ) {
            issueQueryParameter.setTagName( new ArrayList<String>( list19 ) );
        }
        issueQueryParameter.setCreateTimeStart( query.getCreateTimeStart() );
        issueQueryParameter.setCreateTimeEnd( query.getCreateTimeEnd() );
        issueQueryParameter.setCloseTimeStart( query.getCloseTimeStart() );
        issueQueryParameter.setCloseTimeEnd( query.getCloseTimeEnd() );
        issueQueryParameter.setFixVersionIsNull( query.getFixVersionIsNull() );
        issueQueryParameter.setGmtModifiedStart( query.getGmtModifiedStart() );
        issueQueryParameter.setGmtModifiedEnd( query.getGmtModifiedEnd() );
        List<String> list20 = query.getVersionConfirm();
        if ( list20 != null ) {
            issueQueryParameter.setVersionConfirm( new ArrayList<String>( list20 ) );
        }
        List<String> list21 = query.getIssueCodeList();
        if ( list21 != null ) {
            issueQueryParameter.setIssueCodeList( new ArrayList<String>( list21 ) );
        }
        List<Boolean> list22 = query.getTestOmission();
        if ( list22 != null ) {
            issueQueryParameter.setTestOmission( new ArrayList<Boolean>( list22 ) );
        }
        List<Boolean> list23 = query.getCodeDefect();
        if ( list23 != null ) {
            issueQueryParameter.setCodeDefect( new ArrayList<Boolean>( list23 ) );
        }
        List<Boolean> list24 = query.getExamination();
        if ( list24 != null ) {
            issueQueryParameter.setExamination( new ArrayList<Boolean>( list24 ) );
        }
        List<RefuseReason> list25 = query.getRefuseReasonList();
        if ( list25 != null ) {
            issueQueryParameter.setRefuseReasonList( new ArrayList<RefuseReason>( list25 ) );
        }
        List<IssueApplicationType> list26 = query.getApplicationTypeList();
        if ( list26 != null ) {
            issueQueryParameter.setApplicationTypeList( new ArrayList<IssueApplicationType>( list26 ) );
        }
        issueQueryParameter.setVersionType( query.getVersionType() );
        issueQueryParameter.setCCUserId( query.getCCUserId() );
        issueQueryParameter.setType( query.getType() );
        issueQueryParameter.setOrderField( query.getOrderField() );
        issueQueryParameter.setOrderType( query.getOrderType() );
        List<Long> list27 = query.getCcUserIdList();
        if ( list27 != null ) {
            issueQueryParameter.setCcUserIdList( new ArrayList<Long>( list27 ) );
        }
        issueQueryParameter.setValidFlag( query.getValidFlag() );

        return issueQueryParameter;
    }

    @Override
    public List<RelatedMatterStatusCountVO> covertRelatedMatterStatusCountVO(List<com.zto.devops.project.client.model.requirement.entity.RelatedMatterStatusCountVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<RelatedMatterStatusCountVO> list = new ArrayList<RelatedMatterStatusCountVO>( vos.size() );
        for ( com.zto.devops.project.client.model.requirement.entity.RelatedMatterStatusCountVO relatedMatterStatusCountVO : vos ) {
            list.add( relatedMatterStatusCountVOToRelatedMatterStatusCountVO( relatedMatterStatusCountVO ) );
        }

        return list;
    }

    @Override
    public Issue convert(IssueEntity entity) {
        if ( entity == null ) {
            return null;
        }

        Issue issue = new Issue();

        issue.setCreator( issueEntityToUser( entity ) );
        issue.setModifier( issueEntityToUser1( entity ) );
        issue.setHandler( issueEntityToUser2( entity ) );
        issue.setFinder( issueEntityToUser3( entity ) );
        issue.setDeveloper( issueEntityToUser4( entity ) );
        issue.setTester( issueEntityToUser5( entity ) );
        issue.setRequirement( issueEntityToRequirement( entity ) );
        issue.setProduct( issueEntityToProduct( entity ) );
        issue.setFindVersion( issueEntityToVersion( entity ) );
        issue.setFixVersion( issueEntityToVersion1( entity ) );
        issue.setSprint( issueEntityToSprint( entity ) );
        issue.setCode( entity.getCode() );
        issue.setTitle( entity.getTitle() );
        issue.setDescription( entity.getDescription() );
        issue.setEnable( entity.getEnable() );
        issue.setReopenTime( entity.getReopenTime() );
        issue.setStartFixTime( entity.getStartFixTime() );
        issue.setDelayFixTime( entity.getDelayFixTime() );
        issue.setDeliverTime( entity.getDeliverTime() );
        issue.setRejectTime( entity.getRejectTime() );
        issue.setCloseTime( entity.getCloseTime() );
        issue.setFindTime( entity.getFindTime() );
        issue.setUpdateTime( entity.getUpdateTime() );
        issue.setFindEnv( entity.getFindEnv() );
        issue.setFindStage( entity.getFindStage() );
        issue.setPriority( entity.getPriority() );
        issue.setRepetitionRate( entity.getRepetitionRate() );
        issue.setRootCause( entity.getRootCause() );
        issue.setTestMethod( entity.getTestMethod() );
        issue.setType( entity.getType() );
        issue.setStatus( entity.getStatus() );
        issue.setApplicationType( entity.getApplicationType() );

        return issue;
    }

    @Override
    public List<Attachment> convertList(Collection<AttachmentEntity> attachmentEntities) {
        if ( attachmentEntities == null ) {
            return null;
        }

        List<Attachment> list = new ArrayList<Attachment>( attachmentEntities.size() );
        for ( AttachmentEntity attachmentEntity : attachmentEntities ) {
            list.add( attachmentEntityToAttachment( attachmentEntity ) );
        }

        return list;
    }

    @Override
    public List<Tag> convertTagList(Collection<TagEntity> tagEntities) {
        if ( tagEntities == null ) {
            return null;
        }

        List<Tag> list = new ArrayList<Tag>( tagEntities.size() );
        for ( TagEntity tagEntity : tagEntities ) {
            list.add( tagEntityToTag( tagEntity ) );
        }

        return list;
    }

    @Override
    public Comment coverter(CommentEntity comment) {
        if ( comment == null ) {
            return null;
        }

        Comment comment1 = new Comment();

        comment1.setCreator( commentEntityToUser( comment ) );
        comment1.setCode( comment.getCode() );
        comment1.setBusinessCode( comment.getBusinessCode() );
        comment1.setTopRepliedCode( comment.getTopRepliedCode() );
        comment1.setRepliedCode( comment.getRepliedCode() );
        comment1.setRepliedUserName( comment.getRepliedUserName() );
        comment1.setLevel( comment.getLevel() );
        comment1.setContent( comment.getContent() );
        comment1.setEnable( comment.getEnable() );
        comment1.setGmtCreate( comment.getGmtCreate() );
        comment1.setDomain( comment.getDomain() );

        return comment1;
    }

    @Override
    public Set<Comment> convertCommentList(Collection<CommentEntity> commentEntities) {
        if ( commentEntities == null ) {
            return null;
        }

        Set<Comment> set = new HashSet<Comment>( Math.max( (int) ( commentEntities.size() / .75f ) + 1, 16 ) );
        for ( CommentEntity commentEntity : commentEntities ) {
            set.add( coverter( commentEntity ) );
        }

        return set;
    }

    @Override
    public Set<RelevantUser> convertRelevantUserList(Collection<RelevantUserEntity> relevantUserEntities) {
        if ( relevantUserEntities == null ) {
            return null;
        }

        Set<RelevantUser> set = new HashSet<RelevantUser>( Math.max( (int) ( relevantUserEntities.size() / .75f ) + 1, 16 ) );
        for ( RelevantUserEntity relevantUserEntity : relevantUserEntities ) {
            set.add( relevantUserEntityToRelevantUser( relevantUserEntity ) );
        }

        return set;
    }

    @Override
    public IssueBaseVO convertIssueBaseVO(IssueEntity entity) {
        if ( entity == null ) {
            return null;
        }

        IssueBaseVO issueBaseVO = new IssueBaseVO();

        issueBaseVO.setCode( entity.getCode() );
        issueBaseVO.setTitle( entity.getTitle() );
        issueBaseVO.setStatus( entity.getStatus() );
        issueBaseVO.setSprintCode( entity.getSprintCode() );
        issueBaseVO.setPriority( entity.getPriority() );
        issueBaseVO.setCreator( entity.getCreator() );
        issueBaseVO.setGmtCreate( entity.getGmtCreate() );
        issueBaseVO.setCreatorId( entity.getCreatorId() );
        issueBaseVO.setHandleUserName( entity.getHandleUserName() );
        if ( entity.getHandleUserId() != null ) {
            issueBaseVO.setHandleUserId( String.valueOf( entity.getHandleUserId() ) );
        }

        return issueBaseVO;
    }

    @Override
    public IssueQueryParameter covertPageIssueThingQuery(PageIssueThingQuery query) {
        if ( query == null ) {
            return null;
        }

        IssueQueryParameter issueQueryParameter = new IssueQueryParameter();

        issueQueryParameter.setCodeOrTitle( query.getCodeOrTitle() );
        issueQueryParameter.setCurrentUserId( query.getCurrentUserId() );
        List<IssueStatus> list = query.getStatusList();
        if ( list != null ) {
            issueQueryParameter.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        List<IssuePriority> list1 = query.getPriorityList();
        if ( list1 != null ) {
            issueQueryParameter.setPriorityList( new ArrayList<IssuePriority>( list1 ) );
        }
        List<RelatedToMeEnum> list2 = query.getRelatedList();
        if ( list2 != null ) {
            issueQueryParameter.setRelatedList( new ArrayList<RelatedToMeEnum>( list2 ) );
        }
        List<Long> list3 = query.getHandleUserIdList();
        if ( list3 != null ) {
            issueQueryParameter.setHandleUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = query.getDevelopUserIdList();
        if ( list4 != null ) {
            issueQueryParameter.setDevelopUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = query.getTestUserIdList();
        if ( list5 != null ) {
            issueQueryParameter.setTestUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<String> list6 = query.getFindVersionList();
        if ( list6 != null ) {
            issueQueryParameter.setFindVersionList( new ArrayList<String>( list6 ) );
        }
        List<String> list7 = query.getFixVersionList();
        if ( list7 != null ) {
            issueQueryParameter.setFixVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = query.getFixOrFindVersionList();
        if ( list8 != null ) {
            issueQueryParameter.setFixOrFindVersionList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = query.getRelatedRequireList();
        if ( list9 != null ) {
            issueQueryParameter.setRelatedRequireList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = query.getRelatedProductList();
        if ( list10 != null ) {
            issueQueryParameter.setRelatedProductList( new ArrayList<String>( list10 ) );
        }
        List<String> list11 = query.getSprintCode();
        if ( list11 != null ) {
            issueQueryParameter.setSprintCode( new ArrayList<String>( list11 ) );
        }
        List<IssueRootCause> list12 = query.getRootCauseList();
        if ( list12 != null ) {
            issueQueryParameter.setRootCauseList( new ArrayList<IssueRootCause>( list12 ) );
        }
        List<IssueType> list13 = query.getIssueTypeList();
        if ( list13 != null ) {
            issueQueryParameter.setIssueTypeList( new ArrayList<IssueType>( list13 ) );
        }
        List<IssueTestMethod> list14 = query.getTestMethodList();
        if ( list14 != null ) {
            issueQueryParameter.setTestMethodList( new ArrayList<IssueTestMethod>( list14 ) );
        }
        List<IssueRepetitionRate> list15 = query.getRepetitionRateList();
        if ( list15 != null ) {
            issueQueryParameter.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list15 ) );
        }
        List<IssueFindStage> list16 = query.getFindStageList();
        if ( list16 != null ) {
            issueQueryParameter.setFindStageList( new ArrayList<IssueFindStage>( list16 ) );
        }
        List<IssueFindEnv> list17 = query.getFindEnvList();
        if ( list17 != null ) {
            issueQueryParameter.setFindEnvList( new ArrayList<IssueFindEnv>( list17 ) );
        }
        List<Long> list18 = query.getFindUserIdList();
        if ( list18 != null ) {
            issueQueryParameter.setFindUserIdList( new ArrayList<Long>( list18 ) );
        }
        List<String> list19 = query.getTagName();
        if ( list19 != null ) {
            issueQueryParameter.setTagName( new ArrayList<String>( list19 ) );
        }
        issueQueryParameter.setCreateTimeStart( query.getCreateTimeStart() );
        issueQueryParameter.setCreateTimeEnd( query.getCreateTimeEnd() );
        issueQueryParameter.setCloseTimeStart( query.getCloseTimeStart() );
        issueQueryParameter.setCloseTimeEnd( query.getCloseTimeEnd() );
        issueQueryParameter.setFixVersionIsNull( query.getFixVersionIsNull() );
        issueQueryParameter.setGmtModifiedStart( query.getGmtModifiedStart() );
        issueQueryParameter.setGmtModifiedEnd( query.getGmtModifiedEnd() );
        List<String> list20 = query.getVersionConfirm();
        if ( list20 != null ) {
            issueQueryParameter.setVersionConfirm( new ArrayList<String>( list20 ) );
        }
        List<String> list21 = query.getIssueCodeList();
        if ( list21 != null ) {
            issueQueryParameter.setIssueCodeList( new ArrayList<String>( list21 ) );
        }
        List<RefuseReason> list22 = query.getRefuseReasonList();
        if ( list22 != null ) {
            issueQueryParameter.setRefuseReasonList( new ArrayList<RefuseReason>( list22 ) );
        }
        List<IssueApplicationType> list23 = query.getApplicationTypeList();
        if ( list23 != null ) {
            issueQueryParameter.setApplicationTypeList( new ArrayList<IssueApplicationType>( list23 ) );
        }
        issueQueryParameter.setOrderField( query.getOrderField() );
        issueQueryParameter.setOrderType( query.getOrderType() );
        List<Long> list24 = query.getCcUserIdList();
        if ( list24 != null ) {
            issueQueryParameter.setCcUserIdList( new ArrayList<Long>( list24 ) );
        }
        issueQueryParameter.setValidFlag( query.getValidFlag() );

        return issueQueryParameter;
    }

    @Override
    public List<IssueBaseVO> convertIssueBaseVOList(List<IssueEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<IssueBaseVO> list = new ArrayList<IssueBaseVO>( entities.size() );
        for ( IssueEntity issueEntity : entities ) {
            list.add( convertIssueBaseVO( issueEntity ) );
        }

        return list;
    }

    @Override
    public IssueEntityDO convertDO(IssueEntity entity) {
        if ( entity == null ) {
            return null;
        }

        IssueEntityDO issueEntityDO = new IssueEntityDO();

        issueEntityDO.setEnable( entity.getEnable() );
        issueEntityDO.setCreatorId( entity.getCreatorId() );
        issueEntityDO.setCreator( entity.getCreator() );
        issueEntityDO.setGmtCreate( entity.getGmtCreate() );
        issueEntityDO.setModifierId( entity.getModifierId() );
        issueEntityDO.setModifier( entity.getModifier() );
        issueEntityDO.setGmtModified( entity.getGmtModified() );
        issueEntityDO.setCode( entity.getCode() );
        issueEntityDO.setTitle( entity.getTitle() );
        issueEntityDO.setStatus( entity.getStatus() );
        issueEntityDO.setPriority( entity.getPriority() );
        issueEntityDO.setRootCause( entity.getRootCause() );
        issueEntityDO.setType( entity.getType() );
        issueEntityDO.setTestMethod( entity.getTestMethod() );
        issueEntityDO.setFindStage( entity.getFindStage() );
        issueEntityDO.setFindEnv( entity.getFindEnv() );
        issueEntityDO.setRepetitionRate( entity.getRepetitionRate() );
        issueEntityDO.setReopenTime( entity.getReopenTime() );
        issueEntityDO.setReopen( entity.getReopen() );
        issueEntityDO.setActualWorkingHours( entity.getActualWorkingHours() );
        issueEntityDO.setStartFixTime( entity.getStartFixTime() );
        issueEntityDO.setDelayFixTime( entity.getDelayFixTime() );
        issueEntityDO.setDeliverTime( entity.getDeliverTime() );
        issueEntityDO.setRejectTime( entity.getRejectTime() );
        issueEntityDO.setCloseTime( entity.getCloseTime() );
        issueEntityDO.setFindTime( entity.getFindTime() );
        issueEntityDO.setUpdateTime( entity.getUpdateTime() );
        issueEntityDO.setProductCode( entity.getProductCode() );
        issueEntityDO.setProductName( entity.getProductName() );
        issueEntityDO.setRequirementCode( entity.getRequirementCode() );
        issueEntityDO.setRequirementName( entity.getRequirementName() );
        issueEntityDO.setRequirementLevel( entity.getRequirementLevel() );
        issueEntityDO.setFindVersionCode( entity.getFindVersionCode() );
        issueEntityDO.setFindVersionName( entity.getFindVersionName() );
        issueEntityDO.setFixVersionCode( entity.getFixVersionCode() );
        issueEntityDO.setFixVersionName( entity.getFixVersionName() );
        issueEntityDO.setSprintCode( entity.getSprintCode() );
        issueEntityDO.setSprintName( entity.getSprintName() );
        issueEntityDO.setFindUserId( entity.getFindUserId() );
        issueEntityDO.setFindUserName( entity.getFindUserName() );
        issueEntityDO.setUpdateUserId( entity.getUpdateUserId() );
        issueEntityDO.setUpdateUserName( entity.getUpdateUserName() );
        issueEntityDO.setHandleUserName( entity.getHandleUserName() );
        issueEntityDO.setHandleUserId( entity.getHandleUserId() );
        issueEntityDO.setDevelopUserId( entity.getDevelopUserId() );
        issueEntityDO.setDevelopUserName( entity.getDevelopUserName() );
        issueEntityDO.setTestUserId( entity.getTestUserId() );
        issueEntityDO.setTestUserName( entity.getTestUserName() );
        issueEntityDO.setDescription( entity.getDescription() );
        issueEntityDO.setVersionConfirm( entity.getVersionConfirm() );
        issueEntityDO.setOldCode( entity.getOldCode() );
        issueEntityDO.setExamination( entity.getExamination() );
        issueEntityDO.setTestOmission( entity.getTestOmission() );
        issueEntityDO.setCodeDefect( entity.getCodeDefect() );
        issueEntityDO.setTestOmissionVersion( entity.getTestOmissionVersion() );
        issueEntityDO.setCodeDefectVersion( entity.getCodeDefectVersion() );
        issueEntityDO.setApplicationType( entity.getApplicationType() );
        issueEntityDO.setPlanStartDate( entity.getPlanStartDate() );
        issueEntityDO.setPlanEndDate( entity.getPlanEndDate() );
        issueEntityDO.setMsgCode( entity.getMsgCode() );
        issueEntityDO.setIsValid( entity.getIsValid() );

        return issueEntityDO;
    }

    @Override
    public List<IssueEntityDO> convertDOList(List<IssueEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<IssueEntityDO> list = new ArrayList<IssueEntityDO>( entities.size() );
        for ( IssueEntity issueEntity : entities ) {
            list.add( convertDO( issueEntity ) );
        }

        return list;
    }

    @Override
    public List<IssueLegacyVO> convertIssueLegacyVOList(List<IssueEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<IssueLegacyVO> list = new ArrayList<IssueLegacyVO>( entities.size() );
        for ( IssueEntity issueEntity : entities ) {
            list.add( issueEntityToIssueLegacyVO( issueEntity ) );
        }

        return list;
    }

    @Override
    public List<IssueVO> convertVOList(List<IssueEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IssueVO> list = new ArrayList<IssueVO>( entityList.size() );
        for ( IssueEntity issueEntity : entityList ) {
            list.add( convertVO( issueEntity ) );
        }

        return list;
    }

    @Override
    public IssueVO convertVO(IssueEntity entity) {
        if ( entity == null ) {
            return null;
        }

        IssueVO issueVO = new IssueVO();

        issueVO.setValidFlag( entity.getIsValid() );
        issueVO.setCode( entity.getCode() );
        issueVO.setTitle( entity.getTitle() );
        issueVO.setDescription( entity.getDescription() );
        issueVO.setReopenTime( entity.getReopenTime() );
        issueVO.setStartFixTime( entity.getStartFixTime() );
        issueVO.setDelayFixTime( entity.getDelayFixTime() );
        issueVO.setDeliverTime( entity.getDeliverTime() );
        issueVO.setRejectTime( entity.getRejectTime() );
        issueVO.setCloseTime( entity.getCloseTime() );
        issueVO.setGmtCreate( entity.getGmtCreate() );
        issueVO.setGmtModified( entity.getGmtModified() );
        issueVO.setUpdateTime( entity.getUpdateTime() );
        issueVO.setFindTime( entity.getFindTime() );
        issueVO.setStatus( entity.getStatus() );
        issueVO.setFindEnv( entity.getFindEnv() );
        issueVO.setFindStage( entity.getFindStage() );
        issueVO.setPriority( entity.getPriority() );
        issueVO.setRepetitionRate( entity.getRepetitionRate() );
        issueVO.setRootCause( entity.getRootCause() );
        issueVO.setTestMethod( entity.getTestMethod() );
        issueVO.setType( entity.getType() );
        issueVO.setProductCode( entity.getProductCode() );
        issueVO.setProductName( entity.getProductName() );
        issueVO.setSprintCode( entity.getSprintCode() );
        issueVO.setSprintName( entity.getSprintName() );
        issueVO.setRequirementCode( entity.getRequirementCode() );
        issueVO.setRequirementName( entity.getRequirementName() );
        issueVO.setRequirementLevel( entity.getRequirementLevel() );
        issueVO.setFindVersionCode( entity.getFindVersionCode() );
        issueVO.setFindVersionName( entity.getFindVersionName() );
        issueVO.setFixVersionCode( entity.getFixVersionCode() );
        issueVO.setFixVersionName( entity.getFixVersionName() );
        issueVO.setFindUserId( entity.getFindUserId() );
        issueVO.setFindUserName( entity.getFindUserName() );
        issueVO.setHandleUserId( entity.getHandleUserId() );
        issueVO.setHandleUserName( entity.getHandleUserName() );
        issueVO.setDevelopUserId( entity.getDevelopUserId() );
        issueVO.setDevelopUserName( entity.getDevelopUserName() );
        issueVO.setTestUserId( entity.getTestUserId() );
        issueVO.setTestUserName( entity.getTestUserName() );
        issueVO.setCreatorId( entity.getCreatorId() );
        issueVO.setCreator( entity.getCreator() );
        issueVO.setUpdateUserId( entity.getUpdateUserId() );
        issueVO.setUpdateUserName( entity.getUpdateUserName() );
        issueVO.setActualWorkingHours( entity.getActualWorkingHours() );
        issueVO.setVersionConfirm( entity.getVersionConfirm() );
        issueVO.setExamination( entity.getExamination() );
        issueVO.setTestOmission( entity.getTestOmission() );
        issueVO.setCodeDefect( entity.getCodeDefect() );
        issueVO.setTestOmissionVersion( entity.getTestOmissionVersion() );
        issueVO.setCodeDefectVersion( entity.getCodeDefectVersion() );
        issueVO.setApplicationType( entity.getApplicationType() );
        issueVO.setPlanStartDate( entity.getPlanStartDate() );
        issueVO.setPlanEndDate( entity.getPlanEndDate() );
        issueVO.setMsgCode( entity.getMsgCode() );
        issueVO.setReopen( entity.getReopen() );

        return issueVO;
    }

    protected RelatedMatterStatusCountVO relatedMatterStatusCountVOToRelatedMatterStatusCountVO(com.zto.devops.project.client.model.requirement.entity.RelatedMatterStatusCountVO relatedMatterStatusCountVO) {
        if ( relatedMatterStatusCountVO == null ) {
            return null;
        }

        RelatedMatterStatusCountVO relatedMatterStatusCountVO1 = new RelatedMatterStatusCountVO();

        relatedMatterStatusCountVO1.setBusinessCode( relatedMatterStatusCountVO.getBusinessCode() );
        relatedMatterStatusCountVO1.setDomain( relatedMatterStatusCountVO.getDomain() );
        relatedMatterStatusCountVO1.setStatus( relatedMatterStatusCountVO.getStatus() );
        relatedMatterStatusCountVO1.setCount( relatedMatterStatusCountVO.getCount() );

        return relatedMatterStatusCountVO1;
    }

    protected User issueEntityToUser(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getCreatorId() );
        user.setUserName( issueEntity.getCreator() );

        return user;
    }

    protected User issueEntityToUser1(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getModifierId() );
        user.setUserName( issueEntity.getModifier() );

        return user;
    }

    protected User issueEntityToUser2(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getHandleUserId() );
        user.setUserName( issueEntity.getHandleUserName() );

        return user;
    }

    protected User issueEntityToUser3(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getFindUserId() );
        user.setUserName( issueEntity.getFindUserName() );

        return user;
    }

    protected User issueEntityToUser4(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getDevelopUserId() );
        user.setUserName( issueEntity.getDevelopUserName() );

        return user;
    }

    protected User issueEntityToUser5(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getTestUserId() );
        user.setUserName( issueEntity.getTestUserName() );

        return user;
    }

    protected Requirement issueEntityToRequirement(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Requirement requirement = new Requirement();

        requirement.setCode( issueEntity.getRequirementCode() );
        requirement.setName( issueEntity.getRequirementName() );

        return requirement;
    }

    protected Product issueEntityToProduct(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Product product = new Product();

        product.setCode( issueEntity.getProductCode() );
        product.setName( issueEntity.getProductName() );

        return product;
    }

    protected Version issueEntityToVersion(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Version version = new Version();

        version.setCode( issueEntity.getFindVersionCode() );
        version.setName( issueEntity.getFindVersionName() );

        return version;
    }

    protected Version issueEntityToVersion1(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Version version = new Version();

        version.setCode( issueEntity.getFixVersionCode() );
        version.setName( issueEntity.getFixVersionName() );

        return version;
    }

    protected Sprint issueEntityToSprint(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Sprint sprint = new Sprint();

        sprint.setCode( issueEntity.getSprintCode() );
        sprint.setName( issueEntity.getSprintName() );

        return sprint;
    }

    protected Attachment attachmentEntityToAttachment(AttachmentEntity attachmentEntity) {
        if ( attachmentEntity == null ) {
            return null;
        }

        Attachment attachment = new Attachment();

        attachment.setCode( attachmentEntity.getCode() );
        attachment.setBusinessCode( attachmentEntity.getBusinessCode() );
        attachment.setUrl( attachmentEntity.getUrl() );
        attachment.setName( attachmentEntity.getName() );
        attachment.setRemoteFileId( attachmentEntity.getRemoteFileId() );
        attachment.setDomain( attachmentEntity.getDomain() );
        attachment.setType( attachmentEntity.getType() );
        attachment.setDocumentType( attachmentEntity.getDocumentType() );
        attachment.setFileType( attachmentEntity.getFileType() );
        attachment.setSize( attachmentEntity.getSize() );

        return attachment;
    }

    protected Tag tagEntityToTag(TagEntity tagEntity) {
        if ( tagEntity == null ) {
            return null;
        }

        Tag tag = new Tag();

        tag.setCode( tagEntity.getCode() );
        tag.setDomain( tagEntity.getDomain() );
        tag.setBusinessCode( tagEntity.getBusinessCode() );
        tag.setType( tagEntity.getType() );
        tag.setTagAlias( tagEntity.getTagAlias() );
        tag.setTagName( tagEntity.getTagName() );

        return tag;
    }

    protected User commentEntityToUser(CommentEntity commentEntity) {
        if ( commentEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( commentEntity.getCreatorId() );
        user.setUserName( commentEntity.getCreator() );

        return user;
    }

    protected RelevantUser relevantUserEntityToRelevantUser(RelevantUserEntity relevantUserEntity) {
        if ( relevantUserEntity == null ) {
            return null;
        }

        RelevantUser relevantUser = new RelevantUser();

        relevantUser.setBusinessCode( relevantUserEntity.getBusinessCode() );
        relevantUser.setCode( relevantUserEntity.getCode() );
        if ( relevantUserEntity.getUserId() != null ) {
            relevantUser.setUserId( Long.parseLong( relevantUserEntity.getUserId() ) );
        }
        relevantUser.setUserName( relevantUserEntity.getUserName() );
        relevantUser.setDomain( relevantUserEntity.getDomain() );
        relevantUser.setType( relevantUserEntity.getType() );

        return relevantUser;
    }

    protected IssueLegacyVO issueEntityToIssueLegacyVO(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        IssueLegacyVO issueLegacyVO = new IssueLegacyVO();

        issueLegacyVO.setCode( issueEntity.getCode() );
        issueLegacyVO.setTitle( issueEntity.getTitle() );
        issueLegacyVO.setStatus( issueEntity.getStatus() );
        issueLegacyVO.setDevelopUserId( issueEntity.getDevelopUserId() );
        issueLegacyVO.setDevelopUserName( issueEntity.getDevelopUserName() );
        issueLegacyVO.setHandleUserId( issueEntity.getHandleUserId() );
        issueLegacyVO.setHandleUserName( issueEntity.getHandleUserName() );
        issueLegacyVO.setPriority( issueEntity.getPriority() );
        issueLegacyVO.setFindUserId( issueEntity.getFindUserId() );
        issueLegacyVO.setFindUserName( issueEntity.getFindUserName() );
        issueLegacyVO.setFindVersionCode( issueEntity.getFindVersionCode() );
        issueLegacyVO.setFindVersionName( issueEntity.getFindVersionName() );

        return issueLegacyVO;
    }
}
