package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.InterfaceCoverageEntityDO;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageInfoResp;
import com.zto.devops.qc.infrastructure.dao.entity.InterfaceCoverageEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class InterfaceCoverageEntityConverterImpl implements InterfaceCoverageEntityConverter {

    @Override
    public InterfaceCoverageInfoResp converter(InterfaceCoverageEntity entity) {
        if ( entity == null ) {
            return null;
        }

        InterfaceCoverageInfoResp interfaceCoverageInfoResp = new InterfaceCoverageInfoResp();

        interfaceCoverageInfoResp.setInterfaceName( entity.getInterfaceAlias() );
        interfaceCoverageInfoResp.setStatus( entity.getIsCovered() );
        interfaceCoverageInfoResp.setInterfaceType( entity.getInterfaceMethodType() );
        interfaceCoverageInfoResp.setInterfaceDocAddress( entity.getInterfaceDocAddress() );
        interfaceCoverageInfoResp.setInterfaceCallNumber( entity.getInterfaceCallNumber() );
        interfaceCoverageInfoResp.setInterfaceErrorNumber( entity.getInterfaceErrorNumber() );

        return interfaceCoverageInfoResp;
    }

    @Override
    public List<InterfaceCoverageInfoResp> converter(List<InterfaceCoverageEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<InterfaceCoverageInfoResp> list = new ArrayList<InterfaceCoverageInfoResp>( entityList.size() );
        for ( InterfaceCoverageEntity interfaceCoverageEntity : entityList ) {
            list.add( converter( interfaceCoverageEntity ) );
        }

        return list;
    }

    @Override
    public List<InterfaceCoverageEntity> convertToEntity(List<InterfaceCoverageEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<InterfaceCoverageEntity> list = new ArrayList<InterfaceCoverageEntity>( entityDOList.size() );
        for ( InterfaceCoverageEntityDO interfaceCoverageEntityDO : entityDOList ) {
            list.add( interfaceCoverageEntityDOToInterfaceCoverageEntity( interfaceCoverageEntityDO ) );
        }

        return list;
    }

    @Override
    public List<InterfaceCoverageEntityDO> convertToEntityDO(List<InterfaceCoverageEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<InterfaceCoverageEntityDO> list = new ArrayList<InterfaceCoverageEntityDO>( entityList.size() );
        for ( InterfaceCoverageEntity interfaceCoverageEntity : entityList ) {
            list.add( interfaceCoverageEntityToInterfaceCoverageEntityDO( interfaceCoverageEntity ) );
        }

        return list;
    }

    protected InterfaceCoverageEntity interfaceCoverageEntityDOToInterfaceCoverageEntity(InterfaceCoverageEntityDO interfaceCoverageEntityDO) {
        if ( interfaceCoverageEntityDO == null ) {
            return null;
        }

        InterfaceCoverageEntity interfaceCoverageEntity = new InterfaceCoverageEntity();

        interfaceCoverageEntity.setEnable( interfaceCoverageEntityDO.getEnable() );
        interfaceCoverageEntity.setCreatorId( interfaceCoverageEntityDO.getCreatorId() );
        interfaceCoverageEntity.setCreator( interfaceCoverageEntityDO.getCreator() );
        interfaceCoverageEntity.setGmtCreate( interfaceCoverageEntityDO.getGmtCreate() );
        interfaceCoverageEntity.setModifierId( interfaceCoverageEntityDO.getModifierId() );
        interfaceCoverageEntity.setModifier( interfaceCoverageEntityDO.getModifier() );
        interfaceCoverageEntity.setGmtModified( interfaceCoverageEntityDO.getGmtModified() );
        interfaceCoverageEntity.setId( interfaceCoverageEntityDO.getId() );
        interfaceCoverageEntity.setInterfaceCoverageCode( interfaceCoverageEntityDO.getInterfaceCoverageCode() );
        interfaceCoverageEntity.setVersionCode( interfaceCoverageEntityDO.getVersionCode() );
        interfaceCoverageEntity.setVersionName( interfaceCoverageEntityDO.getVersionName() );
        interfaceCoverageEntity.setAppId( interfaceCoverageEntityDO.getAppId() );
        interfaceCoverageEntity.setCommitId( interfaceCoverageEntityDO.getCommitId() );
        interfaceCoverageEntity.setInterfaceDocAddress( interfaceCoverageEntityDO.getInterfaceDocAddress() );
        interfaceCoverageEntity.setInterfaceFullClassName( interfaceCoverageEntityDO.getInterfaceFullClassName() );
        interfaceCoverageEntity.setInterfaceMethodName( interfaceCoverageEntityDO.getInterfaceMethodName() );
        interfaceCoverageEntity.setInterfaceMethodDesc( interfaceCoverageEntityDO.getInterfaceMethodDesc() );
        interfaceCoverageEntity.setInterfaceMethodType( interfaceCoverageEntityDO.getInterfaceMethodType() );
        interfaceCoverageEntity.setInterfaceMethodAnnotation( interfaceCoverageEntityDO.getInterfaceMethodAnnotation() );
        interfaceCoverageEntity.setInterfaceCallNumber( interfaceCoverageEntityDO.getInterfaceCallNumber() );
        interfaceCoverageEntity.setInterfaceErrorNumber( interfaceCoverageEntityDO.getInterfaceErrorNumber() );
        interfaceCoverageEntity.setIsCovered( interfaceCoverageEntityDO.getIsCovered() );
        interfaceCoverageEntity.setModifyClassName( interfaceCoverageEntityDO.getModifyClassName() );
        interfaceCoverageEntity.setModifyMethodName( interfaceCoverageEntityDO.getModifyMethodName() );
        interfaceCoverageEntity.setModifyMethodDesc( interfaceCoverageEntityDO.getModifyMethodDesc() );
        interfaceCoverageEntity.setModifyMethodMd5( interfaceCoverageEntityDO.getModifyMethodMd5() );
        interfaceCoverageEntity.setInterfaceAlias( interfaceCoverageEntityDO.getInterfaceAlias() );
        interfaceCoverageEntity.setMethodParameterStr( interfaceCoverageEntityDO.getMethodParameterStr() );
        interfaceCoverageEntity.setZcatMetricKey( interfaceCoverageEntityDO.getZcatMetricKey() );

        return interfaceCoverageEntity;
    }

    protected InterfaceCoverageEntityDO interfaceCoverageEntityToInterfaceCoverageEntityDO(InterfaceCoverageEntity interfaceCoverageEntity) {
        if ( interfaceCoverageEntity == null ) {
            return null;
        }

        InterfaceCoverageEntityDO interfaceCoverageEntityDO = new InterfaceCoverageEntityDO();

        interfaceCoverageEntityDO.setEnable( interfaceCoverageEntity.getEnable() );
        interfaceCoverageEntityDO.setCreatorId( interfaceCoverageEntity.getCreatorId() );
        interfaceCoverageEntityDO.setCreator( interfaceCoverageEntity.getCreator() );
        interfaceCoverageEntityDO.setGmtCreate( interfaceCoverageEntity.getGmtCreate() );
        interfaceCoverageEntityDO.setModifierId( interfaceCoverageEntity.getModifierId() );
        interfaceCoverageEntityDO.setModifier( interfaceCoverageEntity.getModifier() );
        interfaceCoverageEntityDO.setGmtModified( interfaceCoverageEntity.getGmtModified() );
        if ( interfaceCoverageEntity.hasId() ) {
            interfaceCoverageEntityDO.setId( interfaceCoverageEntity.getId() );
        }
        interfaceCoverageEntityDO.setInterfaceCoverageCode( interfaceCoverageEntity.getInterfaceCoverageCode() );
        interfaceCoverageEntityDO.setVersionCode( interfaceCoverageEntity.getVersionCode() );
        interfaceCoverageEntityDO.setVersionName( interfaceCoverageEntity.getVersionName() );
        interfaceCoverageEntityDO.setAppId( interfaceCoverageEntity.getAppId() );
        interfaceCoverageEntityDO.setCommitId( interfaceCoverageEntity.getCommitId() );
        interfaceCoverageEntityDO.setInterfaceDocAddress( interfaceCoverageEntity.getInterfaceDocAddress() );
        interfaceCoverageEntityDO.setInterfaceFullClassName( interfaceCoverageEntity.getInterfaceFullClassName() );
        interfaceCoverageEntityDO.setInterfaceMethodName( interfaceCoverageEntity.getInterfaceMethodName() );
        interfaceCoverageEntityDO.setInterfaceMethodDesc( interfaceCoverageEntity.getInterfaceMethodDesc() );
        interfaceCoverageEntityDO.setInterfaceMethodType( interfaceCoverageEntity.getInterfaceMethodType() );
        interfaceCoverageEntityDO.setInterfaceMethodAnnotation( interfaceCoverageEntity.getInterfaceMethodAnnotation() );
        interfaceCoverageEntityDO.setInterfaceCallNumber( interfaceCoverageEntity.getInterfaceCallNumber() );
        interfaceCoverageEntityDO.setInterfaceErrorNumber( interfaceCoverageEntity.getInterfaceErrorNumber() );
        interfaceCoverageEntityDO.setIsCovered( interfaceCoverageEntity.getIsCovered() );
        interfaceCoverageEntityDO.setModifyClassName( interfaceCoverageEntity.getModifyClassName() );
        interfaceCoverageEntityDO.setModifyMethodName( interfaceCoverageEntity.getModifyMethodName() );
        interfaceCoverageEntityDO.setModifyMethodDesc( interfaceCoverageEntity.getModifyMethodDesc() );
        interfaceCoverageEntityDO.setModifyMethodMd5( interfaceCoverageEntity.getModifyMethodMd5() );
        interfaceCoverageEntityDO.setInterfaceAlias( interfaceCoverageEntity.getInterfaceAlias() );
        interfaceCoverageEntityDO.setMethodParameterStr( interfaceCoverageEntity.getMethodParameterStr() );
        interfaceCoverageEntityDO.setZcatMetricKey( interfaceCoverageEntity.getZcatMetricKey() );

        return interfaceCoverageEntityDO;
    }
}
