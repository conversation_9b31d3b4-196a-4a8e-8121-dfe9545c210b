package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AttachmentEntityConverterImpl implements AttachmentEntityConverter {

    @Override
    public AttachmentEntity convert(AttachmentVO vo) {
        if ( vo == null ) {
            return null;
        }

        AttachmentEntity attachmentEntity = new AttachmentEntity();

        attachmentEntity.setCreatorId( vo.getCreatorId() );
        attachmentEntity.setCreator( vo.getCreator() );
        attachmentEntity.setGmtCreate( vo.getGmtCreate() );
        attachmentEntity.setModifierId( vo.getModifierId() );
        attachmentEntity.setModifier( vo.getModifier() );
        attachmentEntity.setGmtModified( vo.getGmtModified() );
        attachmentEntity.setCode( vo.getCode() );
        attachmentEntity.setDomain( vo.getDomain() );
        attachmentEntity.setBusinessCode( vo.getBusinessCode() );
        attachmentEntity.setDocumentType( vo.getDocumentType() );
        attachmentEntity.setType( vo.getType() );
        attachmentEntity.setName( vo.getName() );
        attachmentEntity.setRemoteFileId( vo.getRemoteFileId() );
        attachmentEntity.setUrl( vo.getUrl() );
        attachmentEntity.setFileType( vo.getFileType() );
        attachmentEntity.setSize( vo.getSize() );

        return attachmentEntity;
    }

    @Override
    public List<AttachmentEntity> convert(Collection<AttachmentVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<AttachmentEntity> list = new ArrayList<AttachmentEntity>( vo.size() );
        for ( AttachmentVO attachmentVO : vo ) {
            list.add( convert( attachmentVO ) );
        }

        return list;
    }
}
