package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.domain.model.Tag;
import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TagVOConverterImpl implements TagVOConverter {

    @Override
    public TagVO convert(TagEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TagVO tagVO = new TagVO();

        tagVO.setDomain( entity.getDomain() );
        tagVO.setBusinessCode( entity.getBusinessCode() );
        tagVO.setCode( entity.getCode() );
        tagVO.setType( entity.getType() );
        tagVO.setTagAlias( entity.getTagAlias() );
        tagVO.setTagCode( entity.getTagCode() );
        tagVO.setTagName( entity.getTagName() );
        tagVO.setEnable( entity.getEnable() );
        tagVO.setCreatorId( entity.getCreatorId() );

        return tagVO;
    }

    @Override
    public List<TagVO> convert(List<TagEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TagVO> list = new ArrayList<TagVO>( entities.size() );
        for ( TagEntity tagEntity : entities ) {
            list.add( convert( tagEntity ) );
        }

        return list;
    }

    @Override
    public List<TagEntity> convert(Collection<TagVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<TagEntity> list = new ArrayList<TagEntity>( vos.size() );
        for ( TagVO tagVO : vos ) {
            list.add( tagVOToTagEntity( tagVO ) );
        }

        return list;
    }

    @Override
    public List<Tag> convertList(Collection<TagEntity> entities) {
        if ( entities == null ) {
            return null;
        }

        List<Tag> list = new ArrayList<Tag>( entities.size() );
        for ( TagEntity tagEntity : entities ) {
            list.add( tagEntityToTag( tagEntity ) );
        }

        return list;
    }

    @Override
    public List<TagEntity> convertTagEntity(List<TagEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<TagEntity> list1 = new ArrayList<TagEntity>( list.size() );
        for ( TagEntityDO tagEntityDO : list ) {
            list1.add( convert( tagEntityDO ) );
        }

        return list1;
    }

    @Override
    public TagEntity convert(TagEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TagEntity tagEntity = new TagEntity();

        tagEntity.setEnable( entityDO.getEnable() );
        tagEntity.setCreatorId( entityDO.getCreatorId() );
        tagEntity.setCreator( entityDO.getCreator() );
        tagEntity.setGmtCreate( entityDO.getGmtCreate() );
        tagEntity.setModifierId( entityDO.getModifierId() );
        tagEntity.setModifier( entityDO.getModifier() );
        tagEntity.setGmtModified( entityDO.getGmtModified() );
        tagEntity.setCode( entityDO.getCode() );
        tagEntity.setDomain( entityDO.getDomain() );
        tagEntity.setBusinessCode( entityDO.getBusinessCode() );
        tagEntity.setType( entityDO.getType() );
        tagEntity.setTagAlias( entityDO.getTagAlias() );
        tagEntity.setTagName( entityDO.getTagName() );
        tagEntity.setTagCode( entityDO.getTagCode() );

        return tagEntity;
    }

    protected TagEntity tagVOToTagEntity(TagVO tagVO) {
        if ( tagVO == null ) {
            return null;
        }

        TagEntity tagEntity = new TagEntity();

        tagEntity.setEnable( tagVO.getEnable() );
        tagEntity.setCreatorId( tagVO.getCreatorId() );
        tagEntity.setCode( tagVO.getCode() );
        tagEntity.setDomain( tagVO.getDomain() );
        tagEntity.setBusinessCode( tagVO.getBusinessCode() );
        tagEntity.setType( tagVO.getType() );
        tagEntity.setTagAlias( tagVO.getTagAlias() );
        tagEntity.setTagName( tagVO.getTagName() );
        tagEntity.setTagCode( tagVO.getTagCode() );

        return tagEntity;
    }

    protected Tag tagEntityToTag(TagEntity tagEntity) {
        if ( tagEntity == null ) {
            return null;
        }

        Tag tag = new Tag();

        tag.setCode( tagEntity.getCode() );
        tag.setDomain( tagEntity.getDomain() );
        tag.setBusinessCode( tagEntity.getBusinessCode() );
        tag.setType( tagEntity.getType() );
        tag.setTagAlias( tagEntity.getTagAlias() );
        tag.setTagName( tagEntity.getTagName() );

        return tag;
    }
}
