package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTask;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticTaskEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseExecuteRecordEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticTaskEntityConverterImpl implements AutomaticTaskEntityConverter {

    @Override
    public AutomaticTaskEntity convert2Entity(AutomaticTaskEntityDO taskEntityDO) {
        if ( taskEntityDO == null ) {
            return null;
        }

        AutomaticTaskEntity automaticTaskEntity = new AutomaticTaskEntity();

        automaticTaskEntity.setEnable( taskEntityDO.getEnable() );
        automaticTaskEntity.setCreatorId( taskEntityDO.getCreatorId() );
        automaticTaskEntity.setCreator( taskEntityDO.getCreator() );
        automaticTaskEntity.setGmtCreate( taskEntityDO.getGmtCreate() );
        automaticTaskEntity.setModifierId( taskEntityDO.getModifierId() );
        automaticTaskEntity.setModifier( taskEntityDO.getModifier() );
        automaticTaskEntity.setGmtModified( taskEntityDO.getGmtModified() );
        automaticTaskEntity.setId( taskEntityDO.getId() );
        automaticTaskEntity.setCode( taskEntityDO.getCode() );
        automaticTaskEntity.setTaskId( taskEntityDO.getTaskId() );
        automaticTaskEntity.setBuildId( taskEntityDO.getBuildId() );
        automaticTaskEntity.setAutomaticSourceCode( taskEntityDO.getAutomaticSourceCode() );
        automaticTaskEntity.setProductCode( taskEntityDO.getProductCode() );
        automaticTaskEntity.setSourceAddress( taskEntityDO.getSourceAddress() );
        automaticTaskEntity.setFilename( taskEntityDO.getFilename() );
        automaticTaskEntity.setType( taskEntityDO.getType() );
        automaticTaskEntity.setExecuteMode( taskEntityDO.getExecuteMode() );
        automaticTaskEntity.setEnv( taskEntityDO.getEnv() );
        automaticTaskEntity.setExecuteTag( taskEntityDO.getExecuteTag() );
        automaticTaskEntity.setContent( taskEntityDO.getContent() );
        automaticTaskEntity.setStatus( taskEntityDO.getStatus() );
        automaticTaskEntity.setResultFile( taskEntityDO.getResultFile() );
        automaticTaskEntity.setReportFile( taskEntityDO.getReportFile() );
        automaticTaskEntity.setExecLogFile( taskEntityDO.getExecLogFile() );
        automaticTaskEntity.setComment( taskEntityDO.getComment() );
        automaticTaskEntity.setTestPlanCode( taskEntityDO.getTestPlanCode() );
        automaticTaskEntity.setTestStage( taskEntityDO.getTestStage() );
        automaticTaskEntity.setStartTime( taskEntityDO.getStartTime() );
        automaticTaskEntity.setFinishTime( taskEntityDO.getFinishTime() );
        automaticTaskEntity.setCaseFile( taskEntityDO.getCaseFile() );
        automaticTaskEntity.setVersionCode( taskEntityDO.getVersionCode() );
        automaticTaskEntity.setTrigMode( taskEntityDO.getTrigMode() );
        automaticTaskEntity.setBranchName( taskEntityDO.getBranchName() );
        automaticTaskEntity.setWorkDir( taskEntityDO.getWorkDir() );
        automaticTaskEntity.setCommitId( taskEntityDO.getCommitId() );
        automaticTaskEntity.setCoverageFlag( taskEntityDO.getCoverageFlag() );
        automaticTaskEntity.setErrorLogFile( taskEntityDO.getErrorLogFile() );
        automaticTaskEntity.setSchedulerCode( taskEntityDO.getSchedulerCode() );

        return automaticTaskEntity;
    }

    @Override
    public List<AutomaticTaskEntityDO> convert2DOList(List<AutomaticTaskEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AutomaticTaskEntityDO> list = new ArrayList<AutomaticTaskEntityDO>( entityList.size() );
        for ( AutomaticTaskEntity automaticTaskEntity : entityList ) {
            list.add( convert2DO( automaticTaskEntity ) );
        }

        return list;
    }

    @Override
    public AutomaticTaskEntityDO convert2DO(AutomaticTaskEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticTaskEntityDO automaticTaskEntityDO = new AutomaticTaskEntityDO();

        automaticTaskEntityDO.setEnable( entity.getEnable() );
        automaticTaskEntityDO.setCreatorId( entity.getCreatorId() );
        automaticTaskEntityDO.setCreator( entity.getCreator() );
        automaticTaskEntityDO.setGmtCreate( entity.getGmtCreate() );
        automaticTaskEntityDO.setModifierId( entity.getModifierId() );
        automaticTaskEntityDO.setModifier( entity.getModifier() );
        automaticTaskEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            automaticTaskEntityDO.setId( entity.getId() );
        }
        automaticTaskEntityDO.setCode( entity.getCode() );
        automaticTaskEntityDO.setTaskId( entity.getTaskId() );
        automaticTaskEntityDO.setBuildId( entity.getBuildId() );
        automaticTaskEntityDO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        automaticTaskEntityDO.setProductCode( entity.getProductCode() );
        automaticTaskEntityDO.setSourceAddress( entity.getSourceAddress() );
        automaticTaskEntityDO.setFilename( entity.getFilename() );
        automaticTaskEntityDO.setType( entity.getType() );
        automaticTaskEntityDO.setExecuteMode( entity.getExecuteMode() );
        automaticTaskEntityDO.setEnv( entity.getEnv() );
        automaticTaskEntityDO.setExecuteTag( entity.getExecuteTag() );
        automaticTaskEntityDO.setContent( entity.getContent() );
        automaticTaskEntityDO.setStatus( entity.getStatus() );
        automaticTaskEntityDO.setResultFile( entity.getResultFile() );
        automaticTaskEntityDO.setReportFile( entity.getReportFile() );
        automaticTaskEntityDO.setExecLogFile( entity.getExecLogFile() );
        automaticTaskEntityDO.setComment( entity.getComment() );
        automaticTaskEntityDO.setTestPlanCode( entity.getTestPlanCode() );
        automaticTaskEntityDO.setTestStage( entity.getTestStage() );
        automaticTaskEntityDO.setStartTime( entity.getStartTime() );
        automaticTaskEntityDO.setFinishTime( entity.getFinishTime() );
        automaticTaskEntityDO.setCaseFile( entity.getCaseFile() );
        automaticTaskEntityDO.setVersionCode( entity.getVersionCode() );
        automaticTaskEntityDO.setTrigMode( entity.getTrigMode() );
        automaticTaskEntityDO.setBranchName( entity.getBranchName() );
        automaticTaskEntityDO.setWorkDir( entity.getWorkDir() );
        automaticTaskEntityDO.setCommitId( entity.getCommitId() );
        automaticTaskEntityDO.setCoverageFlag( entity.getCoverageFlag() );
        automaticTaskEntityDO.setErrorLogFile( entity.getErrorLogFile() );
        automaticTaskEntityDO.setSchedulerCode( entity.getSchedulerCode() );

        return automaticTaskEntityDO;
    }

    @Override
    public TestcaseExecuteRecordEntity convert(TestcaseExecuteRecordEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TestcaseExecuteRecordEntity testcaseExecuteRecordEntity = new TestcaseExecuteRecordEntity();

        testcaseExecuteRecordEntity.setEnable( entityDO.getEnable() );
        testcaseExecuteRecordEntity.setCreatorId( entityDO.getCreatorId() );
        testcaseExecuteRecordEntity.setCreator( entityDO.getCreator() );
        testcaseExecuteRecordEntity.setGmtCreate( entityDO.getGmtCreate() );
        testcaseExecuteRecordEntity.setModifierId( entityDO.getModifierId() );
        testcaseExecuteRecordEntity.setModifier( entityDO.getModifier() );
        testcaseExecuteRecordEntity.setGmtModified( entityDO.getGmtModified() );
        testcaseExecuteRecordEntity.setId( entityDO.getId() );
        testcaseExecuteRecordEntity.setTestcaseCode( entityDO.getTestcaseCode() );
        testcaseExecuteRecordEntity.setTestPlanCode( entityDO.getTestPlanCode() );
        testcaseExecuteRecordEntity.setTestStage( entityDO.getTestStage() );
        testcaseExecuteRecordEntity.setResult( entityDO.getResult() );
        testcaseExecuteRecordEntity.setAutomaticTaskCode( entityDO.getAutomaticTaskCode() );
        testcaseExecuteRecordEntity.setStartTime( entityDO.getStartTime() );
        testcaseExecuteRecordEntity.setFinishTime( entityDO.getFinishTime() );
        testcaseExecuteRecordEntity.setResultFile( entityDO.getResultFile() );
        testcaseExecuteRecordEntity.setExecLogFile( entityDO.getExecLogFile() );
        testcaseExecuteRecordEntity.setReportFile( entityDO.getReportFile() );

        return testcaseExecuteRecordEntity;
    }

    @Override
    public AutomaticTask convert(AutomaticTaskEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticTask automaticTask = new AutomaticTask();

        automaticTask.setCode( entity.getCode() );
        automaticTask.setTaskId( entity.getTaskId() );
        automaticTask.setBuildId( entity.getBuildId() );
        automaticTask.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        automaticTask.setProductCode( entity.getProductCode() );
        automaticTask.setSourceAddress( entity.getSourceAddress() );
        automaticTask.setFilename( entity.getFilename() );
        automaticTask.setType( entity.getType() );
        automaticTask.setExecuteMode( entity.getExecuteMode() );
        automaticTask.setEnv( entity.getEnv() );
        automaticTask.setExecuteTag( entity.getExecuteTag() );
        automaticTask.setContent( entity.getContent() );
        automaticTask.setStatus( entity.getStatus() );
        automaticTask.setResultFile( entity.getResultFile() );
        automaticTask.setReportFile( entity.getReportFile() );
        automaticTask.setExecLogFile( entity.getExecLogFile() );
        automaticTask.setComment( entity.getComment() );
        automaticTask.setTestPlanCode( entity.getTestPlanCode() );
        automaticTask.setTestStage( entity.getTestStage() );
        automaticTask.setStartTime( entity.getStartTime() );
        automaticTask.setFinishTime( entity.getFinishTime() );
        automaticTask.setCaseFile( entity.getCaseFile() );
        automaticTask.setVersionCode( entity.getVersionCode() );
        automaticTask.setTrigMode( entity.getTrigMode() );
        automaticTask.setBranchName( entity.getBranchName() );
        automaticTask.setWorkDir( entity.getWorkDir() );
        automaticTask.setCommitId( entity.getCommitId() );
        automaticTask.setCoverageFlag( entity.getCoverageFlag() );
        automaticTask.setCreatorId( entity.getCreatorId() );
        automaticTask.setCreator( entity.getCreator() );
        automaticTask.setErrorLogFile( entity.getErrorLogFile() );

        return automaticTask;
    }

    @Override
    public List<TestcaseExecuteRecordEntityDO> convertList(List<TestcaseExecuteRecordEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TestcaseExecuteRecordEntityDO> list = new ArrayList<TestcaseExecuteRecordEntityDO>( entityList.size() );
        for ( TestcaseExecuteRecordEntity testcaseExecuteRecordEntity : entityList ) {
            list.add( convertor( testcaseExecuteRecordEntity ) );
        }

        return list;
    }

    @Override
    public TestcaseExecuteRecordEntityDO convertor(TestcaseExecuteRecordEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TestcaseExecuteRecordEntityDO testcaseExecuteRecordEntityDO = new TestcaseExecuteRecordEntityDO();

        testcaseExecuteRecordEntityDO.setEnable( entity.getEnable() );
        testcaseExecuteRecordEntityDO.setCreatorId( entity.getCreatorId() );
        testcaseExecuteRecordEntityDO.setCreator( entity.getCreator() );
        testcaseExecuteRecordEntityDO.setGmtCreate( entity.getGmtCreate() );
        testcaseExecuteRecordEntityDO.setModifierId( entity.getModifierId() );
        testcaseExecuteRecordEntityDO.setModifier( entity.getModifier() );
        testcaseExecuteRecordEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            testcaseExecuteRecordEntityDO.setId( entity.getId() );
        }
        testcaseExecuteRecordEntityDO.setTestcaseCode( entity.getTestcaseCode() );
        testcaseExecuteRecordEntityDO.setTestPlanCode( entity.getTestPlanCode() );
        testcaseExecuteRecordEntityDO.setTestStage( entity.getTestStage() );
        testcaseExecuteRecordEntityDO.setResult( entity.getResult() );
        testcaseExecuteRecordEntityDO.setAutomaticTaskCode( entity.getAutomaticTaskCode() );
        testcaseExecuteRecordEntityDO.setStartTime( entity.getStartTime() );
        testcaseExecuteRecordEntityDO.setFinishTime( entity.getFinishTime() );
        testcaseExecuteRecordEntityDO.setResultFile( entity.getResultFile() );
        testcaseExecuteRecordEntityDO.setExecLogFile( entity.getExecLogFile() );
        testcaseExecuteRecordEntityDO.setReportFile( entity.getReportFile() );

        return testcaseExecuteRecordEntityDO;
    }
}
