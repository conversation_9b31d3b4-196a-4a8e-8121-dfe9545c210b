package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.ApiCaseEntityDO;
import com.zto.devops.qc.client.model.dto.ApiTestCaseEntityDO;
import com.zto.devops.qc.client.model.dto.ApiTestEntityDO;
import com.zto.devops.qc.client.model.dto.SceneApiRelationEntityDO;
import com.zto.devops.qc.client.model.dto.SceneDatabaseAuthorizeEntityDO;
import com.zto.devops.qc.client.model.dto.SceneIndexEntityDO;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiCaseExceptionVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiLiteInfoVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiSampleCaseVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.PageApiTestCaseVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneIndexVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.TmApiTestCaseVO;
import com.zto.devops.qc.client.model.testmanager.apitest.event.AddApiTestCaseEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.AddApiTestVariableEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.DeleteApiTestVariableEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.EditApiTestCaseEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.EditApiTestVariableEvent;
import com.zto.devops.qc.client.model.testmanager.apitest.event.UpdateApiTestVariableStatusEvent;
import com.zto.devops.qc.infrastructure.dao.entity.ApiCaseEntity;
import com.zto.devops.qc.infrastructure.dao.entity.ApiTestCaseEntity;
import com.zto.devops.qc.infrastructure.dao.entity.ApiTestEntity;
import com.zto.devops.qc.infrastructure.dao.entity.ApiTestVariableEntity;
import com.zto.devops.qc.infrastructure.dao.entity.SceneApiRelationEntity;
import com.zto.devops.qc.infrastructure.dao.entity.SceneDatabaseAuthorizeEntity;
import com.zto.devops.qc.infrastructure.dao.entity.SceneIndexEntity;
import com.zto.devops.qc.infrastructure.dao.entity.SceneInfoEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class ApiTestEntityConverterImpl implements ApiTestEntityConverter {

    @Override
    public ApiTestVariableVO converter(ApiTestVariableEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ApiTestVariableVO apiTestVariableVO = new ApiTestVariableVO();

        apiTestVariableVO.setCreatorId( entity.getCreatorId() );
        apiTestVariableVO.setCreator( entity.getCreator() );
        apiTestVariableVO.setGmtCreate( entity.getGmtCreate() );
        apiTestVariableVO.setModifierId( entity.getModifierId() );
        apiTestVariableVO.setModifier( entity.getModifier() );
        apiTestVariableVO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            apiTestVariableVO.setId( entity.getId() );
        }
        apiTestVariableVO.setProductCode( entity.getProductCode() );
        apiTestVariableVO.setProductName( entity.getProductName() );
        apiTestVariableVO.setLinkCode( entity.getLinkCode() );
        apiTestVariableVO.setVariableCode( entity.getVariableCode() );
        apiTestVariableVO.setVariableName( entity.getVariableName() );
        apiTestVariableVO.setVariableKey( entity.getVariableKey() );
        apiTestVariableVO.setVariableValue( entity.getVariableValue() );
        apiTestVariableVO.setType( entity.getType() );
        apiTestVariableVO.setSceneType( entity.getSceneType() );
        apiTestVariableVO.setUsageType( entity.getUsageType() );
        apiTestVariableVO.setRequiredStatus( entity.getRequiredStatus() );
        apiTestVariableVO.setVariableStatus( entity.getVariableStatus() );
        apiTestVariableVO.setEnable( entity.getEnable() );
        apiTestVariableVO.setVariableDesc( entity.getVariableDesc() );
        apiTestVariableVO.setSubVariableType( entity.getSubVariableType() );
        apiTestVariableVO.setLoginValidTime( entity.getLoginValidTime() );

        return apiTestVariableVO;
    }

    @Override
    public ApiTestVariableEntity converter(AddApiTestVariableEvent event) {
        if ( event == null ) {
            return null;
        }

        ApiTestVariableEntity apiTestVariableEntity = new ApiTestVariableEntity();

        apiTestVariableEntity.setCreatorId( eventTransactorUserId( event ) );
        apiTestVariableEntity.setCreator( eventTransactorUserName( event ) );
        apiTestVariableEntity.setModifierId( eventTransactorUserId( event ) );
        apiTestVariableEntity.setModifier( eventTransactorUserName( event ) );
        apiTestVariableEntity.setGmtModified( event.getOccurred() );
        apiTestVariableEntity.setProductCode( event.getProductCode() );
        apiTestVariableEntity.setProductName( event.getProductName() );
        apiTestVariableEntity.setLinkCode( event.getLinkCode() );
        apiTestVariableEntity.setVariableCode( event.getVariableCode() );
        apiTestVariableEntity.setVariableName( event.getVariableName() );
        apiTestVariableEntity.setVariableKey( event.getVariableKey() );
        apiTestVariableEntity.setVariableValue( event.getVariableValue() );
        apiTestVariableEntity.setType( event.getType() );
        apiTestVariableEntity.setSceneType( event.getSceneType() );
        if ( event.getVariableStatus() != null ) {
            apiTestVariableEntity.setVariableStatus( Boolean.parseBoolean( event.getVariableStatus() ) );
        }
        apiTestVariableEntity.setSubVariableType( event.getSubVariableType() );
        apiTestVariableEntity.setLoginValidTime( event.getLoginValidTime() );

        return apiTestVariableEntity;
    }

    @Override
    public ApiTestVariableEntity converter(EditApiTestVariableEvent event) {
        if ( event == null ) {
            return null;
        }

        ApiTestVariableEntity apiTestVariableEntity = new ApiTestVariableEntity();

        apiTestVariableEntity.setModifierId( eventTransactorUserId1( event ) );
        apiTestVariableEntity.setModifier( eventTransactorUserName1( event ) );
        apiTestVariableEntity.setGmtModified( event.getOccurred() );
        apiTestVariableEntity.setProductCode( event.getProductCode() );
        apiTestVariableEntity.setProductName( event.getProductName() );
        apiTestVariableEntity.setLinkCode( event.getLinkCode() );
        apiTestVariableEntity.setVariableCode( event.getVariableCode() );
        apiTestVariableEntity.setVariableName( event.getVariableName() );
        apiTestVariableEntity.setVariableKey( event.getVariableKey() );
        apiTestVariableEntity.setVariableValue( event.getVariableValue() );
        apiTestVariableEntity.setType( event.getType() );
        if ( event.getVariableStatus() != null ) {
            apiTestVariableEntity.setVariableStatus( Boolean.parseBoolean( event.getVariableStatus() ) );
        }
        apiTestVariableEntity.setSubVariableType( event.getSubVariableType() );
        apiTestVariableEntity.setLoginValidTime( event.getLoginValidTime() );

        return apiTestVariableEntity;
    }

    @Override
    public ApiTestVariableEntity converter(UpdateApiTestVariableStatusEvent event) {
        if ( event == null ) {
            return null;
        }

        ApiTestVariableEntity apiTestVariableEntity = new ApiTestVariableEntity();

        apiTestVariableEntity.setModifierId( eventTransactorUserId2( event ) );
        apiTestVariableEntity.setModifier( eventTransactorUserName2( event ) );
        apiTestVariableEntity.setGmtModified( event.getOccurred() );
        apiTestVariableEntity.setVariableCode( event.getVariableCode() );
        apiTestVariableEntity.setVariableStatus( event.getVariableStatus() );

        return apiTestVariableEntity;
    }

    @Override
    public ApiTestVariableEntity converter(DeleteApiTestVariableEvent event) {
        if ( event == null ) {
            return null;
        }

        ApiTestVariableEntity apiTestVariableEntity = new ApiTestVariableEntity();

        apiTestVariableEntity.setModifierId( eventTransactorUserId3( event ) );
        apiTestVariableEntity.setModifier( eventTransactorUserName3( event ) );
        apiTestVariableEntity.setGmtModified( event.getOccurred() );
        apiTestVariableEntity.setVariableCode( event.getVariableCode() );

        return apiTestVariableEntity;
    }

    @Override
    public List<ApiTestVariableVO> converterToList(List<ApiTestVariableEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiTestVariableVO> list = new ArrayList<ApiTestVariableVO>( entityList.size() );
        for ( ApiTestVariableEntity apiTestVariableEntity : entityList ) {
            list.add( converter( apiTestVariableEntity ) );
        }

        return list;
    }

    @Override
    public ApiTestEntityDO convertApiTestEntityDO(ApiTestEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ApiTestEntityDO apiTestEntityDO = new ApiTestEntityDO();

        apiTestEntityDO.setApiCode( entity.getApiCode() );
        apiTestEntityDO.setApiName( entity.getApiName() );
        apiTestEntityDO.setProductCode( entity.getProductCode() );
        apiTestEntityDO.setAppId( entity.getAppId() );
        apiTestEntityDO.setApiType( entity.getApiType() );
        apiTestEntityDO.setReqMethod( entity.getReqMethod() );
        apiTestEntityDO.setApiAddress( entity.getApiAddress() );
        apiTestEntityDO.setApiDesc( entity.getApiDesc() );
        apiTestEntityDO.setDocId( entity.getDocId() );
        apiTestEntityDO.setDocVersion( entity.getDocVersion() );
        apiTestEntityDO.setApiData( entity.getApiData() );
        apiTestEntityDO.setEnable( entity.getEnable() );
        apiTestEntityDO.setCreatorId( entity.getCreatorId() );
        apiTestEntityDO.setCreator( entity.getCreator() );
        apiTestEntityDO.setGmtCreate( entity.getGmtCreate() );
        apiTestEntityDO.setModifierId( entity.getModifierId() );
        apiTestEntityDO.setModifier( entity.getModifier() );
        apiTestEntityDO.setGmtModified( entity.getGmtModified() );
        apiTestEntityDO.setDocProductCode( entity.getDocProductCode() );
        apiTestEntityDO.setApiTestIndex( entity.getApiTestIndex() );
        apiTestEntityDO.setMainApiCode( entity.getMainApiCode() );
        apiTestEntityDO.setTagValue( entity.getTagValue() );
        apiTestEntityDO.setModifyFlag( entity.getModifyFlag() );
        apiTestEntityDO.setRelatedSceneFlag( entity.getRelatedSceneFlag() );
        apiTestEntityDO.setGatewayApiInfo( entity.getGatewayApiInfo() );

        return apiTestEntityDO;
    }

    @Override
    public List<ApiTestEntityDO> convertApiTestEntityDO(List<ApiTestEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiTestEntityDO> list1 = new ArrayList<ApiTestEntityDO>( list.size() );
        for ( ApiTestEntity apiTestEntity : list ) {
            list1.add( convertApiTestEntityDO( apiTestEntity ) );
        }

        return list1;
    }

    @Override
    public ApiTestEntity convertApiTestEntity(ApiTestEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        ApiTestEntity apiTestEntity = new ApiTestEntity();

        apiTestEntity.setApiCode( entityDO.getApiCode() );
        apiTestEntity.setApiName( entityDO.getApiName() );
        apiTestEntity.setProductCode( entityDO.getProductCode() );
        apiTestEntity.setAppId( entityDO.getAppId() );
        apiTestEntity.setApiType( entityDO.getApiType() );
        apiTestEntity.setReqMethod( entityDO.getReqMethod() );
        apiTestEntity.setApiAddress( entityDO.getApiAddress() );
        apiTestEntity.setApiDesc( entityDO.getApiDesc() );
        apiTestEntity.setDocId( entityDO.getDocId() );
        apiTestEntity.setDocVersion( entityDO.getDocVersion() );
        apiTestEntity.setApiData( entityDO.getApiData() );
        apiTestEntity.setEnable( entityDO.getEnable() );
        apiTestEntity.setCreatorId( entityDO.getCreatorId() );
        apiTestEntity.setCreator( entityDO.getCreator() );
        apiTestEntity.setGmtCreate( entityDO.getGmtCreate() );
        apiTestEntity.setModifierId( entityDO.getModifierId() );
        apiTestEntity.setModifier( entityDO.getModifier() );
        apiTestEntity.setGmtModified( entityDO.getGmtModified() );
        apiTestEntity.setDocProductCode( entityDO.getDocProductCode() );
        apiTestEntity.setApiTestIndex( entityDO.getApiTestIndex() );
        apiTestEntity.setMainApiCode( entityDO.getMainApiCode() );
        apiTestEntity.setTagValue( entityDO.getTagValue() );
        apiTestEntity.setModifyFlag( entityDO.getModifyFlag() );
        apiTestEntity.setRelatedSceneFlag( entityDO.getRelatedSceneFlag() );
        apiTestEntity.setGatewayApiInfo( entityDO.getGatewayApiInfo() );

        return apiTestEntity;
    }

    @Override
    public List<ApiTestEntity> convertApiTestEntity(List<ApiTestEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiTestEntity> list1 = new ArrayList<ApiTestEntity>( list.size() );
        for ( ApiTestEntityDO apiTestEntityDO : list ) {
            list1.add( convertApiTestEntity( apiTestEntityDO ) );
        }

        return list1;
    }

    @Override
    public ApiCaseEntity convertApiCaseEntity(ApiCaseEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        ApiCaseEntity apiCaseEntity = new ApiCaseEntity();

        apiCaseEntity.setCaseCode( entityDO.getCaseCode() );
        apiCaseEntity.setCaseName( entityDO.getCaseName() );
        apiCaseEntity.setApiCode( entityDO.getApiCode() );
        apiCaseEntity.setNodeCode( entityDO.getNodeCode() );
        apiCaseEntity.setSourceType( entityDO.getSourceType() );
        apiCaseEntity.setParentCode( entityDO.getParentCode() );
        apiCaseEntity.setPriority( entityDO.getPriority() );
        apiCaseEntity.setReqData( entityDO.getReqData() );
        apiCaseEntity.setAsserts( entityDO.getAsserts() );
        apiCaseEntity.setUserVariableCode( entityDO.getUserVariableCode() );
        apiCaseEntity.setProductCode( entityDO.getProductCode() );
        apiCaseEntity.setEnable( entityDO.getEnable() );
        apiCaseEntity.setCreatorId( entityDO.getCreatorId() );
        apiCaseEntity.setCreator( entityDO.getCreator() );
        apiCaseEntity.setGmtCreate( entityDO.getGmtCreate() );
        apiCaseEntity.setModifierId( entityDO.getModifierId() );
        apiCaseEntity.setModifier( entityDO.getModifier() );
        apiCaseEntity.setGmtModified( entityDO.getGmtModified() );
        apiCaseEntity.setSceneCode( entityDO.getSceneCode() );

        return apiCaseEntity;
    }

    @Override
    public List<ApiCaseEntity> convertApiCaseEntity(List<ApiCaseEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiCaseEntity> list1 = new ArrayList<ApiCaseEntity>( list.size() );
        for ( ApiCaseEntityDO apiCaseEntityDO : list ) {
            list1.add( convertApiCaseEntity( apiCaseEntityDO ) );
        }

        return list1;
    }

    @Override
    public ApiCaseEntityDO convertApiCaseEntityDO(ApiCaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ApiCaseEntityDO apiCaseEntityDO = new ApiCaseEntityDO();

        apiCaseEntityDO.setCaseCode( entity.getCaseCode() );
        apiCaseEntityDO.setCaseName( entity.getCaseName() );
        apiCaseEntityDO.setApiCode( entity.getApiCode() );
        apiCaseEntityDO.setNodeCode( entity.getNodeCode() );
        apiCaseEntityDO.setSourceType( entity.getSourceType() );
        apiCaseEntityDO.setParentCode( entity.getParentCode() );
        apiCaseEntityDO.setPriority( entity.getPriority() );
        apiCaseEntityDO.setReqData( entity.getReqData() );
        apiCaseEntityDO.setAsserts( entity.getAsserts() );
        apiCaseEntityDO.setUserVariableCode( entity.getUserVariableCode() );
        apiCaseEntityDO.setProductCode( entity.getProductCode() );
        apiCaseEntityDO.setEnable( entity.getEnable() );
        apiCaseEntityDO.setCreatorId( entity.getCreatorId() );
        apiCaseEntityDO.setCreator( entity.getCreator() );
        apiCaseEntityDO.setGmtCreate( entity.getGmtCreate() );
        apiCaseEntityDO.setModifierId( entity.getModifierId() );
        apiCaseEntityDO.setModifier( entity.getModifier() );
        apiCaseEntityDO.setGmtModified( entity.getGmtModified() );
        apiCaseEntityDO.setSceneCode( entity.getSceneCode() );

        return apiCaseEntityDO;
    }

    @Override
    public List<ApiCaseEntityDO> convertApiCaseEntityDO(List<ApiCaseEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiCaseEntityDO> list1 = new ArrayList<ApiCaseEntityDO>( list.size() );
        for ( ApiCaseEntity apiCaseEntity : list ) {
            list1.add( convertApiCaseEntityDO( apiCaseEntity ) );
        }

        return list1;
    }

    @Override
    public ApiVO convertApiVO(ApiTestEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ApiVO apiVO = new ApiVO();

        apiVO.setApiCode( entity.getMainApiCode() );
        apiVO.setTagName( entity.getTagValue() );
        apiVO.setAppId( entity.getAppId() );
        apiVO.setApiName( entity.getApiName() );
        apiVO.setApiAddress( entity.getApiAddress() );
        apiVO.setReqMethod( entity.getReqMethod() );
        apiVO.setDocVersion( entity.getDocVersion() );
        apiVO.setApiDesc( entity.getApiDesc() );
        apiVO.setModifierId( entity.getModifierId() );
        apiVO.setModifier( entity.getModifier() );
        apiVO.setGmtModified( entity.getGmtModified() );
        apiVO.setDocProductCode( entity.getDocProductCode() );
        apiVO.setDocId( entity.getDocId() );
        apiVO.setApiType( entity.getApiType() );
        apiVO.setEnable( entity.getEnable() );

        apiVO.setSceneRelated( entity.getRelatedSceneFlag() == 1 );

        return apiVO;
    }

    @Override
    public List<ApiVO> convertApiVO(List<ApiTestEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiVO> list = new ArrayList<ApiVO>( entityList.size() );
        for ( ApiTestEntity apiTestEntity : entityList ) {
            list.add( convertApiVO( apiTestEntity ) );
        }

        return list;
    }

    @Override
    public List<ApiLiteInfoVO> convertLite(List<ApiTestEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiLiteInfoVO> list = new ArrayList<ApiLiteInfoVO>( entityList.size() );
        for ( ApiTestEntity apiTestEntity : entityList ) {
            list.add( apiTestEntityToApiLiteInfoVO( apiTestEntity ) );
        }

        return list;
    }

    @Override
    public SceneInfoEntity convertSceneInfoEntity(SceneInfoEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneInfoEntity sceneInfoEntity = new SceneInfoEntity();

        sceneInfoEntity.setId( entityDO.getId() );
        sceneInfoEntity.setSceneCode( entityDO.getSceneCode() );
        sceneInfoEntity.setSceneName( entityDO.getSceneName() );
        sceneInfoEntity.setProductCode( entityDO.getProductCode() );
        sceneInfoEntity.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        sceneInfoEntity.setSceneVersion( entityDO.getSceneVersion() );
        sceneInfoEntity.setSceneInfoDesc( entityDO.getSceneInfoDesc() );
        sceneInfoEntity.setSceneOssPath( entityDO.getSceneOssPath() );
        sceneInfoEntity.setSceneOssFile( entityDO.getSceneOssFile() );
        sceneInfoEntity.setSceneBackDataMd5( entityDO.getSceneBackDataMd5() );
        sceneInfoEntity.setStatus( entityDO.getStatus() );
        sceneInfoEntity.setStepRecord( entityDO.getStepRecord() );
        sceneInfoEntity.setEnable( entityDO.getEnable() );
        sceneInfoEntity.setSceneType( entityDO.getSceneType() );
        sceneInfoEntity.setCreatorId( entityDO.getCreatorId() );
        sceneInfoEntity.setCreator( entityDO.getCreator() );
        sceneInfoEntity.setGmtCreate( entityDO.getGmtCreate() );
        sceneInfoEntity.setModifierId( entityDO.getModifierId() );
        sceneInfoEntity.setModifier( entityDO.getModifier() );
        sceneInfoEntity.setGmtModified( entityDO.getGmtModified() );
        sceneInfoEntity.setSceneFrontData( entityDO.getSceneFrontData() );
        sceneInfoEntity.setSceneBackData( entityDO.getSceneBackData() );
        sceneInfoEntity.setShareStatus( entityDO.getShareStatus() );
        sceneInfoEntity.setIsCollect( entityDO.getIsCollect() );
        sceneInfoEntity.setSceneTagData( entityDO.getSceneTagData() );

        return sceneInfoEntity;
    }

    @Override
    public SceneIndexEntity convertSceneIndexEntity(SceneIndexEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneIndexEntity sceneIndexEntity = new SceneIndexEntity();

        sceneIndexEntity.setId( entityDO.getId() );
        sceneIndexEntity.setSceneIndexCode( entityDO.getSceneIndexCode() );
        sceneIndexEntity.setSceneIndexName( entityDO.getSceneIndexName() );
        sceneIndexEntity.setProductCode( entityDO.getProductCode() );
        sceneIndexEntity.setParentCode( entityDO.getParentCode() );
        sceneIndexEntity.setSceneIndexType( entityDO.getSceneIndexType() );
        sceneIndexEntity.setSceneType( entityDO.getSceneType() );
        sceneIndexEntity.setEnable( entityDO.getEnable() );
        sceneIndexEntity.setCreatorId( entityDO.getCreatorId() );
        sceneIndexEntity.setCreator( entityDO.getCreator() );
        sceneIndexEntity.setGmtCreate( entityDO.getGmtCreate() );
        sceneIndexEntity.setModifierId( entityDO.getModifierId() );
        sceneIndexEntity.setModifier( entityDO.getModifier() );
        sceneIndexEntity.setGmtModified( entityDO.getGmtModified() );
        sceneIndexEntity.setTestcaseCode( entityDO.getTestcaseCode() );
        sceneIndexEntity.setIsCollect( entityDO.getIsCollect() );

        return sceneIndexEntity;
    }

    @Override
    public SceneInfoEntityDO convertSceneInfoEntityDO(SceneInfoEntity entity) {
        if ( entity == null ) {
            return null;
        }

        SceneInfoEntityDO sceneInfoEntityDO = new SceneInfoEntityDO();

        sceneInfoEntityDO.setId( entity.getId() );
        sceneInfoEntityDO.setSceneCode( entity.getSceneCode() );
        sceneInfoEntityDO.setSceneName( entity.getSceneName() );
        sceneInfoEntityDO.setProductCode( entity.getProductCode() );
        sceneInfoEntityDO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        sceneInfoEntityDO.setSceneVersion( entity.getSceneVersion() );
        sceneInfoEntityDO.setSceneInfoDesc( entity.getSceneInfoDesc() );
        sceneInfoEntityDO.setSceneOssPath( entity.getSceneOssPath() );
        sceneInfoEntityDO.setSceneOssFile( entity.getSceneOssFile() );
        sceneInfoEntityDO.setSceneBackDataMd5( entity.getSceneBackDataMd5() );
        sceneInfoEntityDO.setStatus( entity.getStatus() );
        sceneInfoEntityDO.setEnable( entity.getEnable() );
        sceneInfoEntityDO.setSceneType( entity.getSceneType() );
        sceneInfoEntityDO.setStepRecord( entity.getStepRecord() );
        sceneInfoEntityDO.setCreatorId( entity.getCreatorId() );
        sceneInfoEntityDO.setCreator( entity.getCreator() );
        sceneInfoEntityDO.setGmtCreate( entity.getGmtCreate() );
        sceneInfoEntityDO.setModifierId( entity.getModifierId() );
        sceneInfoEntityDO.setModifier( entity.getModifier() );
        sceneInfoEntityDO.setGmtModified( entity.getGmtModified() );
        sceneInfoEntityDO.setSceneFrontData( entity.getSceneFrontData() );
        sceneInfoEntityDO.setSceneBackData( entity.getSceneBackData() );
        sceneInfoEntityDO.setShareStatus( entity.getShareStatus() );
        sceneInfoEntityDO.setIsCollect( entity.getIsCollect() );
        sceneInfoEntityDO.setSceneTagData( entity.getSceneTagData() );

        return sceneInfoEntityDO;
    }

    @Override
    public List<SceneInfoEntityDO> converterToSceneInfoEntityList(List<SceneInfoEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SceneInfoEntityDO> list = new ArrayList<SceneInfoEntityDO>( entityList.size() );
        for ( SceneInfoEntity sceneInfoEntity : entityList ) {
            list.add( convertSceneInfoEntityDO( sceneInfoEntity ) );
        }

        return list;
    }

    @Override
    public List<SceneIndexEntity> converterVOList(List<SceneIndexVO> list) {
        if ( list == null ) {
            return null;
        }

        List<SceneIndexEntity> list1 = new ArrayList<SceneIndexEntity>( list.size() );
        for ( SceneIndexVO sceneIndexVO : list ) {
            list1.add( converterVO( sceneIndexVO ) );
        }

        return list1;
    }

    @Override
    public SceneIndexEntity converterVO(SceneIndexVO vo) {
        if ( vo == null ) {
            return null;
        }

        SceneIndexEntity sceneIndexEntity = new SceneIndexEntity();

        sceneIndexEntity.setId( vo.getId() );
        sceneIndexEntity.setSceneIndexCode( vo.getSceneIndexCode() );
        sceneIndexEntity.setSceneIndexName( vo.getSceneIndexName() );
        sceneIndexEntity.setProductCode( vo.getProductCode() );
        sceneIndexEntity.setParentCode( vo.getParentCode() );
        sceneIndexEntity.setSceneIndexType( vo.getSceneIndexType() );
        sceneIndexEntity.setSceneType( vo.getSceneType() );
        sceneIndexEntity.setEnable( vo.getEnable() );
        sceneIndexEntity.setCreatorId( vo.getCreatorId() );
        sceneIndexEntity.setCreator( vo.getCreator() );
        sceneIndexEntity.setGmtCreate( vo.getGmtCreate() );
        sceneIndexEntity.setModifierId( vo.getModifierId() );
        sceneIndexEntity.setModifier( vo.getModifier() );
        sceneIndexEntity.setGmtModified( vo.getGmtModified() );
        sceneIndexEntity.setTestcaseCode( vo.getTestcaseCode() );
        sceneIndexEntity.setIsCollect( vo.getIsCollect() );

        return sceneIndexEntity;
    }

    @Override
    public SceneIndexVO converter(SceneIndexEntity entity) {
        if ( entity == null ) {
            return null;
        }

        SceneIndexVO sceneIndexVO = new SceneIndexVO();

        sceneIndexVO.setId( entity.getId() );
        sceneIndexVO.setSceneIndexCode( entity.getSceneIndexCode() );
        sceneIndexVO.setSceneIndexName( entity.getSceneIndexName() );
        sceneIndexVO.setProductCode( entity.getProductCode() );
        sceneIndexVO.setParentCode( entity.getParentCode() );
        sceneIndexVO.setSceneIndexType( entity.getSceneIndexType() );
        sceneIndexVO.setSceneType( entity.getSceneType() );
        sceneIndexVO.setTestcaseCode( entity.getTestcaseCode() );
        sceneIndexVO.setEnable( entity.getEnable() );
        sceneIndexVO.setCreatorId( entity.getCreatorId() );
        sceneIndexVO.setCreator( entity.getCreator() );
        sceneIndexVO.setGmtCreate( entity.getGmtCreate() );
        sceneIndexVO.setModifierId( entity.getModifierId() );
        sceneIndexVO.setModifier( entity.getModifier() );
        sceneIndexVO.setGmtModified( entity.getGmtModified() );
        sceneIndexVO.setIsCollect( entity.getIsCollect() );

        return sceneIndexVO;
    }

    @Override
    public List<SceneIndexVO> converterList(List<SceneIndexEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<SceneIndexVO> list1 = new ArrayList<SceneIndexVO>( list.size() );
        for ( SceneIndexEntity sceneIndexEntity : list ) {
            list1.add( converter( sceneIndexEntity ) );
        }

        return list1;
    }

    @Override
    public List<ApiTestVariableVO> converter(List<ApiTestVariableEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiTestVariableVO> list1 = new ArrayList<ApiTestVariableVO>( list.size() );
        for ( ApiTestVariableEntity apiTestVariableEntity : list ) {
            list1.add( converter( apiTestVariableEntity ) );
        }

        return list1;
    }

    @Override
    public List<ApiTestVariableEntity> convertVOList(List<ApiTestVariableVO> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiTestVariableEntity> list = new ArrayList<ApiTestVariableEntity>( entityList.size() );
        for ( ApiTestVariableVO apiTestVariableVO : entityList ) {
            list.add( convert( apiTestVariableVO ) );
        }

        return list;
    }

    @Override
    public ApiTestVariableEntity convert(ApiTestVariableVO vo) {
        if ( vo == null ) {
            return null;
        }

        ApiTestVariableEntity apiTestVariableEntity = new ApiTestVariableEntity();

        apiTestVariableEntity.setEnable( vo.getEnable() );
        apiTestVariableEntity.setCreatorId( vo.getCreatorId() );
        apiTestVariableEntity.setCreator( vo.getCreator() );
        apiTestVariableEntity.setGmtCreate( vo.getGmtCreate() );
        apiTestVariableEntity.setModifierId( vo.getModifierId() );
        apiTestVariableEntity.setModifier( vo.getModifier() );
        apiTestVariableEntity.setGmtModified( vo.getGmtModified() );
        apiTestVariableEntity.setId( vo.getId() );
        apiTestVariableEntity.setProductCode( vo.getProductCode() );
        apiTestVariableEntity.setProductName( vo.getProductName() );
        apiTestVariableEntity.setLinkCode( vo.getLinkCode() );
        apiTestVariableEntity.setVariableCode( vo.getVariableCode() );
        apiTestVariableEntity.setVariableName( vo.getVariableName() );
        apiTestVariableEntity.setVariableKey( vo.getVariableKey() );
        apiTestVariableEntity.setVariableValue( vo.getVariableValue() );
        apiTestVariableEntity.setType( vo.getType() );
        apiTestVariableEntity.setSceneType( vo.getSceneType() );
        apiTestVariableEntity.setUsageType( vo.getUsageType() );
        apiTestVariableEntity.setRequiredStatus( vo.getRequiredStatus() );
        apiTestVariableEntity.setVariableDesc( vo.getVariableDesc() );
        apiTestVariableEntity.setSubVariableType( vo.getSubVariableType() );
        apiTestVariableEntity.setLoginValidTime( vo.getLoginValidTime() );

        apiTestVariableEntity.setVariableStatus( java.lang.Boolean.valueOf(vo.getVariableStatus()) );

        return apiTestVariableEntity;
    }

    @Override
    public SceneApiRelationEntity convert(SceneApiRelationEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneApiRelationEntity sceneApiRelationEntity = new SceneApiRelationEntity();

        sceneApiRelationEntity.setEnable( entityDO.getEnable() );
        sceneApiRelationEntity.setCreatorId( entityDO.getCreatorId() );
        sceneApiRelationEntity.setCreator( entityDO.getCreator() );
        sceneApiRelationEntity.setGmtCreate( entityDO.getGmtCreate() );
        sceneApiRelationEntity.setModifierId( entityDO.getModifierId() );
        sceneApiRelationEntity.setModifier( entityDO.getModifier() );
        sceneApiRelationEntity.setGmtModified( entityDO.getGmtModified() );
        sceneApiRelationEntity.setId( entityDO.getId() );
        sceneApiRelationEntity.setSceneCode( entityDO.getSceneCode() );
        sceneApiRelationEntity.setProductCode( entityDO.getProductCode() );
        sceneApiRelationEntity.setAppId( entityDO.getAppId() );
        sceneApiRelationEntity.setApiType( entityDO.getApiType() );
        sceneApiRelationEntity.setApiAddress( entityDO.getApiAddress() );
        sceneApiRelationEntity.setApiTestIndex( entityDO.getApiTestIndex() );
        sceneApiRelationEntity.setRelatedApiName( entityDO.getRelatedApiName() );
        sceneApiRelationEntity.setMainApiCode( entityDO.getMainApiCode() );

        return sceneApiRelationEntity;
    }

    @Override
    public SceneDatabaseAuthorizeEntity convert(SceneDatabaseAuthorizeEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneDatabaseAuthorizeEntity sceneDatabaseAuthorizeEntity = new SceneDatabaseAuthorizeEntity();

        sceneDatabaseAuthorizeEntity.setId( entityDO.getId() );
        sceneDatabaseAuthorizeEntity.setAuthorizeCode( entityDO.getAuthorizeCode() );
        sceneDatabaseAuthorizeEntity.setProductCode( entityDO.getProductCode() );
        sceneDatabaseAuthorizeEntity.setAuthorizeProductCode( entityDO.getAuthorizeProductCode() );
        sceneDatabaseAuthorizeEntity.setAuthorizeProductName( entityDO.getAuthorizeProductName() );
        sceneDatabaseAuthorizeEntity.setDbId( entityDO.getDbId() );
        sceneDatabaseAuthorizeEntity.setDbName( entityDO.getDbName() );
        sceneDatabaseAuthorizeEntity.setPhysicDbName( entityDO.getPhysicDbName() );
        sceneDatabaseAuthorizeEntity.setEnable( entityDO.getEnable() );
        sceneDatabaseAuthorizeEntity.setCreatorId( entityDO.getCreatorId() );
        sceneDatabaseAuthorizeEntity.setCreator( entityDO.getCreator() );
        sceneDatabaseAuthorizeEntity.setGmtCreate( entityDO.getGmtCreate() );
        sceneDatabaseAuthorizeEntity.setModifierId( entityDO.getModifierId() );
        sceneDatabaseAuthorizeEntity.setModifier( entityDO.getModifier() );
        sceneDatabaseAuthorizeEntity.setGmtModified( entityDO.getGmtModified() );

        return sceneDatabaseAuthorizeEntity;
    }

    @Override
    public List<SceneDatabaseAuthorizeEntityDO> convertList(List<SceneDatabaseAuthorizeEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SceneDatabaseAuthorizeEntityDO> list = new ArrayList<SceneDatabaseAuthorizeEntityDO>( entityList.size() );
        for ( SceneDatabaseAuthorizeEntity sceneDatabaseAuthorizeEntity : entityList ) {
            list.add( sceneDatabaseAuthorizeEntityToSceneDatabaseAuthorizeEntityDO( sceneDatabaseAuthorizeEntity ) );
        }

        return list;
    }

    @Override
    public ApiTestCaseEntity converter(AddApiTestCaseEvent event) {
        if ( event == null ) {
            return null;
        }

        ApiTestCaseEntity apiTestCaseEntity = new ApiTestCaseEntity();

        apiTestCaseEntity.setCreatorId( eventTransactorUserId4( event ) );
        apiTestCaseEntity.setCreator( eventTransactorUserName4( event ) );
        apiTestCaseEntity.setModifierId( eventTransactorUserId4( event ) );
        apiTestCaseEntity.setModifier( eventTransactorUserName4( event ) );
        apiTestCaseEntity.setGmtModified( event.getOccurred() );
        apiTestCaseEntity.setCaseCode( event.getCaseCode() );
        apiTestCaseEntity.setCaseName( event.getCaseName() );
        apiTestCaseEntity.setProductCode( event.getProductCode() );
        apiTestCaseEntity.setApiCode( event.getApiCode() );
        apiTestCaseEntity.setStatus( event.getStatus() );
        apiTestCaseEntity.setCaseType( event.getCaseType() );
        apiTestCaseEntity.setCaseReqData( event.getCaseReqData() );
        apiTestCaseEntity.setLatestTaskId( event.getLatestTaskId() );
        apiTestCaseEntity.setRelatedApiAddress( event.getRelatedApiAddress() );
        apiTestCaseEntity.setRelatedApiName( event.getRelatedApiName() );
        apiTestCaseEntity.setApiType( event.getApiType() );
        apiTestCaseEntity.setDocVersion( event.getDocVersion() );
        apiTestCaseEntity.setPublished( event.getPublished() );
        apiTestCaseEntity.setEnable( event.getEnable() );
        apiTestCaseEntity.setAutomaticSourceCode( event.getAutomaticSourceCode() );

        return apiTestCaseEntity;
    }

    @Override
    public List<TmApiTestCaseVO> converterToTmApiTestCaseVOList(List<ApiTestCaseEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<TmApiTestCaseVO> list1 = new ArrayList<TmApiTestCaseVO>( list.size() );
        for ( ApiTestCaseEntity apiTestCaseEntity : list ) {
            list1.add( converterToTmApiTestCaseVO( apiTestCaseEntity ) );
        }

        return list1;
    }

    @Override
    public TmApiTestCaseVO converterToTmApiTestCaseVO(ApiTestCaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TmApiTestCaseVO tmApiTestCaseVO = new TmApiTestCaseVO();

        tmApiTestCaseVO.setProductCode( entity.getProductCode() );
        tmApiTestCaseVO.setCaseCode( entity.getCaseCode() );
        tmApiTestCaseVO.setCaseName( entity.getCaseName() );
        tmApiTestCaseVO.setApiCode( entity.getApiCode() );
        tmApiTestCaseVO.setStatus( entity.getStatus() );
        tmApiTestCaseVO.setCaseType( entity.getCaseType() );
        tmApiTestCaseVO.setCaseReqData( entity.getCaseReqData() );
        tmApiTestCaseVO.setParentCaseCode( entity.getParentCaseCode() );
        tmApiTestCaseVO.setLatestTaskId( entity.getLatestTaskId() );
        tmApiTestCaseVO.setLatestExecuteResult( entity.getLatestExecuteResult() );
        tmApiTestCaseVO.setEnable( entity.getEnable() );
        tmApiTestCaseVO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );

        return tmApiTestCaseVO;
    }

    @Override
    public ApiTestCaseEntity converterEventToVo(EditApiTestCaseEvent event) {
        if ( event == null ) {
            return null;
        }

        ApiTestCaseEntity apiTestCaseEntity = new ApiTestCaseEntity();

        apiTestCaseEntity.setCaseCode( event.getCaseCode() );
        apiTestCaseEntity.setCaseName( event.getCaseName() );
        apiTestCaseEntity.setProductCode( event.getProductCode() );
        apiTestCaseEntity.setApiCode( event.getApiCode() );
        apiTestCaseEntity.setStatus( event.getStatus() );
        apiTestCaseEntity.setCaseType( event.getCaseType() );
        apiTestCaseEntity.setCaseReqData( event.getCaseReqData() );
        apiTestCaseEntity.setRelatedApiAddress( event.getRelatedApiAddress() );
        apiTestCaseEntity.setRelatedApiName( event.getRelatedApiName() );
        apiTestCaseEntity.setApiType( event.getApiType() );
        apiTestCaseEntity.setDocVersion( event.getDocVersion() );
        apiTestCaseEntity.setPublished( event.getPublished() );
        apiTestCaseEntity.setEnable( event.getEnable() );
        apiTestCaseEntity.setAutomaticSourceCode( event.getAutomaticSourceCode() );

        return apiTestCaseEntity;
    }

    @Override
    public ApiTestCaseEntityDO convertApiTestCaseEntityDO(ApiTestCaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ApiTestCaseEntityDO apiTestCaseEntityDO = new ApiTestCaseEntityDO();

        apiTestCaseEntityDO.setId( entity.getId() );
        apiTestCaseEntityDO.setCaseCode( entity.getCaseCode() );
        apiTestCaseEntityDO.setCaseName( entity.getCaseName() );
        apiTestCaseEntityDO.setProductCode( entity.getProductCode() );
        apiTestCaseEntityDO.setApiCode( entity.getApiCode() );
        apiTestCaseEntityDO.setStatus( entity.getStatus() );
        apiTestCaseEntityDO.setCaseType( entity.getCaseType() );
        apiTestCaseEntityDO.setCaseReqData( entity.getCaseReqData() );
        apiTestCaseEntityDO.setParentCaseCode( entity.getParentCaseCode() );
        apiTestCaseEntityDO.setLatestTaskId( entity.getLatestTaskId() );
        apiTestCaseEntityDO.setLatestExecuteResult( entity.getLatestExecuteResult() );
        apiTestCaseEntityDO.setEnable( entity.getEnable() );
        apiTestCaseEntityDO.setCreatorId( entity.getCreatorId() );
        apiTestCaseEntityDO.setCreator( entity.getCreator() );
        apiTestCaseEntityDO.setGmtCreate( entity.getGmtCreate() );
        apiTestCaseEntityDO.setModifierId( entity.getModifierId() );
        apiTestCaseEntityDO.setModifier( entity.getModifier() );
        apiTestCaseEntityDO.setGmtModified( entity.getGmtModified() );
        apiTestCaseEntityDO.setInitStatus( entity.getInitStatus() );
        apiTestCaseEntityDO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        apiTestCaseEntityDO.setLatestExecuteTime( entity.getLatestExecuteTime() );
        apiTestCaseEntityDO.setRelatedApiAddress( entity.getRelatedApiAddress() );
        apiTestCaseEntityDO.setRelatedApiName( entity.getRelatedApiName() );
        apiTestCaseEntityDO.setApiType( entity.getApiType() );
        apiTestCaseEntityDO.setDocVersion( entity.getDocVersion() );
        apiTestCaseEntityDO.setTagValue( entity.getTagValue() );
        apiTestCaseEntityDO.setGenerateFieldSource( entity.getGenerateFieldSource() );
        apiTestCaseEntityDO.setGenerateRules( entity.getGenerateRules() );

        return apiTestCaseEntityDO;
    }

    @Override
    public List<ApiTestCaseEntityDO> convertApiTestCaseEntityDO(List<ApiTestCaseEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiTestCaseEntityDO> list1 = new ArrayList<ApiTestCaseEntityDO>( list.size() );
        for ( ApiTestCaseEntity apiTestCaseEntity : list ) {
            list1.add( convertApiTestCaseEntityDO( apiTestCaseEntity ) );
        }

        return list1;
    }

    @Override
    public PageApiTestCaseVO convertPageApiTestCaseVO(ApiTestCaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        PageApiTestCaseVO pageApiTestCaseVO = new PageApiTestCaseVO();

        pageApiTestCaseVO.setApiName( entity.getRelatedApiName() );
        pageApiTestCaseVO.setApiAddress( entity.getRelatedApiAddress() );
        pageApiTestCaseVO.setCaseTypeNum( entity.getCaseType() );
        pageApiTestCaseVO.setTaskCode( entity.getLatestTaskId() );
        pageApiTestCaseVO.setExecuteTime( entity.getLatestExecuteTime() );
        pageApiTestCaseVO.setTestResult( entity.getLatestExecuteResult() );
        pageApiTestCaseVO.setKey( entity.getGenerateFieldSource() );
        pageApiTestCaseVO.setApiConfigTypeEnum( entity.getGenerateRules() );
        pageApiTestCaseVO.setCaseCode( entity.getCaseCode() );
        pageApiTestCaseVO.setCaseName( entity.getCaseName() );
        pageApiTestCaseVO.setApiCode( entity.getApiCode() );
        pageApiTestCaseVO.setStatus( entity.getStatus() );
        pageApiTestCaseVO.setEnable( entity.getEnable() );
        pageApiTestCaseVO.setDocVersion( entity.getDocVersion() );
        pageApiTestCaseVO.setTagValue( entity.getTagValue() );
        if ( entity.getApiType() != null ) {
            pageApiTestCaseVO.setApiType( entity.getApiType().name() );
        }

        return pageApiTestCaseVO;
    }

    @Override
    public List<PageApiTestCaseVO> convertPageApiTestCaseVOList(List<ApiTestCaseEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<PageApiTestCaseVO> list1 = new ArrayList<PageApiTestCaseVO>( list.size() );
        for ( ApiTestCaseEntity apiTestCaseEntity : list ) {
            list1.add( convertPageApiTestCaseVO( apiTestCaseEntity ) );
        }

        return list1;
    }

    @Override
    public ApiTestCaseEntity convertApiTestCaseEntity(ApiTestCaseEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        ApiTestCaseEntity apiTestCaseEntity = new ApiTestCaseEntity();

        apiTestCaseEntity.setId( entityDO.getId() );
        apiTestCaseEntity.setCaseCode( entityDO.getCaseCode() );
        apiTestCaseEntity.setCaseName( entityDO.getCaseName() );
        apiTestCaseEntity.setProductCode( entityDO.getProductCode() );
        apiTestCaseEntity.setApiCode( entityDO.getApiCode() );
        apiTestCaseEntity.setStatus( entityDO.getStatus() );
        apiTestCaseEntity.setCaseType( entityDO.getCaseType() );
        apiTestCaseEntity.setCaseReqData( entityDO.getCaseReqData() );
        apiTestCaseEntity.setParentCaseCode( entityDO.getParentCaseCode() );
        apiTestCaseEntity.setLatestTaskId( entityDO.getLatestTaskId() );
        apiTestCaseEntity.setLatestExecuteTime( entityDO.getLatestExecuteTime() );
        apiTestCaseEntity.setLatestExecuteResult( entityDO.getLatestExecuteResult() );
        apiTestCaseEntity.setRelatedApiAddress( entityDO.getRelatedApiAddress() );
        apiTestCaseEntity.setRelatedApiName( entityDO.getRelatedApiName() );
        apiTestCaseEntity.setApiType( entityDO.getApiType() );
        apiTestCaseEntity.setDocVersion( entityDO.getDocVersion() );
        apiTestCaseEntity.setTagValue( entityDO.getTagValue() );
        apiTestCaseEntity.setGenerateFieldSource( entityDO.getGenerateFieldSource() );
        apiTestCaseEntity.setGenerateRules( entityDO.getGenerateRules() );
        apiTestCaseEntity.setEnable( entityDO.getEnable() );
        apiTestCaseEntity.setCreatorId( entityDO.getCreatorId() );
        apiTestCaseEntity.setCreator( entityDO.getCreator() );
        apiTestCaseEntity.setGmtCreate( entityDO.getGmtCreate() );
        apiTestCaseEntity.setModifierId( entityDO.getModifierId() );
        apiTestCaseEntity.setModifier( entityDO.getModifier() );
        apiTestCaseEntity.setGmtModified( entityDO.getGmtModified() );
        apiTestCaseEntity.setInitStatus( entityDO.getInitStatus() );
        apiTestCaseEntity.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );

        return apiTestCaseEntity;
    }

    @Override
    public List<ApiTestCaseEntity> convertApiTestCaseEntity(List<ApiTestCaseEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiTestCaseEntity> list1 = new ArrayList<ApiTestCaseEntity>( list.size() );
        for ( ApiTestCaseEntityDO apiTestCaseEntityDO : list ) {
            list1.add( convertApiTestCaseEntity( apiTestCaseEntityDO ) );
        }

        return list1;
    }

    @Override
    public List<ApiCaseExceptionVO> convertApiTestCaseVO(List<ApiTestCaseEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiCaseExceptionVO> list = new ArrayList<ApiCaseExceptionVO>( entityList.size() );
        for ( ApiTestCaseEntity apiTestCaseEntity : entityList ) {
            list.add( apiTestCaseEntityToApiCaseExceptionVO( apiTestCaseEntity ) );
        }

        return list;
    }

    @Override
    public List<SceneApiRelationEntityDO> convert2EntityDO(List<SceneApiRelationEntity> entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        List<SceneApiRelationEntityDO> list = new ArrayList<SceneApiRelationEntityDO>( entityDO.size() );
        for ( SceneApiRelationEntity sceneApiRelationEntity : entityDO ) {
            list.add( sceneApiRelationEntityToSceneApiRelationEntityDO( sceneApiRelationEntity ) );
        }

        return list;
    }

    @Override
    public ApiSampleCaseVO convertApiSampleCaseVO(ApiTestEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ApiSampleCaseVO apiSampleCaseVO = new ApiSampleCaseVO();

        apiSampleCaseVO.setApiCode( entity.getMainApiCode() );
        apiSampleCaseVO.setApiName( entity.getApiName() );
        apiSampleCaseVO.setReqMethod( entity.getReqMethod() );
        apiSampleCaseVO.setApiData( entity.getApiData() );
        apiSampleCaseVO.setDocId( entity.getDocId() );
        apiSampleCaseVO.setDocProductCode( entity.getDocProductCode() );
        apiSampleCaseVO.setProductCode( entity.getProductCode() );
        apiSampleCaseVO.setAppId( entity.getAppId() );
        apiSampleCaseVO.setApiType( entity.getApiType() );
        apiSampleCaseVO.setApiAddress( entity.getApiAddress() );
        apiSampleCaseVO.setDocVersion( entity.getDocVersion() );

        return apiSampleCaseVO;
    }

    @Override
    public List<ApiSampleCaseVO> convertApiSampleCaseVO(List<ApiTestEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiSampleCaseVO> list = new ArrayList<ApiSampleCaseVO>( entityList.size() );
        for ( ApiTestEntity apiTestEntity : entityList ) {
            list.add( convertApiSampleCaseVO( apiTestEntity ) );
        }

        return list;
    }

    private Long eventTransactorUserId(AddApiTestVariableEvent addApiTestVariableEvent) {
        if ( addApiTestVariableEvent == null ) {
            return null;
        }
        User transactor = addApiTestVariableEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(AddApiTestVariableEvent addApiTestVariableEvent) {
        if ( addApiTestVariableEvent == null ) {
            return null;
        }
        User transactor = addApiTestVariableEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId1(EditApiTestVariableEvent editApiTestVariableEvent) {
        if ( editApiTestVariableEvent == null ) {
            return null;
        }
        User transactor = editApiTestVariableEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName1(EditApiTestVariableEvent editApiTestVariableEvent) {
        if ( editApiTestVariableEvent == null ) {
            return null;
        }
        User transactor = editApiTestVariableEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId2(UpdateApiTestVariableStatusEvent updateApiTestVariableStatusEvent) {
        if ( updateApiTestVariableStatusEvent == null ) {
            return null;
        }
        User transactor = updateApiTestVariableStatusEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName2(UpdateApiTestVariableStatusEvent updateApiTestVariableStatusEvent) {
        if ( updateApiTestVariableStatusEvent == null ) {
            return null;
        }
        User transactor = updateApiTestVariableStatusEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId3(DeleteApiTestVariableEvent deleteApiTestVariableEvent) {
        if ( deleteApiTestVariableEvent == null ) {
            return null;
        }
        User transactor = deleteApiTestVariableEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName3(DeleteApiTestVariableEvent deleteApiTestVariableEvent) {
        if ( deleteApiTestVariableEvent == null ) {
            return null;
        }
        User transactor = deleteApiTestVariableEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    protected ApiLiteInfoVO apiTestEntityToApiLiteInfoVO(ApiTestEntity apiTestEntity) {
        if ( apiTestEntity == null ) {
            return null;
        }

        ApiLiteInfoVO apiLiteInfoVO = new ApiLiteInfoVO();

        apiLiteInfoVO.setApiCode( apiTestEntity.getApiCode() );
        apiLiteInfoVO.setApiName( apiTestEntity.getApiName() );
        apiLiteInfoVO.setApiAddress( apiTestEntity.getApiAddress() );

        return apiLiteInfoVO;
    }

    protected SceneDatabaseAuthorizeEntityDO sceneDatabaseAuthorizeEntityToSceneDatabaseAuthorizeEntityDO(SceneDatabaseAuthorizeEntity sceneDatabaseAuthorizeEntity) {
        if ( sceneDatabaseAuthorizeEntity == null ) {
            return null;
        }

        SceneDatabaseAuthorizeEntityDO sceneDatabaseAuthorizeEntityDO = new SceneDatabaseAuthorizeEntityDO();

        sceneDatabaseAuthorizeEntityDO.setId( sceneDatabaseAuthorizeEntity.getId() );
        sceneDatabaseAuthorizeEntityDO.setAuthorizeCode( sceneDatabaseAuthorizeEntity.getAuthorizeCode() );
        sceneDatabaseAuthorizeEntityDO.setProductCode( sceneDatabaseAuthorizeEntity.getProductCode() );
        sceneDatabaseAuthorizeEntityDO.setAuthorizeProductCode( sceneDatabaseAuthorizeEntity.getAuthorizeProductCode() );
        sceneDatabaseAuthorizeEntityDO.setAuthorizeProductName( sceneDatabaseAuthorizeEntity.getAuthorizeProductName() );
        sceneDatabaseAuthorizeEntityDO.setDbId( sceneDatabaseAuthorizeEntity.getDbId() );
        sceneDatabaseAuthorizeEntityDO.setDbName( sceneDatabaseAuthorizeEntity.getDbName() );
        sceneDatabaseAuthorizeEntityDO.setPhysicDbName( sceneDatabaseAuthorizeEntity.getPhysicDbName() );
        sceneDatabaseAuthorizeEntityDO.setEnable( sceneDatabaseAuthorizeEntity.getEnable() );
        sceneDatabaseAuthorizeEntityDO.setCreatorId( sceneDatabaseAuthorizeEntity.getCreatorId() );
        sceneDatabaseAuthorizeEntityDO.setCreator( sceneDatabaseAuthorizeEntity.getCreator() );
        sceneDatabaseAuthorizeEntityDO.setGmtCreate( sceneDatabaseAuthorizeEntity.getGmtCreate() );
        sceneDatabaseAuthorizeEntityDO.setModifierId( sceneDatabaseAuthorizeEntity.getModifierId() );
        sceneDatabaseAuthorizeEntityDO.setModifier( sceneDatabaseAuthorizeEntity.getModifier() );
        sceneDatabaseAuthorizeEntityDO.setGmtModified( sceneDatabaseAuthorizeEntity.getGmtModified() );

        return sceneDatabaseAuthorizeEntityDO;
    }

    private Long eventTransactorUserId4(AddApiTestCaseEvent addApiTestCaseEvent) {
        if ( addApiTestCaseEvent == null ) {
            return null;
        }
        User transactor = addApiTestCaseEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName4(AddApiTestCaseEvent addApiTestCaseEvent) {
        if ( addApiTestCaseEvent == null ) {
            return null;
        }
        User transactor = addApiTestCaseEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    protected ApiCaseExceptionVO apiTestCaseEntityToApiCaseExceptionVO(ApiTestCaseEntity apiTestCaseEntity) {
        if ( apiTestCaseEntity == null ) {
            return null;
        }

        ApiCaseExceptionVO apiCaseExceptionVO = new ApiCaseExceptionVO();

        apiCaseExceptionVO.setCaseReqData( apiTestCaseEntity.getCaseReqData() );
        apiCaseExceptionVO.setCaseCode( apiTestCaseEntity.getCaseCode() );

        return apiCaseExceptionVO;
    }

    protected SceneApiRelationEntityDO sceneApiRelationEntityToSceneApiRelationEntityDO(SceneApiRelationEntity sceneApiRelationEntity) {
        if ( sceneApiRelationEntity == null ) {
            return null;
        }

        SceneApiRelationEntityDO sceneApiRelationEntityDO = new SceneApiRelationEntityDO();

        sceneApiRelationEntityDO.setEnable( sceneApiRelationEntity.getEnable() );
        sceneApiRelationEntityDO.setCreatorId( sceneApiRelationEntity.getCreatorId() );
        sceneApiRelationEntityDO.setCreator( sceneApiRelationEntity.getCreator() );
        sceneApiRelationEntityDO.setGmtCreate( sceneApiRelationEntity.getGmtCreate() );
        sceneApiRelationEntityDO.setModifierId( sceneApiRelationEntity.getModifierId() );
        sceneApiRelationEntityDO.setModifier( sceneApiRelationEntity.getModifier() );
        sceneApiRelationEntityDO.setGmtModified( sceneApiRelationEntity.getGmtModified() );
        if ( sceneApiRelationEntity.hasId() ) {
            sceneApiRelationEntityDO.setId( sceneApiRelationEntity.getId() );
        }
        sceneApiRelationEntityDO.setSceneCode( sceneApiRelationEntity.getSceneCode() );
        sceneApiRelationEntityDO.setProductCode( sceneApiRelationEntity.getProductCode() );
        sceneApiRelationEntityDO.setAppId( sceneApiRelationEntity.getAppId() );
        sceneApiRelationEntityDO.setApiType( sceneApiRelationEntity.getApiType() );
        sceneApiRelationEntityDO.setApiAddress( sceneApiRelationEntity.getApiAddress() );
        sceneApiRelationEntityDO.setRelatedApiName( sceneApiRelationEntity.getRelatedApiName() );
        sceneApiRelationEntityDO.setMainApiCode( sceneApiRelationEntity.getMainApiCode() );
        sceneApiRelationEntityDO.setApiTestIndex( sceneApiRelationEntity.getApiTestIndex() );

        return sceneApiRelationEntityDO;
    }
}
