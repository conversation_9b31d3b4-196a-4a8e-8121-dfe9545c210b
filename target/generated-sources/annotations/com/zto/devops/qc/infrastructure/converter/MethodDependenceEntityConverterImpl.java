package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.MethodDependenceEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.MethodDependenceEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class MethodDependenceEntityConverterImpl implements MethodDependenceEntityConverter {

    @Override
    public List<MethodDependenceEntityDO> covertList(List<MethodDependenceEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MethodDependenceEntityDO> list = new ArrayList<MethodDependenceEntityDO>( entityList.size() );
        for ( MethodDependenceEntity methodDependenceEntity : entityList ) {
            list.add( covert( methodDependenceEntity ) );
        }

        return list;
    }

    @Override
    public MethodDependenceEntityDO covert(MethodDependenceEntity entity) {
        if ( entity == null ) {
            return null;
        }

        MethodDependenceEntityDO methodDependenceEntityDO = new MethodDependenceEntityDO();

        methodDependenceEntityDO.setEnable( entity.getEnable() );
        methodDependenceEntityDO.setCreatorId( entity.getCreatorId() );
        methodDependenceEntityDO.setCreator( entity.getCreator() );
        methodDependenceEntityDO.setGmtCreate( entity.getGmtCreate() );
        methodDependenceEntityDO.setModifierId( entity.getModifierId() );
        methodDependenceEntityDO.setModifier( entity.getModifier() );
        methodDependenceEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            methodDependenceEntityDO.setId( entity.getId() );
        }
        methodDependenceEntityDO.setProductCode( entity.getProductCode() );
        methodDependenceEntityDO.setProductName( entity.getProductName() );
        methodDependenceEntityDO.setMethodCode( entity.getMethodCode() );
        methodDependenceEntityDO.setAppId( entity.getAppId() );
        methodDependenceEntityDO.setVersionCode( entity.getVersionCode() );
        methodDependenceEntityDO.setVersionName( entity.getVersionName() );
        methodDependenceEntityDO.setCommitId( entity.getCommitId() );
        methodDependenceEntityDO.setEntryMethodCode( entity.getEntryMethodCode() );
        methodDependenceEntityDO.setEntryMethodType( entity.getEntryMethodType() );
        methodDependenceEntityDO.setParentMethodCode( entity.getParentMethodCode() );
        methodDependenceEntityDO.setFullClassName( entity.getFullClassName() );
        methodDependenceEntityDO.setMethodName( entity.getMethodName() );
        methodDependenceEntityDO.setMethodDesc( entity.getMethodDesc() );
        methodDependenceEntityDO.setMethodType( entity.getMethodType() );
        methodDependenceEntityDO.setMethodAnnotation( entity.getMethodAnnotation() );
        methodDependenceEntityDO.setMethodLevel( entity.getMethodLevel() );
        methodDependenceEntityDO.setMethodSort( entity.getMethodSort() );
        methodDependenceEntityDO.setMethodParameterStr( entity.getMethodParameterStr() );
        methodDependenceEntityDO.setInterfaceFullClassName( entity.getInterfaceFullClassName() );
        methodDependenceEntityDO.setZcatMetricKey( entity.getZcatMetricKey() );

        return methodDependenceEntityDO;
    }

    @Override
    public List<MethodDependenceEntity> covert2EntityList(List<MethodDependenceEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<MethodDependenceEntity> list = new ArrayList<MethodDependenceEntity>( entityDOList.size() );
        for ( MethodDependenceEntityDO methodDependenceEntityDO : entityDOList ) {
            list.add( covert2Entity( methodDependenceEntityDO ) );
        }

        return list;
    }

    @Override
    public MethodDependenceEntity covert2Entity(MethodDependenceEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        MethodDependenceEntity methodDependenceEntity = new MethodDependenceEntity();

        if ( entityDO.getEntryMethodCode() != null ) {
            methodDependenceEntity.setEntryMethodCode( entityDO.getEntryMethodCode() );
        }
        else {
            methodDependenceEntity.setEntryMethodCode( "" );
        }
        if ( entityDO.getEntryMethodType() != null ) {
            methodDependenceEntity.setEntryMethodType( entityDO.getEntryMethodType() );
        }
        else {
            methodDependenceEntity.setEntryMethodType( "" );
        }
        if ( entityDO.getParentMethodCode() != null ) {
            methodDependenceEntity.setParentMethodCode( entityDO.getParentMethodCode() );
        }
        else {
            methodDependenceEntity.setParentMethodCode( "" );
        }
        if ( entityDO.getFullClassName() != null ) {
            methodDependenceEntity.setFullClassName( entityDO.getFullClassName() );
        }
        else {
            methodDependenceEntity.setFullClassName( "" );
        }
        if ( entityDO.getMethodName() != null ) {
            methodDependenceEntity.setMethodName( entityDO.getMethodName() );
        }
        else {
            methodDependenceEntity.setMethodName( "" );
        }
        if ( entityDO.getMethodDesc() != null ) {
            methodDependenceEntity.setMethodDesc( entityDO.getMethodDesc() );
        }
        else {
            methodDependenceEntity.setMethodDesc( "" );
        }
        if ( entityDO.getMethodType() != null ) {
            methodDependenceEntity.setMethodType( entityDO.getMethodType() );
        }
        else {
            methodDependenceEntity.setMethodType( "" );
        }
        if ( entityDO.getMethodAnnotation() != null ) {
            methodDependenceEntity.setMethodAnnotation( entityDO.getMethodAnnotation() );
        }
        else {
            methodDependenceEntity.setMethodAnnotation( "" );
        }
        if ( entityDO.getMethodLevel() != null ) {
            methodDependenceEntity.setMethodLevel( entityDO.getMethodLevel() );
        }
        else {
            methodDependenceEntity.setMethodLevel( 1 );
        }
        if ( entityDO.getMethodSort() != null ) {
            methodDependenceEntity.setMethodSort( entityDO.getMethodSort() );
        }
        else {
            methodDependenceEntity.setMethodSort( 1 );
        }
        if ( entityDO.getMethodParameterStr() != null ) {
            methodDependenceEntity.setMethodParameterStr( entityDO.getMethodParameterStr() );
        }
        else {
            methodDependenceEntity.setMethodParameterStr( "" );
        }
        if ( entityDO.getInterfaceFullClassName() != null ) {
            methodDependenceEntity.setInterfaceFullClassName( entityDO.getInterfaceFullClassName() );
        }
        else {
            methodDependenceEntity.setInterfaceFullClassName( "" );
        }
        if ( entityDO.getZcatMetricKey() != null ) {
            methodDependenceEntity.setZcatMetricKey( entityDO.getZcatMetricKey() );
        }
        else {
            methodDependenceEntity.setZcatMetricKey( "" );
        }
        if ( entityDO.getEnable() != null ) {
            methodDependenceEntity.setEnable( entityDO.getEnable() );
        }
        else {
            methodDependenceEntity.setEnable( true );
        }
        if ( entityDO.getCreatorId() != null ) {
            methodDependenceEntity.setCreatorId( entityDO.getCreatorId() );
        }
        else {
            methodDependenceEntity.setCreatorId( (long) 0L );
        }
        if ( entityDO.getCreator() != null ) {
            methodDependenceEntity.setCreator( entityDO.getCreator() );
        }
        else {
            methodDependenceEntity.setCreator( "" );
        }
        methodDependenceEntity.setGmtCreate( entityDO.getGmtCreate() );
        methodDependenceEntity.setModifierId( entityDO.getModifierId() );
        methodDependenceEntity.setModifier( entityDO.getModifier() );
        methodDependenceEntity.setGmtModified( entityDO.getGmtModified() );
        methodDependenceEntity.setProductCode( entityDO.getProductCode() );
        methodDependenceEntity.setProductName( entityDO.getProductName() );
        methodDependenceEntity.setMethodCode( entityDO.getMethodCode() );
        methodDependenceEntity.setAppId( entityDO.getAppId() );
        methodDependenceEntity.setVersionCode( entityDO.getVersionCode() );
        methodDependenceEntity.setVersionName( entityDO.getVersionName() );
        methodDependenceEntity.setCommitId( entityDO.getCommitId() );

        return methodDependenceEntity;
    }
}
