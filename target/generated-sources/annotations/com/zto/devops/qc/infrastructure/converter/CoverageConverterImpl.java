package com.zto.devops.qc.infrastructure.converter;

import com.alibaba.fastjson.JSONArray;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.pipeline.client.model.plan.event.ReleasedQcEvent;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.model.dto.CoverageRecordBasicEntityDO;
import com.zto.devops.qc.client.model.dto.CoverageRecordEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.testmanager.coverage.command.BatchCoverageCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.CoverageSwitchBaseCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.GenerateCoverageCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageAppInfoVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageAppInfoVO.CoverageAppInfoVOBuilder;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageBranchBasicVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageExecVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoveragePublishVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordBasicVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageStatusVO;
import com.zto.devops.qc.client.model.testmanager.coverage.event.BatchCoverageEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageSwitchBaseEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.event.GenerateCoverageEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmTestReportVO;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageRecordResp;
import com.zto.devops.qc.domain.model.coverage.ClassInfo;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageBranchBasicEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageExecEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordBasicEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordGenerateEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestReportEntity;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Generated;
import org.jacoco.core.internal.diff.MethodInfo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class CoverageConverterImpl implements CoverageConverter {

    @Override
    public CoveragePublishQuery converter(ReleasedQcEvent event) {
        if ( event == null ) {
            return null;
        }

        CoveragePublishQuery coveragePublishQuery = new CoveragePublishQuery();

        coveragePublishQuery.setCreatorId( eventTransactorUserId( event ) );
        coveragePublishQuery.setCreator( eventTransactorUserName( event ) );
        List<String> list = event.getVersionCodes();
        if ( list != null ) {
            coveragePublishQuery.setVersionCodes( new ArrayList<String>( list ) );
        }
        coveragePublishQuery.setBranchName( event.getBranchName() );
        List<String> list1 = event.getAppIds();
        if ( list1 != null ) {
            coveragePublishQuery.setAppIds( new ArrayList<String>( list1 ) );
        }

        return coveragePublishQuery;
    }

    @Override
    public CoverageSwitchBaseEvent converter(CoverageSwitchBaseCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageSwitchBaseEvent coverageSwitchBaseEvent = new CoverageSwitchBaseEvent();

        coverageSwitchBaseEvent.setAggregateId( command.getAggregateId() );
        coverageSwitchBaseEvent.setTransactor( command.getTransactor() );
        coverageSwitchBaseEvent.setOccurred( command.getOccurred() );
        coverageSwitchBaseEvent.setVersionCode( command.getVersionCode() );
        coverageSwitchBaseEvent.setVersionName( command.getVersionName() );
        coverageSwitchBaseEvent.setAppId( command.getAppId() );

        return coverageSwitchBaseEvent;
    }

    @Override
    public GenerateCoverageEvent conveter(GenerateCoverageCommand command) {
        if ( command == null ) {
            return null;
        }

        GenerateCoverageEvent generateCoverageEvent = new GenerateCoverageEvent();

        generateCoverageEvent.setAggregateId( command.getAggregateId() );
        generateCoverageEvent.setTransactor( command.getTransactor() );
        generateCoverageEvent.setOccurred( command.getOccurred() );
        generateCoverageEvent.setProductCode( command.getProductCode() );
        generateCoverageEvent.setAppId( command.getAppId() );
        generateCoverageEvent.setRecordType( command.getRecordType() );
        generateCoverageEvent.setVersionCode( command.getVersionCode() );
        generateCoverageEvent.setVersionName( command.getVersionName() );

        return generateCoverageEvent;
    }

    @Override
    public BatchCoverageEvent conveter(BatchCoverageCommand command) {
        if ( command == null ) {
            return null;
        }

        BatchCoverageEvent batchCoverageEvent = new BatchCoverageEvent();

        batchCoverageEvent.setAggregateId( command.getAggregateId() );
        batchCoverageEvent.setTransactor( command.getTransactor() );
        batchCoverageEvent.setOccurred( command.getOccurred() );
        batchCoverageEvent.setProductCode( command.getProductCode() );
        batchCoverageEvent.setVersionCode( command.getVersionCode() );
        List<String> list = command.getAppIdList();
        if ( list != null ) {
            batchCoverageEvent.setAppIdList( new ArrayList<String>( list ) );
        }
        batchCoverageEvent.setRecordType( command.getRecordType() );

        return batchCoverageEvent;
    }

    @Override
    public CoverageRecordGenerateParameter converter(GenerateCoverageEvent event) {
        if ( event == null ) {
            return null;
        }

        CoverageRecordGenerateParameter coverageRecordGenerateParameter = new CoverageRecordGenerateParameter();

        coverageRecordGenerateParameter.setProductCode( event.getProductCode() );
        coverageRecordGenerateParameter.setVersionCode( event.getVersionCode() );
        coverageRecordGenerateParameter.setVersionName( event.getVersionName() );
        coverageRecordGenerateParameter.setRecordType( event.getRecordType() );

        return coverageRecordGenerateParameter;
    }

    @Override
    public CoverageRecordGenerateParameter converter(GenerateCoverageCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageRecordGenerateParameter coverageRecordGenerateParameter = new CoverageRecordGenerateParameter();

        coverageRecordGenerateParameter.setProductCode( command.getProductCode() );
        coverageRecordGenerateParameter.setVersionCode( command.getVersionCode() );
        coverageRecordGenerateParameter.setVersionName( command.getVersionName() );
        coverageRecordGenerateParameter.setRecordType( command.getRecordType() );
        coverageRecordGenerateParameter.setDiffType( command.getDiffType() );

        coverageRecordGenerateParameter.setAppIdList( java.util.Arrays.asList(command.getAppId()) );

        return coverageRecordGenerateParameter;
    }

    @Override
    public CoverageRecordGenerateParameter converter(BatchCoverageCommand command) {
        if ( command == null ) {
            return null;
        }

        CoverageRecordGenerateParameter coverageRecordGenerateParameter = new CoverageRecordGenerateParameter();

        coverageRecordGenerateParameter.setProductCode( command.getProductCode() );
        coverageRecordGenerateParameter.setVersionCode( command.getVersionCode() );
        coverageRecordGenerateParameter.setVersionName( command.getVersionName() );
        List<String> list = command.getAppIdList();
        if ( list != null ) {
            coverageRecordGenerateParameter.setAppIdList( new ArrayList<String>( list ) );
        }
        coverageRecordGenerateParameter.setRecordType( command.getRecordType() );
        coverageRecordGenerateParameter.setDiffType( command.getDiffType() );

        return coverageRecordGenerateParameter;
    }

    @Override
    public CoverageStatusVO converter(CoverageRecordEntity coverageRecordEntity) {
        if ( coverageRecordEntity == null ) {
            return null;
        }

        CoverageStatusVO coverageStatusVO = new CoverageStatusVO();

        if ( coverageRecordEntity.getBranchGmtCreate() != null ) {
            coverageStatusVO.setBranchGmtCreate( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( coverageRecordEntity.getBranchGmtCreate() ) );
        }
        if ( coverageRecordEntity.getMasterGmtCreate() != null ) {
            coverageStatusVO.setMasterGmtCreate( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( coverageRecordEntity.getMasterGmtCreate() ) );
        }
        coverageStatusVO.setVersionCode( coverageRecordEntity.getVersionCode() );
        coverageStatusVO.setAppId( coverageRecordEntity.getAppId() );
        coverageStatusVO.setStandardRate( coverageRecordEntity.getStandardRate() );
        coverageStatusVO.setBranchRecordRate( coverageRecordEntity.getBranchRecordRate() );
        coverageStatusVO.setBranchStatus( coverageRecordEntity.getBranchStatus() );
        coverageStatusVO.setBranchRecordUrl( coverageRecordEntity.getBranchRecordUrl() );
        coverageStatusVO.setBranchRemark( coverageRecordEntity.getBranchRemark() );
        coverageStatusVO.setBranchRecordErrorMsg( coverageRecordEntity.getBranchRecordErrorMsg() );
        coverageStatusVO.setBranchCreator( coverageRecordEntity.getBranchCreator() );
        coverageStatusVO.setMasterRecordRate( coverageRecordEntity.getMasterRecordRate() );
        coverageStatusVO.setMasterStatus( coverageRecordEntity.getMasterStatus() );
        coverageStatusVO.setMasterRecordUrl( coverageRecordEntity.getMasterRecordUrl() );
        coverageStatusVO.setMasterRemark( coverageRecordEntity.getMasterRemark() );
        coverageStatusVO.setMasterRecordErrorMsg( coverageRecordEntity.getMasterRecordErrorMsg() );
        coverageStatusVO.setMasterCreator( coverageRecordEntity.getMasterCreator() );
        coverageStatusVO.setComment( coverageRecordEntity.getComment() );

        return coverageStatusVO;
    }

    @Override
    public List<CoveragePublishVO> converterPublishVO(List<CoveragePublishEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<CoveragePublishVO> list = new ArrayList<CoveragePublishVO>( entity.size() );
        for ( CoveragePublishEntity coveragePublishEntity : entity ) {
            list.add( converter( coveragePublishEntity ) );
        }

        return list;
    }

    @Override
    public List<CoverageRecordBasicVO> converterBasicVO(List<CoverageRecordBasicEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<CoverageRecordBasicVO> list = new ArrayList<CoverageRecordBasicVO>( entity.size() );
        for ( CoverageRecordBasicEntity coverageRecordBasicEntity : entity ) {
            list.add( converterBasicVO( coverageRecordBasicEntity ) );
        }

        return list;
    }

    @Override
    public CoverageRecordBasicEntity converter(CoverageRecordBasicVO vo) {
        if ( vo == null ) {
            return null;
        }

        CoverageRecordBasicEntity coverageRecordBasicEntity = new CoverageRecordBasicEntity();

        coverageRecordBasicEntity.setEnable( vo.getEnable() );
        coverageRecordBasicEntity.setCreatorId( vo.getCreatorId() );
        coverageRecordBasicEntity.setCreator( vo.getCreator() );
        coverageRecordBasicEntity.setGmtCreate( vo.getGmtCreate() );
        coverageRecordBasicEntity.setModifierId( vo.getModifierId() );
        coverageRecordBasicEntity.setModifier( vo.getModifier() );
        coverageRecordBasicEntity.setGmtModified( vo.getGmtModified() );
        coverageRecordBasicEntity.setId( vo.getId() );
        coverageRecordBasicEntity.setDeptId( vo.getDeptId() );
        coverageRecordBasicEntity.setDeptName( vo.getDeptName() );
        coverageRecordBasicEntity.setProductCode( vo.getProductCode() );
        coverageRecordBasicEntity.setProductName( vo.getProductName() );
        coverageRecordBasicEntity.setVersionCode( vo.getVersionCode() );
        coverageRecordBasicEntity.setVersionName( vo.getVersionName() );
        coverageRecordBasicEntity.setAppId( vo.getAppId() );
        coverageRecordBasicEntity.setAppName( vo.getAppName() );
        coverageRecordBasicEntity.setRecordType( vo.getRecordType() );
        coverageRecordBasicEntity.setGenerateType( vo.getGenerateType() );
        coverageRecordBasicEntity.setStandardRate( vo.getStandardRate() );
        coverageRecordBasicEntity.setRecordRate( vo.getRecordRate() );
        coverageRecordBasicEntity.setStatus( vo.getStatus() );
        coverageRecordBasicEntity.setRecordUrl( vo.getRecordUrl() );
        coverageRecordBasicEntity.setRemark( vo.getRemark() );
        coverageRecordBasicEntity.setRecordErrorMsg( vo.getRecordErrorMsg() );
        coverageRecordBasicEntity.setTestStrategy( vo.getTestStrategy() );
        coverageRecordBasicEntity.setIsWhiteList( vo.getIsWhiteList() );
        coverageRecordBasicEntity.setBranchName( vo.getBranchName() );
        coverageRecordBasicEntity.setCommitId( vo.getCommitId() );
        coverageRecordBasicEntity.setBasicBranchName( vo.getBasicBranchName() );
        coverageRecordBasicEntity.setBasicCommitId( vo.getBasicCommitId() );
        coverageRecordBasicEntity.setAppType( vo.getAppType() );
        coverageRecordBasicEntity.setDiffType( vo.getDiffType() );
        coverageRecordBasicEntity.setEnvName( vo.getEnvName() );
        coverageRecordBasicEntity.setTaskId( vo.getTaskId() );
        coverageRecordBasicEntity.setComment( vo.getComment() );
        coverageRecordBasicEntity.setFlowLaneType( vo.getFlowLaneType() );
        coverageRecordBasicEntity.setBucketName( vo.getBucketName() );
        coverageRecordBasicEntity.setFileName( vo.getFileName() );

        return coverageRecordBasicEntity;
    }

    @Override
    public CoverageRecordBasicVO converterBasicVO(CoverageRecordBasicEntity entity) {
        if ( entity == null ) {
            return null;
        }

        CoverageRecordBasicVO coverageRecordBasicVO = new CoverageRecordBasicVO();

        coverageRecordBasicVO.setEnable( entity.getEnable() );
        coverageRecordBasicVO.setCreatorId( entity.getCreatorId() );
        coverageRecordBasicVO.setCreator( entity.getCreator() );
        coverageRecordBasicVO.setGmtCreate( entity.getGmtCreate() );
        coverageRecordBasicVO.setModifierId( entity.getModifierId() );
        coverageRecordBasicVO.setModifier( entity.getModifier() );
        coverageRecordBasicVO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            coverageRecordBasicVO.setId( entity.getId() );
        }
        coverageRecordBasicVO.setDeptId( entity.getDeptId() );
        coverageRecordBasicVO.setDeptName( entity.getDeptName() );
        coverageRecordBasicVO.setProductCode( entity.getProductCode() );
        coverageRecordBasicVO.setProductName( entity.getProductName() );
        coverageRecordBasicVO.setVersionCode( entity.getVersionCode() );
        coverageRecordBasicVO.setVersionName( entity.getVersionName() );
        coverageRecordBasicVO.setAppId( entity.getAppId() );
        coverageRecordBasicVO.setAppName( entity.getAppName() );
        coverageRecordBasicVO.setRecordType( entity.getRecordType() );
        coverageRecordBasicVO.setGenerateType( entity.getGenerateType() );
        coverageRecordBasicVO.setStandardRate( entity.getStandardRate() );
        coverageRecordBasicVO.setRecordRate( entity.getRecordRate() );
        coverageRecordBasicVO.setStatus( entity.getStatus() );
        coverageRecordBasicVO.setRecordUrl( entity.getRecordUrl() );
        coverageRecordBasicVO.setRemark( entity.getRemark() );
        coverageRecordBasicVO.setRecordErrorMsg( entity.getRecordErrorMsg() );
        coverageRecordBasicVO.setTestStrategy( entity.getTestStrategy() );
        coverageRecordBasicVO.setIsWhiteList( entity.getIsWhiteList() );
        coverageRecordBasicVO.setBranchName( entity.getBranchName() );
        coverageRecordBasicVO.setCommitId( entity.getCommitId() );
        coverageRecordBasicVO.setBasicBranchName( entity.getBasicBranchName() );
        coverageRecordBasicVO.setBasicCommitId( entity.getBasicCommitId() );
        coverageRecordBasicVO.setAppType( entity.getAppType() );
        coverageRecordBasicVO.setDiffType( entity.getDiffType() );
        coverageRecordBasicVO.setEnvName( entity.getEnvName() );
        coverageRecordBasicVO.setTaskId( entity.getTaskId() );
        coverageRecordBasicVO.setComment( entity.getComment() );
        coverageRecordBasicVO.setFlowLaneType( entity.getFlowLaneType() );
        coverageRecordBasicVO.setBucketName( entity.getBucketName() );
        coverageRecordBasicVO.setFileName( entity.getFileName() );

        return coverageRecordBasicVO;
    }

    @Override
    public List<CoverageRecordVO> converter(List<CoverageRecordEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<CoverageRecordVO> list = new ArrayList<CoverageRecordVO>( entityList.size() );
        for ( CoverageRecordEntity coverageRecordEntity : entityList ) {
            list.add( coverageRecordEntityToCoverageRecordVO( coverageRecordEntity ) );
        }

        return list;
    }

    @Override
    public CoveragePublishEntity converter(CoveragePublishVO vo) {
        if ( vo == null ) {
            return null;
        }

        CoveragePublishEntity coveragePublishEntity = new CoveragePublishEntity();

        coveragePublishEntity.setEnable( vo.getEnable() );
        coveragePublishEntity.setCreatorId( vo.getCreatorId() );
        coveragePublishEntity.setCreator( vo.getCreator() );
        coveragePublishEntity.setGmtCreate( vo.getGmtCreate() );
        coveragePublishEntity.setModifierId( vo.getModifierId() );
        coveragePublishEntity.setModifier( vo.getModifier() );
        coveragePublishEntity.setGmtModified( vo.getGmtModified() );
        coveragePublishEntity.setId( vo.getId() );
        coveragePublishEntity.setAppType( vo.getAppType() );
        coveragePublishEntity.setVersionCode( vo.getVersionCode() );
        coveragePublishEntity.setVersionName( vo.getVersionName() );
        coveragePublishEntity.setAppId( vo.getAppId() );
        coveragePublishEntity.setCommitId( vo.getCommitId() );
        coveragePublishEntity.setBranchName( vo.getBranchName() );
        coveragePublishEntity.setGitUrl( vo.getGitUrl() );
        coveragePublishEntity.setDeploymentIdentityEnum( vo.getDeploymentIdentityEnum() );
        coveragePublishEntity.setServiceName( vo.getServiceName() );
        coveragePublishEntity.setPort( vo.getPort() );
        coveragePublishEntity.setPackageName( vo.getPackageName() );
        coveragePublishEntity.setEnvName( vo.getEnvName() );
        coveragePublishEntity.setOutputFileName( vo.getOutputFileName() );
        coveragePublishEntity.setFlowLaneType( vo.getFlowLaneType() );

        return coveragePublishEntity;
    }

    @Override
    public CoveragePublishVO converter(CoveragePublishEntity entity) {
        if ( entity == null ) {
            return null;
        }

        CoveragePublishVO coveragePublishVO = new CoveragePublishVO();

        coveragePublishVO.setEnable( entity.getEnable() );
        coveragePublishVO.setCreatorId( entity.getCreatorId() );
        coveragePublishVO.setCreator( entity.getCreator() );
        coveragePublishVO.setGmtCreate( entity.getGmtCreate() );
        coveragePublishVO.setModifierId( entity.getModifierId() );
        coveragePublishVO.setModifier( entity.getModifier() );
        coveragePublishVO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            coveragePublishVO.setId( entity.getId() );
        }
        coveragePublishVO.setAppType( entity.getAppType() );
        coveragePublishVO.setVersionCode( entity.getVersionCode() );
        coveragePublishVO.setVersionName( entity.getVersionName() );
        coveragePublishVO.setAppId( entity.getAppId() );
        coveragePublishVO.setCommitId( entity.getCommitId() );
        coveragePublishVO.setBranchName( entity.getBranchName() );
        coveragePublishVO.setGitUrl( entity.getGitUrl() );
        coveragePublishVO.setDeploymentIdentityEnum( entity.getDeploymentIdentityEnum() );
        coveragePublishVO.setServiceName( entity.getServiceName() );
        coveragePublishVO.setPort( entity.getPort() );
        coveragePublishVO.setPackageName( entity.getPackageName() );
        coveragePublishVO.setEnvName( entity.getEnvName() );
        coveragePublishVO.setOutputFileName( entity.getOutputFileName() );
        coveragePublishVO.setFlowLaneType( entity.getFlowLaneType() );

        return coveragePublishVO;
    }

    @Override
    public CoverageRecordGenerateEntity converterGenerateEntity(CoverageRecordGenerateVO vo) {
        if ( vo == null ) {
            return null;
        }

        CoverageRecordGenerateEntity coverageRecordGenerateEntity = new CoverageRecordGenerateEntity();

        coverageRecordGenerateEntity.setEnable( vo.getEnable() );
        coverageRecordGenerateEntity.setCreatorId( vo.getCreatorId() );
        coverageRecordGenerateEntity.setCreator( vo.getCreator() );
        coverageRecordGenerateEntity.setGmtCreate( vo.getGmtCreate() );
        coverageRecordGenerateEntity.setModifierId( vo.getModifierId() );
        coverageRecordGenerateEntity.setModifier( vo.getModifier() );
        coverageRecordGenerateEntity.setGmtModified( vo.getGmtModified() );
        coverageRecordGenerateEntity.setId( vo.getId() );
        coverageRecordGenerateEntity.setVersionCode( vo.getVersionCode() );
        coverageRecordGenerateEntity.setVersionName( vo.getVersionName() );
        coverageRecordGenerateEntity.setAppId( vo.getAppId() );
        coverageRecordGenerateEntity.setRecordType( vo.getRecordType() );
        coverageRecordGenerateEntity.setGenerateType( vo.getGenerateType() );
        coverageRecordGenerateEntity.setBranchName( vo.getBranchName() );
        coverageRecordGenerateEntity.setCommitId( vo.getCommitId() );
        coverageRecordGenerateEntity.setGitUrl( vo.getGitUrl() );
        coverageRecordGenerateEntity.setBasicBranchName( vo.getBasicBranchName() );
        coverageRecordGenerateEntity.setBasicCommitId( vo.getBasicCommitId() );
        coverageRecordGenerateEntity.setPackageName( vo.getPackageName() );
        coverageRecordGenerateEntity.setDownloadUrl( vo.getDownloadUrl() );
        coverageRecordGenerateEntity.setFirstBranchCommitId( vo.getFirstBranchCommitId() );
        coverageRecordGenerateEntity.setMergeDump( vo.getMergeDump() );
        List<String> list = vo.getParent();
        if ( list != null ) {
            coverageRecordGenerateEntity.setParent( new ArrayList<String>( list ) );
        }
        coverageRecordGenerateEntity.setLocalClassesPath( vo.getLocalClassesPath() );
        Set<String> set = vo.getMiddleCommitIdList();
        if ( set != null ) {
            coverageRecordGenerateEntity.setMiddleCommitIdList( new HashSet<String>( set ) );
        }
        coverageRecordGenerateEntity.setLastCommitId( vo.getLastCommitId() );
        coverageRecordGenerateEntity.setReportDto( vo.getReportDto() );
        coverageRecordGenerateEntity.setExecName( vo.getExecName() );
        coverageRecordGenerateEntity.setHtmlPath( vo.getHtmlPath() );
        coverageRecordGenerateEntity.setPName( vo.getPName() );
        coverageRecordGenerateEntity.setDiffType( vo.getDiffType() );
        coverageRecordGenerateEntity.setEnvName( vo.getEnvName() );
        coverageRecordGenerateEntity.setGitProjectId( vo.getGitProjectId() );
        coverageRecordGenerateEntity.setProductCode( vo.getProductCode() );
        coverageRecordGenerateEntity.setOutputFileName( vo.getOutputFileName() );
        if ( vo.getFlowLaneType() != null ) {
            coverageRecordGenerateEntity.setFlowLaneType( Enum.valueOf( FlowLaneTypeEnum.class, vo.getFlowLaneType() ) );
        }

        return coverageRecordGenerateEntity;
    }

    @Override
    public CoverageRecordGenerateVO converterVO(CoverageRecordGenerateEntity entity) {
        if ( entity == null ) {
            return null;
        }

        CoverageRecordGenerateVO coverageRecordGenerateVO = new CoverageRecordGenerateVO();

        coverageRecordGenerateVO.setEnable( entity.getEnable() );
        coverageRecordGenerateVO.setCreatorId( entity.getCreatorId() );
        coverageRecordGenerateVO.setCreator( entity.getCreator() );
        coverageRecordGenerateVO.setGmtCreate( entity.getGmtCreate() );
        coverageRecordGenerateVO.setModifierId( entity.getModifierId() );
        coverageRecordGenerateVO.setModifier( entity.getModifier() );
        coverageRecordGenerateVO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            coverageRecordGenerateVO.setId( entity.getId() );
        }
        coverageRecordGenerateVO.setVersionCode( entity.getVersionCode() );
        coverageRecordGenerateVO.setVersionName( entity.getVersionName() );
        coverageRecordGenerateVO.setAppId( entity.getAppId() );
        coverageRecordGenerateVO.setRecordType( entity.getRecordType() );
        coverageRecordGenerateVO.setGenerateType( entity.getGenerateType() );
        coverageRecordGenerateVO.setBranchName( entity.getBranchName() );
        coverageRecordGenerateVO.setCommitId( entity.getCommitId() );
        coverageRecordGenerateVO.setGitUrl( entity.getGitUrl() );
        coverageRecordGenerateVO.setBasicBranchName( entity.getBasicBranchName() );
        coverageRecordGenerateVO.setBasicCommitId( entity.getBasicCommitId() );
        coverageRecordGenerateVO.setPackageName( entity.getPackageName() );
        coverageRecordGenerateVO.setDownloadUrl( entity.getDownloadUrl() );
        coverageRecordGenerateVO.setFirstBranchCommitId( entity.getFirstBranchCommitId() );
        coverageRecordGenerateVO.setMergeDump( entity.getMergeDump() );
        List<String> list = entity.getParent();
        if ( list != null ) {
            coverageRecordGenerateVO.setParent( new ArrayList<String>( list ) );
        }
        coverageRecordGenerateVO.setLocalClassesPath( entity.getLocalClassesPath() );
        Set<String> set = entity.getMiddleCommitIdList();
        if ( set != null ) {
            coverageRecordGenerateVO.setMiddleCommitIdList( new HashSet<String>( set ) );
        }
        coverageRecordGenerateVO.setLastCommitId( entity.getLastCommitId() );
        coverageRecordGenerateVO.setReportDto( entity.getReportDto() );
        coverageRecordGenerateVO.setExecName( entity.getExecName() );
        coverageRecordGenerateVO.setHtmlPath( entity.getHtmlPath() );
        coverageRecordGenerateVO.setPName( entity.getPName() );
        coverageRecordGenerateVO.setDiffType( entity.getDiffType() );
        coverageRecordGenerateVO.setEnvName( entity.getEnvName() );
        coverageRecordGenerateVO.setGitProjectId( entity.getGitProjectId() );
        coverageRecordGenerateVO.setProductCode( entity.getProductCode() );
        coverageRecordGenerateVO.setOutputFileName( entity.getOutputFileName() );
        if ( entity.getFlowLaneType() != null ) {
            coverageRecordGenerateVO.setFlowLaneType( entity.getFlowLaneType().name() );
        }

        return coverageRecordGenerateVO;
    }

    @Override
    public List<CoverageExecVO> converterExecVO(List<CoverageExecEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<CoverageExecVO> list = new ArrayList<CoverageExecVO>( entity.size() );
        for ( CoverageExecEntity coverageExecEntity : entity ) {
            list.add( coverageExecEntityToCoverageExecVO( coverageExecEntity ) );
        }

        return list;
    }

    @Override
    public CoverageExecEntity converterExecEntity(CoverageExecVO execVO) {
        if ( execVO == null ) {
            return null;
        }

        CoverageExecEntity coverageExecEntity = new CoverageExecEntity();

        coverageExecEntity.setVersionCode( execVO.getVersionCode() );
        coverageExecEntity.setAppId( execVO.getAppId() );
        coverageExecEntity.setBranchName( execVO.getBranchName() );
        coverageExecEntity.setCommitId( execVO.getCommitId() );
        coverageExecEntity.setBucketName( execVO.getBucketName() );
        coverageExecEntity.setExecPath( execVO.getExecPath() );
        coverageExecEntity.setExecName( execVO.getExecName() );
        coverageExecEntity.setCreatorId( execVO.getCreatorId() );
        coverageExecEntity.setCreator( execVO.getCreator() );
        coverageExecEntity.setGmtCreate( execVO.getGmtCreate() );
        coverageExecEntity.setModifierId( execVO.getModifierId() );
        coverageExecEntity.setModifier( execVO.getModifier() );
        coverageExecEntity.setGmtModified( execVO.getGmtModified() );
        coverageExecEntity.setFlowLaneType( execVO.getFlowLaneType() );
        coverageExecEntity.setDiffType( execVO.getDiffType() );

        return coverageExecEntity;
    }

    @Override
    public List<TmTestPlanVO> converterPlanVO(List<TmTestPlanEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<TmTestPlanVO> list = new ArrayList<TmTestPlanVO>( entity.size() );
        for ( TmTestPlanEntity tmTestPlanEntity : entity ) {
            list.add( tmTestPlanEntityToTmTestPlanVO( tmTestPlanEntity ) );
        }

        return list;
    }

    @Override
    public TmTestReportVO converterReportVO(TmTestReportEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TmTestReportVO tmTestReportVO = new TmTestReportVO();

        tmTestReportVO.setReportCode( entity.getReportCode() );
        tmTestReportVO.setReportName( entity.getReportName() );
        tmTestReportVO.setPlanCode( entity.getPlanCode() );
        tmTestReportVO.setReportType( entity.getReportType() );
        tmTestReportVO.setVersionCode( entity.getVersionCode() );
        tmTestReportVO.setVersionName( entity.getVersionName() );
        tmTestReportVO.setTestResult( entity.getTestResult() );
        tmTestReportVO.setActualPresentationDate( entity.getActualPresentationDate() );
        tmTestReportVO.setActualApprovalExitDate( entity.getActualApprovalExitDate() );
        tmTestReportVO.setActualOnlineDate( entity.getActualOnlineDate() );
        tmTestReportVO.setActualTestStart( entity.getActualTestStart() );
        tmTestReportVO.setActualTestEnd( entity.getActualTestEnd() );
        tmTestReportVO.setCheckStartDate( entity.getCheckStartDate() );
        tmTestReportVO.setCheckEndDate( entity.getCheckEndDate() );
        tmTestReportVO.setUpdateTestResultDate( entity.getUpdateTestResultDate() );
        tmTestReportVO.setAutoTestResult( entity.getAutoTestResult() );
        tmTestReportVO.setSecurityUserId( entity.getSecurityUserId() );
        tmTestReportVO.setSecurityUserName( entity.getSecurityUserName() );
        tmTestReportVO.setSecurityTestResult( entity.getSecurityTestResult() );
        tmTestReportVO.setCheckType( entity.getCheckType() );
        tmTestReportVO.setDeveloperCount( entity.getDeveloperCount() );
        tmTestReportVO.setTesterCount( entity.getTesterCount() );
        tmTestReportVO.setTestCaseNum( entity.getTestCaseNum() );
        tmTestReportVO.setPlanCaseNum( entity.getPlanCaseNum() );
        tmTestReportVO.setPermitNum( entity.getPermitNum() );
        tmTestReportVO.setAsPlanedOnline( entity.getAsPlanedOnline() );
        tmTestReportVO.setDelay( entity.getDelay() );
        tmTestReportVO.setSummary( entity.getSummary() );
        tmTestReportVO.setStatus( entity.getStatus() );
        tmTestReportVO.setCodeCoverResult( entity.getCodeCoverResult() );
        tmTestReportVO.setCodeCoverReason( entity.getCodeCoverReason() );
        tmTestReportVO.setPlanPresentationDate( entity.getPlanPresentationDate() );
        tmTestReportVO.setPlanApprovalExitDate( entity.getPlanApprovalExitDate() );
        tmTestReportVO.setPlanOnlineDate( entity.getPlanOnlineDate() );

        return tmTestReportVO;
    }

    @Override
    public List<CoverageRecordBasicEntity> converterBasicEntity(List<CoverageRecordBasicVO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<CoverageRecordBasicEntity> list = new ArrayList<CoverageRecordBasicEntity>( voList.size() );
        for ( CoverageRecordBasicVO coverageRecordBasicVO : voList ) {
            list.add( converter( coverageRecordBasicVO ) );
        }

        return list;
    }

    @Override
    public CoverageBranchBasicEntity converterBasicEntity(CoverageBranchBasicVO basicVO) {
        if ( basicVO == null ) {
            return null;
        }

        CoverageBranchBasicEntity coverageBranchBasicEntity = new CoverageBranchBasicEntity();

        coverageBranchBasicEntity.setEnable( basicVO.getEnable() );
        coverageBranchBasicEntity.setCreatorId( basicVO.getCreatorId() );
        coverageBranchBasicEntity.setCreator( basicVO.getCreator() );
        coverageBranchBasicEntity.setGmtCreate( basicVO.getGmtCreate() );
        coverageBranchBasicEntity.setModifierId( basicVO.getModifierId() );
        coverageBranchBasicEntity.setModifier( basicVO.getModifier() );
        coverageBranchBasicEntity.setGmtModified( basicVO.getGmtModified() );
        coverageBranchBasicEntity.setId( basicVO.getId() );
        coverageBranchBasicEntity.setVersionCode( basicVO.getVersionCode() );
        coverageBranchBasicEntity.setVersionName( basicVO.getVersionName() );
        coverageBranchBasicEntity.setAppId( basicVO.getAppId() );
        coverageBranchBasicEntity.setBranchName( basicVO.getBranchName() );
        coverageBranchBasicEntity.setBasicBranchName( basicVO.getBasicBranchName() );
        coverageBranchBasicEntity.setBasicCommitId( basicVO.getBasicCommitId() );
        coverageBranchBasicEntity.setGitProjectId( basicVO.getGitProjectId() );

        return coverageBranchBasicEntity;
    }

    @Override
    public List<CoverageBranchBasicEntity> converterBasicEntityList(List<CoverageBranchBasicVO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<CoverageBranchBasicEntity> list = new ArrayList<CoverageBranchBasicEntity>( voList.size() );
        for ( CoverageBranchBasicVO coverageBranchBasicVO : voList ) {
            list.add( converterBasicEntity( coverageBranchBasicVO ) );
        }

        return list;
    }

    @Override
    public List<CoverageRecordBasicEntityDO> converterBasicDO(List<CoverageRecordBasicEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<CoverageRecordBasicEntityDO> list = new ArrayList<CoverageRecordBasicEntityDO>( entityList.size() );
        for ( CoverageRecordBasicEntity coverageRecordBasicEntity : entityList ) {
            list.add( coverageRecordBasicEntityToCoverageRecordBasicEntityDO( coverageRecordBasicEntity ) );
        }

        return list;
    }

    @Override
    public CoverageRecordBasicEntity converter(CoverageRecordBasicEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        CoverageRecordBasicEntity coverageRecordBasicEntity = new CoverageRecordBasicEntity();

        coverageRecordBasicEntity.setEnable( entityDO.getEnable() );
        coverageRecordBasicEntity.setCreatorId( entityDO.getCreatorId() );
        coverageRecordBasicEntity.setCreator( entityDO.getCreator() );
        coverageRecordBasicEntity.setGmtCreate( entityDO.getGmtCreate() );
        coverageRecordBasicEntity.setModifierId( entityDO.getModifierId() );
        coverageRecordBasicEntity.setModifier( entityDO.getModifier() );
        coverageRecordBasicEntity.setGmtModified( entityDO.getGmtModified() );
        coverageRecordBasicEntity.setId( entityDO.getId() );
        coverageRecordBasicEntity.setDeptId( entityDO.getDeptId() );
        coverageRecordBasicEntity.setDeptName( entityDO.getDeptName() );
        coverageRecordBasicEntity.setProductCode( entityDO.getProductCode() );
        coverageRecordBasicEntity.setProductName( entityDO.getProductName() );
        coverageRecordBasicEntity.setVersionCode( entityDO.getVersionCode() );
        coverageRecordBasicEntity.setVersionName( entityDO.getVersionName() );
        coverageRecordBasicEntity.setAppId( entityDO.getAppId() );
        coverageRecordBasicEntity.setAppName( entityDO.getAppName() );
        coverageRecordBasicEntity.setRecordType( entityDO.getRecordType() );
        coverageRecordBasicEntity.setGenerateType( entityDO.getGenerateType() );
        coverageRecordBasicEntity.setStandardRate( entityDO.getStandardRate() );
        coverageRecordBasicEntity.setRecordRate( entityDO.getRecordRate() );
        coverageRecordBasicEntity.setStatus( entityDO.getStatus() );
        coverageRecordBasicEntity.setRecordUrl( entityDO.getRecordUrl() );
        coverageRecordBasicEntity.setRemark( entityDO.getRemark() );
        coverageRecordBasicEntity.setRecordErrorMsg( entityDO.getRecordErrorMsg() );
        coverageRecordBasicEntity.setTestStrategy( entityDO.getTestStrategy() );
        coverageRecordBasicEntity.setIsWhiteList( entityDO.getIsWhiteList() );
        coverageRecordBasicEntity.setBranchName( entityDO.getBranchName() );
        coverageRecordBasicEntity.setCommitId( entityDO.getCommitId() );
        coverageRecordBasicEntity.setBasicBranchName( entityDO.getBasicBranchName() );
        coverageRecordBasicEntity.setBasicCommitId( entityDO.getBasicCommitId() );
        coverageRecordBasicEntity.setAppType( entityDO.getAppType() );
        coverageRecordBasicEntity.setDiffType( entityDO.getDiffType() );
        coverageRecordBasicEntity.setEnvName( entityDO.getEnvName() );
        coverageRecordBasicEntity.setTaskId( entityDO.getTaskId() );
        coverageRecordBasicEntity.setComment( entityDO.getComment() );

        return coverageRecordBasicEntity;
    }

    @Override
    public List<CoverageAppInfoVO> converterEntity2VO(List<CoverageRecordBasicEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<CoverageAppInfoVO> list = new ArrayList<CoverageAppInfoVO>( entityList.size() );
        for ( CoverageRecordBasicEntity coverageRecordBasicEntity : entityList ) {
            list.add( converterEntity2VO( coverageRecordBasicEntity ) );
        }

        return list;
    }

    @Override
    public CoverageAppInfoVO converterEntity2VO(CoverageRecordBasicEntity entity) {
        if ( entity == null ) {
            return null;
        }

        CoverageAppInfoVOBuilder coverageAppInfoVO = CoverageAppInfoVO.builder();

        coverageAppInfoVO.reason( entity.getRemark() );
        coverageAppInfoVO.versionCode( entity.getVersionCode() );
        coverageAppInfoVO.versionName( entity.getVersionName() );
        coverageAppInfoVO.appId( entity.getAppId() );
        if ( entity.getStatus() != null ) {
            coverageAppInfoVO.status( entity.getStatus().name() );
        }
        coverageAppInfoVO.recordUrl( entity.getRecordUrl() );
        if ( entity.getRecordType() != null ) {
            coverageAppInfoVO.recordType( entity.getRecordType().name() );
        }
        coverageAppInfoVO.recordRate( entity.getRecordRate() );
        coverageAppInfoVO.standardRate( entity.getStandardRate() );
        coverageAppInfoVO.recordErrorMsg( entity.getRecordErrorMsg() );

        return coverageAppInfoVO.build();
    }

    @Override
    public CoverageRecordEntityDO converterEntityDO(CoverageRecordBasicEntity coverageRecordEntity) {
        if ( coverageRecordEntity == null ) {
            return null;
        }

        CoverageRecordEntityDO coverageRecordEntityDO = new CoverageRecordEntityDO();

        if ( coverageRecordEntity.hasId() ) {
            coverageRecordEntityDO.setId( coverageRecordEntity.getId() );
        }
        coverageRecordEntityDO.setDeptId( coverageRecordEntity.getDeptId() );
        coverageRecordEntityDO.setDeptName( coverageRecordEntity.getDeptName() );
        coverageRecordEntityDO.setProductCode( coverageRecordEntity.getProductCode() );
        coverageRecordEntityDO.setProductName( coverageRecordEntity.getProductName() );
        coverageRecordEntityDO.setVersionCode( coverageRecordEntity.getVersionCode() );
        coverageRecordEntityDO.setVersionName( coverageRecordEntity.getVersionName() );
        coverageRecordEntityDO.setAppId( coverageRecordEntity.getAppId() );
        coverageRecordEntityDO.setAppName( coverageRecordEntity.getAppName() );
        coverageRecordEntityDO.setRecordType( coverageRecordEntity.getRecordType() );
        coverageRecordEntityDO.setGenerateType( coverageRecordEntity.getGenerateType() );
        coverageRecordEntityDO.setStandardRate( coverageRecordEntity.getStandardRate() );
        coverageRecordEntityDO.setRecordRate( coverageRecordEntity.getRecordRate() );
        coverageRecordEntityDO.setStatus( coverageRecordEntity.getStatus() );
        coverageRecordEntityDO.setRecordUrl( coverageRecordEntity.getRecordUrl() );
        coverageRecordEntityDO.setRemark( coverageRecordEntity.getRemark() );
        coverageRecordEntityDO.setRecordErrorMsg( coverageRecordEntity.getRecordErrorMsg() );
        coverageRecordEntityDO.setTestStrategy( coverageRecordEntity.getTestStrategy() );
        coverageRecordEntityDO.setIsWhiteList( coverageRecordEntity.getIsWhiteList() );
        coverageRecordEntityDO.setBranchName( coverageRecordEntity.getBranchName() );
        coverageRecordEntityDO.setCommitId( coverageRecordEntity.getCommitId() );
        coverageRecordEntityDO.setBasicBranchName( coverageRecordEntity.getBasicBranchName() );
        coverageRecordEntityDO.setBasicCommitId( coverageRecordEntity.getBasicCommitId() );
        coverageRecordEntityDO.setAppType( coverageRecordEntity.getAppType() );
        coverageRecordEntityDO.setDiffType( coverageRecordEntity.getDiffType() );
        coverageRecordEntityDO.setEnvName( coverageRecordEntity.getEnvName() );
        coverageRecordEntityDO.setTaskId( coverageRecordEntity.getTaskId() );
        coverageRecordEntityDO.setComment( coverageRecordEntity.getComment() );
        coverageRecordEntityDO.setFlowLaneType( coverageRecordEntity.getFlowLaneType() );
        coverageRecordEntityDO.setBucketName( coverageRecordEntity.getBucketName() );
        coverageRecordEntityDO.setFileName( coverageRecordEntity.getFileName() );

        return coverageRecordEntityDO;
    }

    @Override
    public CoverageRecordResp converterEntity2Resp(CoverageRecordBasicEntity coverageRecordEntity) {
        if ( coverageRecordEntity == null ) {
            return null;
        }

        CoverageRecordResp coverageRecordResp = new CoverageRecordResp();

        coverageRecordResp.setVersionCode( coverageRecordEntity.getVersionCode() );
        coverageRecordResp.setAppId( coverageRecordEntity.getAppId() );
        coverageRecordResp.setRecordUrl( coverageRecordEntity.getRecordUrl() );
        coverageRecordResp.setBranchName( coverageRecordEntity.getBranchName() );
        coverageRecordResp.setCommitId( coverageRecordEntity.getCommitId() );
        coverageRecordResp.setBasicBranchName( coverageRecordEntity.getBasicBranchName() );
        coverageRecordResp.setBasicCommitId( coverageRecordEntity.getBasicCommitId() );

        return coverageRecordResp;
    }

    @Override
    public ClassInfo converterClassInfo(org.jacoco.core.internal.diff.ClassInfo classInfo) {
        if ( classInfo == null ) {
            return null;
        }

        ClassInfo classInfo1 = new ClassInfo();

        classInfo1.setClassFile( classInfo.getClassFile() );
        classInfo1.setClassName( classInfo.getClassName() );
        classInfo1.setPackages( classInfo.getPackages() );
        List<int[]> list = classInfo.getAddLines();
        if ( list != null ) {
            classInfo1.setAddLines( new ArrayList<int[]>( list ) );
        }
        List<int[]> list1 = classInfo.getDelLines();
        if ( list1 != null ) {
            classInfo1.setDelLines( new ArrayList<int[]>( list1 ) );
        }
        classInfo1.setType( classInfo.getType() );
        classInfo1.setMethodInfos( methodInfoListToMethodInfoList( classInfo.getMethodInfos() ) );

        return classInfo1;
    }

    @Override
    public List<CoverageBranchBasicVO> converterBasicEntity2VO(List<CoverageBranchBasicEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<CoverageBranchBasicVO> list = new ArrayList<CoverageBranchBasicVO>( entityList.size() );
        for ( CoverageBranchBasicEntity coverageBranchBasicEntity : entityList ) {
            list.add( coverageBranchBasicEntityToCoverageBranchBasicVO( coverageBranchBasicEntity ) );
        }

        return list;
    }

    private Long eventTransactorUserId(ReleasedQcEvent releasedQcEvent) {
        if ( releasedQcEvent == null ) {
            return null;
        }
        User transactor = releasedQcEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(ReleasedQcEvent releasedQcEvent) {
        if ( releasedQcEvent == null ) {
            return null;
        }
        User transactor = releasedQcEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    protected CoverageRecordVO coverageRecordEntityToCoverageRecordVO(CoverageRecordEntity coverageRecordEntity) {
        if ( coverageRecordEntity == null ) {
            return null;
        }

        CoverageRecordVO coverageRecordVO = new CoverageRecordVO();

        coverageRecordVO.setVersionCode( coverageRecordEntity.getVersionCode() );
        coverageRecordVO.setVersionName( coverageRecordEntity.getVersionName() );
        coverageRecordVO.setAppId( coverageRecordEntity.getAppId() );
        coverageRecordVO.setStandardRate( coverageRecordEntity.getStandardRate() );
        coverageRecordVO.setBranchRecordRate( coverageRecordEntity.getBranchRecordRate() );
        coverageRecordVO.setBranchStatus( coverageRecordEntity.getBranchStatus() );
        coverageRecordVO.setBranchRecordUrl( coverageRecordEntity.getBranchRecordUrl() );
        coverageRecordVO.setBranchRemark( coverageRecordEntity.getBranchRemark() );
        coverageRecordVO.setBranchRecordErrorMsg( coverageRecordEntity.getBranchRecordErrorMsg() );
        coverageRecordVO.setBranchCreator( coverageRecordEntity.getBranchCreator() );
        coverageRecordVO.setBranchGmtCreate( coverageRecordEntity.getBranchGmtCreate() );
        coverageRecordVO.setBranchCommitId( coverageRecordEntity.getBranchCommitId() );
        coverageRecordVO.setBranchGitCompareUrl( coverageRecordEntity.getBranchGitCompareUrl() );
        coverageRecordVO.setBranchBucketName( coverageRecordEntity.getBranchBucketName() );
        coverageRecordVO.setBranchFileName( coverageRecordEntity.getBranchFileName() );
        coverageRecordVO.setBranchDiffType( coverageRecordEntity.getBranchDiffType() );
        coverageRecordVO.setComment( coverageRecordEntity.getComment() );

        return coverageRecordVO;
    }

    protected CoverageExecVO coverageExecEntityToCoverageExecVO(CoverageExecEntity coverageExecEntity) {
        if ( coverageExecEntity == null ) {
            return null;
        }

        CoverageExecVO coverageExecVO = new CoverageExecVO();

        coverageExecVO.setVersionCode( coverageExecEntity.getVersionCode() );
        coverageExecVO.setAppId( coverageExecEntity.getAppId() );
        coverageExecVO.setBranchName( coverageExecEntity.getBranchName() );
        coverageExecVO.setCommitId( coverageExecEntity.getCommitId() );
        coverageExecVO.setBucketName( coverageExecEntity.getBucketName() );
        coverageExecVO.setExecPath( coverageExecEntity.getExecPath() );
        coverageExecVO.setExecName( coverageExecEntity.getExecName() );
        coverageExecVO.setCreatorId( coverageExecEntity.getCreatorId() );
        coverageExecVO.setCreator( coverageExecEntity.getCreator() );
        coverageExecVO.setGmtCreate( coverageExecEntity.getGmtCreate() );
        coverageExecVO.setModifierId( coverageExecEntity.getModifierId() );
        coverageExecVO.setModifier( coverageExecEntity.getModifier() );
        coverageExecVO.setGmtModified( coverageExecEntity.getGmtModified() );
        coverageExecVO.setFlowLaneType( coverageExecEntity.getFlowLaneType() );
        coverageExecVO.setDiffType( coverageExecEntity.getDiffType() );

        return coverageExecVO;
    }

    protected TmTestPlanVO tmTestPlanEntityToTmTestPlanVO(TmTestPlanEntity tmTestPlanEntity) {
        if ( tmTestPlanEntity == null ) {
            return null;
        }

        TmTestPlanVO tmTestPlanVO = new TmTestPlanVO();

        tmTestPlanVO.setCode( tmTestPlanEntity.getCode() );
        tmTestPlanVO.setPlanName( tmTestPlanEntity.getPlanName() );
        tmTestPlanVO.setType( tmTestPlanEntity.getType() );
        tmTestPlanVO.setTestStrategy( tmTestPlanEntity.getTestStrategy() );
        tmTestPlanVO.setStatus( tmTestPlanEntity.getStatus() );
        tmTestPlanVO.setRelationPlanCode( tmTestPlanEntity.getRelationPlanCode() );
        tmTestPlanVO.setDeptId( tmTestPlanEntity.getDeptId() );
        tmTestPlanVO.setDeptName( tmTestPlanEntity.getDeptName() );
        tmTestPlanVO.setProductCode( tmTestPlanEntity.getProductCode() );
        tmTestPlanVO.setProductName( tmTestPlanEntity.getProductName() );
        tmTestPlanVO.setVersionCode( tmTestPlanEntity.getVersionCode() );
        tmTestPlanVO.setVersionName( tmTestPlanEntity.getVersionName() );
        tmTestPlanVO.setStartDate( tmTestPlanEntity.getStartDate() );
        tmTestPlanVO.setPublishDate( tmTestPlanEntity.getPublishDate() );
        tmTestPlanVO.setAccessDate( tmTestPlanEntity.getAccessDate() );
        tmTestPlanVO.setAccessDatePartition( tmTestPlanEntity.getAccessDatePartition() );
        tmTestPlanVO.setPermitDate( tmTestPlanEntity.getPermitDate() );
        tmTestPlanVO.setPermitDatePartition( tmTestPlanEntity.getPermitDatePartition() );
        tmTestPlanVO.setDeveloperNum( tmTestPlanEntity.getDeveloperNum() );
        tmTestPlanVO.setTesterNum( tmTestPlanEntity.getTesterNum() );
        tmTestPlanVO.setComment( tmTestPlanEntity.getComment() );
        tmTestPlanVO.setProductDirectorId( tmTestPlanEntity.getProductDirectorId() );
        tmTestPlanVO.setProductDirectorName( tmTestPlanEntity.getProductDirectorName() );
        tmTestPlanVO.setTestDirectorId( tmTestPlanEntity.getTestDirectorId() );
        tmTestPlanVO.setTestDirectorName( tmTestPlanEntity.getTestDirectorName() );
        tmTestPlanVO.setPlanDirectorId( tmTestPlanEntity.getPlanDirectorId() );
        tmTestPlanVO.setPlanDirectorName( tmTestPlanEntity.getPlanDirectorName() );
        tmTestPlanVO.setCreatorId( tmTestPlanEntity.getCreatorId() );
        tmTestPlanVO.setCreator( tmTestPlanEntity.getCreator() );
        tmTestPlanVO.setGmtCreate( tmTestPlanEntity.getGmtCreate() );
        tmTestPlanVO.setModifierId( tmTestPlanEntity.getModifierId() );
        tmTestPlanVO.setModifier( tmTestPlanEntity.getModifier() );
        tmTestPlanVO.setGmtModified( tmTestPlanEntity.getGmtModified() );
        tmTestPlanVO.setEditNo( tmTestPlanEntity.getEditNo() );
        tmTestPlanVO.setProductSource( tmTestPlanEntity.getProductSource() );
        Map<String, Object> map = tmTestPlanEntity.getStageStatus();
        if ( map != null ) {
            tmTestPlanVO.setStageStatus( new HashMap<String, Object>( map ) );
        }

        return tmTestPlanVO;
    }

    protected CoverageRecordBasicEntityDO coverageRecordBasicEntityToCoverageRecordBasicEntityDO(CoverageRecordBasicEntity coverageRecordBasicEntity) {
        if ( coverageRecordBasicEntity == null ) {
            return null;
        }

        CoverageRecordBasicEntityDO coverageRecordBasicEntityDO = new CoverageRecordBasicEntityDO();

        coverageRecordBasicEntityDO.setEnable( coverageRecordBasicEntity.getEnable() );
        coverageRecordBasicEntityDO.setCreatorId( coverageRecordBasicEntity.getCreatorId() );
        coverageRecordBasicEntityDO.setCreator( coverageRecordBasicEntity.getCreator() );
        coverageRecordBasicEntityDO.setGmtCreate( coverageRecordBasicEntity.getGmtCreate() );
        coverageRecordBasicEntityDO.setModifierId( coverageRecordBasicEntity.getModifierId() );
        coverageRecordBasicEntityDO.setModifier( coverageRecordBasicEntity.getModifier() );
        coverageRecordBasicEntityDO.setGmtModified( coverageRecordBasicEntity.getGmtModified() );
        if ( coverageRecordBasicEntity.hasId() ) {
            coverageRecordBasicEntityDO.setId( coverageRecordBasicEntity.getId() );
        }
        coverageRecordBasicEntityDO.setDeptId( coverageRecordBasicEntity.getDeptId() );
        coverageRecordBasicEntityDO.setDeptName( coverageRecordBasicEntity.getDeptName() );
        coverageRecordBasicEntityDO.setProductCode( coverageRecordBasicEntity.getProductCode() );
        coverageRecordBasicEntityDO.setProductName( coverageRecordBasicEntity.getProductName() );
        coverageRecordBasicEntityDO.setVersionCode( coverageRecordBasicEntity.getVersionCode() );
        coverageRecordBasicEntityDO.setVersionName( coverageRecordBasicEntity.getVersionName() );
        coverageRecordBasicEntityDO.setAppId( coverageRecordBasicEntity.getAppId() );
        coverageRecordBasicEntityDO.setAppName( coverageRecordBasicEntity.getAppName() );
        coverageRecordBasicEntityDO.setRecordType( coverageRecordBasicEntity.getRecordType() );
        coverageRecordBasicEntityDO.setGenerateType( coverageRecordBasicEntity.getGenerateType() );
        coverageRecordBasicEntityDO.setStandardRate( coverageRecordBasicEntity.getStandardRate() );
        coverageRecordBasicEntityDO.setRecordRate( coverageRecordBasicEntity.getRecordRate() );
        coverageRecordBasicEntityDO.setStatus( coverageRecordBasicEntity.getStatus() );
        coverageRecordBasicEntityDO.setRecordUrl( coverageRecordBasicEntity.getRecordUrl() );
        coverageRecordBasicEntityDO.setRemark( coverageRecordBasicEntity.getRemark() );
        coverageRecordBasicEntityDO.setRecordErrorMsg( coverageRecordBasicEntity.getRecordErrorMsg() );
        coverageRecordBasicEntityDO.setTestStrategy( coverageRecordBasicEntity.getTestStrategy() );
        coverageRecordBasicEntityDO.setIsWhiteList( coverageRecordBasicEntity.getIsWhiteList() );
        coverageRecordBasicEntityDO.setBranchName( coverageRecordBasicEntity.getBranchName() );
        coverageRecordBasicEntityDO.setCommitId( coverageRecordBasicEntity.getCommitId() );
        coverageRecordBasicEntityDO.setBasicBranchName( coverageRecordBasicEntity.getBasicBranchName() );
        coverageRecordBasicEntityDO.setBasicCommitId( coverageRecordBasicEntity.getBasicCommitId() );
        coverageRecordBasicEntityDO.setAppType( coverageRecordBasicEntity.getAppType() );
        coverageRecordBasicEntityDO.setDiffType( coverageRecordBasicEntity.getDiffType() );
        coverageRecordBasicEntityDO.setEnvName( coverageRecordBasicEntity.getEnvName() );
        coverageRecordBasicEntityDO.setTaskId( coverageRecordBasicEntity.getTaskId() );
        coverageRecordBasicEntityDO.setComment( coverageRecordBasicEntity.getComment() );

        return coverageRecordBasicEntityDO;
    }

    protected com.zto.devops.qc.domain.model.coverage.MethodInfo methodInfoToMethodInfo(MethodInfo methodInfo) {
        if ( methodInfo == null ) {
            return null;
        }

        com.zto.devops.qc.domain.model.coverage.MethodInfo methodInfo1 = new com.zto.devops.qc.domain.model.coverage.MethodInfo();

        methodInfo1.setMd5( methodInfo.getMd5() );
        methodInfo1.setMethodName( methodInfo.getMethodName() );
        methodInfo1.setParameters( methodInfo.getParameters() );
        methodInfo1.setIsCovered( methodInfo.getIsCovered() );
        JSONArray jSONArray = methodInfo.getReportLineList();
        if ( jSONArray != null ) {
            methodInfo1.setReportLineList( new JSONArray( jSONArray ) );
        }
        methodInfo1.setCovered( methodInfo.getCovered() );
        methodInfo1.setFirstLine( methodInfo.getFirstLine() );
        methodInfo1.setLastLine( methodInfo.getLastLine() );
        methodInfo1.setParameterStr( methodInfo.getParameterStr() );

        return methodInfo1;
    }

    protected List<com.zto.devops.qc.domain.model.coverage.MethodInfo> methodInfoListToMethodInfoList(List<MethodInfo> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.domain.model.coverage.MethodInfo> list1 = new ArrayList<com.zto.devops.qc.domain.model.coverage.MethodInfo>( list.size() );
        for ( MethodInfo methodInfo : list ) {
            list1.add( methodInfoToMethodInfo( methodInfo ) );
        }

        return list1;
    }

    protected CoverageBranchBasicVO coverageBranchBasicEntityToCoverageBranchBasicVO(CoverageBranchBasicEntity coverageBranchBasicEntity) {
        if ( coverageBranchBasicEntity == null ) {
            return null;
        }

        CoverageBranchBasicVO coverageBranchBasicVO = new CoverageBranchBasicVO();

        coverageBranchBasicVO.setEnable( coverageBranchBasicEntity.getEnable() );
        coverageBranchBasicVO.setCreatorId( coverageBranchBasicEntity.getCreatorId() );
        coverageBranchBasicVO.setCreator( coverageBranchBasicEntity.getCreator() );
        coverageBranchBasicVO.setGmtCreate( coverageBranchBasicEntity.getGmtCreate() );
        coverageBranchBasicVO.setModifierId( coverageBranchBasicEntity.getModifierId() );
        coverageBranchBasicVO.setModifier( coverageBranchBasicEntity.getModifier() );
        coverageBranchBasicVO.setGmtModified( coverageBranchBasicEntity.getGmtModified() );
        if ( coverageBranchBasicEntity.hasId() ) {
            coverageBranchBasicVO.setId( coverageBranchBasicEntity.getId() );
        }
        coverageBranchBasicVO.setVersionCode( coverageBranchBasicEntity.getVersionCode() );
        coverageBranchBasicVO.setVersionName( coverageBranchBasicEntity.getVersionName() );
        coverageBranchBasicVO.setAppId( coverageBranchBasicEntity.getAppId() );
        coverageBranchBasicVO.setBranchName( coverageBranchBasicEntity.getBranchName() );
        coverageBranchBasicVO.setBasicBranchName( coverageBranchBasicEntity.getBasicBranchName() );
        coverageBranchBasicVO.setBasicCommitId( coverageBranchBasicEntity.getBasicCommitId() );
        coverageBranchBasicVO.setGitProjectId( coverageBranchBasicEntity.getGitProjectId() );

        return coverageBranchBasicVO;
    }
}
