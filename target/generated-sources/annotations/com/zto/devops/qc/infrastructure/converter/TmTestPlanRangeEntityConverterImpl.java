package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmTestPlanRangeEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanRangeEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmTestPlanRangeEntityConverterImpl implements TmTestPlanRangeEntityConverter {

    @Override
    public List<TmTestPlanRangeEntityDO> convert2DOList(List<TmTestPlanRangeEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmTestPlanRangeEntityDO> list = new ArrayList<TmTestPlanRangeEntityDO>( entityList.size() );
        for ( TmTestPlanRangeEntity tmTestPlanRangeEntity : entityList ) {
            list.add( convert2DO( tmTestPlanRangeEntity ) );
        }

        return list;
    }

    @Override
    public TmTestPlanRangeEntityDO convert2DO(TmTestPlanRangeEntity rangeEntity) {
        if ( rangeEntity == null ) {
            return null;
        }

        TmTestPlanRangeEntityDO tmTestPlanRangeEntityDO = new TmTestPlanRangeEntityDO();

        tmTestPlanRangeEntityDO.setEnable( rangeEntity.getEnable() );
        tmTestPlanRangeEntityDO.setCreatorId( rangeEntity.getCreatorId() );
        tmTestPlanRangeEntityDO.setCreator( rangeEntity.getCreator() );
        tmTestPlanRangeEntityDO.setGmtCreate( rangeEntity.getGmtCreate() );
        tmTestPlanRangeEntityDO.setModifierId( rangeEntity.getModifierId() );
        tmTestPlanRangeEntityDO.setModifier( rangeEntity.getModifier() );
        tmTestPlanRangeEntityDO.setGmtModified( rangeEntity.getGmtModified() );
        if ( rangeEntity.hasId() ) {
            tmTestPlanRangeEntityDO.setId( rangeEntity.getId() );
        }
        tmTestPlanRangeEntityDO.setCode( rangeEntity.getCode() );
        tmTestPlanRangeEntityDO.setPlanCode( rangeEntity.getPlanCode() );
        tmTestPlanRangeEntityDO.setTestRange( rangeEntity.getTestRange() );
        tmTestPlanRangeEntityDO.setTestTime( rangeEntity.getTestTime() );
        tmTestPlanRangeEntityDO.setTestRangeStatus( rangeEntity.getTestRangeStatus() );
        tmTestPlanRangeEntityDO.setPriority( rangeEntity.getPriority() );
        tmTestPlanRangeEntityDO.setPermissionsTestInformation( rangeEntity.getPermissionsTestInformation() );
        tmTestPlanRangeEntityDO.setTestInformation( rangeEntity.getTestInformation() );
        tmTestPlanRangeEntityDO.setStatus( rangeEntity.getStatus() );
        tmTestPlanRangeEntityDO.setExecutorId( rangeEntity.getExecutorId() );
        tmTestPlanRangeEntityDO.setExecutor( rangeEntity.getExecutor() );

        return tmTestPlanRangeEntityDO;
    }

    @Override
    public TmTestPlanRangeEntity convert2Entity(TmTestPlanRangeEntityDO rangeEntityDO) {
        if ( rangeEntityDO == null ) {
            return null;
        }

        TmTestPlanRangeEntity tmTestPlanRangeEntity = new TmTestPlanRangeEntity();

        tmTestPlanRangeEntity.setEnable( rangeEntityDO.getEnable() );
        tmTestPlanRangeEntity.setCreatorId( rangeEntityDO.getCreatorId() );
        tmTestPlanRangeEntity.setCreator( rangeEntityDO.getCreator() );
        tmTestPlanRangeEntity.setGmtCreate( rangeEntityDO.getGmtCreate() );
        tmTestPlanRangeEntity.setModifierId( rangeEntityDO.getModifierId() );
        tmTestPlanRangeEntity.setModifier( rangeEntityDO.getModifier() );
        tmTestPlanRangeEntity.setGmtModified( rangeEntityDO.getGmtModified() );
        tmTestPlanRangeEntity.setId( rangeEntityDO.getId() );
        tmTestPlanRangeEntity.setCode( rangeEntityDO.getCode() );
        tmTestPlanRangeEntity.setPlanCode( rangeEntityDO.getPlanCode() );
        tmTestPlanRangeEntity.setTestRange( rangeEntityDO.getTestRange() );
        tmTestPlanRangeEntity.setTestTime( rangeEntityDO.getTestTime() );
        tmTestPlanRangeEntity.setTestRangeStatus( rangeEntityDO.getTestRangeStatus() );
        tmTestPlanRangeEntity.setPriority( rangeEntityDO.getPriority() );
        tmTestPlanRangeEntity.setPermissionsTestInformation( rangeEntityDO.getPermissionsTestInformation() );
        tmTestPlanRangeEntity.setTestInformation( rangeEntityDO.getTestInformation() );
        tmTestPlanRangeEntity.setStatus( rangeEntityDO.getStatus() );
        tmTestPlanRangeEntity.setExecutorId( rangeEntityDO.getExecutorId() );
        tmTestPlanRangeEntity.setExecutor( rangeEntityDO.getExecutor() );

        return tmTestPlanRangeEntity;
    }
}
