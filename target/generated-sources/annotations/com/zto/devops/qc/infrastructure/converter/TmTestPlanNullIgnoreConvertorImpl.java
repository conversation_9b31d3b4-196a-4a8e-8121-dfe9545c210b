package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanEntity;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:27+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
public class TmTestPlanNullIgnoreConvertorImpl implements TmTestPlanNullIgnoreConvertor {

    @Override
    public void updateTmTestPlanEntity(TmTestPlanVO vo, TmTestPlanEntity entity) {
        if ( vo == null ) {
            return;
        }

        if ( vo.getCreatorId() != null ) {
            entity.setCreatorId( vo.getCreatorId() );
        }
        if ( vo.getCreator() != null ) {
            entity.setCreator( vo.getCreator() );
        }
        if ( vo.getGmtCreate() != null ) {
            entity.setGmtCreate( vo.getGmtCreate() );
        }
        if ( vo.getModifierId() != null ) {
            entity.setModifierId( vo.getModifierId() );
        }
        if ( vo.getModifier() != null ) {
            entity.setModifier( vo.getModifier() );
        }
        if ( vo.getGmtModified() != null ) {
            entity.setGmtModified( vo.getGmtModified() );
        }
        if ( vo.getCode() != null ) {
            entity.setCode( vo.getCode() );
        }
        if ( vo.getDeptId() != null ) {
            entity.setDeptId( vo.getDeptId() );
        }
        if ( vo.getDeptName() != null ) {
            entity.setDeptName( vo.getDeptName() );
        }
        if ( vo.getPlanName() != null ) {
            entity.setPlanName( vo.getPlanName() );
        }
        if ( vo.getStatus() != null ) {
            entity.setStatus( vo.getStatus() );
        }
        if ( vo.getType() != null ) {
            entity.setType( vo.getType() );
        }
        if ( vo.getProductCode() != null ) {
            entity.setProductCode( vo.getProductCode() );
        }
        if ( vo.getProductName() != null ) {
            entity.setProductName( vo.getProductName() );
        }
        if ( vo.getVersionCode() != null ) {
            entity.setVersionCode( vo.getVersionCode() );
        }
        if ( vo.getVersionName() != null ) {
            entity.setVersionName( vo.getVersionName() );
        }
        if ( vo.getTestStrategy() != null ) {
            entity.setTestStrategy( vo.getTestStrategy() );
        }
        if ( vo.getDeveloperNum() != null ) {
            entity.setDeveloperNum( vo.getDeveloperNum() );
        }
        if ( vo.getTesterNum() != null ) {
            entity.setTesterNum( vo.getTesterNum() );
        }
        if ( vo.getAccessDate() != null ) {
            entity.setAccessDate( vo.getAccessDate() );
        }
        if ( vo.getAccessDatePartition() != null ) {
            entity.setAccessDatePartition( vo.getAccessDatePartition() );
        }
        if ( vo.getPermitDate() != null ) {
            entity.setPermitDate( vo.getPermitDate() );
        }
        if ( vo.getPermitDatePartition() != null ) {
            entity.setPermitDatePartition( vo.getPermitDatePartition() );
        }
        if ( vo.getStartDate() != null ) {
            entity.setStartDate( vo.getStartDate() );
        }
        if ( vo.getPublishDate() != null ) {
            entity.setPublishDate( vo.getPublishDate() );
        }
        if ( vo.getProductDirectorId() != null ) {
            entity.setProductDirectorId( vo.getProductDirectorId() );
        }
        if ( vo.getProductDirectorName() != null ) {
            entity.setProductDirectorName( vo.getProductDirectorName() );
        }
        if ( vo.getTestDirectorId() != null ) {
            entity.setTestDirectorId( vo.getTestDirectorId() );
        }
        if ( vo.getTestDirectorName() != null ) {
            entity.setTestDirectorName( vo.getTestDirectorName() );
        }
        if ( vo.getPlanDirectorId() != null ) {
            entity.setPlanDirectorId( vo.getPlanDirectorId() );
        }
        if ( vo.getPlanDirectorName() != null ) {
            entity.setPlanDirectorName( vo.getPlanDirectorName() );
        }
        if ( vo.getRelationPlanCode() != null ) {
            entity.setRelationPlanCode( vo.getRelationPlanCode() );
        }
        if ( vo.getEditNo() != null ) {
            entity.setEditNo( vo.getEditNo() );
        }
        if ( entity.getStageStatus() != null ) {
            Map<String, Object> map = vo.getStageStatus();
            if ( map != null ) {
                entity.getStageStatus().clear();
                entity.getStageStatus().putAll( map );
            }
        }
        else {
            Map<String, Object> map = vo.getStageStatus();
            if ( map != null ) {
                entity.setStageStatus( new HashMap<String, Object>( map ) );
            }
        }
        if ( vo.getProductSource() != null ) {
            entity.setProductSource( vo.getProductSource() );
        }
        if ( vo.getComment() != null ) {
            entity.setComment( vo.getComment() );
        }
    }
}
