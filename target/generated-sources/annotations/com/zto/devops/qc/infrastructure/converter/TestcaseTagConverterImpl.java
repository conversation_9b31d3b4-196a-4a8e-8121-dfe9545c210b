package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagAddedEvent;
import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseTagConverterImpl implements TestcaseTagConverter {

    @Override
    public TagEntity converter(TestcaseTagAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        TagEntity tagEntity = new TagEntity();

        tagEntity.setCreatorId( eventTransactorUserId( event ) );
        tagEntity.setCreator( eventTransactorUserName( event ) );
        tagEntity.setGmtCreate( event.getOccurred() );
        tagEntity.setModifierId( eventTransactorUserId( event ) );
        tagEntity.setModifier( eventTransactorUserName( event ) );
        tagEntity.setGmtModified( event.getOccurred() );
        tagEntity.setCode( event.getCode() );
        tagEntity.setDomain( event.getDomain() );
        tagEntity.setBusinessCode( event.getBusinessCode() );
        tagEntity.setType( event.getType() );
        tagEntity.setTagName( event.getTagName() );

        return tagEntity;
    }

    @Override
    public TagEntityDO convert(TagEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TagEntityDO tagEntityDO = new TagEntityDO();

        tagEntityDO.setEnable( entity.getEnable() );
        tagEntityDO.setCreatorId( entity.getCreatorId() );
        tagEntityDO.setCreator( entity.getCreator() );
        tagEntityDO.setGmtCreate( entity.getGmtCreate() );
        tagEntityDO.setModifierId( entity.getModifierId() );
        tagEntityDO.setModifier( entity.getModifier() );
        tagEntityDO.setGmtModified( entity.getGmtModified() );
        tagEntityDO.setCode( entity.getCode() );
        tagEntityDO.setDomain( entity.getDomain() );
        tagEntityDO.setBusinessCode( entity.getBusinessCode() );
        tagEntityDO.setType( entity.getType() );
        tagEntityDO.setTagAlias( entity.getTagAlias() );
        tagEntityDO.setTagCode( entity.getTagCode() );
        tagEntityDO.setTagName( entity.getTagName() );

        return tagEntityDO;
    }

    @Override
    public List<TagEntityDO> convertList(List<TagEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TagEntityDO> list = new ArrayList<TagEntityDO>( entityList.size() );
        for ( TagEntity tagEntity : entityList ) {
            list.add( convert( tagEntity ) );
        }

        return list;
    }

    @Override
    public TagEntity convert2(TagEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TagEntity tagEntity = new TagEntity();

        tagEntity.setEnable( entityDO.getEnable() );
        tagEntity.setCreatorId( entityDO.getCreatorId() );
        tagEntity.setCreator( entityDO.getCreator() );
        tagEntity.setGmtCreate( entityDO.getGmtCreate() );
        tagEntity.setModifierId( entityDO.getModifierId() );
        tagEntity.setModifier( entityDO.getModifier() );
        tagEntity.setGmtModified( entityDO.getGmtModified() );
        tagEntity.setCode( entityDO.getCode() );
        tagEntity.setDomain( entityDO.getDomain() );
        tagEntity.setBusinessCode( entityDO.getBusinessCode() );
        tagEntity.setType( entityDO.getType() );
        tagEntity.setTagAlias( entityDO.getTagAlias() );
        tagEntity.setTagName( entityDO.getTagName() );
        tagEntity.setTagCode( entityDO.getTagCode() );

        return tagEntity;
    }

    @Override
    public List<TagVO> convertVOList(List<TagEntity> tagList) {
        if ( tagList == null ) {
            return null;
        }

        List<TagVO> list = new ArrayList<TagVO>( tagList.size() );
        for ( TagEntity tagEntity : tagList ) {
            list.add( convertVO( tagEntity ) );
        }

        return list;
    }

    @Override
    public TagVO convertVO(TagEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TagVO tagVO = new TagVO();

        tagVO.setDomain( entity.getDomain() );
        tagVO.setBusinessCode( entity.getBusinessCode() );
        tagVO.setCode( entity.getCode() );
        tagVO.setType( entity.getType() );
        tagVO.setTagAlias( entity.getTagAlias() );
        tagVO.setTagCode( entity.getTagCode() );
        tagVO.setTagName( entity.getTagName() );
        tagVO.setEnable( entity.getEnable() );
        tagVO.setCreatorId( entity.getCreatorId() );

        return tagVO;
    }

    @Override
    public List<TagVO> convert(List<TagEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<TagVO> list = new ArrayList<TagVO>( entity.size() );
        for ( TagEntity tagEntity : entity ) {
            list.add( convertVO( tagEntity ) );
        }

        return list;
    }

    private Long eventTransactorUserId(TestcaseTagAddedEvent testcaseTagAddedEvent) {
        if ( testcaseTagAddedEvent == null ) {
            return null;
        }
        User transactor = testcaseTagAddedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(TestcaseTagAddedEvent testcaseTagAddedEvent) {
        if ( testcaseTagAddedEvent == null ) {
            return null;
        }
        User transactor = testcaseTagAddedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }
}
