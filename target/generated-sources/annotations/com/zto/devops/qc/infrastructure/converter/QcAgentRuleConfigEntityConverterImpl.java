package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.QcAgentRuleConfigEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.QcAgentRuleConfigEntity;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class QcAgentRuleConfigEntityConverterImpl implements QcAgentRuleConfigEntityConverter {

    @Override
    public List<QcAgentRuleConfigEntityDO> convert2DOList(List<QcAgentRuleConfigEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<QcAgentRuleConfigEntityDO> list = new ArrayList<QcAgentRuleConfigEntityDO>( entityList.size() );
        for ( QcAgentRuleConfigEntity qcAgentRuleConfigEntity : entityList ) {
            list.add( convert2DO( qcAgentRuleConfigEntity ) );
        }

        return list;
    }

    @Override
    public QcAgentRuleConfigEntityDO convert2DO(QcAgentRuleConfigEntity entity) {
        if ( entity == null ) {
            return null;
        }

        QcAgentRuleConfigEntityDO qcAgentRuleConfigEntityDO = new QcAgentRuleConfigEntityDO();

        if ( entity.hasId() ) {
            qcAgentRuleConfigEntityDO.setId( entity.getId() );
        }
        qcAgentRuleConfigEntityDO.setAppid( entity.getAppid() );
        qcAgentRuleConfigEntityDO.setVersionCode( entity.getVersionCode() );
        qcAgentRuleConfigEntityDO.setHeuristicClassName( entity.getHeuristicClassName() );
        qcAgentRuleConfigEntityDO.setMethodName( entity.getMethodName() );
        qcAgentRuleConfigEntityDO.setInjectionRuleName( entity.getInjectionRuleName() );
        qcAgentRuleConfigEntityDO.setRuleType( entity.getRuleType() );
        qcAgentRuleConfigEntityDO.setChaosExceptionType( entity.getChaosExceptionType() );
        Map<String, Object> map = entity.getRuleBody();
        if ( map != null ) {
            qcAgentRuleConfigEntityDO.setRuleBody( new HashMap<String, Object>( map ) );
        }
        qcAgentRuleConfigEntityDO.setStatus( entity.getStatus() );
        qcAgentRuleConfigEntityDO.setGmtCreate( entity.getGmtCreate() );
        qcAgentRuleConfigEntityDO.setGmtModified( entity.getGmtModified() );
        qcAgentRuleConfigEntityDO.setCreator( entity.getCreator() );
        qcAgentRuleConfigEntityDO.setModifier( entity.getModifier() );
        qcAgentRuleConfigEntityDO.setEnable( entity.getEnable() );
        qcAgentRuleConfigEntityDO.setInjection( entity.getInjection() );

        return qcAgentRuleConfigEntityDO;
    }
}
