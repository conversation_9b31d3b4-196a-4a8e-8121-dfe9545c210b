package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordLogVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.SimpleTestCase;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.DeleteTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditTestcaseTitleEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseDutyUserChangedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseStatusChangedEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseEntityConverterImpl implements TestcaseEntityConverter {

    @Override
    public TestcaseVO converter(TestcaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TestcaseVO testcaseVO = new TestcaseVO();

        testcaseVO.setPriority( entity.getPriority() );
        testcaseVO.setStatus( entity.getStatus() );
        testcaseVO.setType( entity.getType() );
        testcaseVO.setCode( entity.getCode() );
        testcaseVO.setProductCode( entity.getProductCode() );
        testcaseVO.setParentCode( entity.getParentCode() );
        testcaseVO.setName( entity.getName() );
        testcaseVO.setAttribute( entity.getAttribute() );
        testcaseVO.setPrecondition( entity.getPrecondition() );
        testcaseVO.setDutyUserId( entity.getDutyUserId() );
        testcaseVO.setDutyUser( entity.getDutyUser() );
        testcaseVO.setComment( entity.getComment() );
        testcaseVO.setSort( entity.getSort() );
        testcaseVO.setLayer( entity.getLayer() );
        testcaseVO.setPath( entity.getPath() );
        testcaseVO.setNodeType( entity.getNodeType() );
        testcaseVO.setCreatorId( entity.getCreatorId() );
        testcaseVO.setCreator( entity.getCreator() );
        testcaseVO.setGmtCreate( entity.getGmtCreate() );
        testcaseVO.setModifierId( entity.getModifierId() );
        testcaseVO.setModifier( entity.getModifier() );
        testcaseVO.setGmtModified( entity.getGmtModified() );
        testcaseVO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        testcaseVO.setInterfaceName( entity.getInterfaceName() );
        testcaseVO.setNodeTypePath( entity.getNodeTypePath() );
        testcaseVO.setTestcaseModulePath( entity.getTestcaseModulePath() );
        testcaseVO.setSetHeart( entity.getSetHeart() );
        testcaseVO.setSetCore( entity.getSetCore() );
        testcaseVO.setVersionCode( entity.getVersionCode() );

        return testcaseVO;
    }

    @Override
    public List<TestcaseVO> converterTestcaseList(List<TestcaseEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TestcaseVO> list = new ArrayList<TestcaseVO>( entityList.size() );
        for ( TestcaseEntity testcaseEntity : entityList ) {
            list.add( converter( testcaseEntity ) );
        }

        return list;
    }

    @Override
    public TestcaseEntityDO covert(TestcaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TestcaseEntityDO testcaseEntityDO = new TestcaseEntityDO();

        testcaseEntityDO.setEnable( entity.getEnable() );
        testcaseEntityDO.setCreatorId( entity.getCreatorId() );
        testcaseEntityDO.setCreator( entity.getCreator() );
        testcaseEntityDO.setGmtCreate( entity.getGmtCreate() );
        testcaseEntityDO.setModifierId( entity.getModifierId() );
        testcaseEntityDO.setModifier( entity.getModifier() );
        testcaseEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            testcaseEntityDO.setId( entity.getId() );
        }
        testcaseEntityDO.setCode( entity.getCode() );
        testcaseEntityDO.setProductCode( entity.getProductCode() );
        testcaseEntityDO.setVersionCode( entity.getVersionCode() );
        testcaseEntityDO.setParentCode( entity.getParentCode() );
        testcaseEntityDO.setName( entity.getName() );
        testcaseEntityDO.setAttribute( entity.getAttribute() );
        testcaseEntityDO.setType( entity.getType() );
        testcaseEntityDO.setPriority( entity.getPriority() );
        testcaseEntityDO.setStatus( entity.getStatus() );
        testcaseEntityDO.setPrecondition( entity.getPrecondition() );
        testcaseEntityDO.setDutyUserId( entity.getDutyUserId() );
        testcaseEntityDO.setDutyUser( entity.getDutyUser() );
        testcaseEntityDO.setComment( entity.getComment() );
        testcaseEntityDO.setAbandonReason( entity.getAbandonReason() );
        testcaseEntityDO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        testcaseEntityDO.setNodeType( entity.getNodeType() );
        testcaseEntityDO.setSort( entity.getSort() );
        testcaseEntityDO.setLayer( entity.getLayer() );
        testcaseEntityDO.setPath( entity.getPath() );
        testcaseEntityDO.setList( covertList( entity.getList() ) );
        testcaseEntityDO.setNumber( entity.getNumber() );
        testcaseEntityDO.setNodeTypePath( entity.getNodeTypePath() );
        testcaseEntityDO.setSetHeart( entity.getSetHeart() );
        testcaseEntityDO.setTestcaseModulePath( entity.getTestcaseModulePath() );
        testcaseEntityDO.setInterfaceName( entity.getInterfaceName() );
        testcaseEntityDO.setSetCore( entity.getSetCore() );

        return testcaseEntityDO;
    }

    @Override
    public List<TestcaseEntityDO> covertList(List<TestcaseEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TestcaseEntityDO> list = new ArrayList<TestcaseEntityDO>( entityList.size() );
        for ( TestcaseEntity testcaseEntity : entityList ) {
            list.add( covert( testcaseEntity ) );
        }

        return list;
    }

    @Override
    public TestcaseEntity covert2Entity(TestcaseEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TestcaseEntity testcaseEntity = new TestcaseEntity();

        testcaseEntity.setEnable( entityDO.getEnable() );
        testcaseEntity.setCreatorId( entityDO.getCreatorId() );
        testcaseEntity.setCreator( entityDO.getCreator() );
        testcaseEntity.setGmtCreate( entityDO.getGmtCreate() );
        testcaseEntity.setModifierId( entityDO.getModifierId() );
        testcaseEntity.setModifier( entityDO.getModifier() );
        testcaseEntity.setGmtModified( entityDO.getGmtModified() );
        testcaseEntity.setId( entityDO.getId() );
        testcaseEntity.setCode( entityDO.getCode() );
        testcaseEntity.setProductCode( entityDO.getProductCode() );
        testcaseEntity.setVersionCode( entityDO.getVersionCode() );
        testcaseEntity.setParentCode( entityDO.getParentCode() );
        testcaseEntity.setName( entityDO.getName() );
        testcaseEntity.setAttribute( entityDO.getAttribute() );
        testcaseEntity.setType( entityDO.getType() );
        testcaseEntity.setPriority( entityDO.getPriority() );
        testcaseEntity.setStatus( entityDO.getStatus() );
        testcaseEntity.setPrecondition( entityDO.getPrecondition() );
        testcaseEntity.setDutyUserId( entityDO.getDutyUserId() );
        testcaseEntity.setDutyUser( entityDO.getDutyUser() );
        testcaseEntity.setComment( entityDO.getComment() );
        testcaseEntity.setAbandonReason( entityDO.getAbandonReason() );
        testcaseEntity.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        testcaseEntity.setNodeType( entityDO.getNodeType() );
        testcaseEntity.setSort( entityDO.getSort() );
        testcaseEntity.setLayer( entityDO.getLayer() );
        testcaseEntity.setPath( entityDO.getPath() );
        testcaseEntity.setList( covert2EntityList( entityDO.getList() ) );
        testcaseEntity.setNumber( entityDO.getNumber() );
        testcaseEntity.setNodeTypePath( entityDO.getNodeTypePath() );
        testcaseEntity.setSetHeart( entityDO.getSetHeart() );
        testcaseEntity.setTestcaseModulePath( entityDO.getTestcaseModulePath() );
        testcaseEntity.setInterfaceName( entityDO.getInterfaceName() );
        testcaseEntity.setSetCore( entityDO.getSetCore() );

        return testcaseEntity;
    }

    @Override
    public List<TestcaseEntity> covert2EntityList(List<TestcaseEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<TestcaseEntity> list = new ArrayList<TestcaseEntity>( entityDOList.size() );
        for ( TestcaseEntityDO testcaseEntityDO : entityDOList ) {
            list.add( covert2Entity( testcaseEntityDO ) );
        }

        return list;
    }

    @Override
    public AutomaticRecordLogVO converter(AutomaticSourceLogEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticRecordLogVO automaticRecordLogVO = new AutomaticRecordLogVO();

        automaticRecordLogVO.setStatus( entity.getStatus() );
        if ( entity.hasId() ) {
            automaticRecordLogVO.setId( entity.getId() );
        }
        automaticRecordLogVO.setCode( entity.getCode() );
        automaticRecordLogVO.setProductCode( entity.getProductCode() );
        automaticRecordLogVO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        automaticRecordLogVO.setCreatorId( entity.getCreatorId() );
        automaticRecordLogVO.setCreator( entity.getCreator() );
        automaticRecordLogVO.setGmtCreate( entity.getGmtCreate() );
        automaticRecordLogVO.setModifierId( entity.getModifierId() );
        automaticRecordLogVO.setModifier( entity.getModifier() );
        automaticRecordLogVO.setGmtModified( entity.getGmtModified() );
        automaticRecordLogVO.setAddress( entity.getAddress() );
        automaticRecordLogVO.setType( entity.getType() );
        automaticRecordLogVO.setFileName( entity.getFileName() );
        automaticRecordLogVO.setBucketName( entity.getBucketName() );
        automaticRecordLogVO.setFailInformation( entity.getFailInformation() );
        automaticRecordLogVO.setEnable( entity.getEnable() );
        automaticRecordLogVO.setCommitId( entity.getCommitId() );
        automaticRecordLogVO.setErrorLogFile( entity.getErrorLogFile() );
        automaticRecordLogVO.setAnalyticMethod( entity.getAnalyticMethod() );

        return automaticRecordLogVO;
    }

    @Override
    public TestcaseEntity convert(AddTestcaseEvent event) {
        if ( event == null ) {
            return null;
        }

        TestcaseEntity testcaseEntity = new TestcaseEntity();

        testcaseEntity.setCode( event.getCode() );
        testcaseEntity.setProductCode( event.getProductCode() );
        testcaseEntity.setVersionCode( event.getVersionCode() );
        testcaseEntity.setParentCode( event.getParentCode() );
        testcaseEntity.setName( event.getName() );
        testcaseEntity.setAttribute( event.getAttribute() );
        testcaseEntity.setType( event.getType() );
        testcaseEntity.setPriority( event.getPriority() );
        testcaseEntity.setStatus( event.getStatus() );
        testcaseEntity.setPrecondition( event.getPrecondition() );
        testcaseEntity.setDutyUserId( event.getDutyUserId() );
        testcaseEntity.setDutyUser( event.getDutyUser() );
        testcaseEntity.setComment( event.getComment() );
        testcaseEntity.setAutomaticSourceCode( event.getAutomaticSourceCode() );
        testcaseEntity.setNodeType( event.getNodeType() );
        testcaseEntity.setSort( event.getSort() );
        testcaseEntity.setLayer( event.getLayer() );
        testcaseEntity.setPath( event.getPath() );
        testcaseEntity.setNodeTypePath( event.getNodeTypePath() );
        testcaseEntity.setTestcaseModulePath( event.getTestcaseModulePath() );
        testcaseEntity.setInterfaceName( event.getInterfaceName() );
        testcaseEntity.setSetCore( event.getSetCore() );

        return testcaseEntity;
    }

    @Override
    public TestcaseEntity convert(EditTestcaseEvent event) {
        if ( event == null ) {
            return null;
        }

        TestcaseEntity testcaseEntity = new TestcaseEntity();

        testcaseEntity.setEnable( event.getEnable() );
        testcaseEntity.setCode( event.getCode() );
        testcaseEntity.setProductCode( event.getProductCode() );
        testcaseEntity.setParentCode( event.getParentCode() );
        testcaseEntity.setName( event.getName() );
        testcaseEntity.setAttribute( event.getAttribute() );
        testcaseEntity.setType( event.getType() );
        testcaseEntity.setPriority( event.getPriority() );
        testcaseEntity.setStatus( event.getStatus() );
        testcaseEntity.setPrecondition( event.getPrecondition() );
        testcaseEntity.setDutyUserId( event.getDutyUserId() );
        testcaseEntity.setDutyUser( event.getDutyUser() );
        testcaseEntity.setComment( event.getComment() );
        testcaseEntity.setSort( event.getSort() );
        testcaseEntity.setLayer( event.getLayer() );
        testcaseEntity.setPath( event.getPath() );

        return testcaseEntity;
    }

    @Override
    public TestcaseEntity convertorDeleteToEntity(DeleteTestcaseEvent event) {
        if ( event == null ) {
            return null;
        }

        TestcaseEntity testcaseEntity = new TestcaseEntity();

        testcaseEntity.setCode( event.getCode() );
        testcaseEntity.setAttribute( event.getAttribute() );
        testcaseEntity.setType( event.getType() );

        return testcaseEntity;
    }

    @Override
    public TestcaseEntity converter(TestcaseStatusChangedEvent event) {
        if ( event == null ) {
            return null;
        }

        TestcaseEntity testcaseEntity = new TestcaseEntity();

        testcaseEntity.setModifierId( eventTransactorUserId( event ) );
        testcaseEntity.setModifier( eventTransactorUserName( event ) );
        testcaseEntity.setGmtModified( event.getOccurred() );
        testcaseEntity.setCode( event.getCode() );
        testcaseEntity.setStatus( event.getStatus() );
        testcaseEntity.setAbandonReason( event.getAbandonReason() );

        return testcaseEntity;
    }

    @Override
    public TestcaseEntity converter(TestcaseDutyUserChangedEvent event) {
        if ( event == null ) {
            return null;
        }

        TestcaseEntity testcaseEntity = new TestcaseEntity();

        testcaseEntity.setModifierId( eventTransactorUserId1( event ) );
        testcaseEntity.setModifier( eventTransactorUserName1( event ) );
        testcaseEntity.setGmtModified( event.getOccurred() );
        testcaseEntity.setCode( event.getCode() );
        testcaseEntity.setDutyUserId( event.getDutyUserId() );
        testcaseEntity.setDutyUser( event.getDutyUser() );

        return testcaseEntity;
    }

    @Override
    public com.zto.devops.qc.infrastructure.impexp.entity.TestcaseEntity convertor(ListTestcaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        com.zto.devops.qc.infrastructure.impexp.entity.TestcaseEntity testcaseEntity = new com.zto.devops.qc.infrastructure.impexp.entity.TestcaseEntity();

        testcaseEntity.setName( vo.getName() );
        testcaseEntity.setCode( vo.getCode() );
        testcaseEntity.setFullName( vo.getFullName() );
        testcaseEntity.setPrecondition( vo.getPrecondition() );
        testcaseEntity.setComment( vo.getComment() );
        testcaseEntity.setDutyUser( vo.getDutyUser() );
        testcaseEntity.setExecuteNum( vo.getExecuteNum() );
        testcaseEntity.setCreator( vo.getCreator() );
        testcaseEntity.setModifier( vo.getModifier() );

        testcaseEntity.setPriorityDesc( null == vo.getPriority()? "" : vo.getPriority().getValue() );
        testcaseEntity.setStatusDesc( null == vo.getStatus()? "" : vo.getStatus().getDesc() );
        testcaseEntity.setTypeDesc( null == vo.getType()? "" : vo.getType().getValue() );

        return testcaseEntity;
    }

    @Override
    public TestcaseEntity convertorEditTitleEntity(EditTestcaseTitleEvent event) {
        if ( event == null ) {
            return null;
        }

        TestcaseEntity testcaseEntity = new TestcaseEntity();

        testcaseEntity.setModifierId( eventTransactorUserId2( event ) );
        testcaseEntity.setModifier( eventTransactorUserName2( event ) );
        testcaseEntity.setGmtModified( event.getOccurred() );
        testcaseEntity.setCode( event.getCode() );
        testcaseEntity.setProductCode( event.getProductCode() );
        testcaseEntity.setVersionCode( event.getVersionCode() );
        testcaseEntity.setParentCode( event.getParentCode() );
        testcaseEntity.setName( event.getName() );
        testcaseEntity.setAttribute( event.getAttribute() );
        testcaseEntity.setType( event.getType() );
        testcaseEntity.setPriority( event.getPriority() );

        return testcaseEntity;
    }

    @Override
    public SimpleTestCase convertSimpleTestCase(TestcaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        String code = null;
        String name = null;

        code = entity.getCode();
        name = entity.getName();

        SimpleTestCase simpleTestCase = new SimpleTestCase( code, name );

        return simpleTestCase;
    }

    @Override
    public List<SimpleTestCase> convertSimpleTestCase(List<TestcaseEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<SimpleTestCase> list1 = new ArrayList<SimpleTestCase>( list.size() );
        for ( TestcaseEntity testcaseEntity : list ) {
            list1.add( convertSimpleTestCase( testcaseEntity ) );
        }

        return list1;
    }

    private Long eventTransactorUserId(TestcaseStatusChangedEvent testcaseStatusChangedEvent) {
        if ( testcaseStatusChangedEvent == null ) {
            return null;
        }
        User transactor = testcaseStatusChangedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(TestcaseStatusChangedEvent testcaseStatusChangedEvent) {
        if ( testcaseStatusChangedEvent == null ) {
            return null;
        }
        User transactor = testcaseStatusChangedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId1(TestcaseDutyUserChangedEvent testcaseDutyUserChangedEvent) {
        if ( testcaseDutyUserChangedEvent == null ) {
            return null;
        }
        User transactor = testcaseDutyUserChangedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName1(TestcaseDutyUserChangedEvent testcaseDutyUserChangedEvent) {
        if ( testcaseDutyUserChangedEvent == null ) {
            return null;
        }
        User transactor = testcaseDutyUserChangedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId2(EditTestcaseTitleEvent editTestcaseTitleEvent) {
        if ( editTestcaseTitleEvent == null ) {
            return null;
        }
        User transactor = editTestcaseTitleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName2(EditTestcaseTitleEvent editTestcaseTitleEvent) {
        if ( editTestcaseTitleEvent == null ) {
            return null;
        }
        User transactor = editTestcaseTitleEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }
}
