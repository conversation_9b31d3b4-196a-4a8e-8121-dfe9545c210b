package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.dto.RelevantUserEntityDO;
import com.zto.devops.qc.client.model.issue.entity.CurrentHandlerVO;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class RelevantUserDOConvertorImpl implements RelevantUserDOConvertor {

    @Override
    public RelevantUserEntityDO covert(RelevantUserEntity entity) {
        if ( entity == null ) {
            return null;
        }

        RelevantUserEntityDO relevantUserEntityDO = new RelevantUserEntityDO();

        relevantUserEntityDO.setEnable( entity.getEnable() );
        relevantUserEntityDO.setCreatorId( entity.getCreatorId() );
        relevantUserEntityDO.setCreator( entity.getCreator() );
        relevantUserEntityDO.setGmtCreate( entity.getGmtCreate() );
        relevantUserEntityDO.setModifierId( entity.getModifierId() );
        relevantUserEntityDO.setModifier( entity.getModifier() );
        relevantUserEntityDO.setGmtModified( entity.getGmtModified() );
        relevantUserEntityDO.setCode( entity.getCode() );
        relevantUserEntityDO.setUserId( entity.getUserId() );
        relevantUserEntityDO.setUserName( entity.getUserName() );
        relevantUserEntityDO.setHandleType( entity.getHandleType() );
        relevantUserEntityDO.setType( entity.getType() );
        relevantUserEntityDO.setBusinessCode( entity.getBusinessCode() );
        relevantUserEntityDO.setDomain( entity.getDomain() );
        relevantUserEntityDO.setOldObjectId( entity.getOldObjectId() );
        relevantUserEntityDO.setAction( entity.getAction() );
        relevantUserEntityDO.setAccessTag( entity.getAccessTag() );

        return relevantUserEntityDO;
    }

    @Override
    public List<RelevantUserEntityDO> covert(List<RelevantUserEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<RelevantUserEntityDO> list = new ArrayList<RelevantUserEntityDO>( entity.size() );
        for ( RelevantUserEntity relevantUserEntity : entity ) {
            list.add( covert( relevantUserEntity ) );
        }

        return list;
    }

    @Override
    public RelevantUserEntity convert(CurrentHandlerVO currentHandlerVO) {
        if ( currentHandlerVO == null ) {
            return null;
        }

        RelevantUserEntity relevantUserEntity = new RelevantUserEntity();

        relevantUserEntity.setCode( currentHandlerVO.getCode() );
        relevantUserEntity.setUserId( currentHandlerVO.getUserId() );
        relevantUserEntity.setUserName( currentHandlerVO.getUserName() );
        relevantUserEntity.setHandleType( currentHandlerVO.getHandleType() );
        if ( currentHandlerVO.getType() != null ) {
            relevantUserEntity.setType( Enum.valueOf( RelevantUserTypeEnum.class, currentHandlerVO.getType() ) );
        }
        relevantUserEntity.setBusinessCode( currentHandlerVO.getBusinessCode() );
        if ( currentHandlerVO.getDomain() != null ) {
            relevantUserEntity.setDomain( Enum.valueOf( DomainEnum.class, currentHandlerVO.getDomain() ) );
        }
        relevantUserEntity.setAction( currentHandlerVO.getAction() );
        relevantUserEntity.setAccessTag( currentHandlerVO.getAccessTag() );

        return relevantUserEntity;
    }

    @Override
    public List<RelevantUserVO> convert2VO(List<RelevantUserEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<RelevantUserVO> list = new ArrayList<RelevantUserVO>( entityList.size() );
        for ( RelevantUserEntity relevantUserEntity : entityList ) {
            list.add( relevantUserEntityToRelevantUserVO( relevantUserEntity ) );
        }

        return list;
    }

    @Override
    public RelevantUserEntity covert(RelevantUserEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        RelevantUserEntity relevantUserEntity = new RelevantUserEntity();

        relevantUserEntity.setEnable( entityDO.getEnable() );
        relevantUserEntity.setCreatorId( entityDO.getCreatorId() );
        relevantUserEntity.setCreator( entityDO.getCreator() );
        relevantUserEntity.setGmtCreate( entityDO.getGmtCreate() );
        relevantUserEntity.setModifierId( entityDO.getModifierId() );
        relevantUserEntity.setModifier( entityDO.getModifier() );
        relevantUserEntity.setGmtModified( entityDO.getGmtModified() );
        relevantUserEntity.setCode( entityDO.getCode() );
        relevantUserEntity.setUserId( entityDO.getUserId() );
        relevantUserEntity.setUserName( entityDO.getUserName() );
        relevantUserEntity.setHandleType( entityDO.getHandleType() );
        relevantUserEntity.setType( entityDO.getType() );
        relevantUserEntity.setBusinessCode( entityDO.getBusinessCode() );
        relevantUserEntity.setDomain( entityDO.getDomain() );
        relevantUserEntity.setOldObjectId( entityDO.getOldObjectId() );
        relevantUserEntity.setAction( entityDO.getAction() );
        relevantUserEntity.setAccessTag( entityDO.getAccessTag() );

        return relevantUserEntity;
    }

    protected RelevantUserVO relevantUserEntityToRelevantUserVO(RelevantUserEntity relevantUserEntity) {
        if ( relevantUserEntity == null ) {
            return null;
        }

        RelevantUserVO relevantUserVO = new RelevantUserVO();

        relevantUserVO.setType( relevantUserEntity.getType() );
        relevantUserVO.setBusinessCode( relevantUserEntity.getBusinessCode() );
        relevantUserVO.setCode( relevantUserEntity.getCode() );
        if ( relevantUserEntity.getUserId() != null ) {
            relevantUserVO.setUserId( Long.parseLong( relevantUserEntity.getUserId() ) );
        }
        relevantUserVO.setUserName( relevantUserEntity.getUserName() );
        relevantUserVO.setDomain( relevantUserEntity.getDomain() );
        relevantUserVO.setCreatorId( relevantUserEntity.getCreatorId() );
        relevantUserVO.setCreator( relevantUserEntity.getCreator() );

        return relevantUserVO;
    }
}
