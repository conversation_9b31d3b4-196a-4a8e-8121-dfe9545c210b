package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.Field;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.pipeline.client.enums.AutomaticStatusEnum;
import com.zto.devops.pipeline.client.model.application.entity.JacocoApplicationVO;
import com.zto.devops.pipeline.client.model.application.entity.PageApplicationVO;
import com.zto.devops.pipeline.client.model.application.query.PageApplicationQuery;
import com.zto.devops.pipeline.client.model.cluster.entity.FindVersionByNamespaceVO;
import com.zto.devops.pipeline.client.model.flow.entity.VersionInfo;
import com.zto.devops.pipeline.client.model.flow.query.FindOssFileUrlQuery;
import com.zto.devops.pipeline.client.model.flow.query.FindVersionFlowDateQuery;
import com.zto.devops.pipeline.client.model.flow.query.ListFlowVersionJacocoQuery;
import com.zto.devops.pipeline.client.service.application.model.ApplicationDetailResp;
import com.zto.devops.product.client.enums.LifeCycleEnum;
import com.zto.devops.product.client.enums.MemberTypeEnum;
import com.zto.devops.product.client.enums.ProductSourceEnum;
import com.zto.devops.product.client.enums.ProductTypeEnum;
import com.zto.devops.product.client.model.product.entity.PipelineProductVO;
import com.zto.devops.product.client.model.product.entity.ProductAll;
import com.zto.devops.product.client.model.product.entity.ProductMemberVO;
import com.zto.devops.product.client.model.product.entity.ProductVO;
import com.zto.devops.product.client.model.product.entity.SimpleProductQueryListVO;
import com.zto.devops.product.client.model.product.query.FindProductByIdQuery;
import com.zto.devops.product.client.model.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.project.client.enums.requirement.CheckType;
import com.zto.devops.project.client.enums.requirement.DeliveryQuality;
import com.zto.devops.project.client.enums.requirement.DeliveryResult;
import com.zto.devops.project.client.enums.requirement.DeliverySatisfaction;
import com.zto.devops.project.client.enums.requirement.DeliveryTime;
import com.zto.devops.project.client.enums.requirement.RequirementStatus;
import com.zto.devops.project.client.enums.version.PublishStrategyEnum;
import com.zto.devops.project.client.model.requirement.entity.CCManVO;
import com.zto.devops.project.client.model.requirement.entity.SimpleRequirementVO;
import com.zto.devops.project.client.model.requirement.entity.TagPoJo;
import com.zto.devops.project.client.model.requirement.query.FindHolidayQuery;
import com.zto.devops.project.client.model.rpc.enums.FlowStatusEnum;
import com.zto.devops.project.client.model.rpc.pipeline.TestcaseByBusinessCodeVO;
import com.zto.devops.project.client.model.sprint.query.SimpleListSprintQuery;
import com.zto.devops.project.client.model.version.entity.SimpleVersionVO;
import com.zto.devops.project.client.model.version.entity.VersionVO;
import com.zto.devops.qc.client.enums.rpc.CategoryEnum;
import com.zto.devops.qc.client.enums.rpc.DeployTypeEnum;
import com.zto.devops.qc.client.enums.rpc.EnvEnum;
import com.zto.devops.qc.client.enums.rpc.FlowBusinessTypeEnum;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.rpc.LevelEnum;
import com.zto.devops.qc.client.enums.rpc.NamespaceTypeEnum;
import com.zto.devops.qc.client.enums.rpc.TestStrategyEnum;
import com.zto.devops.qc.client.enums.rpc.UsedFrequencyEnum;
import com.zto.devops.qc.client.enums.rpc.UserLevelEnum;
import com.zto.devops.qc.client.enums.rpc.UserSourceEnum;
import com.zto.devops.qc.client.enums.rpc.UserTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.model.issue.entity.HolidayVO;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationInfo;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationResp;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationVO;
import com.zto.devops.qc.client.model.rpc.pipeline.FeatureVO;
import com.zto.devops.qc.client.model.rpc.pipeline.FlowBaseDetailVO;
import com.zto.devops.qc.client.model.rpc.pipeline.FlowBranchVO;
import com.zto.devops.qc.client.model.rpc.pipeline.GeneralVO;
import com.zto.devops.qc.client.model.rpc.pipeline.JacocoInstanceVO;
import com.zto.devops.qc.client.model.rpc.pipeline.JacocoVersionVO;
import com.zto.devops.qc.client.model.rpc.pipeline.NamespaceResp;
import com.zto.devops.qc.client.model.rpc.pipeline.VersionContainSubVO;
import com.zto.devops.qc.client.model.rpc.pipeline.VersionFlowDateVO;
import com.zto.devops.qc.client.model.rpc.pipeline.VersionSimpleVO;
import com.zto.devops.qc.client.model.rpc.pipeline.query.FindVersionByNamespaceQuery;
import com.zto.devops.qc.client.model.rpc.pipeline.query.VersionReleaseBranchCommitQuery;
import com.zto.devops.qc.client.model.rpc.product.ProductAllVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.rpc.product.query.AllProductsQuery;
import com.zto.devops.qc.client.model.rpc.project.FindVersionByActualPublishDateQuery;
import com.zto.devops.qc.client.model.rpc.project.FollowerVO;
import com.zto.devops.qc.client.model.rpc.project.ProgressVO;
import com.zto.devops.qc.client.model.rpc.project.RequirementDetailVo;
import com.zto.devops.qc.client.model.rpc.project.RequirementVO;
import com.zto.devops.qc.client.model.rpc.project.ReviewVO;
import com.zto.devops.qc.client.model.rpc.project.SimpleListRequirementQuery;
import com.zto.devops.qc.client.model.rpc.project.SimpleSprintVO;
import com.zto.devops.qc.client.model.rpc.project.SimpleVersionListQuery;
import com.zto.devops.qc.client.model.rpc.project.VersionInfoVO;
import com.zto.devops.qc.client.model.rpc.project.enums.DockService;
import com.zto.devops.qc.client.model.rpc.project.enums.VersionMergeTypeEnum;
import com.zto.devops.qc.client.model.rpc.user.ListUserQuery;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ButtonVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.FieldVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackCDEvent;
import com.zto.devops.user.client.model.user.entity.UserSelectVO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class RpcVOConvertorImpl implements RpcVOConvertor {

    @Override
    public com.zto.devops.project.client.model.requirement.query.SimpleListRequirementQuery covertSimpleListRequirementQuery(SimpleListRequirementQuery query) {
        if ( query == null ) {
            return null;
        }

        com.zto.devops.project.client.model.requirement.query.SimpleListRequirementQuery simpleListRequirementQuery = new com.zto.devops.project.client.model.requirement.query.SimpleListRequirementQuery();

        List<String> list = query.getRequirementCode();
        if ( list != null ) {
            simpleListRequirementQuery.setRequirementCode( new ArrayList<String>( list ) );
        }

        return simpleListRequirementQuery;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.project.SimpleRequirementVO> covertSimpleRequirementVO(List<SimpleRequirementVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.project.SimpleRequirementVO> list = new ArrayList<com.zto.devops.qc.client.model.rpc.project.SimpleRequirementVO>( vo.size() );
        for ( SimpleRequirementVO simpleRequirementVO : vo ) {
            list.add( simpleRequirementVOToSimpleRequirementVO( simpleRequirementVO ) );
        }

        return list;
    }

    @Override
    public SimpleListSprintQuery covertSimpleListSprintQuery(com.zto.devops.qc.client.model.rpc.project.SimpleListSprintQuery query) {
        if ( query == null ) {
            return null;
        }

        SimpleListSprintQuery simpleListSprintQuery = new SimpleListSprintQuery();

        List<String> list = query.getCode();
        if ( list != null ) {
            simpleListSprintQuery.setCode( new ArrayList<String>( list ) );
        }

        return simpleListSprintQuery;
    }

    @Override
    public List<SimpleSprintVO> covertSimpleSprintVO(List<com.zto.devops.project.client.model.sprint.entity.SimpleSprintVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<SimpleSprintVO> list = new ArrayList<SimpleSprintVO>( vos.size() );
        for ( com.zto.devops.project.client.model.sprint.entity.SimpleSprintVO simpleSprintVO : vos ) {
            list.add( simpleSprintVOToSimpleSprintVO( simpleSprintVO ) );
        }

        return list;
    }

    @Override
    public com.zto.devops.project.client.model.version.query.SimpleVersionListQuery covertSimpleVersionListQuery(SimpleVersionListQuery query) {
        if ( query == null ) {
            return null;
        }

        com.zto.devops.project.client.model.version.query.SimpleVersionListQuery simpleVersionListQuery = new com.zto.devops.project.client.model.version.query.SimpleVersionListQuery();

        List<String> list = query.getCode();
        if ( list != null ) {
            simpleVersionListQuery.setCode( new ArrayList<String>( list ) );
        }

        return simpleVersionListQuery;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.project.SimpleVersionVO> covertSimpleVersionVO(List<SimpleVersionVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.project.SimpleVersionVO> list = new ArrayList<com.zto.devops.qc.client.model.rpc.project.SimpleVersionVO>( vos.size() );
        for ( SimpleVersionVO simpleVersionVO : vos ) {
            list.add( simpleVersionVOToSimpleVersionVO( simpleVersionVO ) );
        }

        return list;
    }

    @Override
    public RequirementVO convertRequirementVO(com.zto.devops.project.client.model.requirement.entity.RequirementVO vo) {
        if ( vo == null ) {
            return null;
        }

        RequirementVO requirementVO = new RequirementVO();

        requirementVO.setCode( vo.getCode() );
        requirementVO.setTitle( vo.getTitle() );
        requirementVO.setType( vo.getType() );
        requirementVO.setTypeName( vo.getTypeName() );
        requirementVO.setProductCode( vo.getProductCode() );
        requirementVO.setProductName( vo.getProductName() );
        requirementVO.setStatus( requirementStatusToRequirementStatus( vo.getStatus() ) );
        requirementVO.setPriority( vo.getPriority() );
        requirementVO.setSprintCode( vo.getSprintCode() );
        requirementVO.setCreator( vo.getCreator() );
        requirementVO.setGmtCreate( vo.getGmtCreate() );
        requirementVO.setCreatorId( vo.getCreatorId() );
        requirementVO.setLevel( vo.getLevel() );
        requirementVO.setProjectCode( vo.getProjectCode() );
        requirementVO.setDockingUserId( vo.getDockingUserId() );
        requirementVO.setDockingUserName( vo.getDockingUserName() );
        requirementVO.setDockingDeptId( vo.getDockingDeptId() );
        requirementVO.setDockingDeptName( vo.getDockingDeptName() );
        requirementVO.setVersionCode( vo.getVersionCode() );
        requirementVO.setVersionName( vo.getVersionName() );

        return requirementVO;
    }

    @Override
    public FindHolidayQuery covertFindHolidayQuery(com.zto.devops.qc.client.model.issue.query.FindHolidayQuery findHolidayQuery) {
        if ( findHolidayQuery == null ) {
            return null;
        }

        FindHolidayQuery findHolidayQuery1 = new FindHolidayQuery();

        findHolidayQuery1.setStartDate( findHolidayQuery.getStartDate() );
        findHolidayQuery1.setEndDate( findHolidayQuery.getEndDate() );
        List<Integer> list = findHolidayQuery.getType();
        if ( list != null ) {
            findHolidayQuery1.setType( new ArrayList<Integer>( list ) );
        }

        return findHolidayQuery1;
    }

    @Override
    public List<HolidayVO> covertHolidayVO(List<com.zto.devops.project.client.model.requirement.entity.HolidayVO> rpcHolidayVOS) {
        if ( rpcHolidayVOS == null ) {
            return null;
        }

        List<HolidayVO> list = new ArrayList<HolidayVO>( rpcHolidayVOS.size() );
        for ( com.zto.devops.project.client.model.requirement.entity.HolidayVO holidayVO : rpcHolidayVOS ) {
            list.add( holidayVOToHolidayVO( holidayVO ) );
        }

        return list;
    }

    @Override
    public VersionInfoVO convertVersionInfoVO(com.zto.devops.project.client.model.version.entity.VersionInfoVO vo) {
        if ( vo == null ) {
            return null;
        }

        VersionInfoVO versionInfoVO = new VersionInfoVO();

        versionInfoVO.setCode( vo.getCode() );
        versionInfoVO.setType( vo.getType() );
        versionInfoVO.setName( vo.getName() );
        versionInfoVO.setStatus( vo.getStatus() );
        versionInfoVO.setIsConfirm( vo.getIsConfirm() );
        versionInfoVO.setVersionDesc( vo.getVersionDesc() );
        versionInfoVO.setTestStrategy( vo.getTestStrategy() );
        versionInfoVO.setStartDate( vo.getStartDate() );
        versionInfoVO.setPresentationDate( vo.getPresentationDate() );
        versionInfoVO.setApprovalExitDate( vo.getApprovalExitDate() );
        versionInfoVO.setPublishDate( vo.getPublishDate() );
        versionInfoVO.setActualStartDate( vo.getActualStartDate() );
        versionInfoVO.setActualPresentationDate( vo.getActualPresentationDate() );
        versionInfoVO.setActualApprovalExitDate( vo.getActualApprovalExitDate() );
        versionInfoVO.setActualPublishDate( vo.getActualPublishDate() );

        return versionInfoVO;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.project.VersionVO convertVersionVO(VersionVO versionVO) {
        if ( versionVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.VersionVO versionVO1 = new com.zto.devops.qc.client.model.rpc.project.VersionVO();

        versionVO1.setCode( versionVO.getCode() );
        versionVO1.setProductCode( versionVO.getProductCode() );
        versionVO1.setProjectCode( versionVO.getProjectCode() );
        versionVO1.setProjectName( versionVO.getProjectName() );
        versionVO1.setType( versionVO.getType() );
        versionVO1.setName( versionVO.getName() );
        versionVO1.setVersionNum( versionVO.getVersionNum() );
        versionVO1.setStatus( versionVO.getStatus() );
        versionVO1.setIsConfirm( versionVO.getIsConfirm() );
        versionVO1.setVersionDesc( versionVO.getVersionDesc() );
        versionVO1.setTestStrategy( versionVO.getTestStrategy() );
        versionVO1.setStartDate( versionVO.getStartDate() );
        versionVO1.setPresentationDate( versionVO.getPresentationDate() );
        versionVO1.setApprovalExitDate( versionVO.getApprovalExitDate() );
        versionVO1.setPublishDate( versionVO.getPublishDate() );
        versionVO1.setActualStartDate( versionVO.getActualStartDate() );
        versionVO1.setActualPresentationDate( versionVO.getActualPresentationDate() );
        versionVO1.setActualApprovalExitDate( versionVO.getActualApprovalExitDate() );
        versionVO1.setActualPublishDate( versionVO.getActualPublishDate() );
        versionVO1.setCreateReason( versionVO.getCreateReason() );
        versionVO1.setCreateReasonDesc( versionVO.getCreateReasonDesc() );
        versionVO1.setPublishStrategy( publishStrategyEnumToPublishStrategyEnum( versionVO.getPublishStrategy() ) );
        versionVO1.setPublishStrategyName( versionVO.getPublishStrategyName() );
        versionVO1.setRiskAddedFlag( versionVO.getRiskAddedFlag() );
        versionVO1.setProductName( versionVO.getProductName() );
        versionVO1.setDeptName( versionVO.getDeptName() );
        versionVO1.setCreatorId( versionVO.getCreatorId() );
        versionVO1.setCreator( versionVO.getCreator() );
        versionVO1.setGmtCreate( versionVO.getGmtCreate() );
        versionVO1.setModifierId( versionVO.getModifierId() );
        versionVO1.setModifier( versionVO.getModifier() );
        versionVO1.setGmtModified( versionVO.getGmtModified() );
        List<Button> list = versionVO.getButtonVOS();
        if ( list != null ) {
            versionVO1.setButtonVOS( new ArrayList<Button>( list ) );
        }
        List<Field> list1 = versionVO.getFieldVOS();
        if ( list1 != null ) {
            versionVO1.setFieldVOS( new ArrayList<Field>( list1 ) );
        }
        versionVO1.setVNum1( versionVO.getVNum1() );
        versionVO1.setVNum2( versionVO.getVNum2() );
        versionVO1.setVNum3( versionVO.getVNum3() );
        versionVO1.setSort( versionVO.isSort() );
        versionVO1.setWhetherFail( versionVO.getWhetherFail() );
        versionVO1.setCodeReview( versionVO.getCodeReview() );
        versionVO1.setNeedUiTest( versionVO.getNeedUiTest() );
        versionVO1.setVerName( versionVO.getVerName() );
        versionVO1.setMergeType( versionMergeTypeEnumToVersionMergeTypeEnum( versionVO.getMergeType() ) );
        versionVO1.setSubVersions( convertVersionVOS( versionVO.getSubVersions() ) );

        return versionVO1;
    }

    @Override
    public com.zto.devops.user.client.model.user.query.ListUserQuery covertListUserQuery(ListUserQuery query) {
        if ( query == null ) {
            return null;
        }

        com.zto.devops.user.client.model.user.query.ListUserQuery listUserQuery = new com.zto.devops.user.client.model.user.query.ListUserQuery();

        listUserQuery.setCnName( query.getCnName() );
        listUserQuery.setBizCode( query.getBizCode() );
        List<String> list = query.getBizCodeList();
        if ( list != null ) {
            listUserQuery.setBizCodeList( new ArrayList<String>( list ) );
        }
        List<String> list1 = query.getDeptNoList();
        if ( list1 != null ) {
            listUserQuery.setDeptNoList( new ArrayList<String>( list1 ) );
        }
        List<Integer> list2 = query.getStatusList();
        if ( list2 != null ) {
            listUserQuery.setStatusList( new ArrayList<Integer>( list2 ) );
        }
        listUserQuery.setDeptId( query.getDeptId() );
        List<Long> list3 = query.getDeptIds();
        if ( list3 != null ) {
            listUserQuery.setDeptIds( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = query.getSsoUserIdList();
        if ( list4 != null ) {
            listUserQuery.setSsoUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = query.getZtoAccountList();
        if ( list5 != null ) {
            listUserQuery.setZtoAccountList( new ArrayList<String>( list5 ) );
        }
        List<String> list6 = query.getDevAccountList();
        if ( list6 != null ) {
            listUserQuery.setDevAccountList( new ArrayList<String>( list6 ) );
        }
        listUserQuery.setRows( query.getRows() );
        listUserQuery.setNameSpelling( query.getNameSpelling() );
        listUserQuery.setSimpleSpelling( query.getSimpleSpelling() );
        listUserQuery.setFullSpelling( query.getFullSpelling() );

        return listUserQuery;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.user.UserSelectVO> covertUserSelectVO(List<UserSelectVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.user.UserSelectVO> list = new ArrayList<com.zto.devops.qc.client.model.rpc.user.UserSelectVO>( vos.size() );
        for ( UserSelectVO userSelectVO : vos ) {
            list.add( userSelectVOToUserSelectVO( userSelectVO ) );
        }

        return list;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> converterProductMemberVOList(List<ProductMemberVO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> list = new ArrayList<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO>( voList.size() );
        for ( ProductMemberVO productMemberVO : voList ) {
            list.add( productMemberVOToProductMemberVO( productMemberVO ) );
        }

        return list;
    }

    @Override
    public SimpleQueryVO convertSimpleQueryVO(com.zto.devops.product.client.model.product.entity.SimpleQueryVO simpleQueryVO) {
        if ( simpleQueryVO == null ) {
            return null;
        }

        SimpleQueryVO simpleQueryVO1 = new SimpleQueryVO();

        simpleQueryVO1.setProductCode( simpleQueryVO.getProductCode() );
        simpleQueryVO1.setProductName( simpleQueryVO.getProductName() );
        simpleQueryVO1.setSerialId( simpleQueryVO.getSerialId() );
        simpleQueryVO1.setProductLineCode( simpleQueryVO.getProductLineCode() );
        simpleQueryVO1.setDeptId( simpleQueryVO.getDeptId() );
        simpleQueryVO1.setDeptName( simpleQueryVO.getDeptName() );
        simpleQueryVO1.setDeptNo( simpleQueryVO.getDeptNo() );
        simpleQueryVO1.setDescription( simpleQueryVO.getDescription() );
        simpleQueryVO1.setAvatar( simpleQueryVO.getAvatar() );
        simpleQueryVO1.setType( productTypeEnumToProductTypeEnum( simpleQueryVO.getType() ) );
        simpleQueryVO1.setLevel( levelEnumToLevelEnum( simpleQueryVO.getLevel() ) );
        simpleQueryVO1.setCategory( categoryEnumToCategoryEnum( simpleQueryVO.getCategory() ) );
        simpleQueryVO1.setLifeCycle( lifeCycleEnumToLifeCycleEnum( simpleQueryVO.getLifeCycle() ) );
        simpleQueryVO1.setUserSource( userSourceEnumToUserSourceEnum( simpleQueryVO.getUserSource() ) );
        simpleQueryVO1.setProductSource( productSourceEnumToProductSourceEnum( simpleQueryVO.getProductSource() ) );
        simpleQueryVO1.setUserType( userTypeEnumToUserTypeEnum( simpleQueryVO.getUserType() ) );
        simpleQueryVO1.setUserNumberLevel( userLevelEnumToUserLevelEnum( simpleQueryVO.getUserNumberLevel() ) );
        simpleQueryVO1.setUseFrequency( usedFrequencyEnumToUsedFrequencyEnum( simpleQueryVO.getUseFrequency() ) );
        simpleQueryVO1.setProductUserId( simpleQueryVO.getProductUserId() );
        simpleQueryVO1.setProductUserName( simpleQueryVO.getProductUserName() );
        simpleQueryVO1.setDevelopUserId( simpleQueryVO.getDevelopUserId() );
        simpleQueryVO1.setDevelopUserName( simpleQueryVO.getDevelopUserName() );
        simpleQueryVO1.setProjectUserId( simpleQueryVO.getProjectUserId() );
        simpleQueryVO1.setProjectUserName( simpleQueryVO.getProjectUserName() );
        simpleQueryVO1.setTestUserId( simpleQueryVO.getTestUserId() );
        simpleQueryVO1.setTestUserName( simpleQueryVO.getTestUserName() );
        simpleQueryVO1.setTestData( simpleQueryVO.getTestData() );
        simpleQueryVO1.setFreeze( simpleQueryVO.getFreeze() );
        simpleQueryVO1.setIsMiddleware( simpleQueryVO.getIsMiddleware() );
        simpleQueryVO1.setIsReview( simpleQueryVO.getIsReview() );
        simpleQueryVO1.setGrade( simpleQueryVO.getGrade() );

        return simpleQueryVO1;
    }

    @Override
    public ListProductMemberByPIdQuery convertProductMemberQuery(com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery simpleQueryVO) {
        if ( simpleQueryVO == null ) {
            return null;
        }

        ListProductMemberByPIdQuery listProductMemberByPIdQuery = new ListProductMemberByPIdQuery();

        listProductMemberByPIdQuery.setProductCode( simpleQueryVO.getProductCode() );
        List<String> list = simpleQueryVO.getMemberTypes();
        if ( list != null ) {
            listProductMemberByPIdQuery.setMemberTypes( new ArrayList<String>( list ) );
        }

        return listProductMemberByPIdQuery;
    }

    @Override
    public FindOssFileUrlQuery convertFindOssFileUrlQuery(com.zto.devops.qc.client.model.rpc.pipeline.query.FindOssFileUrlQuery query) {
        if ( query == null ) {
            return null;
        }

        FindOssFileUrlQuery findOssFileUrlQuery = new FindOssFileUrlQuery();

        findOssFileUrlQuery.setFileName( query.getFileName() );

        return findOssFileUrlQuery;
    }

    @Override
    public ListFlowVersionJacocoQuery convertListFlowVersionJacocoQuery(com.zto.devops.qc.client.model.rpc.pipeline.query.ListFlowVersionJacocoQuery query) {
        if ( query == null ) {
            return null;
        }

        ListFlowVersionJacocoQuery listFlowVersionJacocoQuery = new ListFlowVersionJacocoQuery();

        listFlowVersionJacocoQuery.setFlowCode( query.getFlowCode() );

        return listFlowVersionJacocoQuery;
    }

    @Override
    public JacocoVersionVO convertJacocoVersionVO(com.zto.devops.pipeline.client.model.flow.entity.JacocoVersionVO jacocoVersionVO) {
        if ( jacocoVersionVO == null ) {
            return null;
        }

        JacocoVersionVO jacocoVersionVO1 = new JacocoVersionVO();

        jacocoVersionVO1.setVersions( versionInfoListToVersionInfoList( jacocoVersionVO.getVersions() ) );

        return jacocoVersionVO1;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO> convertJacocoApplicationVOList(List<JacocoApplicationVO> jacocoVersionVO) {
        if ( jacocoVersionVO == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO> list = new ArrayList<com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO>( jacocoVersionVO.size() );
        for ( JacocoApplicationVO jacocoApplicationVO : jacocoVersionVO ) {
            list.add( jacocoApplicationVOToJacocoApplicationVO( jacocoApplicationVO ) );
        }

        return list;
    }

    @Override
    public PageApplicationQuery convertPageApplicationQuery(com.zto.devops.qc.client.model.rpc.pipeline.query.PageApplicationQuery query) {
        if ( query == null ) {
            return null;
        }

        PageApplicationQuery pageApplicationQuery = new PageApplicationQuery();

        pageApplicationQuery.setTransactor( query.getTransactor() );
        pageApplicationQuery.setPage( query.getPage() );
        pageApplicationQuery.setSize( query.getSize() );
        pageApplicationQuery.setProductCode( query.getProductCode() );
        List<String> list = query.getApplicationTypeCode();
        if ( list != null ) {
            pageApplicationQuery.setApplicationTypeCode( new ArrayList<String>( list ) );
        }
        pageApplicationQuery.setName( query.getName() );
        pageApplicationQuery.setAppId( query.getAppId() );
        List<Long> list1 = query.getAlerterIds();
        if ( list1 != null ) {
            pageApplicationQuery.setAlerterIds( new ArrayList<Long>( list1 ) );
        }
        pageApplicationQuery.setNeedPermissions( query.getNeedPermissions() );
        List<Boolean> list2 = query.getWhiteList();
        if ( list2 != null ) {
            pageApplicationQuery.setWhiteList( new ArrayList<Boolean>( list2 ) );
        }

        return pageApplicationQuery;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.pipeline.PageApplicationVO convertPageApplicationVO(PageApplicationVO pageApplicationVO) {
        if ( pageApplicationVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.PageApplicationVO pageApplicationVO1 = new com.zto.devops.qc.client.model.rpc.pipeline.PageApplicationVO();

        pageApplicationVO1.setList( convertApplicationVOList( pageApplicationVO.getList() ) );
        pageApplicationVO1.setTotal( pageApplicationVO.getTotal() );

        return pageApplicationVO1;
    }

    @Override
    public List<RequirementDetailVo> convertRequirementDetailVo(List<com.zto.devops.project.client.model.requirement.entity.RequirementDetailVo> requirementByCodeListQuery) {
        if ( requirementByCodeListQuery == null ) {
            return null;
        }

        List<RequirementDetailVo> list = new ArrayList<RequirementDetailVo>( requirementByCodeListQuery.size() );
        for ( com.zto.devops.project.client.model.requirement.entity.RequirementDetailVo requirementDetailVo : requirementByCodeListQuery ) {
            list.add( requirementDetailVoToRequirementDetailVo( requirementDetailVo ) );
        }

        return list;
    }

    @Override
    public FindVersionFlowDateQuery coverterFindVersionFlowDateQuery(com.zto.devops.qc.client.model.rpc.pipeline.query.FindVersionFlowDateQuery query) {
        if ( query == null ) {
            return null;
        }

        FindVersionFlowDateQuery findVersionFlowDateQuery = new FindVersionFlowDateQuery();

        findVersionFlowDateQuery.setVersionCode( query.getVersionCode() );

        return findVersionFlowDateQuery;
    }

    @Override
    public VersionFlowDateVO coverterVersionFlowDateVO(com.zto.devops.pipeline.client.model.flow.entity.VersionFlowDateVO vo) {
        if ( vo == null ) {
            return null;
        }

        VersionFlowDateVO versionFlowDateVO = new VersionFlowDateVO();

        versionFlowDateVO.setDevelopDate( vo.getDevelopDate() );
        versionFlowDateVO.setSubmitTestDate( vo.getSubmitTestDate() );
        versionFlowDateVO.setReleaseDate( vo.getReleaseDate() );
        versionFlowDateVO.setTestAccessDate( vo.getTestAccessDate() );
        versionFlowDateVO.setTestExitDate( vo.getTestExitDate() );

        return versionFlowDateVO;
    }

    @Override
    public FindProductByIdQuery coverterFindProductByIdQuery(com.zto.devops.qc.client.model.rpc.product.query.FindProductByIdQuery query) {
        if ( query == null ) {
            return null;
        }

        FindProductByIdQuery findProductByIdQuery = new FindProductByIdQuery();

        findProductByIdQuery.setCode( query.getCode() );

        return findProductByIdQuery;
    }

    @Override
    public com.zto.devops.product.client.model.product.query.AllProductsQuery convertAllProductsQuery(AllProductsQuery query) {
        if ( query == null ) {
            return null;
        }

        com.zto.devops.product.client.model.product.query.AllProductsQuery allProductsQuery = new com.zto.devops.product.client.model.product.query.AllProductsQuery();

        allProductsQuery.setTransactor( query.getTransactor() );
        allProductsQuery.setPage( query.getPage() );
        allProductsQuery.setSize( query.getSize() );

        return allProductsQuery;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.product.ProductVO convertProductVO(ProductVO vo) {
        if ( vo == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.product.ProductVO productVO = new com.zto.devops.qc.client.model.rpc.product.ProductVO();

        productVO.setProductLineName( vo.getProductLineName() );
        productVO.setProductLineCode( vo.getProductLineCode() );
        productVO.setCode( vo.getCode() );
        productVO.setName( vo.getName() );
        productVO.setAvatar( vo.getAvatar() );
        productVO.setDescription( vo.getDescription() );
        productVO.setType( productTypeEnumToProductTypeEnum( vo.getType() ) );
        productVO.setTypeDesc( vo.getTypeDesc() );
        productVO.setLevel( levelEnumToLevelEnum( vo.getLevel() ) );
        productVO.setLevelDesc( vo.getLevelDesc() );
        productVO.setCategory( categoryEnumToCategoryEnum( vo.getCategory() ) );
        productVO.setCategoryDesc( vo.getCategoryDesc() );
        productVO.setLifeCycle( lifeCycleEnumToLifeCycleEnum( vo.getLifeCycle() ) );
        productVO.setLifeCycleDesc( vo.getLifeCycleDesc() );
        productVO.setUserSource( userSourceEnumToUserSourceEnum( vo.getUserSource() ) );
        productVO.setUserSourceDesc( vo.getUserSourceDesc() );
        productVO.setUserType( userTypeEnumToUserTypeEnum( vo.getUserType() ) );
        productVO.setUserTypeDesc( vo.getUserTypeDesc() );
        productVO.setProductSource( productSourceEnumToProductSourceEnum( vo.getProductSource() ) );
        productVO.setProductSourceDesc( vo.getProductSourceDesc() );
        productVO.setUserNumberLevel( userLevelEnumToUserLevelEnum( vo.getUserNumberLevel() ) );
        productVO.setUserNumberLevelDesc( vo.getUserNumberLevelDesc() );
        productVO.setUseFrequency( usedFrequencyEnumToUsedFrequencyEnum( vo.getUseFrequency() ) );
        productVO.setUseFrequencyDesc( vo.getUseFrequencyDesc() );
        productVO.setProductUserName( vo.getProductUserName() );
        productVO.setProductUserId( vo.getProductUserId() );
        productVO.setDevelopUserName( vo.getDevelopUserName() );
        productVO.setDevelopUserId( vo.getDevelopUserId() );
        productVO.setProjectUserName( vo.getProjectUserName() );
        productVO.setProjectUserId( vo.getProjectUserId() );
        productVO.setTestUserName( vo.getTestUserName() );
        productVO.setTestUserId( vo.getTestUserId() );
        productVO.setTestData( vo.getTestData() );
        productVO.setTestDataDesc( vo.getTestDataDesc() );
        productVO.setDeptId( vo.getDeptId() );
        productVO.setDeptName( vo.getDeptName() );
        productVO.setDeptNo( vo.getDeptNo() );
        productVO.setIsMiddleware( vo.getIsMiddleware() );
        productVO.setIsTest( vo.getIsTest() );
        productVO.setFreeze( vo.getFreeze() );
        productVO.setTenantCode( vo.getTenantCode() );

        return productVO;
    }

    @Override
    public ProductAllVO convertProductAllVO(com.zto.devops.product.client.model.product.entity.ProductAllVO vo) {
        if ( vo == null ) {
            return null;
        }

        ProductAllVO productAllVO = new ProductAllVO();

        productAllVO.setList( productAllListToProductAllList( vo.getList() ) );
        productAllVO.setTotal( vo.getTotal() );

        return productAllVO;
    }

    @Override
    public NamespaceResp covertNamespaceResp(com.zto.devops.pipeline.client.service.inner.model.NamespaceResp vo) {
        if ( vo == null ) {
            return null;
        }

        NamespaceResp namespaceResp = new NamespaceResp();

        namespaceResp.setCode( vo.getCode() );
        namespaceResp.setName( vo.getName() );
        namespaceResp.setTag( vo.getTag() );

        return namespaceResp;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.product.PipelineProductVO> convertPipelineProductVO(List<PipelineProductVO> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.product.PipelineProductVO> list1 = new ArrayList<com.zto.devops.qc.client.model.rpc.product.PipelineProductVO>( list.size() );
        for ( PipelineProductVO pipelineProductVO : list ) {
            list1.add( pipelineProductVOToPipelineProductVO( pipelineProductVO ) );
        }

        return list1;
    }

    @Override
    public List<ApplicationResp> convertApplicationResp(List<com.zto.devops.pipeline.client.service.inner.model.ApplicationResp> list) {
        if ( list == null ) {
            return null;
        }

        List<ApplicationResp> list1 = new ArrayList<ApplicationResp>( list.size() );
        for ( com.zto.devops.pipeline.client.service.inner.model.ApplicationResp applicationResp : list ) {
            list1.add( applicationRespToApplicationResp( applicationResp ) );
        }

        return list1;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> convertProductMemberVO(List<ProductMemberVO> productMembers) {
        if ( productMembers == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> list = new ArrayList<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO>( productMembers.size() );
        for ( ProductMemberVO productMemberVO : productMembers ) {
            list.add( productMemberVOToProductMemberVO( productMemberVO ) );
        }

        return list;
    }

    @Override
    public com.zto.devops.pipeline.client.model.flow.event.ExecuteCallbackCDEvent convertExecuteCallbackCDEvent(ExecuteCallbackCDEvent event) {
        if ( event == null ) {
            return null;
        }

        com.zto.devops.pipeline.client.model.flow.event.ExecuteCallbackCDEvent executeCallbackCDEvent = new com.zto.devops.pipeline.client.model.flow.event.ExecuteCallbackCDEvent();

        executeCallbackCDEvent.setAggregateId( event.getAggregateId() );
        executeCallbackCDEvent.setEventType( event.getEventType() );
        executeCallbackCDEvent.setTransactor( event.getTransactor() );
        executeCallbackCDEvent.setOccurred( event.getOccurred() );
        executeCallbackCDEvent.setTaskId( event.getTaskId() );
        executeCallbackCDEvent.setStatus( automaticStatusEnumToAutomaticStatusEnum( event.getStatus() ) );
        executeCallbackCDEvent.setFlowCode( event.getFlowCode() );
        executeCallbackCDEvent.setTotalCaseCount( event.getTotalCaseCount() );
        executeCallbackCDEvent.setPassedCaseCount( event.getPassedCaseCount() );
        executeCallbackCDEvent.setFailedCaseCount( event.getFailedCaseCount() );
        executeCallbackCDEvent.setTerminatedCaseCount( event.getTerminatedCaseCount() );
        executeCallbackCDEvent.setOtherCaseCount( event.getOtherCaseCount() );
        executeCallbackCDEvent.setNodeCode( event.getNodeCode() );

        return executeCallbackCDEvent;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.product.SimpleProductQueryListVO convertSimpleQueryVO(SimpleProductQueryListVO simpleProductQueryListVO) {
        if ( simpleProductQueryListVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.product.SimpleProductQueryListVO simpleProductQueryListVO1 = new com.zto.devops.qc.client.model.rpc.product.SimpleProductQueryListVO();

        simpleProductQueryListVO1.setSimpleQueryVOlist( simpleQueryVOListToSimpleQueryVOList( simpleProductQueryListVO.getSimpleQueryVOlist() ) );

        return simpleProductQueryListVO1;
    }

    @Override
    public FlowBranchVO convertFlowBranchVO(com.zto.devops.pipeline.client.model.application.entity.FlowBranchVO flowBranchVO) {
        if ( flowBranchVO == null ) {
            return null;
        }

        FlowBranchVO flowBranchVO1 = new FlowBranchVO();

        flowBranchVO1.setFlowCode( flowBranchVO.getFlowCode() );
        flowBranchVO1.setFeatureBranch( flowBranchVO.getFeatureBranch() );
        flowBranchVO1.setReleaseBranch( flowBranchVO.getReleaseBranch() );

        return flowBranchVO1;
    }

    @Override
    public List<FeatureVO> convertFeatureVOList(List<com.zto.devops.pipeline.client.model.plan.entity.FeatureVO> list) {
        if ( list == null ) {
            return null;
        }

        List<FeatureVO> list1 = new ArrayList<FeatureVO>( list.size() );
        for ( com.zto.devops.pipeline.client.model.plan.entity.FeatureVO featureVO : list ) {
            list1.add( featureVOToFeatureVO( featureVO ) );
        }

        return list1;
    }

    @Override
    public FlowBaseDetailVO convertFlowDetail(com.zto.devops.pipeline.client.model.flow.entity.FlowBaseDetailVO vo) {
        if ( vo == null ) {
            return null;
        }

        FlowBaseDetailVO flowBaseDetailVO = new FlowBaseDetailVO();

        flowBaseDetailVO.setProductCode( vo.getProductCode() );
        flowBaseDetailVO.setFlowBusinessType( flowBusinessTypeEnumToFlowBusinessTypeEnum( vo.getFlowBusinessType() ) );
        flowBaseDetailVO.setFlowCode( vo.getFlowCode() );
        flowBaseDetailVO.setFlowName( vo.getFlowName() );
        flowBaseDetailVO.setFlowStatus( flowStatusEnumToFlowStatusEnum1( vo.getFlowStatus() ) );
        flowBaseDetailVO.setFlowStatusDesc( vo.getFlowStatusDesc() );
        flowBaseDetailVO.setFeatureBranch( vo.getFeatureBranch() );
        flowBaseDetailVO.setFeatureBranchLockType( vo.getFeatureBranchLockType() );
        flowBaseDetailVO.setFeatureBranchLockMessage( vo.getFeatureBranchLockMessage() );
        flowBaseDetailVO.setBackRemark( vo.getBackRemark() );
        flowBaseDetailVO.setAcceptRemark( vo.getAcceptRemark() );
        flowBaseDetailVO.setCodeReview( vo.getCodeReview() );
        flowBaseDetailVO.setVersionCode( vo.getVersionCode() );
        flowBaseDetailVO.setVersionName( vo.getVersionName() );
        flowBaseDetailVO.setPlanSubmitTestDate( vo.getPlanSubmitTestDate() );
        flowBaseDetailVO.setPlanOnlineDate( vo.getPlanOnlineDate() );
        flowBaseDetailVO.setDesc( vo.getDesc() );

        return flowBaseDetailVO;
    }

    @Override
    public com.zto.devops.project.client.model.version.query.FindVersionByActualPublishDateQuery covertActualPublishDateQuery(FindVersionByActualPublishDateQuery query) {
        if ( query == null ) {
            return null;
        }

        com.zto.devops.project.client.model.version.query.FindVersionByActualPublishDateQuery findVersionByActualPublishDateQuery = new com.zto.devops.project.client.model.version.query.FindVersionByActualPublishDateQuery();

        findVersionByActualPublishDateQuery.setProductCode( query.getProductCode() );
        findVersionByActualPublishDateQuery.setBeginTime( query.getBeginTime() );
        findVersionByActualPublishDateQuery.setEndTime( query.getEndTime() );

        return findVersionByActualPublishDateQuery;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.project.VersionVO> convertVersionVOS(List<VersionVO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.project.VersionVO> list = new ArrayList<com.zto.devops.qc.client.model.rpc.project.VersionVO>( voList.size() );
        for ( VersionVO versionVO : voList ) {
            list.add( convertVersionVO( versionVO ) );
        }

        return list;
    }

    @Override
    public VersionSimpleVO covertVersionSimpleVO(com.zto.devops.pipeline.client.model.flow.entity.VersionSimpleVO vo) {
        if ( vo == null ) {
            return null;
        }

        VersionSimpleVO versionSimpleVO = new VersionSimpleVO();

        versionSimpleVO.setVersionCode( vo.getVersionCode() );
        versionSimpleVO.setName( vo.getName() );
        versionSimpleVO.setVersionNum( vo.getVersionNum() );
        versionSimpleVO.setTag( vo.getTag() );

        return versionSimpleVO;
    }

    @Override
    public com.zto.devops.pipeline.client.model.cluster.query.FindVersionByNamespaceQuery convert(FindVersionByNamespaceQuery query) {
        if ( query == null ) {
            return null;
        }

        com.zto.devops.pipeline.client.model.cluster.query.FindVersionByNamespaceQuery findVersionByNamespaceQuery = new com.zto.devops.pipeline.client.model.cluster.query.FindVersionByNamespaceQuery();

        findVersionByNamespaceQuery.setProductCode( query.getProductCode() );
        findVersionByNamespaceQuery.setName( query.getName() );

        return findVersionByNamespaceQuery;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.pipeline.FindVersionByNamespaceVO convert(FindVersionByNamespaceVO vo) {
        if ( vo == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.FindVersionByNamespaceVO findVersionByNamespaceVO = new com.zto.devops.qc.client.model.rpc.pipeline.FindVersionByNamespaceVO();

        findVersionByNamespaceVO.setProductCode( vo.getProductCode() );
        findVersionByNamespaceVO.setVersionCode( vo.getVersionCode() );
        findVersionByNamespaceVO.setVersionName( vo.getVersionName() );
        findVersionByNamespaceVO.setFlowCode( vo.getFlowCode() );

        return findVersionByNamespaceVO;
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.pipeline.ApplicationDetailResp convert(ApplicationDetailResp resp) {
        if ( resp == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.ApplicationDetailResp applicationDetailResp = new com.zto.devops.qc.client.model.rpc.pipeline.ApplicationDetailResp();

        applicationDetailResp.setCode( resp.getCode() );
        applicationDetailResp.setProductCode( resp.getProductCode() );
        applicationDetailResp.setProductName( resp.getProductName() );
        applicationDetailResp.setName( resp.getName() );
        applicationDetailResp.setAppId( resp.getAppId() );
        applicationDetailResp.setApolloAppId( resp.getApolloAppId() );
        applicationDetailResp.setApplicationTypeCode( resp.getApplicationTypeCode() );
        applicationDetailResp.setApplicationTypeName( resp.getApplicationTypeName() );
        applicationDetailResp.setDescription( resp.getDescription() );
        applicationDetailResp.setGitProjectUrl( resp.getGitProjectUrl() );
        applicationDetailResp.setGitProjectId( resp.getGitProjectId() );
        applicationDetailResp.setBuildParams( resp.getBuildParams() );
        applicationDetailResp.setDeploymentParam( resp.getDeploymentParam() );
        applicationDetailResp.setDefaultDeploymentParam( resp.getDefaultDeploymentParam() );
        applicationDetailResp.setTypeInput( resp.getTypeInput() );
        applicationDetailResp.setBaseImage( resp.getBaseImage() );
        applicationDetailResp.setJvmParam( resp.getJvmParam() );
        applicationDetailResp.setFirstAlerter( resp.getFirstAlerter() );
        applicationDetailResp.setSecondAlerter( resp.getSecondAlerter() );
        applicationDetailResp.setCoverageStandardValue( resp.getCoverageStandardValue() );
        applicationDetailResp.setWhiteList( resp.getWhiteList() );
        applicationDetailResp.setWhiteListReason( resp.getWhiteListReason() );
        applicationDetailResp.setBackButtonList( generalVOListToGeneralVOList( resp.getBackButtonList() ) );
        applicationDetailResp.setCanAddDomain( resp.getCanAddDomain() );
        List<String> list1 = resp.getAppletReleaseChannelList();
        if ( list1 != null ) {
            applicationDetailResp.setAppletReleaseChannelList( new ArrayList<String>( list1 ) );
        }
        applicationDetailResp.setAppletFatQrCodeLink( resp.getAppletFatQrCodeLink() );
        applicationDetailResp.setAppletProQrCodeLink( resp.getAppletProQrCodeLink() );
        applicationDetailResp.setCoreApplication( resp.getCoreApplication() );
        List<String> list2 = resp.getMainDevelopers();
        if ( list2 != null ) {
            applicationDetailResp.setMainDevelopers( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = resp.getMainTester();
        if ( list3 != null ) {
            applicationDetailResp.setMainTester( new ArrayList<String>( list3 ) );
        }
        List<String> list4 = resp.getMembers();
        if ( list4 != null ) {
            applicationDetailResp.setMembers( new ArrayList<String>( list4 ) );
        }

        return applicationDetailResp;
    }

    @Override
    public List<ApplicationVO> convertApplicationVOList(List<com.zto.devops.pipeline.client.model.application.entity.ApplicationVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ApplicationVO> list1 = new ArrayList<ApplicationVO>( list.size() );
        for ( com.zto.devops.pipeline.client.model.application.entity.ApplicationVO applicationVO : list ) {
            list1.add( applicationVOToApplicationVO( applicationVO ) );
        }

        return list1;
    }

    @Override
    public VersionContainSubVO convertVersionContainSubVO(com.zto.devops.pipeline.client.model.flow.entity.VersionContainSubVO versionByVersionCodeAndAppId) {
        if ( versionByVersionCodeAndAppId == null ) {
            return null;
        }

        VersionContainSubVO versionContainSubVO = new VersionContainSubVO();

        versionContainSubVO.setCode( versionByVersionCodeAndAppId.getCode() );
        versionContainSubVO.setName( versionByVersionCodeAndAppId.getName() );
        versionContainSubVO.setProductCode( versionByVersionCodeAndAppId.getProductCode() );
        versionContainSubVO.setVersionCode( versionByVersionCodeAndAppId.getVersionCode() );
        versionContainSubVO.setMergeType( versionByVersionCodeAndAppId.getMergeType() );
        List<String> list = versionByVersionCodeAndAppId.getSubVersionCodes();
        if ( list != null ) {
            versionContainSubVO.setSubVersionCodes( new ArrayList<String>( list ) );
        }

        return versionContainSubVO;
    }

    @Override
    public com.zto.devops.pipeline.client.model.flow.query.VersionReleaseBranchCommitQuery convert(VersionReleaseBranchCommitQuery query) {
        if ( query == null ) {
            return null;
        }

        com.zto.devops.pipeline.client.model.flow.query.VersionReleaseBranchCommitQuery versionReleaseBranchCommitQuery = new com.zto.devops.pipeline.client.model.flow.query.VersionReleaseBranchCommitQuery();

        versionReleaseBranchCommitQuery.setVersionCode( query.getVersionCode() );
        versionReleaseBranchCommitQuery.setAppId( query.getAppId() );

        return versionReleaseBranchCommitQuery;
    }

    protected com.zto.devops.qc.client.model.rpc.project.SimpleRequirementVO simpleRequirementVOToSimpleRequirementVO(SimpleRequirementVO simpleRequirementVO) {
        if ( simpleRequirementVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.SimpleRequirementVO simpleRequirementVO1 = new com.zto.devops.qc.client.model.rpc.project.SimpleRequirementVO();

        simpleRequirementVO1.setProductCode( simpleRequirementVO.getProductCode() );
        simpleRequirementVO1.setProductName( simpleRequirementVO.getProductName() );
        simpleRequirementVO1.setTitle( simpleRequirementVO.getTitle() );
        simpleRequirementVO1.setCode( simpleRequirementVO.getCode() );
        simpleRequirementVO1.setType( simpleRequirementVO.getType() );
        simpleRequirementVO1.setPriority( simpleRequirementVO.getPriority() );

        return simpleRequirementVO1;
    }

    protected SimpleSprintVO simpleSprintVOToSimpleSprintVO(com.zto.devops.project.client.model.sprint.entity.SimpleSprintVO simpleSprintVO) {
        if ( simpleSprintVO == null ) {
            return null;
        }

        SimpleSprintVO simpleSprintVO1 = new SimpleSprintVO();

        simpleSprintVO1.setCode( simpleSprintVO.getCode() );
        simpleSprintVO1.setName( simpleSprintVO.getName() );
        simpleSprintVO1.setDescription( simpleSprintVO.getDescription() );
        simpleSprintVO1.setProductCode( simpleSprintVO.getProductCode() );
        simpleSprintVO1.setProductName( simpleSprintVO.getProductName() );

        return simpleSprintVO1;
    }

    protected com.zto.devops.qc.client.enums.rpc.FlowStatusEnum flowStatusEnumToFlowStatusEnum(FlowStatusEnum flowStatusEnum) {
        if ( flowStatusEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.FlowStatusEnum flowStatusEnum1;

        switch ( flowStatusEnum ) {
            case START: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.START;
            break;
            case END: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.END;
            break;
            case SCHEDULING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.SCHEDULING;
            break;
            case VERSION_MERGING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.VERSION_MERGING;
            break;
            case VERSION_MERGE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.VERSION_MERGE_FAILED;
            break;
            case DEVELOPING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.DEVELOPING;
            break;
            case WAIT_MERGE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_MERGE;
            break;
            case MERGE_TO_TEST: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_TEST;
            break;
            case MERGE_TO_PROD: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_PROD;
            break;
            case MERGE_TO_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_RELEASE;
            break;
            case MERGE_TRUNK: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TRUNK;
            break;
            case SMOKING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.SMOKING;
            break;
            case TESTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.TESTING;
            break;
            case WAIT_REGRESS: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_REGRESS;
            break;
            case ITERATING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ITERATING;
            break;
            case REGRESSING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSING;
            break;
            case REGRESSING_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSING_AUDIT;
            break;
            case REGRESSED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSED;
            break;
            case REGRESS_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESS_FAILED;
            break;
            case WAIT_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_AUDIT;
            break;
            case AUDIT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.AUDIT_FAILED;
            break;
            case WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_RELEASE;
            break;
            case RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.RELEASING;
            break;
            case RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.RELEASE_FAILED;
            break;
            case BACKUP_APPLY_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_APPLY_RELEASE;
            break;
            case BACKUP_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_WAIT_RELEASE;
            break;
            case BACKUP_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_RELEASING;
            break;
            case BACKUP_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_RELEASE_FAILED;
            break;
            case ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTING;
            break;
            case DELAY_ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.DELAY_ACCEPTING;
            break;
            case ACCEPTED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED;
            break;
            case ACCEPTED_WAIT_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_WAIT_AUDIT;
            break;
            case ACCEPTED_AUDIT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_AUDIT_FAILED;
            break;
            case ACCEPTED_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_WAIT_RELEASE;
            break;
            case ACCEPTED_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_RELEASING;
            break;
            case ACCEPTED_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_RELEASE_FAILED;
            break;
            case BACKUP_ACCEPTED_APPLY_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_APPLY_RELEASE;
            break;
            case BACKUP_ACCEPTED_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_WAIT_RELEASE;
            break;
            case BACKUP_ACCEPTED_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_RELEASING;
            break;
            case BACKUP_ACCEPTED_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_RELEASE_FAILED;
            break;
            case ACCEPTED_ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_ACCEPTING;
            break;
            case ARCHIVED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ARCHIVED;
            break;
            case CLOSED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.CLOSED;
            break;
            case BACKED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKED;
            break;
            case ACCEPT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPT_FAILED;
            break;
            case APPLICATION_CHECK: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.APPLICATION_CHECK;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + flowStatusEnum );
        }

        return flowStatusEnum1;
    }

    protected TestStrategyEnum testStrategyEnumToTestStrategyEnum(com.zto.devops.project.client.enums.version.TestStrategyEnum testStrategyEnum) {
        if ( testStrategyEnum == null ) {
            return null;
        }

        TestStrategyEnum testStrategyEnum1;

        switch ( testStrategyEnum ) {
            case EXPLORE_TEST: testStrategyEnum1 = TestStrategyEnum.EXPLORE_TEST;
            break;
            case ACCEPTANCE_TEST: testStrategyEnum1 = TestStrategyEnum.ACCEPTANCE_TEST;
            break;
            case ALLOW_EXIT_TEST: testStrategyEnum1 = TestStrategyEnum.ALLOW_EXIT_TEST;
            break;
            case ALL_TEST: testStrategyEnum1 = TestStrategyEnum.ALL_TEST;
            break;
            case STANDARD_TEST: testStrategyEnum1 = TestStrategyEnum.STANDARD_TEST;
            break;
            case SIMPLE_TEST: testStrategyEnum1 = TestStrategyEnum.SIMPLE_TEST;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + testStrategyEnum );
        }

        return testStrategyEnum1;
    }

    protected com.zto.devops.qc.client.enums.rpc.PublishStrategyEnum publishStrategyEnumToPublishStrategyEnum(PublishStrategyEnum publishStrategyEnum) {
        if ( publishStrategyEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.PublishStrategyEnum publishStrategyEnum1;

        switch ( publishStrategyEnum ) {
            case AUTO: publishStrategyEnum1 = com.zto.devops.qc.client.enums.rpc.PublishStrategyEnum.AUTO;
            break;
            case MANUAL: publishStrategyEnum1 = com.zto.devops.qc.client.enums.rpc.PublishStrategyEnum.MANUAL;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + publishStrategyEnum );
        }

        return publishStrategyEnum1;
    }

    protected com.zto.devops.qc.client.model.rpc.project.SimpleVersionVO simpleVersionVOToSimpleVersionVO(SimpleVersionVO simpleVersionVO) {
        if ( simpleVersionVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.SimpleVersionVO simpleVersionVO1 = new com.zto.devops.qc.client.model.rpc.project.SimpleVersionVO();

        simpleVersionVO1.setCode( simpleVersionVO.getCode() );
        simpleVersionVO1.setProductCode( simpleVersionVO.getProductCode() );
        simpleVersionVO1.setName( simpleVersionVO.getName() );
        simpleVersionVO1.setStartDate( simpleVersionVO.getStartDate() );
        simpleVersionVO1.setPresentationDate( simpleVersionVO.getPresentationDate() );
        simpleVersionVO1.setApprovalExitDate( simpleVersionVO.getApprovalExitDate() );
        simpleVersionVO1.setPublishDate( simpleVersionVO.getPublishDate() );
        simpleVersionVO1.setProjectCode( simpleVersionVO.getProjectCode() );
        simpleVersionVO1.setProjectName( simpleVersionVO.getProjectName() );
        simpleVersionVO1.setType( simpleVersionVO.getType() );
        simpleVersionVO1.setVersionNum( simpleVersionVO.getVersionNum() );
        simpleVersionVO1.setStatus( flowStatusEnumToFlowStatusEnum( simpleVersionVO.getStatus() ) );
        simpleVersionVO1.setIsConfirm( simpleVersionVO.getIsConfirm() );
        simpleVersionVO1.setVersionDesc( simpleVersionVO.getVersionDesc() );
        simpleVersionVO1.setTestStrategy( testStrategyEnumToTestStrategyEnum( simpleVersionVO.getTestStrategy() ) );
        simpleVersionVO1.setCreateReason( simpleVersionVO.getCreateReason() );
        simpleVersionVO1.setRiskAddedFlag( simpleVersionVO.getRiskAddedFlag() );
        simpleVersionVO1.setProductName( simpleVersionVO.getProductName() );
        simpleVersionVO1.setDeptId( simpleVersionVO.getDeptId() );
        simpleVersionVO1.setDeptName( simpleVersionVO.getDeptName() );
        simpleVersionVO1.setDeptNo( simpleVersionVO.getDeptNo() );
        simpleVersionVO1.setPublishStrategy( publishStrategyEnumToPublishStrategyEnum( simpleVersionVO.getPublishStrategy() ) );

        return simpleVersionVO1;
    }

    protected com.zto.devops.qc.client.enums.rpc.RequirementStatus requirementStatusToRequirementStatus(RequirementStatus requirementStatus) {
        if ( requirementStatus == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.RequirementStatus requirementStatus1;

        switch ( requirementStatus ) {
            case START: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.START;
            break;
            case END: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.END;
            break;
            case ASSESSING: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.ASSESSING;
            break;
            case REVIEWING: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.REVIEWING;
            break;
            case FAIL_REVIEW: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.FAIL_REVIEW;
            break;
            case FAIL_ASSESS: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.FAIL_ASSESS;
            break;
            case ADOPTING: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.ADOPTING;
            break;
            case SCHEDULING: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.SCHEDULING;
            break;
            case DEVELOPING: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.DEVELOPING;
            break;
            case TESTING: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.TESTING;
            break;
            case CHECKING: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.CHECKING;
            break;
            case HANDLING: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.HANDLING;
            break;
            case FINISH: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.FINISH;
            break;
            case HAS_CLOSE: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.HAS_CLOSE;
            break;
            case HANG_UP: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.HANG_UP;
            break;
            case UNKNOWN: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.UNKNOWN;
            break;
            case DELETE: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.DELETE;
            break;
            case ACCEPT_FAIL: requirementStatus1 = com.zto.devops.qc.client.enums.rpc.RequirementStatus.ACCEPT_FAIL;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + requirementStatus );
        }

        return requirementStatus1;
    }

    protected HolidayVO holidayVOToHolidayVO(com.zto.devops.project.client.model.requirement.entity.HolidayVO holidayVO) {
        if ( holidayVO == null ) {
            return null;
        }

        HolidayVO holidayVO1 = new HolidayVO();

        holidayVO1.setId( holidayVO.getId() );
        holidayVO1.setHolidayCode( holidayVO.getHolidayCode() );
        holidayVO1.setHoliday( holidayVO.getHoliday() );
        holidayVO1.setType( holidayVO.getType() );
        holidayVO1.setRemark( holidayVO.getRemark() );
        holidayVO1.setCode( holidayVO.getCode() );
        holidayVO1.setCreateDate( holidayVO.getCreateDate() );
        holidayVO1.setCreateUserId( holidayVO.getCreateUserId() );
        holidayVO1.setCreateUserName( holidayVO.getCreateUserName() );
        holidayVO1.setUpdateDate( holidayVO.getUpdateDate() );
        holidayVO1.setUpdateUserId( holidayVO.getUpdateUserId() );
        holidayVO1.setUpdateUserName( holidayVO.getUpdateUserName() );
        holidayVO1.setWorkDay( holidayVO.getWorkDay() );
        holidayVO1.setTotalDay( holidayVO.getTotalDay() );

        return holidayVO1;
    }

    protected VersionMergeTypeEnum versionMergeTypeEnumToVersionMergeTypeEnum(com.zto.devops.project.client.enums.version.VersionMergeTypeEnum versionMergeTypeEnum) {
        if ( versionMergeTypeEnum == null ) {
            return null;
        }

        VersionMergeTypeEnum versionMergeTypeEnum1;

        switch ( versionMergeTypeEnum ) {
            case MERGED_MAJOR_VERSION: versionMergeTypeEnum1 = VersionMergeTypeEnum.MERGED_MAJOR_VERSION;
            break;
            case MERGED_MINOR_VERSION: versionMergeTypeEnum1 = VersionMergeTypeEnum.MERGED_MINOR_VERSION;
            break;
            case NORMAL_VERSION: versionMergeTypeEnum1 = VersionMergeTypeEnum.NORMAL_VERSION;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + versionMergeTypeEnum );
        }

        return versionMergeTypeEnum1;
    }

    protected com.zto.devops.qc.client.model.rpc.user.UserSelectVO userSelectVOToUserSelectVO(UserSelectVO userSelectVO) {
        if ( userSelectVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.user.UserSelectVO userSelectVO1 = new com.zto.devops.qc.client.model.rpc.user.UserSelectVO();

        userSelectVO1.setSsoUserId( userSelectVO.getSsoUserId() );
        userSelectVO1.setCnName( userSelectVO.getCnName() );
        userSelectVO1.setSex( userSelectVO.getSex() );
        userSelectVO1.setDeptId( userSelectVO.getDeptId() );
        userSelectVO1.setDeptName( userSelectVO.getDeptName() );
        userSelectVO1.setDeptNo( userSelectVO.getDeptNo() );
        userSelectVO1.setDeptFullName( userSelectVO.getDeptFullName() );
        userSelectVO1.setStationName( userSelectVO.getStationName() );
        userSelectVO1.setStationCode( userSelectVO.getStationCode() );
        userSelectVO1.setPositionCode( userSelectVO.getPositionCode() );
        userSelectVO1.setPositionName( userSelectVO.getPositionName() );
        userSelectVO1.setEmployeeQuickQuery( userSelectVO.getEmployeeQuickQuery() );
        userSelectVO1.setEmployeeSimpleSpelling( userSelectVO.getEmployeeSimpleSpelling() );
        if ( userSelectVO.getGitUserId() != null ) {
            userSelectVO1.setGitUserId( userSelectVO.getGitUserId().intValue() );
        }
        userSelectVO1.setLesseeCode( userSelectVO.getLesseeCode() );
        userSelectVO1.setAvatar( userSelectVO.getAvatar() );
        userSelectVO1.setUserCode( userSelectVO.getUserCode() );
        userSelectVO1.setZtoAccount( userSelectVO.getZtoAccount() );
        userSelectVO1.setDevAccount( userSelectVO.getDevAccount() );
        userSelectVO1.setStatus( userSelectVO.getStatus() );
        userSelectVO1.setEmail( userSelectVO.getEmail() );
        userSelectVO1.setEmpNo( userSelectVO.getEmpNo() );
        userSelectVO1.setNodeName( userSelectVO.getNodeName() );
        userSelectVO1.setBaseId( userSelectVO.getBaseId() );
        userSelectVO1.setBizCode( userSelectVO.getBizCode() );
        userSelectVO1.setWhiteList( userSelectVO.getWhiteList() );
        userSelectVO1.setQaxUserId( userSelectVO.getQaxUserId() );
        List<String> list = userSelectVO.getShortcutOperation();
        if ( list != null ) {
            userSelectVO1.setShortcutOperation( new ArrayList<String>( list ) );
        }

        return userSelectVO1;
    }

    protected com.zto.devops.qc.client.enums.rpc.MemberTypeEnum memberTypeEnumToMemberTypeEnum(MemberTypeEnum memberTypeEnum) {
        if ( memberTypeEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.MemberTypeEnum memberTypeEnum1;

        switch ( memberTypeEnum ) {
            case PRODUCTER_OWNER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.PRODUCTER_OWNER;
            break;
            case DEVELOPER_OWNER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DEVELOPER_OWNER;
            break;
            case TESTER_OWNER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.TESTER_OWNER;
            break;
            case PROJECTER_OWNER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.PROJECTER_OWNER;
            break;
            case DESIGNER_OWNER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DESIGNER_OWNER;
            break;
            case OPERATER_OWNER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.OPERATER_OWNER;
            break;
            case DEVOPSER_OWNER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DEVOPSER_OWNER;
            break;
            case PRODUCTER_M: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.PRODUCTER_M;
            break;
            case DEVELOPER_M: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DEVELOPER_M;
            break;
            case TESTER_M: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.TESTER_M;
            break;
            case PROJECTER_M: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.PROJECTER_M;
            break;
            case DESIGNER_M: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DESIGNER_M;
            break;
            case OPERATER_M: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.OPERATER_M;
            break;
            case DEVOPSER_M: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DEVOPSER_M;
            break;
            case PRODUCTER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.PRODUCTER;
            break;
            case DEVELOPER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DEVELOPER;
            break;
            case TESTER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.TESTER;
            break;
            case PROJECTER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.PROJECTER;
            break;
            case DESIGNER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DESIGNER;
            break;
            case OPERATER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.OPERATER;
            break;
            case DEVOPSER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.DEVOPSER;
            break;
            case CODE_REVIEWER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.CODE_REVIEWER;
            break;
            case PRODUCT_LINE_ARCH: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.PRODUCT_LINE_ARCH;
            break;
            case ARCHITECT: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.ARCHITECT;
            break;
            case SUPPER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.SUPPER;
            break;
            case OTHER: memberTypeEnum1 = com.zto.devops.qc.client.enums.rpc.MemberTypeEnum.OTHER;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + memberTypeEnum );
        }

        return memberTypeEnum1;
    }

    protected com.zto.devops.qc.client.model.rpc.product.ProductMemberVO productMemberVOToProductMemberVO(ProductMemberVO productMemberVO) {
        if ( productMemberVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.product.ProductMemberVO productMemberVO1 = new com.zto.devops.qc.client.model.rpc.product.ProductMemberVO();

        productMemberVO1.setMemberType( memberTypeEnumToMemberTypeEnum( productMemberVO.getMemberType() ) );
        productMemberVO1.setMemberTypeDesc( productMemberVO.getMemberTypeDesc() );
        productMemberVO1.setDeptName( productMemberVO.getDeptName() );
        productMemberVO1.setUserName( productMemberVO.getUserName() );
        productMemberVO1.setUserId( productMemberVO.getUserId() );
        productMemberVO1.setDeptId( productMemberVO.getDeptId() );
        productMemberVO1.setCode( productMemberVO.getCode() );
        productMemberVO1.setProductCode( productMemberVO.getProductCode() );
        productMemberVO1.setDevAccount( productMemberVO.getDevAccount() );
        if ( productMemberVO.getGitUserId() != null ) {
            productMemberVO1.setGitUserId( productMemberVO.getGitUserId().intValue() );
        }

        return productMemberVO1;
    }

    protected com.zto.devops.qc.client.enums.rpc.ProductTypeEnum productTypeEnumToProductTypeEnum(ProductTypeEnum productTypeEnum) {
        if ( productTypeEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.ProductTypeEnum productTypeEnum1;

        switch ( productTypeEnum ) {
            case OTHER: productTypeEnum1 = com.zto.devops.qc.client.enums.rpc.ProductTypeEnum.OTHER;
            break;
            case CENTER_POINT: productTypeEnum1 = com.zto.devops.qc.client.enums.rpc.ProductTypeEnum.CENTER_POINT;
            break;
            case DEPT_POINT: productTypeEnum1 = com.zto.devops.qc.client.enums.rpc.ProductTypeEnum.DEPT_POINT;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + productTypeEnum );
        }

        return productTypeEnum1;
    }

    protected LevelEnum levelEnumToLevelEnum(com.zto.devops.product.client.enums.LevelEnum levelEnum) {
        if ( levelEnum == null ) {
            return null;
        }

        LevelEnum levelEnum1;

        switch ( levelEnum ) {
            case OTHER: levelEnum1 = LevelEnum.OTHER;
            break;
            case HEIGHT: levelEnum1 = LevelEnum.HEIGHT;
            break;
            case MIDDLE: levelEnum1 = LevelEnum.MIDDLE;
            break;
            case LOW: levelEnum1 = LevelEnum.LOW;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + levelEnum );
        }

        return levelEnum1;
    }

    protected CategoryEnum categoryEnumToCategoryEnum(com.zto.devops.product.client.enums.CategoryEnum categoryEnum) {
        if ( categoryEnum == null ) {
            return null;
        }

        CategoryEnum categoryEnum1;

        switch ( categoryEnum ) {
            case UTILITY: categoryEnum1 = CategoryEnum.UTILITY;
            break;
            case MANAGE: categoryEnum1 = CategoryEnum.MANAGE;
            break;
            case TECHNOLOGY: categoryEnum1 = CategoryEnum.TECHNOLOGY;
            break;
            case ABILITYTOSUPORT: categoryEnum1 = CategoryEnum.ABILITYTOSUPORT;
            break;
            case OTHER: categoryEnum1 = CategoryEnum.OTHER;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + categoryEnum );
        }

        return categoryEnum1;
    }

    protected com.zto.devops.qc.client.enums.rpc.LifeCycleEnum lifeCycleEnumToLifeCycleEnum(LifeCycleEnum lifeCycleEnum) {
        if ( lifeCycleEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.LifeCycleEnum lifeCycleEnum1;

        switch ( lifeCycleEnum ) {
            case ENTRY_STAGE: lifeCycleEnum1 = com.zto.devops.qc.client.enums.rpc.LifeCycleEnum.ENTRY_STAGE;
            break;
            case GROWTH_STAGE: lifeCycleEnum1 = com.zto.devops.qc.client.enums.rpc.LifeCycleEnum.GROWTH_STAGE;
            break;
            case MATURITY_STAGE: lifeCycleEnum1 = com.zto.devops.qc.client.enums.rpc.LifeCycleEnum.MATURITY_STAGE;
            break;
            case DECLINE_STAGE: lifeCycleEnum1 = com.zto.devops.qc.client.enums.rpc.LifeCycleEnum.DECLINE_STAGE;
            break;
            case OTHER: lifeCycleEnum1 = com.zto.devops.qc.client.enums.rpc.LifeCycleEnum.OTHER;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + lifeCycleEnum );
        }

        return lifeCycleEnum1;
    }

    protected UserSourceEnum userSourceEnumToUserSourceEnum(com.zto.devops.product.client.enums.UserSourceEnum userSourceEnum) {
        if ( userSourceEnum == null ) {
            return null;
        }

        UserSourceEnum userSourceEnum1;

        switch ( userSourceEnum ) {
            case OTHER: userSourceEnum1 = UserSourceEnum.OTHER;
            break;
            case INTERNAL: userSourceEnum1 = UserSourceEnum.INTERNAL;
            break;
            case EXTEERNAL: userSourceEnum1 = UserSourceEnum.EXTEERNAL;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + userSourceEnum );
        }

        return userSourceEnum1;
    }

    protected com.zto.devops.qc.client.enums.rpc.ProductSourceEnum productSourceEnumToProductSourceEnum(ProductSourceEnum productSourceEnum) {
        if ( productSourceEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.ProductSourceEnum productSourceEnum1;

        switch ( productSourceEnum ) {
            case OTHER: productSourceEnum1 = com.zto.devops.qc.client.enums.rpc.ProductSourceEnum.OTHER;
            break;
            case EXTERNAL_INNOVATE: productSourceEnum1 = com.zto.devops.qc.client.enums.rpc.ProductSourceEnum.EXTERNAL_INNOVATE;
            break;
            case SELF_INNOVATE: productSourceEnum1 = com.zto.devops.qc.client.enums.rpc.ProductSourceEnum.SELF_INNOVATE;
            break;
            case INNOVATE: productSourceEnum1 = com.zto.devops.qc.client.enums.rpc.ProductSourceEnum.INNOVATE;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + productSourceEnum );
        }

        return productSourceEnum1;
    }

    protected UserTypeEnum userTypeEnumToUserTypeEnum(com.zto.devops.product.client.enums.UserTypeEnum userTypeEnum) {
        if ( userTypeEnum == null ) {
            return null;
        }

        UserTypeEnum userTypeEnum1;

        switch ( userTypeEnum ) {
            case OTHER: userTypeEnum1 = UserTypeEnum.OTHER;
            break;
            case TOB: userTypeEnum1 = UserTypeEnum.TOB;
            break;
            case TOC: userTypeEnum1 = UserTypeEnum.TOC;
            break;
            case TOBC: userTypeEnum1 = UserTypeEnum.TOBC;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + userTypeEnum );
        }

        return userTypeEnum1;
    }

    protected UserLevelEnum userLevelEnumToUserLevelEnum(com.zto.devops.product.client.enums.UserLevelEnum userLevelEnum) {
        if ( userLevelEnum == null ) {
            return null;
        }

        UserLevelEnum userLevelEnum1;

        switch ( userLevelEnum ) {
            case OTHER: userLevelEnum1 = UserLevelEnum.OTHER;
            break;
            case DECADE: userLevelEnum1 = UserLevelEnum.DECADE;
            break;
            case HUNDRED: userLevelEnum1 = UserLevelEnum.HUNDRED;
            break;
            case THOUSAND: userLevelEnum1 = UserLevelEnum.THOUSAND;
            break;
            case TEN_THOUSAND: userLevelEnum1 = UserLevelEnum.TEN_THOUSAND;
            break;
            case HUNDRED_THOUSAND: userLevelEnum1 = UserLevelEnum.HUNDRED_THOUSAND;
            break;
            case MILLION: userLevelEnum1 = UserLevelEnum.MILLION;
            break;
            case TEN_MILLION: userLevelEnum1 = UserLevelEnum.TEN_MILLION;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + userLevelEnum );
        }

        return userLevelEnum1;
    }

    protected UsedFrequencyEnum usedFrequencyEnumToUsedFrequencyEnum(com.zto.devops.product.client.enums.UsedFrequencyEnum usedFrequencyEnum) {
        if ( usedFrequencyEnum == null ) {
            return null;
        }

        UsedFrequencyEnum usedFrequencyEnum1;

        switch ( usedFrequencyEnum ) {
            case OTHER: usedFrequencyEnum1 = UsedFrequencyEnum.OTHER;
            break;
            case BIT: usedFrequencyEnum1 = UsedFrequencyEnum.BIT;
            break;
            case DECADE: usedFrequencyEnum1 = UsedFrequencyEnum.DECADE;
            break;
            case HUNDRED: usedFrequencyEnum1 = UsedFrequencyEnum.HUNDRED;
            break;
            case THOUSAND: usedFrequencyEnum1 = UsedFrequencyEnum.THOUSAND;
            break;
            case TENTHOUSAND: usedFrequencyEnum1 = UsedFrequencyEnum.TENTHOUSAND;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + usedFrequencyEnum );
        }

        return usedFrequencyEnum1;
    }

    protected ApplicationInfo applicationInfoToApplicationInfo(com.zto.devops.pipeline.client.model.flow.entity.ApplicationInfo applicationInfo) {
        if ( applicationInfo == null ) {
            return null;
        }

        ApplicationInfo applicationInfo1 = new ApplicationInfo();

        applicationInfo1.setAppId( applicationInfo.getAppId() );
        applicationInfo1.setEnableJacoco( applicationInfo.isEnableJacoco() );

        return applicationInfo1;
    }

    protected List<ApplicationInfo> applicationInfoListToApplicationInfoList(List<com.zto.devops.pipeline.client.model.flow.entity.ApplicationInfo> list) {
        if ( list == null ) {
            return null;
        }

        List<ApplicationInfo> list1 = new ArrayList<ApplicationInfo>( list.size() );
        for ( com.zto.devops.pipeline.client.model.flow.entity.ApplicationInfo applicationInfo : list ) {
            list1.add( applicationInfoToApplicationInfo( applicationInfo ) );
        }

        return list1;
    }

    protected com.zto.devops.qc.client.model.rpc.pipeline.VersionInfo versionInfoToVersionInfo(VersionInfo versionInfo) {
        if ( versionInfo == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.VersionInfo versionInfo1 = new com.zto.devops.qc.client.model.rpc.pipeline.VersionInfo();

        versionInfo1.setCode( versionInfo.getCode() );
        versionInfo1.setName( versionInfo.getName() );
        versionInfo1.setProductCode( versionInfo.getProductCode() );
        versionInfo1.setApps( applicationInfoListToApplicationInfoList( versionInfo.getApps() ) );

        return versionInfo1;
    }

    protected List<com.zto.devops.qc.client.model.rpc.pipeline.VersionInfo> versionInfoListToVersionInfoList(List<VersionInfo> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.pipeline.VersionInfo> list1 = new ArrayList<com.zto.devops.qc.client.model.rpc.pipeline.VersionInfo>( list.size() );
        for ( VersionInfo versionInfo : list ) {
            list1.add( versionInfoToVersionInfo( versionInfo ) );
        }

        return list1;
    }

    protected EnvEnum envEnumToEnvEnum(com.zto.devops.pipeline.client.enums.EnvEnum envEnum) {
        if ( envEnum == null ) {
            return null;
        }

        EnvEnum envEnum1;

        switch ( envEnum ) {
            case DEV: envEnum1 = EnvEnum.DEV;
            break;
            case FAT: envEnum1 = EnvEnum.FAT;
            break;
            case PRE: envEnum1 = EnvEnum.PRE;
            break;
            case PRO: envEnum1 = EnvEnum.PRO;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + envEnum );
        }

        return envEnum1;
    }

    protected NamespaceTypeEnum namespaceTypeEnumToNamespaceTypeEnum(com.zto.devops.pipeline.client.enums.NamespaceTypeEnum namespaceTypeEnum) {
        if ( namespaceTypeEnum == null ) {
            return null;
        }

        NamespaceTypeEnum namespaceTypeEnum1;

        switch ( namespaceTypeEnum ) {
            case DEFAULT_EMPTY: namespaceTypeEnum1 = NamespaceTypeEnum.DEFAULT_EMPTY;
            break;
            case BASE: namespaceTypeEnum1 = NamespaceTypeEnum.BASE;
            break;
            case BASE_DEV: namespaceTypeEnum1 = NamespaceTypeEnum.BASE_DEV;
            break;
            case BASE_FAT: namespaceTypeEnum1 = NamespaceTypeEnum.BASE_FAT;
            break;
            case AUTO_PRE: namespaceTypeEnum1 = NamespaceTypeEnum.AUTO_PRE;
            break;
            case BACKUP: namespaceTypeEnum1 = NamespaceTypeEnum.BACKUP;
            break;
            case BASE_TEST: namespaceTypeEnum1 = NamespaceTypeEnum.BASE_TEST;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + namespaceTypeEnum );
        }

        return namespaceTypeEnum1;
    }

    protected JacocoInstanceVO jacocoInstanceVOToJacocoInstanceVO(com.zto.devops.pipeline.client.model.instance.entity.JacocoInstanceVO jacocoInstanceVO) {
        if ( jacocoInstanceVO == null ) {
            return null;
        }

        JacocoInstanceVO jacocoInstanceVO1 = new JacocoInstanceVO();

        jacocoInstanceVO1.setType( jacocoInstanceVO.getType() );
        jacocoInstanceVO1.setIp( jacocoInstanceVO.getIp() );
        jacocoInstanceVO1.setName( jacocoInstanceVO.getName() );

        return jacocoInstanceVO1;
    }

    protected List<JacocoInstanceVO> jacocoInstanceVOListToJacocoInstanceVOList(List<com.zto.devops.pipeline.client.model.instance.entity.JacocoInstanceVO> list) {
        if ( list == null ) {
            return null;
        }

        List<JacocoInstanceVO> list1 = new ArrayList<JacocoInstanceVO>( list.size() );
        for ( com.zto.devops.pipeline.client.model.instance.entity.JacocoInstanceVO jacocoInstanceVO : list ) {
            list1.add( jacocoInstanceVOToJacocoInstanceVO( jacocoInstanceVO ) );
        }

        return list1;
    }

    protected FlowLaneTypeEnum flowLaneTypeEnumToFlowLaneTypeEnum(com.zto.devops.pipeline.client.enums.FlowLaneTypeEnum flowLaneTypeEnum) {
        if ( flowLaneTypeEnum == null ) {
            return null;
        }

        FlowLaneTypeEnum flowLaneTypeEnum1;

        switch ( flowLaneTypeEnum ) {
            case FLOW_FEATURE: flowLaneTypeEnum1 = FlowLaneTypeEnum.FLOW_FEATURE;
            break;
            case FLOW_TEST: flowLaneTypeEnum1 = FlowLaneTypeEnum.FLOW_TEST;
            break;
            case FLOW_PROD: flowLaneTypeEnum1 = FlowLaneTypeEnum.FLOW_PROD;
            break;
            case FLOW_ACCEPTED: flowLaneTypeEnum1 = FlowLaneTypeEnum.FLOW_ACCEPTED;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + flowLaneTypeEnum );
        }

        return flowLaneTypeEnum1;
    }

    protected com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO jacocoApplicationVOToJacocoApplicationVO(JacocoApplicationVO jacocoApplicationVO) {
        if ( jacocoApplicationVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO jacocoApplicationVO1 = new com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO();

        jacocoApplicationVO1.setCode( jacocoApplicationVO.getCode() );
        jacocoApplicationVO1.setAppId( jacocoApplicationVO.getAppId() );
        jacocoApplicationVO1.setBranchName( jacocoApplicationVO.getBranchName() );
        jacocoApplicationVO1.setCommitId( jacocoApplicationVO.getCommitId() );
        jacocoApplicationVO1.setPackageName( jacocoApplicationVO.getPackageName() );
        jacocoApplicationVO1.setGitUrl( jacocoApplicationVO.getGitUrl() );
        jacocoApplicationVO1.setPort( jacocoApplicationVO.getPort() );
        jacocoApplicationVO1.setNamespaceName( jacocoApplicationVO.getNamespaceName() );
        jacocoApplicationVO1.setEnv( envEnumToEnvEnum( jacocoApplicationVO.getEnv() ) );
        jacocoApplicationVO1.setType( namespaceTypeEnumToNamespaceTypeEnum( jacocoApplicationVO.getType() ) );
        jacocoApplicationVO1.setDeployCode( jacocoApplicationVO.getDeployCode() );
        List<Version> list = jacocoApplicationVO.getVersions();
        if ( list != null ) {
            jacocoApplicationVO1.setVersions( new ArrayList<Version>( list ) );
        }
        jacocoApplicationVO1.setInstances( jacocoInstanceVOListToJacocoInstanceVOList( jacocoApplicationVO.getInstances() ) );
        jacocoApplicationVO1.setOutputFileName( jacocoApplicationVO.getOutputFileName() );
        jacocoApplicationVO1.setFlowLaneType( flowLaneTypeEnumToFlowLaneTypeEnum( jacocoApplicationVO.getFlowLaneType() ) );

        return jacocoApplicationVO1;
    }

    protected TestcaseStatusEnum testcaseStatusEnumToTestcaseStatusEnum(com.zto.devops.project.client.model.rpc.enums.TestcaseStatusEnum testcaseStatusEnum) {
        if ( testcaseStatusEnum == null ) {
            return null;
        }

        TestcaseStatusEnum testcaseStatusEnum1;

        switch ( testcaseStatusEnum ) {
            case NORMAL: testcaseStatusEnum1 = TestcaseStatusEnum.NORMAL;
            break;
            case DISABLE: testcaseStatusEnum1 = TestcaseStatusEnum.DISABLE;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + testcaseStatusEnum );
        }

        return testcaseStatusEnum1;
    }

    protected com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO testcaseByBusinessCodeVOToTestcaseByBusinessCodeVO(TestcaseByBusinessCodeVO testcaseByBusinessCodeVO) {
        if ( testcaseByBusinessCodeVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO testcaseByBusinessCodeVO1 = new com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO();

        testcaseByBusinessCodeVO1.setCode( testcaseByBusinessCodeVO.getCode() );
        testcaseByBusinessCodeVO1.setTitle( testcaseByBusinessCodeVO.getTitle() );
        testcaseByBusinessCodeVO1.setStatus( testcaseStatusEnumToTestcaseStatusEnum( testcaseByBusinessCodeVO.getStatus() ) );
        testcaseByBusinessCodeVO1.setStatusDesc( testcaseByBusinessCodeVO.getStatusDesc() );
        testcaseByBusinessCodeVO1.setDutyUserId( testcaseByBusinessCodeVO.getDutyUserId() );
        testcaseByBusinessCodeVO1.setDutyUser( testcaseByBusinessCodeVO.getDutyUser() );

        return testcaseByBusinessCodeVO1;
    }

    protected List<com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO> testcaseByBusinessCodeVOListToTestcaseByBusinessCodeVOList(List<TestcaseByBusinessCodeVO> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO> list1 = new ArrayList<com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO>( list.size() );
        for ( TestcaseByBusinessCodeVO testcaseByBusinessCodeVO : list ) {
            list1.add( testcaseByBusinessCodeVOToTestcaseByBusinessCodeVO( testcaseByBusinessCodeVO ) );
        }

        return list1;
    }

    protected com.zto.devops.qc.client.model.rpc.project.TagPoJo tagPoJoToTagPoJo(TagPoJo tagPoJo) {
        if ( tagPoJo == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.TagPoJo tagPoJo1 = new com.zto.devops.qc.client.model.rpc.project.TagPoJo();

        tagPoJo1.setTagName( tagPoJo.getTagName() );
        tagPoJo1.setCreatorId( tagPoJo.getCreatorId() );
        tagPoJo1.setTagCode( tagPoJo.getTagCode() );
        tagPoJo1.setCode( tagPoJo.getCode() );
        tagPoJo1.setBusinessCode( tagPoJo.getBusinessCode() );
        tagPoJo1.setType( tagPoJo.getType() );

        return tagPoJo1;
    }

    protected List<com.zto.devops.qc.client.model.rpc.project.TagPoJo> tagPoJoListToTagPoJoList(List<TagPoJo> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.project.TagPoJo> list1 = new ArrayList<com.zto.devops.qc.client.model.rpc.project.TagPoJo>( list.size() );
        for ( TagPoJo tagPoJo : list ) {
            list1.add( tagPoJoToTagPoJo( tagPoJo ) );
        }

        return list1;
    }

    protected com.zto.devops.qc.client.model.rpc.project.CCManVO cCManVOToCCManVO(CCManVO cCManVO) {
        if ( cCManVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.CCManVO cCManVO1 = new com.zto.devops.qc.client.model.rpc.project.CCManVO();

        cCManVO1.setCcManId( cCManVO.getCcManId() );
        cCManVO1.setCcManName( cCManVO.getCcManName() );
        cCManVO1.setCcManCode( cCManVO.getCcManCode() );

        return cCManVO1;
    }

    protected List<com.zto.devops.qc.client.model.rpc.project.CCManVO> cCManVOListToCCManVOList(List<CCManVO> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.project.CCManVO> list1 = new ArrayList<com.zto.devops.qc.client.model.rpc.project.CCManVO>( list.size() );
        for ( CCManVO cCManVO : list ) {
            list1.add( cCManVOToCCManVO( cCManVO ) );
        }

        return list1;
    }

    protected FollowerVO followerVOToFollowerVO(com.zto.devops.project.client.model.requirement.entity.FollowerVO followerVO) {
        if ( followerVO == null ) {
            return null;
        }

        FollowerVO followerVO1 = new FollowerVO();

        followerVO1.setFollowerId( followerVO.getFollowerId() );
        followerVO1.setFollowerName( followerVO.getFollowerName() );
        followerVO1.setFollowerCode( followerVO.getFollowerCode() );

        return followerVO1;
    }

    protected List<FollowerVO> followerVOListToFollowerVOList(List<com.zto.devops.project.client.model.requirement.entity.FollowerVO> list) {
        if ( list == null ) {
            return null;
        }

        List<FollowerVO> list1 = new ArrayList<FollowerVO>( list.size() );
        for ( com.zto.devops.project.client.model.requirement.entity.FollowerVO followerVO : list ) {
            list1.add( followerVOToFollowerVO( followerVO ) );
        }

        return list1;
    }

    protected ProgressVO progressVOToProgressVO(com.zto.devops.project.client.model.requirement.entity.ProgressVO progressVO) {
        if ( progressVO == null ) {
            return null;
        }

        ProgressVO progressVO1 = new ProgressVO();

        progressVO1.setName( progressVO.getName() );
        progressVO1.setCode( progressVO.getCode() );
        progressVO1.setIndex( progressVO.getIndex() );

        return progressVO1;
    }

    protected List<ProgressVO> progressVOListToProgressVOList(List<com.zto.devops.project.client.model.requirement.entity.ProgressVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ProgressVO> list1 = new ArrayList<ProgressVO>( list.size() );
        for ( com.zto.devops.project.client.model.requirement.entity.ProgressVO progressVO : list ) {
            list1.add( progressVOToProgressVO( progressVO ) );
        }

        return list1;
    }

    protected ButtonVO buttonToButtonVO(Button button) {
        if ( button == null ) {
            return null;
        }

        ButtonVO buttonVO = new ButtonVO();

        buttonVO.setName( button.getName() );
        buttonVO.setCode( button.getCode() );
        buttonVO.setIndex( button.getIndex() );

        return buttonVO;
    }

    protected List<ButtonVO> buttonListToButtonVOList(List<Button> list) {
        if ( list == null ) {
            return null;
        }

        List<ButtonVO> list1 = new ArrayList<ButtonVO>( list.size() );
        for ( Button button : list ) {
            list1.add( buttonToButtonVO( button ) );
        }

        return list1;
    }

    protected FieldVO fieldToFieldVO(Field field) {
        if ( field == null ) {
            return null;
        }

        FieldVO fieldVO = new FieldVO();

        fieldVO.setName( field.getName() );
        fieldVO.setCode( field.getCode() );

        return fieldVO;
    }

    protected List<FieldVO> fieldListToFieldVOList(List<Field> list) {
        if ( list == null ) {
            return null;
        }

        List<FieldVO> list1 = new ArrayList<FieldVO>( list.size() );
        for ( Field field : list ) {
            list1.add( fieldToFieldVO( field ) );
        }

        return list1;
    }

    protected ReviewVO reviewVOToReviewVO(com.zto.devops.project.client.model.requirement.entity.ReviewVO reviewVO) {
        if ( reviewVO == null ) {
            return null;
        }

        ReviewVO reviewVO1 = new ReviewVO();

        reviewVO1.setName( reviewVO.getName() );
        reviewVO1.setCode( reviewVO.getCode() );
        reviewVO1.setIndex( reviewVO.getIndex() );

        return reviewVO1;
    }

    protected List<ReviewVO> reviewVOListToReviewVOList(List<com.zto.devops.project.client.model.requirement.entity.ReviewVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ReviewVO> list1 = new ArrayList<ReviewVO>( list.size() );
        for ( com.zto.devops.project.client.model.requirement.entity.ReviewVO reviewVO : list ) {
            list1.add( reviewVOToReviewVO( reviewVO ) );
        }

        return list1;
    }

    protected com.zto.devops.qc.client.model.rpc.project.enums.CheckType checkTypeToCheckType(CheckType checkType) {
        if ( checkType == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.enums.CheckType checkType1;

        switch ( checkType ) {
            case DESIGN_CHECK: checkType1 = com.zto.devops.qc.client.model.rpc.project.enums.CheckType.DESIGN_CHECK;
            break;
            case TRAIN_CHECK: checkType1 = com.zto.devops.qc.client.model.rpc.project.enums.CheckType.TRAIN_CHECK;
            break;
            case OTHER_CHECK: checkType1 = com.zto.devops.qc.client.model.rpc.project.enums.CheckType.OTHER_CHECK;
            break;
            case REQ_COMMENT: checkType1 = com.zto.devops.qc.client.model.rpc.project.enums.CheckType.REQ_COMMENT;
            break;
            case UNKNOWN: checkType1 = com.zto.devops.qc.client.model.rpc.project.enums.CheckType.UNKNOWN;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + checkType );
        }

        return checkType1;
    }

    protected com.zto.devops.qc.client.model.rpc.project.enums.DeliveryTime deliveryTimeToDeliveryTime(DeliveryTime deliveryTime) {
        if ( deliveryTime == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.enums.DeliveryTime deliveryTime1;

        switch ( deliveryTime ) {
            case ONTIME_DELIVERY: deliveryTime1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryTime.ONTIME_DELIVERY;
            break;
            case EARLY_DELIVERY: deliveryTime1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryTime.EARLY_DELIVERY;
            break;
            case LATE_DELIVERY: deliveryTime1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryTime.LATE_DELIVERY;
            break;
            case COM_SATISFACTION: deliveryTime1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryTime.COM_SATISFACTION;
            break;
            case DIS_SATISFACTION: deliveryTime1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryTime.DIS_SATISFACTION;
            break;
            case UNKNOWN: deliveryTime1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryTime.UNKNOWN;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + deliveryTime );
        }

        return deliveryTime1;
    }

    protected com.zto.devops.qc.client.model.rpc.project.enums.DeliveryQuality deliveryQualityToDeliveryQuality(DeliveryQuality deliveryQuality) {
        if ( deliveryQuality == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.enums.DeliveryQuality deliveryQuality1;

        switch ( deliveryQuality ) {
            case ONE_TIME_PASS: deliveryQuality1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryQuality.ONE_TIME_PASS;
            break;
            case MUL_TIME_PASS: deliveryQuality1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryQuality.MUL_TIME_PASS;
            break;
            case NO_STAND_PASS: deliveryQuality1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryQuality.NO_STAND_PASS;
            break;
            case COM_SATISFACTION: deliveryQuality1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryQuality.COM_SATISFACTION;
            break;
            case DIS_SATISFACTION: deliveryQuality1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryQuality.DIS_SATISFACTION;
            break;
            case UNKNOWN: deliveryQuality1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryQuality.UNKNOWN;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + deliveryQuality );
        }

        return deliveryQuality1;
    }

    protected com.zto.devops.qc.client.model.rpc.project.enums.DeliverySatisfaction deliverySatisfactionToDeliverySatisfaction(DeliverySatisfaction deliverySatisfaction) {
        if ( deliverySatisfaction == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.enums.DeliverySatisfaction deliverySatisfaction1;

        switch ( deliverySatisfaction ) {
            case GREAT_SATISFACTION: deliverySatisfaction1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliverySatisfaction.GREAT_SATISFACTION;
            break;
            case COM_SATISFACTION: deliverySatisfaction1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliverySatisfaction.COM_SATISFACTION;
            break;
            case DIS_SATISFACTION: deliverySatisfaction1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliverySatisfaction.DIS_SATISFACTION;
            break;
            case UNKNOWN: deliverySatisfaction1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliverySatisfaction.UNKNOWN;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + deliverySatisfaction );
        }

        return deliverySatisfaction1;
    }

    protected DockService dockServiceToDockService(com.zto.devops.project.client.enums.requirement.DockService dockService) {
        if ( dockService == null ) {
            return null;
        }

        DockService dockService1;

        switch ( dockService ) {
            case COOPERATE_WELL: dockService1 = DockService.COOPERATE_WELL;
            break;
            case TASK_REQUIRED: dockService1 = DockService.TASK_REQUIRED;
            break;
            case COMMUNICATE_IMPROVED: dockService1 = DockService.COMMUNICATE_IMPROVED;
            break;
            case UNKNOWN: dockService1 = DockService.UNKNOWN;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + dockService );
        }

        return dockService1;
    }

    protected com.zto.devops.qc.client.model.rpc.project.enums.DeliveryResult deliveryResultToDeliveryResult(DeliveryResult deliveryResult) {
        if ( deliveryResult == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.project.enums.DeliveryResult deliveryResult1;

        switch ( deliveryResult ) {
            case EXCEED_EXPECT: deliveryResult1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryResult.EXCEED_EXPECT;
            break;
            case ACHIEVE_EXPECT: deliveryResult1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryResult.ACHIEVE_EXPECT;
            break;
            case DISDACHIEVE_EXPECT: deliveryResult1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryResult.DISDACHIEVE_EXPECT;
            break;
            case UNKNOWN: deliveryResult1 = com.zto.devops.qc.client.model.rpc.project.enums.DeliveryResult.UNKNOWN;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + deliveryResult );
        }

        return deliveryResult1;
    }

    protected RequirementDetailVo requirementDetailVoToRequirementDetailVo(com.zto.devops.project.client.model.requirement.entity.RequirementDetailVo requirementDetailVo) {
        if ( requirementDetailVo == null ) {
            return null;
        }

        RequirementDetailVo requirementDetailVo1 = new RequirementDetailVo();

        requirementDetailVo1.setSubRequirementNum( requirementDetailVo.getSubRequirementNum() );
        requirementDetailVo1.setTaskNum( requirementDetailVo.getTaskNum() );
        requirementDetailVo1.setTestCasesNum( requirementDetailVo.getTestCasesNum() );
        requirementDetailVo1.setIssuesNum( requirementDetailVo.getIssuesNum() );
        requirementDetailVo1.setReviewNum( requirementDetailVo.getReviewNum() );
        requirementDetailVo1.setFileNum( requirementDetailVo.getFileNum() );
        requirementDetailVo1.setCode( requirementDetailVo.getCode() );
        requirementDetailVo1.setParentCode( requirementDetailVo.getParentCode() );
        requirementDetailVo1.setLevel( requirementDetailVo.getLevel() );
        requirementDetailVo1.setTitle( requirementDetailVo.getTitle() );
        requirementDetailVo1.setStatus( requirementDetailVo.getStatus() );
        requirementDetailVo1.setStatusName( requirementDetailVo.getStatusName() );
        requirementDetailVo1.setPriority( requirementDetailVo.getPriority() );
        requirementDetailVo1.setPriorityName( requirementDetailVo.getPriorityName() );
        requirementDetailVo1.setDockingUserName( requirementDetailVo.getDockingUserName() );
        requirementDetailVo1.setDockingUserId( requirementDetailVo.getDockingUserId() );
        requirementDetailVo1.setBackground( requirementDetailVo.getBackground() );
        requirementDetailVo1.setWorth( requirementDetailVo.getWorth() );
        requirementDetailVo1.setDescription( requirementDetailVo.getDescription() );
        requirementDetailVo1.setType( requirementDetailVo.getType() );
        requirementDetailVo1.setTypeName( requirementDetailVo.getTypeName() );
        requirementDetailVo1.setProductName( requirementDetailVo.getProductName() );
        requirementDetailVo1.setProductCode( requirementDetailVo.getProductCode() );
        requirementDetailVo1.setVersionCode( requirementDetailVo.getVersionCode() );
        requirementDetailVo1.setVersionName( requirementDetailVo.getVersionName() );
        requirementDetailVo1.setSprintCode( requirementDetailVo.getSprintCode() );
        requirementDetailVo1.setSprintName( requirementDetailVo.getSprintName() );
        requirementDetailVo1.setProjectCode( requirementDetailVo.getProjectCode() );
        requirementDetailVo1.setProjectName( requirementDetailVo.getProjectName() );
        requirementDetailVo1.setTestCaseList( testcaseByBusinessCodeVOListToTestcaseByBusinessCodeVOList( requirementDetailVo.getTestCaseList() ) );
        requirementDetailVo1.setTagPoJos( tagPoJoListToTagPoJoList( requirementDetailVo.getTagPoJos() ) );
        requirementDetailVo1.setSource( requirementDetailVo.getSource() );
        List<User> list2 = requirementDetailVo.getCurrentHandlerUser();
        if ( list2 != null ) {
            requirementDetailVo1.setCurrentHandlerUser( new ArrayList<User>( list2 ) );
        }
        requirementDetailVo1.setCurrentHandlerUserNames( requirementDetailVo.getCurrentHandlerUserNames() );
        requirementDetailVo1.setCurrentProgress( requirementDetailVo.getCurrentProgress() );
        List<String> list3 = requirementDetailVo.getDyTags();
        if ( list3 != null ) {
            requirementDetailVo1.setDyTags( new ArrayList<String>( list3 ) );
        }
        requirementDetailVo1.setDockingDeptId( requirementDetailVo.getDockingDeptId() );
        requirementDetailVo1.setDockingDeptName( requirementDetailVo.getDockingDeptName() );
        requirementDetailVo1.setIntroducerId( requirementDetailVo.getIntroducerId() );
        requirementDetailVo1.setIntroducerName( requirementDetailVo.getIntroducerName() );
        requirementDetailVo1.setIntroducerDeptId( requirementDetailVo.getIntroducerDeptId() );
        requirementDetailVo1.setIntroducerDeptName( requirementDetailVo.getIntroducerDeptName() );
        requirementDetailVo1.setCcManList( cCManVOListToCCManVOList( requirementDetailVo.getCcManList() ) );
        requirementDetailVo1.setFollowerList( followerVOListToFollowerVOList( requirementDetailVo.getFollowerList() ) );
        requirementDetailVo1.setModifierIdView( requirementDetailVo.getModifierIdView() );
        requirementDetailVo1.setModifierView( requirementDetailVo.getModifierView() );
        requirementDetailVo1.setGmtCreate( requirementDetailVo.getGmtCreate() );
        requirementDetailVo1.setReviewCreate( requirementDetailVo.getReviewCreate() );
        requirementDetailVo1.setExpectEndDate( requirementDetailVo.getExpectEndDate() );
        requirementDetailVo1.setFinishDate( requirementDetailVo.getFinishDate() );
        requirementDetailVo1.setActualFinishDate( requirementDetailVo.getActualFinishDate() );
        requirementDetailVo1.setGmtModifiedView( requirementDetailVo.getGmtModifiedView() );
        requirementDetailVo1.setReviewResult( requirementDetailVo.getReviewResult() );
        requirementDetailVo1.setWorthLevel( requirementDetailVo.getWorthLevel() );
        requirementDetailVo1.setFocus( requirementDetailVo.isFocus() );
        requirementDetailVo1.setReviewOption( requirementDetailVo.getReviewOption() );
        requirementDetailVo1.setFocusResult( requirementDetailVo.getFocusResult() );
        requirementDetailVo1.setFocusOption( requirementDetailVo.getFocusOption() );
        requirementDetailVo1.setOptionProgress( progressVOListToProgressVOList( requirementDetailVo.getOptionProgress() ) );
        requirementDetailVo1.setButtonVOS( buttonListToButtonVOList( requirementDetailVo.getButtonVOS() ) );
        requirementDetailVo1.setFieldVOS( fieldListToFieldVOList( requirementDetailVo.getFieldVOS() ) );
        requirementDetailVo1.setReviewVOS( reviewVOListToReviewVOList( requirementDetailVo.getReviewVOS() ) );
        requirementDetailVo1.setHanpUpReason( requirementDetailVo.getHanpUpReason() );
        requirementDetailVo1.setCloseReason( requirementDetailVo.getCloseReason() );
        requirementDetailVo1.setCreator( requirementDetailVo.getCreator() );
        requirementDetailVo1.setCreatorId( requirementDetailVo.getCreatorId() );
        requirementDetailVo1.setCheckType( checkTypeToCheckType( requirementDetailVo.getCheckType() ) );
        requirementDetailVo1.setDeliveryTime( deliveryTimeToDeliveryTime( requirementDetailVo.getDeliveryTime() ) );
        requirementDetailVo1.setDeliveryQuality( deliveryQualityToDeliveryQuality( requirementDetailVo.getDeliveryQuality() ) );
        requirementDetailVo1.setSatisfaction( deliverySatisfactionToDeliverySatisfaction( requirementDetailVo.getSatisfaction() ) );
        requirementDetailVo1.setDockService( dockServiceToDockService( requirementDetailVo.getDockService() ) );
        requirementDetailVo1.setDeliveryResult( deliveryResultToDeliveryResult( requirementDetailVo.getDeliveryResult() ) );
        requirementDetailVo1.setContent( requirementDetailVo.getContent() );
        requirementDetailVo1.setIsCheck( requirementDetailVo.getIsCheck() );
        requirementDetailVo1.setMsgCode( requirementDetailVo.getMsgCode() );
        requirementDetailVo1.setWarn( requirementDetailVo.getWarn() );
        requirementDetailVo1.setWarnDay( requirementDetailVo.getWarnDay() );

        return requirementDetailVo1;
    }

    protected com.zto.devops.qc.client.model.rpc.product.ProductAll productAllToProductAll(ProductAll productAll) {
        if ( productAll == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.product.ProductAll productAll1 = new com.zto.devops.qc.client.model.rpc.product.ProductAll();

        productAll1.setCode( productAll.getCode() );
        productAll1.setName( productAll.getName() );
        productAll1.setDescription( productAll.getDescription() );
        productAll1.setFreeze( productAll.getFreeze() );
        productAll1.setAvatar( productAll.getAvatar() );
        productAll1.setIsFollow( productAll.getIsFollow() );
        productAll1.setDeptId( productAll.getDeptId() );
        productAll1.setProductUserId( productAll.getProductUserId() );
        productAll1.setProductUserName( productAll.getProductUserName() );
        productAll1.setDeptName( productAll.getDeptName() );
        productAll1.setTestUserId( productAll.getTestUserId() );
        productAll1.setTestUserName( productAll.getTestUserName() );
        productAll1.setProductSource( productAll.getProductSource() );
        productAll1.setGrade( productAll.getGrade() );

        return productAll1;
    }

    protected List<com.zto.devops.qc.client.model.rpc.product.ProductAll> productAllListToProductAllList(List<ProductAll> list) {
        if ( list == null ) {
            return null;
        }

        List<com.zto.devops.qc.client.model.rpc.product.ProductAll> list1 = new ArrayList<com.zto.devops.qc.client.model.rpc.product.ProductAll>( list.size() );
        for ( ProductAll productAll : list ) {
            list1.add( productAllToProductAll( productAll ) );
        }

        return list1;
    }

    protected com.zto.devops.qc.client.model.rpc.product.PipelineProductVO pipelineProductVOToPipelineProductVO(PipelineProductVO pipelineProductVO) {
        if ( pipelineProductVO == null ) {
            return null;
        }

        com.zto.devops.qc.client.model.rpc.product.PipelineProductVO pipelineProductVO1 = new com.zto.devops.qc.client.model.rpc.product.PipelineProductVO();

        pipelineProductVO1.setName( pipelineProductVO.getName() );
        pipelineProductVO1.setCode( pipelineProductVO.getCode() );

        return pipelineProductVO1;
    }

    protected ApplicationResp applicationRespToApplicationResp(com.zto.devops.pipeline.client.service.inner.model.ApplicationResp applicationResp) {
        if ( applicationResp == null ) {
            return null;
        }

        ApplicationResp applicationResp1 = new ApplicationResp();

        applicationResp1.setCode( applicationResp.getCode() );
        applicationResp1.setName( applicationResp.getName() );
        applicationResp1.setAppId( applicationResp.getAppId() );
        applicationResp1.setCoreApplication( applicationResp.getCoreApplication() );

        return applicationResp1;
    }

    protected AutomaticStatusEnum automaticStatusEnumToAutomaticStatusEnum(com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum automaticStatusEnum) {
        if ( automaticStatusEnum == null ) {
            return null;
        }

        AutomaticStatusEnum automaticStatusEnum1;

        switch ( automaticStatusEnum ) {
            case UNKNOWN: automaticStatusEnum1 = AutomaticStatusEnum.UNKNOWN;
            break;
            case NOT_STARTED: automaticStatusEnum1 = AutomaticStatusEnum.NOT_STARTED;
            break;
            case SUBMITTED: automaticStatusEnum1 = AutomaticStatusEnum.SUBMITTED;
            break;
            case IN_PROGRESS: automaticStatusEnum1 = AutomaticStatusEnum.IN_PROGRESS;
            break;
            case SUCCESS: automaticStatusEnum1 = AutomaticStatusEnum.SUCCESS;
            break;
            case FAIL: automaticStatusEnum1 = AutomaticStatusEnum.FAIL;
            break;
            case TERMINATION: automaticStatusEnum1 = AutomaticStatusEnum.TERMINATION;
            break;
            case ERROR: automaticStatusEnum1 = AutomaticStatusEnum.ERROR;
            break;
            case ANALYSIS: automaticStatusEnum1 = AutomaticStatusEnum.ANALYSIS;
            break;
            case ANALYSISSUCCESSNOTCONFIRM: automaticStatusEnum1 = AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM;
            break;
            case ANALYSISSUCCESSABANDONED: automaticStatusEnum1 = AutomaticStatusEnum.ANALYSISSUCCESSABANDONED;
            break;
            case NALYSISSUCCESSCONFIRMED: automaticStatusEnum1 = AutomaticStatusEnum.NALYSISSUCCESSCONFIRMED;
            break;
            case CONFIRMING: automaticStatusEnum1 = AutomaticStatusEnum.CONFIRMING;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + automaticStatusEnum );
        }

        return automaticStatusEnum1;
    }

    protected List<SimpleQueryVO> simpleQueryVOListToSimpleQueryVOList(List<com.zto.devops.product.client.model.product.entity.SimpleQueryVO> list) {
        if ( list == null ) {
            return null;
        }

        List<SimpleQueryVO> list1 = new ArrayList<SimpleQueryVO>( list.size() );
        for ( com.zto.devops.product.client.model.product.entity.SimpleQueryVO simpleQueryVO : list ) {
            list1.add( convertSimpleQueryVO( simpleQueryVO ) );
        }

        return list1;
    }

    protected FeatureVO featureVOToFeatureVO(com.zto.devops.pipeline.client.model.plan.entity.FeatureVO featureVO) {
        if ( featureVO == null ) {
            return null;
        }

        FeatureVO featureVO1 = new FeatureVO();

        featureVO1.setName( featureVO.getName() );
        featureVO1.setDesc( featureVO.getDesc() );
        featureVO1.setKey( featureVO.getKey() );
        featureVO1.setValue( featureVO.getValue() );
        featureVO1.setIsShow( featureVO.getIsShow() );
        featureVO1.setUnAutoEnvDesc( featureVO.getUnAutoEnvDesc() );
        featureVO1.setAutoEnvDesc( featureVO.getAutoEnvDesc() );
        featureVO1.setUrl( featureVO.getUrl() );
        featureVO1.setCanSwitch( featureVO.getCanSwitch() );

        return featureVO1;
    }

    protected FlowBusinessTypeEnum flowBusinessTypeEnumToFlowBusinessTypeEnum(com.zto.devops.pipeline.client.enums.FlowBusinessTypeEnum flowBusinessTypeEnum) {
        if ( flowBusinessTypeEnum == null ) {
            return null;
        }

        FlowBusinessTypeEnum flowBusinessTypeEnum1;

        switch ( flowBusinessTypeEnum ) {
            case INTEGRATION: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.INTEGRATION;
            break;
            case DELIVERY: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.DELIVERY;
            break;
            case INT_DEPLOYMENT: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.INT_DEPLOYMENT;
            break;
            case HOT_DEPLOYMENT: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.HOT_DEPLOYMENT;
            break;
            case HOTFIX: flowBusinessTypeEnum1 = FlowBusinessTypeEnum.HOTFIX;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + flowBusinessTypeEnum );
        }

        return flowBusinessTypeEnum1;
    }

    protected com.zto.devops.qc.client.enums.rpc.FlowStatusEnum flowStatusEnumToFlowStatusEnum1(com.zto.devops.pipeline.client.enums.FlowStatusEnum flowStatusEnum) {
        if ( flowStatusEnum == null ) {
            return null;
        }

        com.zto.devops.qc.client.enums.rpc.FlowStatusEnum flowStatusEnum1;

        switch ( flowStatusEnum ) {
            case START: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.START;
            break;
            case END: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.END;
            break;
            case SCHEDULING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.SCHEDULING;
            break;
            case VERSION_MERGING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.VERSION_MERGING;
            break;
            case VERSION_MERGE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.VERSION_MERGE_FAILED;
            break;
            case DEVELOPING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.DEVELOPING;
            break;
            case WAIT_MERGE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_MERGE;
            break;
            case MERGE_TO_TEST: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_TEST;
            break;
            case MERGE_TO_PROD: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_PROD;
            break;
            case MERGE_TO_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TO_RELEASE;
            break;
            case MERGE_TRUNK: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.MERGE_TRUNK;
            break;
            case SMOKING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.SMOKING;
            break;
            case TESTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.TESTING;
            break;
            case WAIT_REGRESS: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_REGRESS;
            break;
            case ITERATING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ITERATING;
            break;
            case REGRESSING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSING;
            break;
            case REGRESSING_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSING_AUDIT;
            break;
            case REGRESSED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESSED;
            break;
            case REGRESS_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.REGRESS_FAILED;
            break;
            case WAIT_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_AUDIT;
            break;
            case AUDIT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.AUDIT_FAILED;
            break;
            case WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.WAIT_RELEASE;
            break;
            case RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.RELEASING;
            break;
            case RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.RELEASE_FAILED;
            break;
            case BACKUP_APPLY_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_APPLY_RELEASE;
            break;
            case BACKUP_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_WAIT_RELEASE;
            break;
            case BACKUP_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_RELEASING;
            break;
            case BACKUP_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_RELEASE_FAILED;
            break;
            case ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTING;
            break;
            case DELAY_ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.DELAY_ACCEPTING;
            break;
            case ACCEPTED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED;
            break;
            case ACCEPTED_WAIT_AUDIT: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_WAIT_AUDIT;
            break;
            case ACCEPTED_AUDIT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_AUDIT_FAILED;
            break;
            case ACCEPTED_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_WAIT_RELEASE;
            break;
            case ACCEPTED_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_RELEASING;
            break;
            case ACCEPTED_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_RELEASE_FAILED;
            break;
            case BACKUP_ACCEPTED_APPLY_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_APPLY_RELEASE;
            break;
            case BACKUP_ACCEPTED_WAIT_RELEASE: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_WAIT_RELEASE;
            break;
            case BACKUP_ACCEPTED_RELEASING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_RELEASING;
            break;
            case BACKUP_ACCEPTED_RELEASE_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKUP_ACCEPTED_RELEASE_FAILED;
            break;
            case ACCEPTED_ACCEPTING: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPTED_ACCEPTING;
            break;
            case ARCHIVED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ARCHIVED;
            break;
            case CLOSED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.CLOSED;
            break;
            case BACKED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.BACKED;
            break;
            case ACCEPT_FAILED: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.ACCEPT_FAILED;
            break;
            case APPLICATION_CHECK: flowStatusEnum1 = com.zto.devops.qc.client.enums.rpc.FlowStatusEnum.APPLICATION_CHECK;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + flowStatusEnum );
        }

        return flowStatusEnum1;
    }

    protected GeneralVO generalVOToGeneralVO(com.zto.devops.pipeline.client.model.common.model.GeneralVO generalVO) {
        if ( generalVO == null ) {
            return null;
        }

        GeneralVO generalVO1 = new GeneralVO();

        generalVO1.setCode( generalVO.getCode() );
        generalVO1.setName( generalVO.getName() );
        generalVO1.setStatus( generalVO.getStatus() );
        List<String> list = generalVO.getRole();
        if ( list != null ) {
            generalVO1.setRole( new ArrayList<String>( list ) );
        }
        generalVO1.setDescription( generalVO.getDescription() );
        generalVO1.setUrl( generalVO.getUrl() );
        generalVO1.setSort( generalVO.getSort() );

        return generalVO1;
    }

    protected List<GeneralVO> generalVOListToGeneralVOList(List<com.zto.devops.pipeline.client.model.common.model.GeneralVO> list) {
        if ( list == null ) {
            return null;
        }

        List<GeneralVO> list1 = new ArrayList<GeneralVO>( list.size() );
        for ( com.zto.devops.pipeline.client.model.common.model.GeneralVO generalVO : list ) {
            list1.add( generalVOToGeneralVO( generalVO ) );
        }

        return list1;
    }

    protected DeployTypeEnum deployTypeEnumToDeployTypeEnum(com.zto.devops.pipeline.client.enums.DeployTypeEnum deployTypeEnum) {
        if ( deployTypeEnum == null ) {
            return null;
        }

        DeployTypeEnum deployTypeEnum1;

        switch ( deployTypeEnum ) {
            case SPRINGBOOT: deployTypeEnum1 = DeployTypeEnum.SPRINGBOOT;
            break;
            case NGINX: deployTypeEnum1 = DeployTypeEnum.NGINX;
            break;
            case H5_GRAY: deployTypeEnum1 = DeployTypeEnum.H5_GRAY;
            break;
            case EXECUTABLE: deployTypeEnum1 = DeployTypeEnum.EXECUTABLE;
            break;
            case H5_HYBRID: deployTypeEnum1 = DeployTypeEnum.H5_HYBRID;
            break;
            case H5_CACHE: deployTypeEnum1 = DeployTypeEnum.H5_CACHE;
            break;
            case TOMCAT: deployTypeEnum1 = DeployTypeEnum.TOMCAT;
            break;
            case ZSS: deployTypeEnum1 = DeployTypeEnum.ZSS;
            break;
            case ZGP_WEB: deployTypeEnum1 = DeployTypeEnum.ZGP_WEB;
            break;
            case NGINX_W3C: deployTypeEnum1 = DeployTypeEnum.NGINX_W3C;
            break;
            case EMPTY: deployTypeEnum1 = DeployTypeEnum.EMPTY;
            break;
            case PYTHON: deployTypeEnum1 = DeployTypeEnum.PYTHON;
            break;
            case NODE_SERVER: deployTypeEnum1 = DeployTypeEnum.NODE_SERVER;
            break;
            case MOBILE: deployTypeEnum1 = DeployTypeEnum.MOBILE;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + deployTypeEnum );
        }

        return deployTypeEnum1;
    }

    protected ApplicationVO applicationVOToApplicationVO(com.zto.devops.pipeline.client.model.application.entity.ApplicationVO applicationVO) {
        if ( applicationVO == null ) {
            return null;
        }

        ApplicationVO applicationVO1 = new ApplicationVO();

        applicationVO1.setProductCode( applicationVO.getProductCode() );
        applicationVO1.setCode( applicationVO.getCode() );
        applicationVO1.setName( applicationVO.getName() );
        applicationVO1.setAppId( applicationVO.getAppId() );
        applicationVO1.setApplicationTypeCode( applicationVO.getApplicationTypeCode() );
        applicationVO1.setApplicationTypeName( applicationVO.getApplicationTypeName() );
        applicationVO1.setDeployType( deployTypeEnumToDeployTypeEnum( applicationVO.getDeployType() ) );
        applicationVO1.setGmtCreate( applicationVO.getGmtCreate() );
        applicationVO1.setCreatorId( applicationVO.getCreatorId() );
        applicationVO1.setCreator( applicationVO.getCreator() );
        applicationVO1.setFirstAlerterId( applicationVO.getFirstAlerterId() );
        applicationVO1.setFirstAlerterName( applicationVO.getFirstAlerterName() );
        applicationVO1.setSecondAlerterId( applicationVO.getSecondAlerterId() );
        applicationVO1.setSecondAlerterName( applicationVO.getSecondAlerterName() );
        if ( applicationVO.getGitProjectId() != null ) {
            applicationVO1.setGitProjectId( applicationVO.getGitProjectId().intValue() );
        }
        applicationVO1.setCoverageStandardValue( applicationVO.getCoverageStandardValue() );
        applicationVO1.setWhiteList( applicationVO.getWhiteList() );
        applicationVO1.setWhiteListReason( applicationVO.getWhiteListReason() );
        applicationVO1.setGmtModified( applicationVO.getGmtModified() );
        applicationVO1.setBuildType( applicationVO.getBuildType() );
        applicationVO1.setQaxProjectId( applicationVO.getQaxProjectId() );
        applicationVO1.setBackButtonList( generalVOListToGeneralVOList( applicationVO.getBackButtonList() ) );
        Map<String, Object> map = applicationVO.getTypeInput();
        if ( map != null ) {
            applicationVO1.setTypeInput( new HashMap<String, Object>( map ) );
        }
        applicationVO1.setProductName( applicationVO.getProductName() );
        applicationVO1.setApolloAppId( applicationVO.getApolloAppId() );
        applicationVO1.setTypeCode( applicationVO.getTypeCode() );
        applicationVO1.setBaseImage( applicationVO.getBaseImage() );
        applicationVO1.setDescription( applicationVO.getDescription() );
        applicationVO1.setEnable( applicationVO.getEnable() );
        applicationVO1.setModifierId( applicationVO.getModifierId() );
        applicationVO1.setModifier( applicationVO.getModifier() );

        return applicationVO1;
    }
}
