package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.QcVersionQualityMetricsEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.QcVersionQualityMetricsEntity;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class QcVersionQualityMetricsConverterImpl implements QcVersionQualityMetricsConverter {

    @Override
    public QcVersionQualityMetricsEntity convert(QcVersionQualityMetricsEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        QcVersionQualityMetricsEntity qcVersionQualityMetricsEntity = new QcVersionQualityMetricsEntity();

        qcVersionQualityMetricsEntity.setEnable( entityDO.getEnable() );
        qcVersionQualityMetricsEntity.setCreatorId( entityDO.getCreatorId() );
        qcVersionQualityMetricsEntity.setCreator( entityDO.getCreator() );
        qcVersionQualityMetricsEntity.setGmtCreate( entityDO.getGmtCreate() );
        qcVersionQualityMetricsEntity.setModifierId( entityDO.getModifierId() );
        qcVersionQualityMetricsEntity.setModifier( entityDO.getModifier() );
        qcVersionQualityMetricsEntity.setGmtModified( entityDO.getGmtModified() );
        qcVersionQualityMetricsEntity.setVersionCode( entityDO.getVersionCode() );
        qcVersionQualityMetricsEntity.setVersionName( entityDO.getVersionName() );
        qcVersionQualityMetricsEntity.setProductCode( entityDO.getProductCode() );
        qcVersionQualityMetricsEntity.setProductName( entityDO.getProductName() );
        qcVersionQualityMetricsEntity.setMetricType( entityDO.getMetricType() );
        qcVersionQualityMetricsEntity.setMetricValue( entityDO.getMetricValue() );

        return qcVersionQualityMetricsEntity;
    }
}
