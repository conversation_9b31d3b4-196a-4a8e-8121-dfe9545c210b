package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmEmailEntityDO;
import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.EmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.VersionEmailVO;
import com.zto.devops.qc.infrastructure.dao.entity.TmEmailEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmVersionEmailEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class EmailVOConverterImpl implements EmailVOConverter {

    @Override
    public DetailEmailVO convert(TmEmailEntity entity) {
        if ( entity == null ) {
            return null;
        }

        DetailEmailVO detailEmailVO = new DetailEmailVO();

        if ( entity.getSenderId() != null ) {
            detailEmailVO.setSendUserId( String.valueOf( entity.getSenderId() ) );
        }
        detailEmailVO.setSendUserName( entity.getSender() );
        detailEmailVO.setSendTime( entity.getSendDate() );
        detailEmailVO.setEmailCode( entity.getEmailCode() );
        detailEmailVO.setPreview( entity.getPreview() );
        detailEmailVO.setBusinessCode( entity.getBusinessCode() );
        detailEmailVO.setRelatePlanCode( entity.getRelatePlanCode() );
        detailEmailVO.setVersionCode( entity.getVersionCode() );
        detailEmailVO.setProductCode( entity.getProductCode() );
        detailEmailVO.setEmailSource( entity.getEmailSource() );
        detailEmailVO.setDataSource( entity.getDataSource() );

        return detailEmailVO;
    }

    @Override
    public EmailVO convertVO(TmEmailEntity entity) {
        if ( entity == null ) {
            return null;
        }

        EmailVO emailVO = new EmailVO();

        emailVO.setEmailTypeEnum( entity.getEmailType() );
        emailVO.setVersionDesc( entity.getVersionName() );
        if ( entity.getSenderId() != null ) {
            emailVO.setSendUserId( String.valueOf( entity.getSenderId() ) );
        }
        emailVO.setSendUserName( entity.getSender() );
        emailVO.setSendTime( entity.getSendDate() );
        emailVO.setEmailCode( entity.getEmailCode() );
        emailVO.setEmailName( entity.getEmailName() );
        emailVO.setBusinessCode( entity.getBusinessCode() );
        emailVO.setVersionCode( entity.getVersionCode() );
        emailVO.setRelatePlanCode( entity.getRelatePlanCode() );
        emailVO.setRelatePlanName( entity.getRelatePlanName() );
        emailVO.setPlanPresentationDate( entity.getPlanPresentationDate() );
        emailVO.setPlanApprovalExitDate( entity.getPlanApprovalExitDate() );

        return emailVO;
    }

    @Override
    public List<EmailVO> convert(List<TmEmailEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EmailVO> list = new ArrayList<EmailVO>( entityList.size() );
        for ( TmEmailEntity tmEmailEntity : entityList ) {
            list.add( convertVO( tmEmailEntity ) );
        }

        return list;
    }

    @Override
    public List<VersionEmailVO> convert(Collection<TmVersionEmailEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<VersionEmailVO> list = new ArrayList<VersionEmailVO>( entityList.size() );
        for ( TmVersionEmailEntity tmVersionEmailEntity : entityList ) {
            list.add( tmVersionEmailEntityToVersionEmailVO( tmVersionEmailEntity ) );
        }

        return list;
    }

    @Override
    public List<TmEmailEntityDO> convert2DOList(List<TmEmailEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmEmailEntityDO> list = new ArrayList<TmEmailEntityDO>( entityList.size() );
        for ( TmEmailEntity tmEmailEntity : entityList ) {
            list.add( tmEmailEntityToTmEmailEntityDO( tmEmailEntity ) );
        }

        return list;
    }

    @Override
    public TmEmailEntity convert2Entity(TmEmailEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TmEmailEntity tmEmailEntity = new TmEmailEntity();

        tmEmailEntity.setEnable( entityDO.getEnable() );
        tmEmailEntity.setCreatorId( entityDO.getCreatorId() );
        tmEmailEntity.setCreator( entityDO.getCreator() );
        tmEmailEntity.setGmtCreate( entityDO.getGmtCreate() );
        tmEmailEntity.setModifierId( entityDO.getModifierId() );
        tmEmailEntity.setModifier( entityDO.getModifier() );
        tmEmailEntity.setGmtModified( entityDO.getGmtModified() );
        tmEmailEntity.setId( entityDO.getId() );
        tmEmailEntity.setEmailCode( entityDO.getEmailCode() );
        tmEmailEntity.setEmailName( entityDO.getEmailName() );
        tmEmailEntity.setEmailType( entityDO.getEmailType() );
        tmEmailEntity.setEmailSource( entityDO.getEmailSource() );
        tmEmailEntity.setBusinessCode( entityDO.getBusinessCode() );
        tmEmailEntity.setBusinessName( entityDO.getBusinessName() );
        tmEmailEntity.setRelatePlanCode( entityDO.getRelatePlanCode() );
        tmEmailEntity.setRelatePlanName( entityDO.getRelatePlanName() );
        tmEmailEntity.setProductCode( entityDO.getProductCode() );
        tmEmailEntity.setProductName( entityDO.getProductName() );
        tmEmailEntity.setVersionCode( entityDO.getVersionCode() );
        tmEmailEntity.setVersionName( entityDO.getVersionName() );
        tmEmailEntity.setPlanPresentationDate( entityDO.getPlanPresentationDate() );
        tmEmailEntity.setPlanApprovalExitDate( entityDO.getPlanApprovalExitDate() );
        tmEmailEntity.setPreview( entityDO.getPreview() );
        tmEmailEntity.setSendDate( entityDO.getSendDate() );
        tmEmailEntity.setSenderId( entityDO.getSenderId() );
        tmEmailEntity.setSender( entityDO.getSender() );
        tmEmailEntity.setSyncCreate( entityDO.getSyncCreate() );
        tmEmailEntity.setSyncModified( entityDO.getSyncModified() );
        tmEmailEntity.setDataSource( entityDO.getDataSource() );

        return tmEmailEntity;
    }

    protected VersionEmailVO tmVersionEmailEntityToVersionEmailVO(TmVersionEmailEntity tmVersionEmailEntity) {
        if ( tmVersionEmailEntity == null ) {
            return null;
        }

        VersionEmailVO versionEmailVO = new VersionEmailVO();

        versionEmailVO.setEmailCode( tmVersionEmailEntity.getEmailCode() );
        versionEmailVO.setEmailName( tmVersionEmailEntity.getEmailName() );
        versionEmailVO.setBusinessCode( tmVersionEmailEntity.getBusinessCode() );
        versionEmailVO.setEmailType( tmVersionEmailEntity.getEmailType() );
        versionEmailVO.setVersionName( tmVersionEmailEntity.getVersionName() );
        versionEmailVO.setVersionCode( tmVersionEmailEntity.getVersionCode() );
        versionEmailVO.setProductCode( tmVersionEmailEntity.getProductCode() );
        versionEmailVO.setProductName( tmVersionEmailEntity.getProductName() );
        versionEmailVO.setGmtCreate( tmVersionEmailEntity.getGmtCreate() );
        versionEmailVO.setGmtModified( tmVersionEmailEntity.getGmtModified() );
        versionEmailVO.setCreator( tmVersionEmailEntity.getCreator() );
        if ( tmVersionEmailEntity.getCreatorId() != null ) {
            versionEmailVO.setCreatorId( String.valueOf( tmVersionEmailEntity.getCreatorId() ) );
        }
        versionEmailVO.setModifier( tmVersionEmailEntity.getModifier() );
        if ( tmVersionEmailEntity.getModifierId() != null ) {
            versionEmailVO.setModifierId( String.valueOf( tmVersionEmailEntity.getModifierId() ) );
        }
        versionEmailVO.setSender( tmVersionEmailEntity.getSender() );
        versionEmailVO.setSenderId( tmVersionEmailEntity.getSenderId() );
        versionEmailVO.setSendDate( tmVersionEmailEntity.getSendDate() );
        versionEmailVO.setSource( tmVersionEmailEntity.getSource() );

        return versionEmailVO;
    }

    protected TmEmailEntityDO tmEmailEntityToTmEmailEntityDO(TmEmailEntity tmEmailEntity) {
        if ( tmEmailEntity == null ) {
            return null;
        }

        TmEmailEntityDO tmEmailEntityDO = new TmEmailEntityDO();

        tmEmailEntityDO.setEnable( tmEmailEntity.getEnable() );
        tmEmailEntityDO.setCreatorId( tmEmailEntity.getCreatorId() );
        tmEmailEntityDO.setCreator( tmEmailEntity.getCreator() );
        tmEmailEntityDO.setGmtCreate( tmEmailEntity.getGmtCreate() );
        tmEmailEntityDO.setModifierId( tmEmailEntity.getModifierId() );
        tmEmailEntityDO.setModifier( tmEmailEntity.getModifier() );
        tmEmailEntityDO.setGmtModified( tmEmailEntity.getGmtModified() );
        if ( tmEmailEntity.hasId() ) {
            tmEmailEntityDO.setId( tmEmailEntity.getId() );
        }
        tmEmailEntityDO.setEmailCode( tmEmailEntity.getEmailCode() );
        tmEmailEntityDO.setEmailName( tmEmailEntity.getEmailName() );
        tmEmailEntityDO.setEmailType( tmEmailEntity.getEmailType() );
        tmEmailEntityDO.setEmailSource( tmEmailEntity.getEmailSource() );
        tmEmailEntityDO.setBusinessCode( tmEmailEntity.getBusinessCode() );
        tmEmailEntityDO.setBusinessName( tmEmailEntity.getBusinessName() );
        tmEmailEntityDO.setRelatePlanCode( tmEmailEntity.getRelatePlanCode() );
        tmEmailEntityDO.setRelatePlanName( tmEmailEntity.getRelatePlanName() );
        tmEmailEntityDO.setProductCode( tmEmailEntity.getProductCode() );
        tmEmailEntityDO.setProductName( tmEmailEntity.getProductName() );
        tmEmailEntityDO.setVersionCode( tmEmailEntity.getVersionCode() );
        tmEmailEntityDO.setVersionName( tmEmailEntity.getVersionName() );
        tmEmailEntityDO.setPlanPresentationDate( tmEmailEntity.getPlanPresentationDate() );
        tmEmailEntityDO.setPlanApprovalExitDate( tmEmailEntity.getPlanApprovalExitDate() );
        tmEmailEntityDO.setPreview( tmEmailEntity.getPreview() );
        tmEmailEntityDO.setSendDate( tmEmailEntity.getSendDate() );
        tmEmailEntityDO.setSenderId( tmEmailEntity.getSenderId() );
        tmEmailEntityDO.setSender( tmEmailEntity.getSender() );
        tmEmailEntityDO.setSyncCreate( tmEmailEntity.getSyncCreate() );
        tmEmailEntityDO.setSyncModified( tmEmailEntity.getSyncModified() );
        tmEmailEntityDO.setDataSource( tmEmailEntity.getDataSource() );

        return tmEmailEntityDO;
    }
}
