package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.RelatedToMeEnum;
import com.zto.devops.qc.client.model.dto.TmPlanIssueEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TmPlanIssueEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmTestPlanIssueEntityConverterImpl implements TmTestPlanIssueEntityConverter {

    @Override
    public TmPlanIssueEntity covert2Entity(TmPlanIssueEntityDO issueEntityDO) {
        if ( issueEntityDO == null ) {
            return null;
        }

        TmPlanIssueEntity tmPlanIssueEntity = new TmPlanIssueEntity();

        tmPlanIssueEntity.setEnable( issueEntityDO.getEnable() );
        tmPlanIssueEntity.setCreatorId( issueEntityDO.getCreatorId() );
        tmPlanIssueEntity.setCreator( issueEntityDO.getCreator() );
        tmPlanIssueEntity.setGmtCreate( issueEntityDO.getGmtCreate() );
        tmPlanIssueEntity.setModifierId( issueEntityDO.getModifierId() );
        tmPlanIssueEntity.setModifier( issueEntityDO.getModifier() );
        tmPlanIssueEntity.setGmtModified( issueEntityDO.getGmtModified() );
        tmPlanIssueEntity.setPlanCode( issueEntityDO.getPlanCode() );
        tmPlanIssueEntity.setVersionCode( issueEntityDO.getVersionCode() );
        tmPlanIssueEntity.setCodeOrTitle( issueEntityDO.getCodeOrTitle() );
        List<IssueStatus> list = issueEntityDO.getStatusList();
        if ( list != null ) {
            tmPlanIssueEntity.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        tmPlanIssueEntity.setCurrentUserId( issueEntityDO.getCurrentUserId() );
        List<Long> list1 = issueEntityDO.getHandleUserIdList();
        if ( list1 != null ) {
            tmPlanIssueEntity.setHandleUserIdList( new ArrayList<Long>( list1 ) );
        }
        List<Long> list2 = issueEntityDO.getDevelopUserIdList();
        if ( list2 != null ) {
            tmPlanIssueEntity.setDevelopUserIdList( new ArrayList<Long>( list2 ) );
        }
        List<Long> list3 = issueEntityDO.getTestUserIdList();
        if ( list3 != null ) {
            tmPlanIssueEntity.setTestUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<RelatedToMeEnum> list4 = issueEntityDO.getRelatedList();
        if ( list4 != null ) {
            tmPlanIssueEntity.setRelatedList( new ArrayList<RelatedToMeEnum>( list4 ) );
        }
        List<IssuePriority> list5 = issueEntityDO.getPriorityList();
        if ( list5 != null ) {
            tmPlanIssueEntity.setPriorityList( new ArrayList<IssuePriority>( list5 ) );
        }

        return tmPlanIssueEntity;
    }
}
