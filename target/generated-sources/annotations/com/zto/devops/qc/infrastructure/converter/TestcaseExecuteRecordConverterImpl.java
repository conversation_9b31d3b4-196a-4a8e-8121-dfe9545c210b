package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseExecuteRecordEntity;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseExecuteRecordConverterImpl implements TestcaseExecuteRecordConverter {

    @Override
    public TestcaseExecuteRecordEntity convert2Entity(TestcaseExecuteRecordEntityDO executeRecordEntityDO) {
        if ( executeRecordEntityDO == null ) {
            return null;
        }

        TestcaseExecuteRecordEntity testcaseExecuteRecordEntity = new TestcaseExecuteRecordEntity();

        testcaseExecuteRecordEntity.setEnable( executeRecordEntityDO.getEnable() );
        testcaseExecuteRecordEntity.setCreatorId( executeRecordEntityDO.getCreatorId() );
        testcaseExecuteRecordEntity.setCreator( executeRecordEntityDO.getCreator() );
        testcaseExecuteRecordEntity.setGmtCreate( executeRecordEntityDO.getGmtCreate() );
        testcaseExecuteRecordEntity.setModifierId( executeRecordEntityDO.getModifierId() );
        testcaseExecuteRecordEntity.setModifier( executeRecordEntityDO.getModifier() );
        testcaseExecuteRecordEntity.setGmtModified( executeRecordEntityDO.getGmtModified() );
        testcaseExecuteRecordEntity.setId( executeRecordEntityDO.getId() );
        testcaseExecuteRecordEntity.setTestcaseCode( executeRecordEntityDO.getTestcaseCode() );
        testcaseExecuteRecordEntity.setTestPlanCode( executeRecordEntityDO.getTestPlanCode() );
        testcaseExecuteRecordEntity.setTestStage( executeRecordEntityDO.getTestStage() );
        testcaseExecuteRecordEntity.setResult( executeRecordEntityDO.getResult() );
        testcaseExecuteRecordEntity.setAutomaticTaskCode( executeRecordEntityDO.getAutomaticTaskCode() );
        testcaseExecuteRecordEntity.setStartTime( executeRecordEntityDO.getStartTime() );
        testcaseExecuteRecordEntity.setFinishTime( executeRecordEntityDO.getFinishTime() );
        testcaseExecuteRecordEntity.setResultFile( executeRecordEntityDO.getResultFile() );
        testcaseExecuteRecordEntity.setExecLogFile( executeRecordEntityDO.getExecLogFile() );
        testcaseExecuteRecordEntity.setReportFile( executeRecordEntityDO.getReportFile() );

        return testcaseExecuteRecordEntity;
    }
}
