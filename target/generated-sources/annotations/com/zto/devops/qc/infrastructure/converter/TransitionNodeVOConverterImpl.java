package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TransitionNodeVO;
import com.zto.devops.qc.infrastructure.dao.entity.TransitionNodeEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TransitionNodeVOConverterImpl implements TransitionNodeVOConverter {

    @Override
    public List<TransitionNodeVO> converterList(List<TransitionNodeEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TransitionNodeVO> list = new ArrayList<TransitionNodeVO>( entityList.size() );
        for ( TransitionNodeEntity transitionNodeEntity : entityList ) {
            list.add( transitionNodeEntityToTransitionNodeVO( transitionNodeEntity ) );
        }

        return list;
    }

    @Override
    public List<TransitionNodeVO> convertTransitionNodeVO(Collection<TransitionNodeEntity> transitionNodeEntities) {
        if ( transitionNodeEntities == null ) {
            return null;
        }

        List<TransitionNodeVO> list = new ArrayList<TransitionNodeVO>( transitionNodeEntities.size() );
        for ( TransitionNodeEntity transitionNodeEntity : transitionNodeEntities ) {
            list.add( transitionNodeEntityToTransitionNodeVO( transitionNodeEntity ) );
        }

        return list;
    }

    @Override
    public List<TransitionNodeEntityDO> converter2DOList(List<TransitionNodeEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TransitionNodeEntityDO> list = new ArrayList<TransitionNodeEntityDO>( entityList.size() );
        for ( TransitionNodeEntity transitionNodeEntity : entityList ) {
            list.add( transitionNodeEntityToTransitionNodeEntityDO( transitionNodeEntity ) );
        }

        return list;
    }

    protected TransitionNodeVO transitionNodeEntityToTransitionNodeVO(TransitionNodeEntity transitionNodeEntity) {
        if ( transitionNodeEntity == null ) {
            return null;
        }

        TransitionNodeVO transitionNodeVO = new TransitionNodeVO();

        transitionNodeVO.setCode( transitionNodeEntity.getCode() );
        transitionNodeVO.setDomain( transitionNodeEntity.getDomain() );
        transitionNodeVO.setBusinessCode( transitionNodeEntity.getBusinessCode() );
        transitionNodeVO.setCurStatus( transitionNodeEntity.getCurStatus() );
        transitionNodeVO.setContent( transitionNodeEntity.getContent() );
        transitionNodeVO.setReason( transitionNodeEntity.getReason() );
        transitionNodeVO.setNextStatus( transitionNodeEntity.getNextStatus() );
        transitionNodeVO.setEnable( transitionNodeEntity.getEnable() );
        transitionNodeVO.setCreatorId( transitionNodeEntity.getCreatorId() );
        transitionNodeVO.setCreator( transitionNodeEntity.getCreator() );
        transitionNodeVO.setGmtCreate( transitionNodeEntity.getGmtCreate() );
        transitionNodeVO.setModifierId( transitionNodeEntity.getModifierId() );
        transitionNodeVO.setModifier( transitionNodeEntity.getModifier() );
        transitionNodeVO.setGmtModified( transitionNodeEntity.getGmtModified() );

        return transitionNodeVO;
    }

    protected TransitionNodeEntityDO transitionNodeEntityToTransitionNodeEntityDO(TransitionNodeEntity transitionNodeEntity) {
        if ( transitionNodeEntity == null ) {
            return null;
        }

        TransitionNodeEntityDO transitionNodeEntityDO = new TransitionNodeEntityDO();

        transitionNodeEntityDO.setCode( transitionNodeEntity.getCode() );
        transitionNodeEntityDO.setDomain( transitionNodeEntity.getDomain() );
        transitionNodeEntityDO.setBusinessCode( transitionNodeEntity.getBusinessCode() );
        transitionNodeEntityDO.setCurStatus( transitionNodeEntity.getCurStatus() );
        transitionNodeEntityDO.setContent( transitionNodeEntity.getContent() );
        transitionNodeEntityDO.setReason( transitionNodeEntity.getReason() );
        transitionNodeEntityDO.setNextStatus( transitionNodeEntity.getNextStatus() );
        transitionNodeEntityDO.setEnable( transitionNodeEntity.getEnable() );
        transitionNodeEntityDO.setCreatorId( transitionNodeEntity.getCreatorId() );
        transitionNodeEntityDO.setCreator( transitionNodeEntity.getCreator() );
        transitionNodeEntityDO.setGmtCreate( transitionNodeEntity.getGmtCreate() );
        transitionNodeEntityDO.setModifierId( transitionNodeEntity.getModifierId() );
        transitionNodeEntityDO.setModifier( transitionNodeEntity.getModifier() );
        transitionNodeEntityDO.setGmtModified( transitionNodeEntity.getGmtModified() );

        return transitionNodeEntityDO;
    }
}
