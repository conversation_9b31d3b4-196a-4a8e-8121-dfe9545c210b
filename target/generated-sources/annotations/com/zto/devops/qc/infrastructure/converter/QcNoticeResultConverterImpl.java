package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.QcNoticeResultEntityDO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.infrastructure.dao.entity.QcNoticeResultEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class QcNoticeResultConverterImpl implements QcNoticeResultConverter {

    @Override
    public List<SendUserInfoVO> convert(List<QcNoticeResultEntity> entitys) {
        if ( entitys == null ) {
            return null;
        }

        List<SendUserInfoVO> list = new ArrayList<SendUserInfoVO>( entitys.size() );
        for ( QcNoticeResultEntity qcNoticeResultEntity : entitys ) {
            list.add( qcNoticeResultEntityToSendUserInfoVO( qcNoticeResultEntity ) );
        }

        return list;
    }

    @Override
    public QcNoticeResultEntity convert(SendUserInfoVO sendUserInfoVO) {
        if ( sendUserInfoVO == null ) {
            return null;
        }

        QcNoticeResultEntity qcNoticeResultEntity = new QcNoticeResultEntity();

        qcNoticeResultEntity.setUserStation( sendUserInfoVO.getStation() );
        qcNoticeResultEntity.setUserAvatar( sendUserInfoVO.getAvatar() );
        if ( sendUserInfoVO.getUserId() != null ) {
            qcNoticeResultEntity.setUserId( sendUserInfoVO.getUserId().intValue() );
        }
        qcNoticeResultEntity.setUserName( sendUserInfoVO.getUserName() );
        qcNoticeResultEntity.setUserType( sendUserInfoVO.getUserType() );
        qcNoticeResultEntity.setDeptName( sendUserInfoVO.getDeptName() );
        qcNoticeResultEntity.setEmail( sendUserInfoVO.getEmail() );

        return qcNoticeResultEntity;
    }

    @Override
    public List<QcNoticeResultEntity> convertSendUserInfoVOS(List<SendUserInfoVO> sendUserInfoVO) {
        if ( sendUserInfoVO == null ) {
            return null;
        }

        List<QcNoticeResultEntity> list = new ArrayList<QcNoticeResultEntity>( sendUserInfoVO.size() );
        for ( SendUserInfoVO sendUserInfoVO1 : sendUserInfoVO ) {
            list.add( convert( sendUserInfoVO1 ) );
        }

        return list;
    }

    @Override
    public List<QcNoticeResultEntityDO> convert2DOList(List<QcNoticeResultEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<QcNoticeResultEntityDO> list = new ArrayList<QcNoticeResultEntityDO>( entityList.size() );
        for ( QcNoticeResultEntity qcNoticeResultEntity : entityList ) {
            list.add( qcNoticeResultEntityToQcNoticeResultEntityDO( qcNoticeResultEntity ) );
        }

        return list;
    }

    protected SendUserInfoVO qcNoticeResultEntityToSendUserInfoVO(QcNoticeResultEntity qcNoticeResultEntity) {
        if ( qcNoticeResultEntity == null ) {
            return null;
        }

        SendUserInfoVO sendUserInfoVO = new SendUserInfoVO();

        sendUserInfoVO.setUserName( qcNoticeResultEntity.getUserName() );
        if ( qcNoticeResultEntity.getUserId() != null ) {
            sendUserInfoVO.setUserId( qcNoticeResultEntity.getUserId().longValue() );
        }
        sendUserInfoVO.setUserType( qcNoticeResultEntity.getUserType() );
        sendUserInfoVO.setDeptName( qcNoticeResultEntity.getDeptName() );
        sendUserInfoVO.setEmail( qcNoticeResultEntity.getEmail() );

        return sendUserInfoVO;
    }

    protected QcNoticeResultEntityDO qcNoticeResultEntityToQcNoticeResultEntityDO(QcNoticeResultEntity qcNoticeResultEntity) {
        if ( qcNoticeResultEntity == null ) {
            return null;
        }

        QcNoticeResultEntityDO qcNoticeResultEntityDO = new QcNoticeResultEntityDO();

        qcNoticeResultEntityDO.setEnable( qcNoticeResultEntity.getEnable() );
        qcNoticeResultEntityDO.setCreatorId( qcNoticeResultEntity.getCreatorId() );
        qcNoticeResultEntityDO.setCreator( qcNoticeResultEntity.getCreator() );
        qcNoticeResultEntityDO.setGmtCreate( qcNoticeResultEntity.getGmtCreate() );
        qcNoticeResultEntityDO.setModifierId( qcNoticeResultEntity.getModifierId() );
        qcNoticeResultEntityDO.setModifier( qcNoticeResultEntity.getModifier() );
        qcNoticeResultEntityDO.setGmtModified( qcNoticeResultEntity.getGmtModified() );
        qcNoticeResultEntityDO.setCode( qcNoticeResultEntity.getCode() );
        qcNoticeResultEntityDO.setBusinessCode( qcNoticeResultEntity.getBusinessCode() );
        qcNoticeResultEntityDO.setUserId( qcNoticeResultEntity.getUserId() );
        qcNoticeResultEntityDO.setUserName( qcNoticeResultEntity.getUserName() );
        qcNoticeResultEntityDO.setUserAvatar( qcNoticeResultEntity.getUserAvatar() );
        qcNoticeResultEntityDO.setUserStation( qcNoticeResultEntity.getUserStation() );
        qcNoticeResultEntityDO.setDeptCode( qcNoticeResultEntity.getDeptCode() );
        qcNoticeResultEntityDO.setDeptName( qcNoticeResultEntity.getDeptName() );
        qcNoticeResultEntityDO.setUserType( qcNoticeResultEntity.getUserType() );
        qcNoticeResultEntityDO.setNoticeType( qcNoticeResultEntity.getNoticeType() );
        qcNoticeResultEntityDO.setStatus( qcNoticeResultEntity.getStatus() );
        qcNoticeResultEntityDO.setSendTime( qcNoticeResultEntity.getSendTime() );
        qcNoticeResultEntityDO.setFailSendReson( qcNoticeResultEntity.getFailSendReson() );
        qcNoticeResultEntityDO.setMsgId( qcNoticeResultEntity.getMsgId() );
        qcNoticeResultEntityDO.setEmail( qcNoticeResultEntity.getEmail() );

        return qcNoticeResultEntityDO;
    }
}
