package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueVersionEditedEvent;
import com.zto.devops.qc.infrastructure.dao.entity.IssueEntity;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueEntityConverterImpl implements IssueEntityConverter {

    @Override
    public IssueEntity convert(IssueAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueEntity issueEntity = new IssueEntity();

        issueEntity.setFindUserId( eventFinderUserId( event ) );
        issueEntity.setFindUserName( eventFinderUserName( event ) );
        issueEntity.setHandleUserId( eventHandlerUserId( event ) );
        issueEntity.setHandleUserName( eventHandlerUserName( event ) );
        issueEntity.setDevelopUserId( eventDeveloperUserId( event ) );
        issueEntity.setDevelopUserName( eventDeveloperUserName( event ) );
        issueEntity.setTestUserId( eventTesterUserId( event ) );
        issueEntity.setTestUserName( eventTesterUserName( event ) );
        issueEntity.setUpdateUserId( eventTransactorUserId( event ) );
        issueEntity.setUpdateUserName( eventTransactorUserName( event ) );
        issueEntity.setProductCode( eventProductCode( event ) );
        issueEntity.setProductName( eventProductName( event ) );
        issueEntity.setRequirementCode( eventRequirementCode( event ) );
        issueEntity.setRequirementName( eventRequirementName( event ) );
        issueEntity.setFindVersionCode( eventFindVersionCode( event ) );
        issueEntity.setFindVersionName( eventFindVersionName( event ) );
        issueEntity.setFixVersionCode( eventFixVersionCode( event ) );
        issueEntity.setFixVersionName( eventFixVersionName( event ) );
        issueEntity.setSprintCode( eventSprintCode( event ) );
        issueEntity.setSprintName( eventSprintName( event ) );
        issueEntity.setCode( event.getCode() );
        issueEntity.setTitle( event.getTitle() );
        issueEntity.setStatus( event.getStatus() );
        issueEntity.setPriority( event.getPriority() );
        issueEntity.setRootCause( event.getRootCause() );
        issueEntity.setType( event.getType() );
        issueEntity.setTestMethod( event.getTestMethod() );
        issueEntity.setFindStage( event.getFindStage() );
        issueEntity.setFindEnv( event.getFindEnv() );
        issueEntity.setRepetitionRate( event.getRepetitionRate() );
        issueEntity.setFindTime( event.getFindTime() );
        issueEntity.setUpdateTime( event.getUpdateTime() );
        issueEntity.setRequirementLevel( event.getRequirementLevel() );
        issueEntity.setDescription( event.getDescription() );
        issueEntity.setVersionConfirm( event.getVersionConfirm() );
        issueEntity.setApplicationType( event.getApplicationType() );
        issueEntity.setMsgCode( event.getMsgCode() );

        return issueEntity;
    }

    @Override
    public IssueEntity convert(IssueEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueEntity issueEntity = new IssueEntity();

        issueEntity.setHandleUserId( eventHandlerUserId1( event ) );
        issueEntity.setHandleUserName( eventHandlerUserName1( event ) );
        issueEntity.setDevelopUserId( eventDeveloperUserId1( event ) );
        issueEntity.setDevelopUserName( eventDeveloperUserName1( event ) );
        issueEntity.setTestUserId( eventTesterUserId1( event ) );
        issueEntity.setTestUserName( eventTesterUserName1( event ) );
        issueEntity.setModifierId( eventTransactorUserId1( event ) );
        issueEntity.setModifier( eventTransactorUserName1( event ) );
        issueEntity.setUpdateUserId( eventTransactorUserId1( event ) );
        issueEntity.setUpdateUserName( eventTransactorUserName1( event ) );
        issueEntity.setRequirementCode( eventRequirementCode1( event ) );
        issueEntity.setRequirementName( eventRequirementName1( event ) );
        issueEntity.setFindVersionCode( eventFindVersionCode1( event ) );
        issueEntity.setFindVersionName( eventFindVersionName1( event ) );
        issueEntity.setFixVersionCode( eventFixVersionCode1( event ) );
        issueEntity.setFixVersionName( eventFixVersionName1( event ) );
        issueEntity.setSprintCode( eventSprintCode1( event ) );
        issueEntity.setSprintName( eventSprintName1( event ) );
        issueEntity.setCode( event.getCode() );
        issueEntity.setTitle( event.getTitle() );
        issueEntity.setPriority( event.getPriority() );
        issueEntity.setRootCause( event.getRootCause() );
        issueEntity.setType( event.getType() );
        issueEntity.setTestMethod( event.getTestMethod() );
        issueEntity.setFindStage( event.getFindStage() );
        issueEntity.setFindEnv( event.getFindEnv() );
        issueEntity.setRepetitionRate( event.getRepetitionRate() );
        issueEntity.setUpdateTime( event.getUpdateTime() );
        issueEntity.setRequirementLevel( event.getRequirementLevel() );
        issueEntity.setDescription( event.getDescription() );
        issueEntity.setVersionConfirm( event.getVersionConfirm() );
        issueEntity.setApplicationType( event.getApplicationType() );
        issueEntity.setPlanStartDate( event.getPlanStartDate() );
        issueEntity.setPlanEndDate( event.getPlanEndDate() );

        return issueEntity;
    }

    @Override
    public IssueEntity convertVersion(IssueVersionEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        IssueEntity issueEntity = new IssueEntity();

        issueEntity.setHandleUserId( eventHandlerUserId2( event ) );
        issueEntity.setHandleUserName( eventHandlerUserName2( event ) );
        issueEntity.setDevelopUserId( eventDeveloperUserId2( event ) );
        issueEntity.setDevelopUserName( eventDeveloperUserName2( event ) );
        issueEntity.setTestUserId( eventTesterUserId2( event ) );
        issueEntity.setTestUserName( eventTesterUserName2( event ) );
        issueEntity.setModifierId( eventTransactorUserId2( event ) );
        issueEntity.setModifier( eventTransactorUserName2( event ) );
        issueEntity.setUpdateUserId( eventTransactorUserId2( event ) );
        issueEntity.setUpdateUserName( eventTransactorUserName2( event ) );
        issueEntity.setRequirementCode( eventRequirementCode2( event ) );
        issueEntity.setRequirementName( eventRequirementName2( event ) );
        issueEntity.setFindVersionCode( eventFindVersionCode2( event ) );
        issueEntity.setFindVersionName( eventFindVersionName2( event ) );
        issueEntity.setFixVersionCode( eventFixVersionCode2( event ) );
        issueEntity.setFixVersionName( eventFixVersionName2( event ) );
        issueEntity.setSprintCode( eventSprintCode2( event ) );
        issueEntity.setCode( event.getCode() );
        issueEntity.setTitle( event.getTitle() );
        issueEntity.setPriority( event.getPriority() );
        issueEntity.setRootCause( event.getRootCause() );
        issueEntity.setType( event.getType() );
        issueEntity.setTestMethod( event.getTestMethod() );
        issueEntity.setFindStage( event.getFindStage() );
        issueEntity.setFindEnv( event.getFindEnv() );
        issueEntity.setRepetitionRate( event.getRepetitionRate() );
        issueEntity.setUpdateTime( event.getUpdateTime() );
        issueEntity.setRequirementLevel( event.getRequirementLevel() );
        issueEntity.setDescription( event.getDescription() );
        issueEntity.setVersionConfirm( event.getVersionConfirm() );

        return issueEntity;
    }

    private Long eventFinderUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User finder = issueAddedEvent.getFinder();
        if ( finder == null ) {
            return null;
        }
        Long userId = finder.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventFinderUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User finder = issueAddedEvent.getFinder();
        if ( finder == null ) {
            return null;
        }
        String userName = finder.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventHandlerUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User handler = issueAddedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        Long userId = handler.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventHandlerUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User handler = issueAddedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        String userName = handler.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventDeveloperUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User developer = issueAddedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        Long userId = developer.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventDeveloperUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User developer = issueAddedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        String userName = developer.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTesterUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User tester = issueAddedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        Long userId = tester.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTesterUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User tester = issueAddedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        String userName = tester.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User transactor = issueAddedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        User transactor = issueAddedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private String eventProductCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Product product = issueAddedEvent.getProduct();
        if ( product == null ) {
            return null;
        }
        String code = product.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventProductName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Product product = issueAddedEvent.getProduct();
        if ( product == null ) {
            return null;
        }
        String name = product.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventRequirementCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Requirement requirement = issueAddedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String code = requirement.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventRequirementName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Requirement requirement = issueAddedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String name = requirement.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFindVersionCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Version findVersion = issueAddedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String code = findVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFindVersionName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Version findVersion = issueAddedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String name = findVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFixVersionCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Version fixVersion = issueAddedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String code = fixVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFixVersionName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Version fixVersion = issueAddedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String name = fixVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventSprintCode(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Sprint sprint = issueAddedEvent.getSprint();
        if ( sprint == null ) {
            return null;
        }
        String code = sprint.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventSprintName(IssueAddedEvent issueAddedEvent) {
        if ( issueAddedEvent == null ) {
            return null;
        }
        Sprint sprint = issueAddedEvent.getSprint();
        if ( sprint == null ) {
            return null;
        }
        String name = sprint.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private Long eventHandlerUserId1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User handler = issueEditedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        Long userId = handler.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventHandlerUserName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User handler = issueEditedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        String userName = handler.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventDeveloperUserId1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User developer = issueEditedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        Long userId = developer.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventDeveloperUserName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User developer = issueEditedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        String userName = developer.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTesterUserId1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User tester = issueEditedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        Long userId = tester.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTesterUserName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User tester = issueEditedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        String userName = tester.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User transactor = issueEditedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        User transactor = issueEditedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private String eventRequirementCode1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Requirement requirement = issueEditedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String code = requirement.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventRequirementName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Requirement requirement = issueEditedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String name = requirement.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFindVersionCode1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version findVersion = issueEditedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String code = findVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFindVersionName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version findVersion = issueEditedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String name = findVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFixVersionCode1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version fixVersion = issueEditedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String code = fixVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFixVersionName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Version fixVersion = issueEditedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String name = fixVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventSprintCode1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Sprint sprint = issueEditedEvent.getSprint();
        if ( sprint == null ) {
            return null;
        }
        String code = sprint.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventSprintName1(IssueEditedEvent issueEditedEvent) {
        if ( issueEditedEvent == null ) {
            return null;
        }
        Sprint sprint = issueEditedEvent.getSprint();
        if ( sprint == null ) {
            return null;
        }
        String name = sprint.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private Long eventHandlerUserId2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        User handler = issueVersionEditedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        Long userId = handler.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventHandlerUserName2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        User handler = issueVersionEditedEvent.getHandler();
        if ( handler == null ) {
            return null;
        }
        String userName = handler.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventDeveloperUserId2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        User developer = issueVersionEditedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        Long userId = developer.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventDeveloperUserName2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        User developer = issueVersionEditedEvent.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        String userName = developer.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTesterUserId2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        User tester = issueVersionEditedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        Long userId = tester.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTesterUserName2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        User tester = issueVersionEditedEvent.getTester();
        if ( tester == null ) {
            return null;
        }
        String userName = tester.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long eventTransactorUserId2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        User transactor = issueVersionEditedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        User transactor = issueVersionEditedEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private String eventRequirementCode2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        Requirement requirement = issueVersionEditedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String code = requirement.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventRequirementName2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        Requirement requirement = issueVersionEditedEvent.getRequirement();
        if ( requirement == null ) {
            return null;
        }
        String name = requirement.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFindVersionCode2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        Version findVersion = issueVersionEditedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String code = findVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFindVersionName2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        Version findVersion = issueVersionEditedEvent.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String name = findVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventFixVersionCode2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        Version fixVersion = issueVersionEditedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String code = fixVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String eventFixVersionName2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        Version fixVersion = issueVersionEditedEvent.getFixVersion();
        if ( fixVersion == null ) {
            return null;
        }
        String name = fixVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String eventSprintCode2(IssueVersionEditedEvent issueVersionEditedEvent) {
        if ( issueVersionEditedEvent == null ) {
            return null;
        }
        Sprint sprint = issueVersionEditedEvent.getSprint();
        if ( sprint == null ) {
            return null;
        }
        String code = sprint.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }
}
