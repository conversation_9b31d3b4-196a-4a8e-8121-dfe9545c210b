package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:27+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AttachmentVOSConverterImpl implements AttachmentVOSConverter {

    @Override
    public AttachmentVO convert(AttachmentEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AttachmentVO attachmentVO = new AttachmentVO();

        attachmentVO.setDomain( entity.getDomain() );
        attachmentVO.setBusinessCode( entity.getBusinessCode() );
        attachmentVO.setCode( entity.getCode() );
        attachmentVO.setUrl( entity.getUrl() );
        attachmentVO.setName( entity.getName() );
        attachmentVO.setRemoteFileId( entity.getRemoteFileId() );
        attachmentVO.setType( entity.getType() );
        attachmentVO.setDocumentType( entity.getDocumentType() );
        attachmentVO.setFileType( entity.getFileType() );
        attachmentVO.setSize( entity.getSize() );
        attachmentVO.setCreatorId( entity.getCreatorId() );
        attachmentVO.setCreator( entity.getCreator() );
        attachmentVO.setGmtCreate( entity.getGmtCreate() );
        attachmentVO.setModifierId( entity.getModifierId() );
        attachmentVO.setModifier( entity.getModifier() );
        attachmentVO.setGmtModified( entity.getGmtModified() );

        return attachmentVO;
    }

    @Override
    public List<AttachmentVO> convert(Collection<AttachmentEntity> entity) {
        if ( entity == null ) {
            return null;
        }

        List<AttachmentVO> list = new ArrayList<AttachmentVO>( entity.size() );
        for ( AttachmentEntity attachmentEntity : entity ) {
            list.add( convert( attachmentEntity ) );
        }

        return list;
    }

    @Override
    public AttachmentEntity convertVO(AttachmentVO vo) {
        if ( vo == null ) {
            return null;
        }

        AttachmentEntity attachmentEntity = new AttachmentEntity();

        attachmentEntity.setCreatorId( vo.getCreatorId() );
        attachmentEntity.setCreator( vo.getCreator() );
        attachmentEntity.setGmtCreate( vo.getGmtCreate() );
        attachmentEntity.setModifierId( vo.getModifierId() );
        attachmentEntity.setModifier( vo.getModifier() );
        attachmentEntity.setGmtModified( vo.getGmtModified() );
        attachmentEntity.setCode( vo.getCode() );
        attachmentEntity.setDomain( vo.getDomain() );
        attachmentEntity.setBusinessCode( vo.getBusinessCode() );
        attachmentEntity.setDocumentType( vo.getDocumentType() );
        attachmentEntity.setType( vo.getType() );
        attachmentEntity.setName( vo.getName() );
        attachmentEntity.setRemoteFileId( vo.getRemoteFileId() );
        attachmentEntity.setUrl( vo.getUrl() );
        attachmentEntity.setFileType( vo.getFileType() );
        attachmentEntity.setSize( vo.getSize() );

        return attachmentEntity;
    }

    @Override
    public List<AttachmentEntity> convertVOList(List<AttachmentVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<AttachmentEntity> list = new ArrayList<AttachmentEntity>( vos.size() );
        for ( AttachmentVO attachmentVO : vos ) {
            list.add( convertVO( attachmentVO ) );
        }

        return list;
    }
}
