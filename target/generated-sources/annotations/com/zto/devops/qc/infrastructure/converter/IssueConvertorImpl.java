package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.domain.model.Attachment;
import com.zto.devops.qc.domain.model.Comment;
import com.zto.devops.qc.domain.model.Issue;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CommentEntity;
import com.zto.devops.qc.infrastructure.dao.entity.IssueEntity;
import com.zto.devops.qc.infrastructure.dao.entity.StatisticsVersionIssueEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TransitionNodeEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueConvertorImpl implements IssueConvertor {

    @Override
    public Issue convert(IssueEntity entity) {
        if ( entity == null ) {
            return null;
        }

        Issue issue = new Issue();

        issue.setCreator( issueEntityToUser( entity ) );
        issue.setModifier( issueEntityToUser1( entity ) );
        issue.setHandler( issueEntityToUser2( entity ) );
        issue.setFinder( issueEntityToUser3( entity ) );
        issue.setDeveloper( issueEntityToUser4( entity ) );
        issue.setTester( issueEntityToUser5( entity ) );
        issue.setRequirement( issueEntityToRequirement( entity ) );
        issue.setProduct( issueEntityToProduct( entity ) );
        issue.setFindVersion( issueEntityToVersion( entity ) );
        issue.setFixVersion( issueEntityToVersion1( entity ) );
        issue.setSprint( issueEntityToSprint( entity ) );
        issue.setCode( entity.getCode() );
        issue.setTitle( entity.getTitle() );
        issue.setDescription( entity.getDescription() );
        issue.setEnable( entity.getEnable() );
        issue.setReopenTime( entity.getReopenTime() );
        issue.setStartFixTime( entity.getStartFixTime() );
        issue.setDelayFixTime( entity.getDelayFixTime() );
        issue.setDeliverTime( entity.getDeliverTime() );
        issue.setRejectTime( entity.getRejectTime() );
        issue.setCloseTime( entity.getCloseTime() );
        issue.setFindTime( entity.getFindTime() );
        issue.setUpdateTime( entity.getUpdateTime() );
        issue.setFindEnv( entity.getFindEnv() );
        issue.setFindStage( entity.getFindStage() );
        issue.setPriority( entity.getPriority() );
        issue.setRepetitionRate( entity.getRepetitionRate() );
        issue.setRootCause( entity.getRootCause() );
        issue.setTestMethod( entity.getTestMethod() );
        issue.setType( entity.getType() );
        issue.setStatus( entity.getStatus() );
        issue.setApplicationType( entity.getApplicationType() );

        return issue;
    }

    @Override
    public List<Attachment> convertList(Collection<AttachmentEntity> attachmentEntities) {
        if ( attachmentEntities == null ) {
            return null;
        }

        List<Attachment> list = new ArrayList<Attachment>( attachmentEntities.size() );
        for ( AttachmentEntity attachmentEntity : attachmentEntities ) {
            list.add( attachmentEntityToAttachment( attachmentEntity ) );
        }

        return list;
    }

    @Override
    public Comment convert(CommentEntity comment) {
        if ( comment == null ) {
            return null;
        }

        Comment comment1 = new Comment();

        comment1.setCreator( commentEntityToUser( comment ) );
        comment1.setCode( comment.getCode() );
        comment1.setBusinessCode( comment.getBusinessCode() );
        comment1.setTopRepliedCode( comment.getTopRepliedCode() );
        comment1.setRepliedCode( comment.getRepliedCode() );
        comment1.setRepliedUserName( comment.getRepliedUserName() );
        comment1.setLevel( comment.getLevel() );
        comment1.setContent( comment.getContent() );
        comment1.setEnable( comment.getEnable() );
        comment1.setGmtCreate( comment.getGmtCreate() );
        comment1.setDomain( comment.getDomain() );

        return comment1;
    }

    @Override
    public Set<Comment> convertCommentList(Collection<CommentEntity> commentEntities) {
        if ( commentEntities == null ) {
            return null;
        }

        Set<Comment> set = new HashSet<Comment>( Math.max( (int) ( commentEntities.size() / .75f ) + 1, 16 ) );
        for ( CommentEntity commentEntity : commentEntities ) {
            set.add( convert( commentEntity ) );
        }

        return set;
    }

    @Override
    public IssueEntityDO convert2DO(IssueEntity entity) {
        if ( entity == null ) {
            return null;
        }

        IssueEntityDO issueEntityDO = new IssueEntityDO();

        issueEntityDO.setEnable( entity.getEnable() );
        issueEntityDO.setCreatorId( entity.getCreatorId() );
        issueEntityDO.setCreator( entity.getCreator() );
        issueEntityDO.setGmtCreate( entity.getGmtCreate() );
        issueEntityDO.setModifierId( entity.getModifierId() );
        issueEntityDO.setModifier( entity.getModifier() );
        issueEntityDO.setGmtModified( entity.getGmtModified() );
        issueEntityDO.setCode( entity.getCode() );
        issueEntityDO.setTitle( entity.getTitle() );
        issueEntityDO.setStatus( entity.getStatus() );
        issueEntityDO.setPriority( entity.getPriority() );
        issueEntityDO.setRootCause( entity.getRootCause() );
        issueEntityDO.setType( entity.getType() );
        issueEntityDO.setTestMethod( entity.getTestMethod() );
        issueEntityDO.setFindStage( entity.getFindStage() );
        issueEntityDO.setFindEnv( entity.getFindEnv() );
        issueEntityDO.setRepetitionRate( entity.getRepetitionRate() );
        issueEntityDO.setReopenTime( entity.getReopenTime() );
        issueEntityDO.setReopen( entity.getReopen() );
        issueEntityDO.setActualWorkingHours( entity.getActualWorkingHours() );
        issueEntityDO.setStartFixTime( entity.getStartFixTime() );
        issueEntityDO.setDelayFixTime( entity.getDelayFixTime() );
        issueEntityDO.setDeliverTime( entity.getDeliverTime() );
        issueEntityDO.setRejectTime( entity.getRejectTime() );
        issueEntityDO.setCloseTime( entity.getCloseTime() );
        issueEntityDO.setFindTime( entity.getFindTime() );
        issueEntityDO.setUpdateTime( entity.getUpdateTime() );
        issueEntityDO.setProductCode( entity.getProductCode() );
        issueEntityDO.setProductName( entity.getProductName() );
        issueEntityDO.setRequirementCode( entity.getRequirementCode() );
        issueEntityDO.setRequirementName( entity.getRequirementName() );
        issueEntityDO.setRequirementLevel( entity.getRequirementLevel() );
        issueEntityDO.setFindVersionCode( entity.getFindVersionCode() );
        issueEntityDO.setFindVersionName( entity.getFindVersionName() );
        issueEntityDO.setFixVersionCode( entity.getFixVersionCode() );
        issueEntityDO.setFixVersionName( entity.getFixVersionName() );
        issueEntityDO.setSprintCode( entity.getSprintCode() );
        issueEntityDO.setSprintName( entity.getSprintName() );
        issueEntityDO.setFindUserId( entity.getFindUserId() );
        issueEntityDO.setFindUserName( entity.getFindUserName() );
        issueEntityDO.setUpdateUserId( entity.getUpdateUserId() );
        issueEntityDO.setUpdateUserName( entity.getUpdateUserName() );
        issueEntityDO.setHandleUserName( entity.getHandleUserName() );
        issueEntityDO.setHandleUserId( entity.getHandleUserId() );
        issueEntityDO.setDevelopUserId( entity.getDevelopUserId() );
        issueEntityDO.setDevelopUserName( entity.getDevelopUserName() );
        issueEntityDO.setTestUserId( entity.getTestUserId() );
        issueEntityDO.setTestUserName( entity.getTestUserName() );
        issueEntityDO.setDescription( entity.getDescription() );
        issueEntityDO.setVersionConfirm( entity.getVersionConfirm() );
        issueEntityDO.setOldCode( entity.getOldCode() );
        issueEntityDO.setExamination( entity.getExamination() );
        issueEntityDO.setTestOmission( entity.getTestOmission() );
        issueEntityDO.setCodeDefect( entity.getCodeDefect() );
        issueEntityDO.setTestOmissionVersion( entity.getTestOmissionVersion() );
        issueEntityDO.setCodeDefectVersion( entity.getCodeDefectVersion() );
        issueEntityDO.setApplicationType( entity.getApplicationType() );
        issueEntityDO.setPlanStartDate( entity.getPlanStartDate() );
        issueEntityDO.setPlanEndDate( entity.getPlanEndDate() );
        issueEntityDO.setMsgCode( entity.getMsgCode() );
        issueEntityDO.setIsValid( entity.getIsValid() );

        return issueEntityDO;
    }

    @Override
    public List<IssueEntityDO> convert2DOList(List<IssueEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IssueEntityDO> list = new ArrayList<IssueEntityDO>( entityList.size() );
        for ( IssueEntity issueEntity : entityList ) {
            list.add( convert2DO( issueEntity ) );
        }

        return list;
    }

    @Override
    public List<TransitionNodeEntityDO> convert2TransitionNodeEntityDOList(List<TransitionNodeEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TransitionNodeEntityDO> list = new ArrayList<TransitionNodeEntityDO>( entityList.size() );
        for ( TransitionNodeEntity transitionNodeEntity : entityList ) {
            list.add( transitionNodeEntityToTransitionNodeEntityDO( transitionNodeEntity ) );
        }

        return list;
    }

    @Override
    public StatisticsVersionIssueEntityDO convert(StatisticsVersionIssueEntity entity) {
        if ( entity == null ) {
            return null;
        }

        StatisticsVersionIssueEntityDO statisticsVersionIssueEntityDO = new StatisticsVersionIssueEntityDO();

        statisticsVersionIssueEntityDO.setVersionCode( entity.getVersionCode() );
        statisticsVersionIssueEntityDO.setName( entity.getName() );
        statisticsVersionIssueEntityDO.setStatus( entity.getStatus() );
        statisticsVersionIssueEntityDO.setType( entity.getType() );
        statisticsVersionIssueEntityDO.setActualPublishDate( entity.getActualPublishDate() );
        statisticsVersionIssueEntityDO.setProductCode( entity.getProductCode() );
        statisticsVersionIssueEntityDO.setDeptId( entity.getDeptId() );
        statisticsVersionIssueEntityDO.setDeptNo( entity.getDeptNo() );
        statisticsVersionIssueEntityDO.setUrgencyRepairTime( entity.getUrgencyRepairTime() );
        statisticsVersionIssueEntityDO.setHighRepairTime( entity.getHighRepairTime() );
        statisticsVersionIssueEntityDO.setMiddleRepairTime( entity.getMiddleRepairTime() );
        statisticsVersionIssueEntityDO.setLowRepairTime( entity.getLowRepairTime() );
        statisticsVersionIssueEntityDO.setUrgencyNum( entity.getUrgencyNum() );
        statisticsVersionIssueEntityDO.setHighNum( entity.getHighNum() );
        statisticsVersionIssueEntityDO.setMiddleNum( entity.getMiddleNum() );
        statisticsVersionIssueEntityDO.setLowNum( entity.getLowNum() );
        statisticsVersionIssueEntityDO.setUrgencyPunctualNum( entity.getUrgencyPunctualNum() );
        statisticsVersionIssueEntityDO.setHighPunctualNum( entity.getHighPunctualNum() );
        statisticsVersionIssueEntityDO.setMiddlePunctualNum( entity.getMiddlePunctualNum() );
        statisticsVersionIssueEntityDO.setUrgencyReopenNum( entity.getUrgencyReopenNum() );
        statisticsVersionIssueEntityDO.setHighReopenNum( entity.getHighReopenNum() );
        statisticsVersionIssueEntityDO.setMiddleReopenNum( entity.getMiddleReopenNum() );
        statisticsVersionIssueEntityDO.setLowReopenNum( entity.getLowReopenNum() );
        statisticsVersionIssueEntityDO.setUrgencyEffectiveNum( entity.getUrgencyEffectiveNum() );
        statisticsVersionIssueEntityDO.setHighEffectiveNum( entity.getHighEffectiveNum() );
        statisticsVersionIssueEntityDO.setMiddleEffectiveNum( entity.getMiddleEffectiveNum() );
        statisticsVersionIssueEntityDO.setLowEffectiveNum( entity.getLowEffectiveNum() );
        statisticsVersionIssueEntityDO.setRequirementIssueNum( entity.getRequirementIssueNum() );
        statisticsVersionIssueEntityDO.setVersionGmtCreate( entity.getVersionGmtCreate() );
        statisticsVersionIssueEntityDO.setGmtModified( entity.getGmtModified() );

        return statisticsVersionIssueEntityDO;
    }

    @Override
    public StatisticsVersionIssueEntity convert(StatisticsVersionIssueEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        StatisticsVersionIssueEntity statisticsVersionIssueEntity = new StatisticsVersionIssueEntity();

        statisticsVersionIssueEntity.setVersionCode( entityDO.getVersionCode() );
        statisticsVersionIssueEntity.setName( entityDO.getName() );
        statisticsVersionIssueEntity.setStatus( entityDO.getStatus() );
        statisticsVersionIssueEntity.setType( entityDO.getType() );
        statisticsVersionIssueEntity.setActualPublishDate( entityDO.getActualPublishDate() );
        statisticsVersionIssueEntity.setProductCode( entityDO.getProductCode() );
        statisticsVersionIssueEntity.setDeptId( entityDO.getDeptId() );
        statisticsVersionIssueEntity.setDeptNo( entityDO.getDeptNo() );
        statisticsVersionIssueEntity.setUrgencyRepairTime( entityDO.getUrgencyRepairTime() );
        statisticsVersionIssueEntity.setHighRepairTime( entityDO.getHighRepairTime() );
        statisticsVersionIssueEntity.setMiddleRepairTime( entityDO.getMiddleRepairTime() );
        statisticsVersionIssueEntity.setLowRepairTime( entityDO.getLowRepairTime() );
        statisticsVersionIssueEntity.setUrgencyNum( entityDO.getUrgencyNum() );
        statisticsVersionIssueEntity.setHighNum( entityDO.getHighNum() );
        statisticsVersionIssueEntity.setMiddleNum( entityDO.getMiddleNum() );
        statisticsVersionIssueEntity.setLowNum( entityDO.getLowNum() );
        statisticsVersionIssueEntity.setUrgencyPunctualNum( entityDO.getUrgencyPunctualNum() );
        statisticsVersionIssueEntity.setHighPunctualNum( entityDO.getHighPunctualNum() );
        statisticsVersionIssueEntity.setMiddlePunctualNum( entityDO.getMiddlePunctualNum() );
        statisticsVersionIssueEntity.setUrgencyReopenNum( entityDO.getUrgencyReopenNum() );
        statisticsVersionIssueEntity.setHighReopenNum( entityDO.getHighReopenNum() );
        statisticsVersionIssueEntity.setMiddleReopenNum( entityDO.getMiddleReopenNum() );
        statisticsVersionIssueEntity.setLowReopenNum( entityDO.getLowReopenNum() );
        statisticsVersionIssueEntity.setUrgencyEffectiveNum( entityDO.getUrgencyEffectiveNum() );
        statisticsVersionIssueEntity.setHighEffectiveNum( entityDO.getHighEffectiveNum() );
        statisticsVersionIssueEntity.setMiddleEffectiveNum( entityDO.getMiddleEffectiveNum() );
        statisticsVersionIssueEntity.setLowEffectiveNum( entityDO.getLowEffectiveNum() );
        statisticsVersionIssueEntity.setRequirementIssueNum( entityDO.getRequirementIssueNum() );
        statisticsVersionIssueEntity.setVersionGmtCreate( entityDO.getVersionGmtCreate() );
        statisticsVersionIssueEntity.setGmtModified( entityDO.getGmtModified() );

        return statisticsVersionIssueEntity;
    }

    protected User issueEntityToUser(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getCreatorId() );
        user.setUserName( issueEntity.getCreator() );

        return user;
    }

    protected User issueEntityToUser1(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getModifierId() );
        user.setUserName( issueEntity.getModifier() );

        return user;
    }

    protected User issueEntityToUser2(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getHandleUserId() );
        user.setUserName( issueEntity.getHandleUserName() );

        return user;
    }

    protected User issueEntityToUser3(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getFindUserId() );
        user.setUserName( issueEntity.getFindUserName() );

        return user;
    }

    protected User issueEntityToUser4(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getDevelopUserId() );
        user.setUserName( issueEntity.getDevelopUserName() );

        return user;
    }

    protected User issueEntityToUser5(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( issueEntity.getTestUserId() );
        user.setUserName( issueEntity.getTestUserName() );

        return user;
    }

    protected Requirement issueEntityToRequirement(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Requirement requirement = new Requirement();

        requirement.setCode( issueEntity.getRequirementCode() );
        requirement.setName( issueEntity.getRequirementName() );

        return requirement;
    }

    protected Product issueEntityToProduct(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Product product = new Product();

        product.setCode( issueEntity.getProductCode() );
        product.setName( issueEntity.getProductName() );

        return product;
    }

    protected Version issueEntityToVersion(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Version version = new Version();

        version.setCode( issueEntity.getFindVersionCode() );
        version.setName( issueEntity.getFindVersionName() );

        return version;
    }

    protected Version issueEntityToVersion1(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Version version = new Version();

        version.setCode( issueEntity.getFixVersionCode() );
        version.setName( issueEntity.getFixVersionName() );

        return version;
    }

    protected Sprint issueEntityToSprint(IssueEntity issueEntity) {
        if ( issueEntity == null ) {
            return null;
        }

        Sprint sprint = new Sprint();

        sprint.setCode( issueEntity.getSprintCode() );
        sprint.setName( issueEntity.getSprintName() );

        return sprint;
    }

    protected Attachment attachmentEntityToAttachment(AttachmentEntity attachmentEntity) {
        if ( attachmentEntity == null ) {
            return null;
        }

        Attachment attachment = new Attachment();

        attachment.setCode( attachmentEntity.getCode() );
        attachment.setBusinessCode( attachmentEntity.getBusinessCode() );
        attachment.setUrl( attachmentEntity.getUrl() );
        attachment.setName( attachmentEntity.getName() );
        attachment.setRemoteFileId( attachmentEntity.getRemoteFileId() );
        attachment.setDomain( attachmentEntity.getDomain() );
        attachment.setType( attachmentEntity.getType() );
        attachment.setDocumentType( attachmentEntity.getDocumentType() );
        attachment.setFileType( attachmentEntity.getFileType() );
        attachment.setSize( attachmentEntity.getSize() );

        return attachment;
    }

    protected User commentEntityToUser(CommentEntity commentEntity) {
        if ( commentEntity == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( commentEntity.getCreatorId() );
        user.setUserName( commentEntity.getCreator() );

        return user;
    }

    protected TransitionNodeEntityDO transitionNodeEntityToTransitionNodeEntityDO(TransitionNodeEntity transitionNodeEntity) {
        if ( transitionNodeEntity == null ) {
            return null;
        }

        TransitionNodeEntityDO transitionNodeEntityDO = new TransitionNodeEntityDO();

        transitionNodeEntityDO.setCode( transitionNodeEntity.getCode() );
        transitionNodeEntityDO.setDomain( transitionNodeEntity.getDomain() );
        transitionNodeEntityDO.setBusinessCode( transitionNodeEntity.getBusinessCode() );
        transitionNodeEntityDO.setCurStatus( transitionNodeEntity.getCurStatus() );
        transitionNodeEntityDO.setContent( transitionNodeEntity.getContent() );
        transitionNodeEntityDO.setReason( transitionNodeEntity.getReason() );
        transitionNodeEntityDO.setNextStatus( transitionNodeEntity.getNextStatus() );
        transitionNodeEntityDO.setEnable( transitionNodeEntity.getEnable() );
        transitionNodeEntityDO.setCreatorId( transitionNodeEntity.getCreatorId() );
        transitionNodeEntityDO.setCreator( transitionNodeEntity.getCreator() );
        transitionNodeEntityDO.setGmtCreate( transitionNodeEntity.getGmtCreate() );
        transitionNodeEntityDO.setModifierId( transitionNodeEntity.getModifierId() );
        transitionNodeEntityDO.setModifier( transitionNodeEntity.getModifier() );
        transitionNodeEntityDO.setGmtModified( transitionNodeEntity.getGmtModified() );

        return transitionNodeEntityDO;
    }
}
