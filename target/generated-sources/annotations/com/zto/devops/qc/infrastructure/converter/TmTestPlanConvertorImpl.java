package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanRangeEntityDO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestFunctionPointVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.event.EditPlanStatusEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.SendTestPlanEvent;
import com.zto.devops.qc.infrastructure.dao.entity.TestFunctionPointEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestPlanEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmEmailEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanRangeEntity;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmTestPlanConvertorImpl implements TmTestPlanConvertor {

    @Override
    public TmTestPlanEntity convert(TmTestPlanVO vo) {
        if ( vo == null ) {
            return null;
        }

        TmTestPlanEntity tmTestPlanEntity = new TmTestPlanEntity();

        tmTestPlanEntity.setCreatorId( vo.getCreatorId() );
        tmTestPlanEntity.setCreator( vo.getCreator() );
        tmTestPlanEntity.setGmtCreate( vo.getGmtCreate() );
        tmTestPlanEntity.setModifierId( vo.getModifierId() );
        tmTestPlanEntity.setModifier( vo.getModifier() );
        tmTestPlanEntity.setGmtModified( vo.getGmtModified() );
        tmTestPlanEntity.setCode( vo.getCode() );
        tmTestPlanEntity.setDeptId( vo.getDeptId() );
        tmTestPlanEntity.setDeptName( vo.getDeptName() );
        tmTestPlanEntity.setPlanName( vo.getPlanName() );
        tmTestPlanEntity.setStatus( vo.getStatus() );
        tmTestPlanEntity.setType( vo.getType() );
        tmTestPlanEntity.setProductCode( vo.getProductCode() );
        tmTestPlanEntity.setProductName( vo.getProductName() );
        tmTestPlanEntity.setVersionCode( vo.getVersionCode() );
        tmTestPlanEntity.setVersionName( vo.getVersionName() );
        tmTestPlanEntity.setTestStrategy( vo.getTestStrategy() );
        tmTestPlanEntity.setDeveloperNum( vo.getDeveloperNum() );
        tmTestPlanEntity.setTesterNum( vo.getTesterNum() );
        tmTestPlanEntity.setAccessDate( vo.getAccessDate() );
        tmTestPlanEntity.setAccessDatePartition( vo.getAccessDatePartition() );
        tmTestPlanEntity.setPermitDate( vo.getPermitDate() );
        tmTestPlanEntity.setPermitDatePartition( vo.getPermitDatePartition() );
        tmTestPlanEntity.setStartDate( vo.getStartDate() );
        tmTestPlanEntity.setPublishDate( vo.getPublishDate() );
        tmTestPlanEntity.setProductDirectorId( vo.getProductDirectorId() );
        tmTestPlanEntity.setProductDirectorName( vo.getProductDirectorName() );
        tmTestPlanEntity.setTestDirectorId( vo.getTestDirectorId() );
        tmTestPlanEntity.setTestDirectorName( vo.getTestDirectorName() );
        tmTestPlanEntity.setPlanDirectorId( vo.getPlanDirectorId() );
        tmTestPlanEntity.setPlanDirectorName( vo.getPlanDirectorName() );
        tmTestPlanEntity.setRelationPlanCode( vo.getRelationPlanCode() );
        tmTestPlanEntity.setEditNo( vo.getEditNo() );
        Map<String, Object> map = vo.getStageStatus();
        if ( map != null ) {
            tmTestPlanEntity.setStageStatus( new HashMap<String, Object>( map ) );
        }
        tmTestPlanEntity.setProductSource( vo.getProductSource() );
        tmTestPlanEntity.setComment( vo.getComment() );

        return tmTestPlanEntity;
    }

    @Override
    public TestFunctionPointEntity convert(TestFunctionPointVO vo) {
        if ( vo == null ) {
            return null;
        }

        TestFunctionPointEntity testFunctionPointEntity = new TestFunctionPointEntity();

        testFunctionPointEntity.setCode( vo.getCode() );
        testFunctionPointEntity.setType( vo.getType() );
        testFunctionPointEntity.setFunctionPoint( vo.getFunctionPoint() );
        testFunctionPointEntity.setDirectorId( vo.getDirectorId() );
        testFunctionPointEntity.setDirectorName( vo.getDirectorName() );
        testFunctionPointEntity.setNumber( vo.getNumber() );

        return testFunctionPointEntity;
    }

    @Override
    public TmTestPlanEntity convert(EditPlanStatusEvent event) {
        if ( event == null ) {
            return null;
        }

        TmTestPlanEntity tmTestPlanEntity = new TmTestPlanEntity();

        tmTestPlanEntity.setCode( event.getPlanCode() );
        tmTestPlanEntity.setType( event.getPlanType() );
        tmTestPlanEntity.setStatus( event.getStatus() );
        tmTestPlanEntity.setRelationPlanCode( event.getRelationPlanCode() );

        return tmTestPlanEntity;
    }

    @Override
    public TmEmailEntity convert(SendTestPlanEvent event) {
        if ( event == null ) {
            return null;
        }

        TmEmailEntity tmEmailEntity = new TmEmailEntity();

        tmEmailEntity.setEmailName( event.getName() );
        tmEmailEntity.setPlanPresentationDate( event.getAccessDate() );
        tmEmailEntity.setPlanApprovalExitDate( event.getPermitDate() );
        tmEmailEntity.setRelatePlanCode( event.getRelationPlanCode() );
        tmEmailEntity.setRelatePlanName( event.getRelationPlanName() );
        tmEmailEntity.setEmailCode( event.getEmailCode() );
        if ( event.getEmailType() != null ) {
            tmEmailEntity.setEmailType( Enum.valueOf( EmailTypeEnum.class, event.getEmailType() ) );
        }
        tmEmailEntity.setBusinessCode( event.getBusinessCode() );
        tmEmailEntity.setBusinessName( event.getBusinessName() );
        tmEmailEntity.setProductCode( event.getProductCode() );
        tmEmailEntity.setProductName( event.getProductName() );
        tmEmailEntity.setVersionCode( event.getVersionCode() );
        tmEmailEntity.setVersionName( event.getVersionName() );
        tmEmailEntity.setPreview( event.getPreview() );
        tmEmailEntity.setSendDate( event.getSendDate() );
        tmEmailEntity.setSenderId( event.getSenderId() );
        tmEmailEntity.setSender( event.getSender() );

        return tmEmailEntity;
    }

    @Override
    public TmTestPlanEntityDO covert(TmTestPlanEntity tmTestPlanEntity) {
        if ( tmTestPlanEntity == null ) {
            return null;
        }

        TmTestPlanEntityDO tmTestPlanEntityDO = new TmTestPlanEntityDO();

        tmTestPlanEntityDO.setEnable( tmTestPlanEntity.getEnable() );
        tmTestPlanEntityDO.setCreatorId( tmTestPlanEntity.getCreatorId() );
        tmTestPlanEntityDO.setCreator( tmTestPlanEntity.getCreator() );
        tmTestPlanEntityDO.setGmtCreate( tmTestPlanEntity.getGmtCreate() );
        tmTestPlanEntityDO.setModifierId( tmTestPlanEntity.getModifierId() );
        tmTestPlanEntityDO.setModifier( tmTestPlanEntity.getModifier() );
        tmTestPlanEntityDO.setGmtModified( tmTestPlanEntity.getGmtModified() );
        tmTestPlanEntityDO.setCode( tmTestPlanEntity.getCode() );
        tmTestPlanEntityDO.setDeptId( tmTestPlanEntity.getDeptId() );
        tmTestPlanEntityDO.setDeptName( tmTestPlanEntity.getDeptName() );
        tmTestPlanEntityDO.setPlanName( tmTestPlanEntity.getPlanName() );
        tmTestPlanEntityDO.setStatus( tmTestPlanEntity.getStatus() );
        tmTestPlanEntityDO.setType( tmTestPlanEntity.getType() );
        tmTestPlanEntityDO.setProductCode( tmTestPlanEntity.getProductCode() );
        tmTestPlanEntityDO.setProductName( tmTestPlanEntity.getProductName() );
        tmTestPlanEntityDO.setVersionCode( tmTestPlanEntity.getVersionCode() );
        tmTestPlanEntityDO.setVersionName( tmTestPlanEntity.getVersionName() );
        tmTestPlanEntityDO.setTestStrategy( tmTestPlanEntity.getTestStrategy() );
        tmTestPlanEntityDO.setDeveloperNum( tmTestPlanEntity.getDeveloperNum() );
        tmTestPlanEntityDO.setTesterNum( tmTestPlanEntity.getTesterNum() );
        tmTestPlanEntityDO.setAccessDate( tmTestPlanEntity.getAccessDate() );
        tmTestPlanEntityDO.setAccessDatePartition( tmTestPlanEntity.getAccessDatePartition() );
        tmTestPlanEntityDO.setPermitDate( tmTestPlanEntity.getPermitDate() );
        tmTestPlanEntityDO.setPermitDatePartition( tmTestPlanEntity.getPermitDatePartition() );
        tmTestPlanEntityDO.setStartDate( tmTestPlanEntity.getStartDate() );
        tmTestPlanEntityDO.setPublishDate( tmTestPlanEntity.getPublishDate() );
        tmTestPlanEntityDO.setProductDirectorId( tmTestPlanEntity.getProductDirectorId() );
        tmTestPlanEntityDO.setProductDirectorName( tmTestPlanEntity.getProductDirectorName() );
        tmTestPlanEntityDO.setTestDirectorId( tmTestPlanEntity.getTestDirectorId() );
        tmTestPlanEntityDO.setTestDirectorName( tmTestPlanEntity.getTestDirectorName() );
        tmTestPlanEntityDO.setPlanDirectorId( tmTestPlanEntity.getPlanDirectorId() );
        tmTestPlanEntityDO.setPlanDirectorName( tmTestPlanEntity.getPlanDirectorName() );
        tmTestPlanEntityDO.setRelationPlanCode( tmTestPlanEntity.getRelationPlanCode() );
        tmTestPlanEntityDO.setEditNo( tmTestPlanEntity.getEditNo() );
        Map<String, Object> map = tmTestPlanEntity.getStageStatus();
        if ( map != null ) {
            tmTestPlanEntityDO.setStageStatus( new HashMap<String, Object>( map ) );
        }
        tmTestPlanEntityDO.setHistoryStatus( tmTestPlanEntity.getHistoryStatus() );
        tmTestPlanEntityDO.setProductSource( tmTestPlanEntity.getProductSource() );
        tmTestPlanEntityDO.setComment( tmTestPlanEntity.getComment() );
        tmTestPlanEntityDO.setCheckFlag( tmTestPlanEntity.getCheckFlag() );

        return tmTestPlanEntityDO;
    }

    @Override
    public List<TmTestPlanEntityDO> covertList(List<TmTestPlanEntity> tmTestPlanEntityList) {
        if ( tmTestPlanEntityList == null ) {
            return null;
        }

        List<TmTestPlanEntityDO> list = new ArrayList<TmTestPlanEntityDO>( tmTestPlanEntityList.size() );
        for ( TmTestPlanEntity tmTestPlanEntity : tmTestPlanEntityList ) {
            list.add( covert( tmTestPlanEntity ) );
        }

        return list;
    }

    @Override
    public TestPlanEntityDO oldConvert(TestPlanEntity testPlanEntity) {
        if ( testPlanEntity == null ) {
            return null;
        }

        TestPlanEntityDO testPlanEntityDO = new TestPlanEntityDO();

        testPlanEntityDO.setEnable( testPlanEntity.getEnable() );
        testPlanEntityDO.setCreatorId( testPlanEntity.getCreatorId() );
        testPlanEntityDO.setCreator( testPlanEntity.getCreator() );
        testPlanEntityDO.setGmtCreate( testPlanEntity.getGmtCreate() );
        testPlanEntityDO.setModifierId( testPlanEntity.getModifierId() );
        testPlanEntityDO.setModifier( testPlanEntity.getModifier() );
        testPlanEntityDO.setGmtModified( testPlanEntity.getGmtModified() );
        testPlanEntityDO.setCode( testPlanEntity.getCode() );
        testPlanEntityDO.setPlanName( testPlanEntity.getPlanName() );
        testPlanEntityDO.setProductCode( testPlanEntity.getProductCode() );
        testPlanEntityDO.setProductName( testPlanEntity.getProductName() );
        testPlanEntityDO.setProductSource( testPlanEntity.getProductSource() );
        testPlanEntityDO.setType( testPlanEntity.getType() );
        testPlanEntityDO.setTypeDesc( testPlanEntity.getTypeDesc() );
        testPlanEntityDO.setPlanCode( testPlanEntity.getPlanCode() );
        testPlanEntityDO.setTestDirectorId( testPlanEntity.getTestDirectorId() );
        testPlanEntityDO.setTestDirectorName( testPlanEntity.getTestDirectorName() );
        testPlanEntityDO.setProductDirectorId( testPlanEntity.getProductDirectorId() );
        testPlanEntityDO.setProductDirectorName( testPlanEntity.getProductDirectorName() );
        testPlanEntityDO.setEditNo( testPlanEntity.getEditNo() );
        testPlanEntityDO.setStatus( testPlanEntity.getStatus() );
        testPlanEntityDO.setPreview( testPlanEntity.getPreview() );
        testPlanEntityDO.setVersionName( testPlanEntity.getVersionName() );
        testPlanEntityDO.setVersionCode( testPlanEntity.getVersionCode() );
        testPlanEntityDO.setDeptId( testPlanEntity.getDeptId() );
        testPlanEntityDO.setDeptName( testPlanEntity.getDeptName() );
        testPlanEntityDO.setJson( testPlanEntity.getJson() );

        return testPlanEntityDO;
    }

    @Override
    public List<TestPlanEntityDO> oldConvertList(List<TestPlanEntity> testPlanEntityList) {
        if ( testPlanEntityList == null ) {
            return null;
        }

        List<TestPlanEntityDO> list = new ArrayList<TestPlanEntityDO>( testPlanEntityList.size() );
        for ( TestPlanEntity testPlanEntity : testPlanEntityList ) {
            list.add( oldConvert( testPlanEntity ) );
        }

        return list;
    }

    @Override
    public TmTestPlanRangeEntityDO covert(TmTestPlanRangeEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TmTestPlanRangeEntityDO tmTestPlanRangeEntityDO = new TmTestPlanRangeEntityDO();

        tmTestPlanRangeEntityDO.setEnable( entity.getEnable() );
        tmTestPlanRangeEntityDO.setCreatorId( entity.getCreatorId() );
        tmTestPlanRangeEntityDO.setCreator( entity.getCreator() );
        tmTestPlanRangeEntityDO.setGmtCreate( entity.getGmtCreate() );
        tmTestPlanRangeEntityDO.setModifierId( entity.getModifierId() );
        tmTestPlanRangeEntityDO.setModifier( entity.getModifier() );
        tmTestPlanRangeEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            tmTestPlanRangeEntityDO.setId( entity.getId() );
        }
        tmTestPlanRangeEntityDO.setCode( entity.getCode() );
        tmTestPlanRangeEntityDO.setPlanCode( entity.getPlanCode() );
        tmTestPlanRangeEntityDO.setTestRange( entity.getTestRange() );
        tmTestPlanRangeEntityDO.setTestTime( entity.getTestTime() );
        tmTestPlanRangeEntityDO.setTestRangeStatus( entity.getTestRangeStatus() );
        tmTestPlanRangeEntityDO.setPriority( entity.getPriority() );
        tmTestPlanRangeEntityDO.setPermissionsTestInformation( entity.getPermissionsTestInformation() );
        tmTestPlanRangeEntityDO.setTestInformation( entity.getTestInformation() );
        tmTestPlanRangeEntityDO.setStatus( entity.getStatus() );
        tmTestPlanRangeEntityDO.setExecutorId( entity.getExecutorId() );
        tmTestPlanRangeEntityDO.setExecutor( entity.getExecutor() );

        return tmTestPlanRangeEntityDO;
    }

    @Override
    public TmTestPlanEntity convert2Entity(TmTestPlanEntityDO updateEntityDO) {
        if ( updateEntityDO == null ) {
            return null;
        }

        TmTestPlanEntity tmTestPlanEntity = new TmTestPlanEntity();

        tmTestPlanEntity.setEnable( updateEntityDO.getEnable() );
        tmTestPlanEntity.setCreatorId( updateEntityDO.getCreatorId() );
        tmTestPlanEntity.setCreator( updateEntityDO.getCreator() );
        tmTestPlanEntity.setGmtCreate( updateEntityDO.getGmtCreate() );
        tmTestPlanEntity.setModifierId( updateEntityDO.getModifierId() );
        tmTestPlanEntity.setModifier( updateEntityDO.getModifier() );
        tmTestPlanEntity.setGmtModified( updateEntityDO.getGmtModified() );
        tmTestPlanEntity.setCode( updateEntityDO.getCode() );
        tmTestPlanEntity.setDeptId( updateEntityDO.getDeptId() );
        tmTestPlanEntity.setDeptName( updateEntityDO.getDeptName() );
        tmTestPlanEntity.setPlanName( updateEntityDO.getPlanName() );
        tmTestPlanEntity.setStatus( updateEntityDO.getStatus() );
        tmTestPlanEntity.setType( updateEntityDO.getType() );
        tmTestPlanEntity.setProductCode( updateEntityDO.getProductCode() );
        tmTestPlanEntity.setProductName( updateEntityDO.getProductName() );
        tmTestPlanEntity.setVersionCode( updateEntityDO.getVersionCode() );
        tmTestPlanEntity.setVersionName( updateEntityDO.getVersionName() );
        tmTestPlanEntity.setTestStrategy( updateEntityDO.getTestStrategy() );
        tmTestPlanEntity.setDeveloperNum( updateEntityDO.getDeveloperNum() );
        tmTestPlanEntity.setTesterNum( updateEntityDO.getTesterNum() );
        tmTestPlanEntity.setAccessDate( updateEntityDO.getAccessDate() );
        tmTestPlanEntity.setAccessDatePartition( updateEntityDO.getAccessDatePartition() );
        tmTestPlanEntity.setPermitDate( updateEntityDO.getPermitDate() );
        tmTestPlanEntity.setPermitDatePartition( updateEntityDO.getPermitDatePartition() );
        tmTestPlanEntity.setStartDate( updateEntityDO.getStartDate() );
        tmTestPlanEntity.setPublishDate( updateEntityDO.getPublishDate() );
        tmTestPlanEntity.setProductDirectorId( updateEntityDO.getProductDirectorId() );
        tmTestPlanEntity.setProductDirectorName( updateEntityDO.getProductDirectorName() );
        tmTestPlanEntity.setTestDirectorId( updateEntityDO.getTestDirectorId() );
        tmTestPlanEntity.setTestDirectorName( updateEntityDO.getTestDirectorName() );
        tmTestPlanEntity.setPlanDirectorId( updateEntityDO.getPlanDirectorId() );
        tmTestPlanEntity.setPlanDirectorName( updateEntityDO.getPlanDirectorName() );
        tmTestPlanEntity.setRelationPlanCode( updateEntityDO.getRelationPlanCode() );
        tmTestPlanEntity.setEditNo( updateEntityDO.getEditNo() );
        Map<String, Object> map = updateEntityDO.getStageStatus();
        if ( map != null ) {
            tmTestPlanEntity.setStageStatus( new HashMap<String, Object>( map ) );
        }
        tmTestPlanEntity.setHistoryStatus( updateEntityDO.getHistoryStatus() );
        tmTestPlanEntity.setProductSource( updateEntityDO.getProductSource() );
        tmTestPlanEntity.setComment( updateEntityDO.getComment() );
        tmTestPlanEntity.setCheckFlag( updateEntityDO.getCheckFlag() );

        return tmTestPlanEntity;
    }

    @Override
    public List<TmTestPlanVO> converter2VO(List<TmTestPlanEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmTestPlanVO> list = new ArrayList<TmTestPlanVO>( entityList.size() );
        for ( TmTestPlanEntity tmTestPlanEntity : entityList ) {
            list.add( tmTestPlanEntityToTmTestPlanVO( tmTestPlanEntity ) );
        }

        return list;
    }

    protected TmTestPlanVO tmTestPlanEntityToTmTestPlanVO(TmTestPlanEntity tmTestPlanEntity) {
        if ( tmTestPlanEntity == null ) {
            return null;
        }

        TmTestPlanVO tmTestPlanVO = new TmTestPlanVO();

        tmTestPlanVO.setCode( tmTestPlanEntity.getCode() );
        tmTestPlanVO.setPlanName( tmTestPlanEntity.getPlanName() );
        tmTestPlanVO.setType( tmTestPlanEntity.getType() );
        tmTestPlanVO.setTestStrategy( tmTestPlanEntity.getTestStrategy() );
        tmTestPlanVO.setStatus( tmTestPlanEntity.getStatus() );
        tmTestPlanVO.setRelationPlanCode( tmTestPlanEntity.getRelationPlanCode() );
        tmTestPlanVO.setDeptId( tmTestPlanEntity.getDeptId() );
        tmTestPlanVO.setDeptName( tmTestPlanEntity.getDeptName() );
        tmTestPlanVO.setProductCode( tmTestPlanEntity.getProductCode() );
        tmTestPlanVO.setProductName( tmTestPlanEntity.getProductName() );
        tmTestPlanVO.setVersionCode( tmTestPlanEntity.getVersionCode() );
        tmTestPlanVO.setVersionName( tmTestPlanEntity.getVersionName() );
        tmTestPlanVO.setStartDate( tmTestPlanEntity.getStartDate() );
        tmTestPlanVO.setPublishDate( tmTestPlanEntity.getPublishDate() );
        tmTestPlanVO.setAccessDate( tmTestPlanEntity.getAccessDate() );
        tmTestPlanVO.setAccessDatePartition( tmTestPlanEntity.getAccessDatePartition() );
        tmTestPlanVO.setPermitDate( tmTestPlanEntity.getPermitDate() );
        tmTestPlanVO.setPermitDatePartition( tmTestPlanEntity.getPermitDatePartition() );
        tmTestPlanVO.setDeveloperNum( tmTestPlanEntity.getDeveloperNum() );
        tmTestPlanVO.setTesterNum( tmTestPlanEntity.getTesterNum() );
        tmTestPlanVO.setComment( tmTestPlanEntity.getComment() );
        tmTestPlanVO.setProductDirectorId( tmTestPlanEntity.getProductDirectorId() );
        tmTestPlanVO.setProductDirectorName( tmTestPlanEntity.getProductDirectorName() );
        tmTestPlanVO.setTestDirectorId( tmTestPlanEntity.getTestDirectorId() );
        tmTestPlanVO.setTestDirectorName( tmTestPlanEntity.getTestDirectorName() );
        tmTestPlanVO.setPlanDirectorId( tmTestPlanEntity.getPlanDirectorId() );
        tmTestPlanVO.setPlanDirectorName( tmTestPlanEntity.getPlanDirectorName() );
        tmTestPlanVO.setCreatorId( tmTestPlanEntity.getCreatorId() );
        tmTestPlanVO.setCreator( tmTestPlanEntity.getCreator() );
        tmTestPlanVO.setGmtCreate( tmTestPlanEntity.getGmtCreate() );
        tmTestPlanVO.setModifierId( tmTestPlanEntity.getModifierId() );
        tmTestPlanVO.setModifier( tmTestPlanEntity.getModifier() );
        tmTestPlanVO.setGmtModified( tmTestPlanEntity.getGmtModified() );
        tmTestPlanVO.setEditNo( tmTestPlanEntity.getEditNo() );
        tmTestPlanVO.setProductSource( tmTestPlanEntity.getProductSource() );
        Map<String, Object> map = tmTestPlanEntity.getStageStatus();
        if ( map != null ) {
            tmTestPlanVO.setStageStatus( new HashMap<String, Object>( map ) );
        }

        return tmTestPlanVO;
    }
}
