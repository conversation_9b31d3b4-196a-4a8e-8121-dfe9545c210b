package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.ApiGlobalConfigurationEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.ApiGlobalConfigurationEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class ApiGlobalConfigurationEntityConverterImpl implements ApiGlobalConfigurationEntityConverter {

    @Override
    public List<ApiGlobalConfigurationEntity> convertApiGlobalConfigurationEntity(List<ApiGlobalConfigurationEntityDO> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiGlobalConfigurationEntity> list1 = new ArrayList<ApiGlobalConfigurationEntity>( list.size() );
        for ( ApiGlobalConfigurationEntityDO apiGlobalConfigurationEntityDO : list ) {
            list1.add( convertApiGlobalConfigurationEntity( apiGlobalConfigurationEntityDO ) );
        }

        return list1;
    }

    @Override
    public ApiGlobalConfigurationEntity convertApiGlobalConfigurationEntity(ApiGlobalConfigurationEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        ApiGlobalConfigurationEntity apiGlobalConfigurationEntity = new ApiGlobalConfigurationEntity();

        apiGlobalConfigurationEntity.setApiConfigCode( entityDO.getApiConfigCode() );
        apiGlobalConfigurationEntity.setProductCode( entityDO.getProductCode() );
        apiGlobalConfigurationEntity.setApiConfigType( entityDO.getApiConfigType() );
        apiGlobalConfigurationEntity.setApiConfigScope( entityDO.getApiConfigScope() );
        apiGlobalConfigurationEntity.setApiConfigValue( entityDO.getApiConfigValue() );
        apiGlobalConfigurationEntity.setApiConfigAssert( entityDO.getApiConfigAssert() );
        apiGlobalConfigurationEntity.setEnable( entityDO.getEnable() );
        apiGlobalConfigurationEntity.setCreatorId( entityDO.getCreatorId() );
        apiGlobalConfigurationEntity.setModifierId( entityDO.getModifierId() );
        apiGlobalConfigurationEntity.setGmtCreate( entityDO.getGmtCreate() );
        apiGlobalConfigurationEntity.setGmtModified( entityDO.getGmtModified() );
        apiGlobalConfigurationEntity.setCreator( entityDO.getCreator() );
        apiGlobalConfigurationEntity.setModifier( entityDO.getModifier() );

        return apiGlobalConfigurationEntity;
    }

    @Override
    public List<ApiGlobalConfigurationEntityDO> convertApiGlobalConfigurationEntityDO(List<ApiGlobalConfigurationEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<ApiGlobalConfigurationEntityDO> list1 = new ArrayList<ApiGlobalConfigurationEntityDO>( list.size() );
        for ( ApiGlobalConfigurationEntity apiGlobalConfigurationEntity : list ) {
            list1.add( convertApiGlobalConfigurationEntityDO( apiGlobalConfigurationEntity ) );
        }

        return list1;
    }

    @Override
    public ApiGlobalConfigurationEntityDO convertApiGlobalConfigurationEntityDO(ApiGlobalConfigurationEntity entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        ApiGlobalConfigurationEntityDO apiGlobalConfigurationEntityDO = new ApiGlobalConfigurationEntityDO();

        apiGlobalConfigurationEntityDO.setApiConfigCode( entityDO.getApiConfigCode() );
        apiGlobalConfigurationEntityDO.setProductCode( entityDO.getProductCode() );
        apiGlobalConfigurationEntityDO.setApiConfigType( entityDO.getApiConfigType() );
        apiGlobalConfigurationEntityDO.setApiConfigScope( entityDO.getApiConfigScope() );
        apiGlobalConfigurationEntityDO.setApiConfigValue( entityDO.getApiConfigValue() );
        apiGlobalConfigurationEntityDO.setApiConfigAssert( entityDO.getApiConfigAssert() );
        apiGlobalConfigurationEntityDO.setEnable( entityDO.getEnable() );
        apiGlobalConfigurationEntityDO.setCreatorId( entityDO.getCreatorId() );
        apiGlobalConfigurationEntityDO.setCreator( entityDO.getCreator() );
        apiGlobalConfigurationEntityDO.setGmtCreate( entityDO.getGmtCreate() );
        apiGlobalConfigurationEntityDO.setModifierId( entityDO.getModifierId() );
        apiGlobalConfigurationEntityDO.setModifier( entityDO.getModifier() );
        apiGlobalConfigurationEntityDO.setGmtModified( entityDO.getGmtModified() );

        return apiGlobalConfigurationEntityDO;
    }
}
