package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddTestcaseEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogTestcaseEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:27+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSourceLogTestcaseEntityConverterImpl implements AutomaticSourceLogTestcaseEntityConverter {

    @Override
    public AutomaticSourceLogTestcaseEntity convert(AddTestcaseEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSourceLogTestcaseEntity automaticSourceLogTestcaseEntity = new AutomaticSourceLogTestcaseEntity();

        automaticSourceLogTestcaseEntity.setCode( event.getCode() );
        automaticSourceLogTestcaseEntity.setProductCode( event.getProductCode() );
        automaticSourceLogTestcaseEntity.setParentCode( event.getParentCode() );
        automaticSourceLogTestcaseEntity.setName( event.getName() );
        if ( event.getAttribute() != null ) {
            automaticSourceLogTestcaseEntity.setAttribute( event.getAttribute().name() );
        }
        if ( event.getType() != null ) {
            automaticSourceLogTestcaseEntity.setType( event.getType().name() );
        }
        if ( event.getPriority() != null ) {
            automaticSourceLogTestcaseEntity.setPriority( event.getPriority().name() );
        }
        if ( event.getStatus() != null ) {
            automaticSourceLogTestcaseEntity.setStatus( event.getStatus().name() );
        }
        automaticSourceLogTestcaseEntity.setPrecondition( event.getPrecondition() );
        automaticSourceLogTestcaseEntity.setDutyUserId( event.getDutyUserId() );
        automaticSourceLogTestcaseEntity.setDutyUser( event.getDutyUser() );
        automaticSourceLogTestcaseEntity.setComment( event.getComment() );
        if ( event.getNodeType() != null ) {
            automaticSourceLogTestcaseEntity.setNodeType( event.getNodeType().name() );
        }
        automaticSourceLogTestcaseEntity.setSort( event.getSort() );
        automaticSourceLogTestcaseEntity.setLayer( event.getLayer() );
        automaticSourceLogTestcaseEntity.setPath( event.getPath() );
        automaticSourceLogTestcaseEntity.setNodeTypePath( event.getNodeTypePath() );
        automaticSourceLogTestcaseEntity.setFlag( event.getFlag() );
        automaticSourceLogTestcaseEntity.setInterfaceName( event.getInterfaceName() );

        return automaticSourceLogTestcaseEntity;
    }

    @Override
    public AutomaticSourceLogTestcaseVO convertVO(AutomaticSourceLogTestcaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticSourceLogTestcaseVO automaticSourceLogTestcaseVO = new AutomaticSourceLogTestcaseVO();

        automaticSourceLogTestcaseVO.setFlag( entity.getFlag() );
        automaticSourceLogTestcaseVO.setStatus( entity.getStatus() );
        automaticSourceLogTestcaseVO.setAttribute( entity.getAttribute() );
        automaticSourceLogTestcaseVO.setCode( entity.getCode() );
        automaticSourceLogTestcaseVO.setProductCode( entity.getProductCode() );
        automaticSourceLogTestcaseVO.setParentCode( entity.getParentCode() );
        automaticSourceLogTestcaseVO.setName( entity.getName() );
        automaticSourceLogTestcaseVO.setType( entity.getType() );
        automaticSourceLogTestcaseVO.setPriority( entity.getPriority() );
        automaticSourceLogTestcaseVO.setPrecondition( entity.getPrecondition() );
        automaticSourceLogTestcaseVO.setDutyUserId( entity.getDutyUserId() );
        automaticSourceLogTestcaseVO.setDutyUser( entity.getDutyUser() );
        automaticSourceLogTestcaseVO.setComment( entity.getComment() );
        automaticSourceLogTestcaseVO.setAutomaticSourceLogCode( entity.getAutomaticSourceLogCode() );
        automaticSourceLogTestcaseVO.setNodeType( entity.getNodeType() );
        automaticSourceLogTestcaseVO.setSort( entity.getSort() );
        automaticSourceLogTestcaseVO.setLayer( entity.getLayer() );
        automaticSourceLogTestcaseVO.setPath( entity.getPath() );
        automaticSourceLogTestcaseVO.setEnable( entity.getEnable() );
        automaticSourceLogTestcaseVO.setCreatorId( entity.getCreatorId() );
        automaticSourceLogTestcaseVO.setCreator( entity.getCreator() );
        automaticSourceLogTestcaseVO.setGmtCreate( entity.getGmtCreate() );
        automaticSourceLogTestcaseVO.setModifierId( entity.getModifierId() );
        automaticSourceLogTestcaseVO.setModifier( entity.getModifier() );
        automaticSourceLogTestcaseVO.setGmtModified( entity.getGmtModified() );
        automaticSourceLogTestcaseVO.setNodeTypePath( entity.getNodeTypePath() );
        automaticSourceLogTestcaseVO.setTestcaseCode( entity.getTestcaseCode() );
        automaticSourceLogTestcaseVO.setInterfaceName( entity.getInterfaceName() );

        return automaticSourceLogTestcaseVO;
    }

    @Override
    public List<AutomaticSourceLogTestcaseVO> convertVOList(List<AutomaticSourceLogTestcaseEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<AutomaticSourceLogTestcaseVO> list1 = new ArrayList<AutomaticSourceLogTestcaseVO>( list.size() );
        for ( AutomaticSourceLogTestcaseEntity automaticSourceLogTestcaseEntity : list ) {
            list1.add( convertVO( automaticSourceLogTestcaseEntity ) );
        }

        return list1;
    }
}
