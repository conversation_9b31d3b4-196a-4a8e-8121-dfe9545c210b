package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddAutomaticRecordEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticSuccessEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditAutomaticRecordEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSourceRecordEntityConverterImpl implements AutomaticSourceRecordEntityConverter {

    @Override
    public AutomaticRecordVO convertVO(AutomaticSourceRecordEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticRecordVO automaticRecordVO = new AutomaticRecordVO();

        automaticRecordVO.setCode( entity.getCode() );
        automaticRecordVO.setProductCode( entity.getProductCode() );
        automaticRecordVO.setName( entity.getName() );
        automaticRecordVO.setComment( entity.getComment() );
        automaticRecordVO.setAddress( entity.getAddress() );
        automaticRecordVO.setType( entity.getType() );
        automaticRecordVO.setCreatorId( entity.getCreatorId() );
        automaticRecordVO.setCreator( entity.getCreator() );
        automaticRecordVO.setGmtCreate( entity.getGmtCreate() );
        automaticRecordVO.setModifierId( entity.getModifierId() );
        automaticRecordVO.setModifier( entity.getModifier() );
        automaticRecordVO.setGmtModified( entity.getGmtModified() );
        automaticRecordVO.setFileName( entity.getFileName() );
        automaticRecordVO.setBucketName( entity.getBucketName() );
        automaticRecordVO.setDataFileAddress( entity.getDataFileAddress() );
        automaticRecordVO.setExtendJarAddress( entity.getExtendJarAddress() );
        automaticRecordVO.setThirdJarAddress( entity.getThirdJarAddress() );
        automaticRecordVO.setFailInformation( entity.getFailInformation() );
        automaticRecordVO.setStatus( entity.getStatus() );
        automaticRecordVO.setPersonLiableId( entity.getPersonLiableId() );
        automaticRecordVO.setPersonLiable( entity.getPersonLiable() );
        automaticRecordVO.setTestcaseCode( entity.getTestcaseCode() );
        automaticRecordVO.setWorkSpace( entity.getWorkSpace() );
        automaticRecordVO.setBranch( entity.getBranch() );
        automaticRecordVO.setCommitId( entity.getCommitId() );
        automaticRecordVO.setScanDirectory( entity.getScanDirectory() );
        automaticRecordVO.setErrorLogFile( entity.getErrorLogFile() );
        automaticRecordVO.setAutoAnalysisFlag( entity.getAutoAnalysisFlag() );

        return automaticRecordVO;
    }

    @Override
    public List<AutomaticRecordVO> convertVOList(List<AutomaticSourceRecordEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AutomaticRecordVO> list = new ArrayList<AutomaticRecordVO>( entityList.size() );
        for ( AutomaticSourceRecordEntity automaticSourceRecordEntity : entityList ) {
            list.add( convertVO( automaticSourceRecordEntity ) );
        }

        return list;
    }

    @Override
    public AutomaticSourceRecordEntity convert(AddAutomaticRecordEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSourceRecordEntity automaticSourceRecordEntity = new AutomaticSourceRecordEntity();

        automaticSourceRecordEntity.setPersonLiableId( eventTransactorUserId( event ) );
        automaticSourceRecordEntity.setPersonLiable( eventTransactorUserName( event ) );
        automaticSourceRecordEntity.setCode( event.getCode() );
        automaticSourceRecordEntity.setProductCode( event.getProductCode() );
        automaticSourceRecordEntity.setName( event.getName() );
        automaticSourceRecordEntity.setComment( event.getComment() );
        automaticSourceRecordEntity.setAddress( event.getAddress() );
        automaticSourceRecordEntity.setType( event.getType() );
        automaticSourceRecordEntity.setDataFileAddress( event.getDataFileAddress() );
        automaticSourceRecordEntity.setExtendJarAddress( event.getExtendJarAddress() );
        automaticSourceRecordEntity.setThirdJarAddress( event.getThirdJarAddress() );
        automaticSourceRecordEntity.setFileName( event.getFileName() );
        automaticSourceRecordEntity.setBucketName( event.getBucketName() );
        automaticSourceRecordEntity.setTestcaseCode( event.getTestcaseCode() );
        automaticSourceRecordEntity.setWorkSpace( event.getWorkSpace() );
        automaticSourceRecordEntity.setBranch( event.getBranch() );
        automaticSourceRecordEntity.setCommitId( event.getCommitId() );
        automaticSourceRecordEntity.setScanDirectory( event.getScanDirectory() );
        automaticSourceRecordEntity.setAutoAnalysisFlag( event.getAutoAnalysisFlag() );

        return automaticSourceRecordEntity;
    }

    @Override
    public AutomaticSourceRecordEntity convert(AutomaticSourceRecordEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        AutomaticSourceRecordEntity automaticSourceRecordEntity = new AutomaticSourceRecordEntity();

        automaticSourceRecordEntity.setEnable( entityDO.getEnable() );
        automaticSourceRecordEntity.setCreatorId( entityDO.getCreatorId() );
        automaticSourceRecordEntity.setCreator( entityDO.getCreator() );
        automaticSourceRecordEntity.setGmtCreate( entityDO.getGmtCreate() );
        automaticSourceRecordEntity.setModifierId( entityDO.getModifierId() );
        automaticSourceRecordEntity.setModifier( entityDO.getModifier() );
        automaticSourceRecordEntity.setGmtModified( entityDO.getGmtModified() );
        automaticSourceRecordEntity.setId( entityDO.getId() );
        automaticSourceRecordEntity.setCode( entityDO.getCode() );
        automaticSourceRecordEntity.setProductCode( entityDO.getProductCode() );
        automaticSourceRecordEntity.setName( entityDO.getName() );
        automaticSourceRecordEntity.setComment( entityDO.getComment() );
        automaticSourceRecordEntity.setAddress( entityDO.getAddress() );
        automaticSourceRecordEntity.setType( entityDO.getType() );
        automaticSourceRecordEntity.setAddCaseNo( entityDO.getAddCaseNo() );
        automaticSourceRecordEntity.setUpdateCaseNo( entityDO.getUpdateCaseNo() );
        automaticSourceRecordEntity.setDeleteCaseNo( entityDO.getDeleteCaseNo() );
        automaticSourceRecordEntity.setStatus( entityDO.getStatus() );
        automaticSourceRecordEntity.setDataFileAddress( entityDO.getDataFileAddress() );
        automaticSourceRecordEntity.setExtendJarAddress( entityDO.getExtendJarAddress() );
        automaticSourceRecordEntity.setThirdJarAddress( entityDO.getThirdJarAddress() );
        automaticSourceRecordEntity.setFileName( entityDO.getFileName() );
        automaticSourceRecordEntity.setBucketName( entityDO.getBucketName() );
        automaticSourceRecordEntity.setFailInformation( entityDO.getFailInformation() );
        automaticSourceRecordEntity.setLastAutomaticSourceLogCode( entityDO.getLastAutomaticSourceLogCode() );
        automaticSourceRecordEntity.setPersonLiableId( entityDO.getPersonLiableId() );
        automaticSourceRecordEntity.setPersonLiable( entityDO.getPersonLiable() );
        automaticSourceRecordEntity.setTestcaseCode( entityDO.getTestcaseCode() );
        automaticSourceRecordEntity.setWorkSpace( entityDO.getWorkSpace() );
        automaticSourceRecordEntity.setBranch( entityDO.getBranch() );
        automaticSourceRecordEntity.setCommitId( entityDO.getCommitId() );
        automaticSourceRecordEntity.setScanDirectory( entityDO.getScanDirectory() );
        automaticSourceRecordEntity.setErrorLogFile( entityDO.getErrorLogFile() );
        automaticSourceRecordEntity.setAutoAnalysisFlag( entityDO.getAutoAnalysisFlag() );

        return automaticSourceRecordEntity;
    }

    @Override
    public AutomaticSourceRecordEntityDO convert(AutomaticSourceRecordEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticSourceRecordEntityDO automaticSourceRecordEntityDO = new AutomaticSourceRecordEntityDO();

        automaticSourceRecordEntityDO.setEnable( entity.getEnable() );
        automaticSourceRecordEntityDO.setCreatorId( entity.getCreatorId() );
        automaticSourceRecordEntityDO.setCreator( entity.getCreator() );
        automaticSourceRecordEntityDO.setGmtCreate( entity.getGmtCreate() );
        automaticSourceRecordEntityDO.setModifierId( entity.getModifierId() );
        automaticSourceRecordEntityDO.setModifier( entity.getModifier() );
        automaticSourceRecordEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            automaticSourceRecordEntityDO.setId( entity.getId() );
        }
        automaticSourceRecordEntityDO.setCode( entity.getCode() );
        automaticSourceRecordEntityDO.setProductCode( entity.getProductCode() );
        automaticSourceRecordEntityDO.setName( entity.getName() );
        automaticSourceRecordEntityDO.setComment( entity.getComment() );
        automaticSourceRecordEntityDO.setAddress( entity.getAddress() );
        automaticSourceRecordEntityDO.setType( entity.getType() );
        automaticSourceRecordEntityDO.setAddCaseNo( entity.getAddCaseNo() );
        automaticSourceRecordEntityDO.setUpdateCaseNo( entity.getUpdateCaseNo() );
        automaticSourceRecordEntityDO.setDeleteCaseNo( entity.getDeleteCaseNo() );
        automaticSourceRecordEntityDO.setStatus( entity.getStatus() );
        automaticSourceRecordEntityDO.setDataFileAddress( entity.getDataFileAddress() );
        automaticSourceRecordEntityDO.setExtendJarAddress( entity.getExtendJarAddress() );
        automaticSourceRecordEntityDO.setThirdJarAddress( entity.getThirdJarAddress() );
        automaticSourceRecordEntityDO.setFileName( entity.getFileName() );
        automaticSourceRecordEntityDO.setBucketName( entity.getBucketName() );
        automaticSourceRecordEntityDO.setFailInformation( entity.getFailInformation() );
        automaticSourceRecordEntityDO.setLastAutomaticSourceLogCode( entity.getLastAutomaticSourceLogCode() );
        automaticSourceRecordEntityDO.setPersonLiableId( entity.getPersonLiableId() );
        automaticSourceRecordEntityDO.setPersonLiable( entity.getPersonLiable() );
        automaticSourceRecordEntityDO.setTestcaseCode( entity.getTestcaseCode() );
        automaticSourceRecordEntityDO.setWorkSpace( entity.getWorkSpace() );
        automaticSourceRecordEntityDO.setBranch( entity.getBranch() );
        automaticSourceRecordEntityDO.setCommitId( entity.getCommitId() );
        automaticSourceRecordEntityDO.setScanDirectory( entity.getScanDirectory() );
        automaticSourceRecordEntityDO.setErrorLogFile( entity.getErrorLogFile() );
        automaticSourceRecordEntityDO.setAutoAnalysisFlag( entity.getAutoAnalysisFlag() );

        return automaticSourceRecordEntityDO;
    }

    @Override
    public List<AutomaticSourceRecordEntityDO> convert(List<AutomaticSourceRecordEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AutomaticSourceRecordEntityDO> list = new ArrayList<AutomaticSourceRecordEntityDO>( entityList.size() );
        for ( AutomaticSourceRecordEntity automaticSourceRecordEntity : entityList ) {
            list.add( convert( automaticSourceRecordEntity ) );
        }

        return list;
    }

    @Override
    public AutomaticSourceRecordEntity convert(EditAutomaticRecordEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSourceRecordEntity automaticSourceRecordEntity = new AutomaticSourceRecordEntity();

        automaticSourceRecordEntity.setCode( event.getCode() );
        automaticSourceRecordEntity.setProductCode( event.getProductCode() );
        automaticSourceRecordEntity.setName( event.getName() );
        automaticSourceRecordEntity.setComment( event.getComment() );
        automaticSourceRecordEntity.setAddress( event.getAddress() );
        automaticSourceRecordEntity.setType( event.getType() );
        automaticSourceRecordEntity.setStatus( event.getStatus() );
        automaticSourceRecordEntity.setDataFileAddress( event.getDataFileAddress() );
        automaticSourceRecordEntity.setExtendJarAddress( event.getExtendJarAddress() );
        automaticSourceRecordEntity.setThirdJarAddress( event.getThirdJarAddress() );
        automaticSourceRecordEntity.setFileName( event.getFileName() );
        automaticSourceRecordEntity.setBucketName( event.getBucketName() );
        automaticSourceRecordEntity.setTestcaseCode( event.getTestcaseCode() );
        automaticSourceRecordEntity.setWorkSpace( event.getWorkSpace() );
        automaticSourceRecordEntity.setBranch( event.getBranch() );
        automaticSourceRecordEntity.setCommitId( event.getCommitId() );
        automaticSourceRecordEntity.setScanDirectory( event.getScanDirectory() );
        automaticSourceRecordEntity.setAutoAnalysisFlag( event.getAutoAnalysisFlag() );

        return automaticSourceRecordEntity;
    }

    @Override
    public AutomaticSourceRecordEntity convert(AutomaticSuccessEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSourceRecordEntity automaticSourceRecordEntity = new AutomaticSourceRecordEntity();

        automaticSourceRecordEntity.setCode( event.getCode() );
        automaticSourceRecordEntity.setProductCode( event.getProductCode() );
        automaticSourceRecordEntity.setAddCaseNo( event.getAddCaseNo() );
        automaticSourceRecordEntity.setUpdateCaseNo( event.getUpdateCaseNo() );
        automaticSourceRecordEntity.setDeleteCaseNo( event.getDeleteCaseNo() );
        automaticSourceRecordEntity.setFileName( event.getFileName() );
        automaticSourceRecordEntity.setCommitId( event.getCommitId() );

        return automaticSourceRecordEntity;
    }

    private Long eventTransactorUserId(AddAutomaticRecordEvent addAutomaticRecordEvent) {
        if ( addAutomaticRecordEvent == null ) {
            return null;
        }
        User transactor = addAutomaticRecordEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        Long userId = transactor.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String eventTransactorUserName(AddAutomaticRecordEvent addAutomaticRecordEvent) {
        if ( addAutomaticRecordEvent == null ) {
            return null;
        }
        User transactor = addAutomaticRecordEvent.getTransactor();
        if ( transactor == null ) {
            return null;
        }
        String userName = transactor.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }
}
