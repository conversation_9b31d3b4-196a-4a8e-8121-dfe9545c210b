package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.AutomaticPreExecutionEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSchedulerEntityDO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.AutomaticSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerAddedEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerEditedEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticPreExecutionEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSchedulerEntityConverterImpl implements AutomaticSchedulerEntityConverter {

    @Override
    public AutomaticSchedulerEntity covert2Entity(AutomaticSchedulerEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        AutomaticSchedulerEntity automaticSchedulerEntity = new AutomaticSchedulerEntity();

        automaticSchedulerEntity.setEnable( entityDO.getEnable() );
        automaticSchedulerEntity.setCreatorId( entityDO.getCreatorId() );
        automaticSchedulerEntity.setCreator( entityDO.getCreator() );
        automaticSchedulerEntity.setGmtCreate( entityDO.getGmtCreate() );
        automaticSchedulerEntity.setModifierId( entityDO.getModifierId() );
        automaticSchedulerEntity.setModifier( entityDO.getModifier() );
        automaticSchedulerEntity.setGmtModified( entityDO.getGmtModified() );
        automaticSchedulerEntity.setId( entityDO.getId() );
        automaticSchedulerEntity.setSchedulerCode( entityDO.getSchedulerCode() );
        automaticSchedulerEntity.setSchedulerName( entityDO.getSchedulerName() );
        automaticSchedulerEntity.setProductCode( entityDO.getProductCode() );
        automaticSchedulerEntity.setSwitchFlag( entityDO.getSwitchFlag() );
        automaticSchedulerEntity.setCrontab( entityDO.getCrontab() );
        automaticSchedulerEntity.setExecuteEnv( entityDO.getExecuteEnv() );
        automaticSchedulerEntity.setExecuteTag( entityDO.getExecuteTag() );
        automaticSchedulerEntity.setExecuteSpaceCode( entityDO.getExecuteSpaceCode() );
        automaticSchedulerEntity.setCoverageFlag( entityDO.getCoverageFlag() );
        automaticSchedulerEntity.setMessageFlag( entityDO.getMessageFlag() );
        automaticSchedulerEntity.setExecuteResult( entityDO.getExecuteResult() );
        automaticSchedulerEntity.setExecutorId( entityDO.getExecutorId() );
        automaticSchedulerEntity.setExecutor( entityDO.getExecutor() );
        automaticSchedulerEntity.setExecuteTime( entityDO.getExecuteTime() );
        automaticSchedulerEntity.setTaskId( entityDO.getTaskId() );

        return automaticSchedulerEntity;
    }

    @Override
    public AutomaticSchedulerEntityDO converter(AutomaticSchedulerEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticSchedulerEntityDO automaticSchedulerEntityDO = new AutomaticSchedulerEntityDO();

        automaticSchedulerEntityDO.setEnable( entity.getEnable() );
        automaticSchedulerEntityDO.setCreatorId( entity.getCreatorId() );
        automaticSchedulerEntityDO.setCreator( entity.getCreator() );
        automaticSchedulerEntityDO.setGmtCreate( entity.getGmtCreate() );
        automaticSchedulerEntityDO.setModifierId( entity.getModifierId() );
        automaticSchedulerEntityDO.setModifier( entity.getModifier() );
        automaticSchedulerEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            automaticSchedulerEntityDO.setId( entity.getId() );
        }
        automaticSchedulerEntityDO.setSchedulerCode( entity.getSchedulerCode() );
        automaticSchedulerEntityDO.setSchedulerName( entity.getSchedulerName() );
        automaticSchedulerEntityDO.setProductCode( entity.getProductCode() );
        automaticSchedulerEntityDO.setSwitchFlag( entity.getSwitchFlag() );
        automaticSchedulerEntityDO.setCrontab( entity.getCrontab() );
        automaticSchedulerEntityDO.setExecuteEnv( entity.getExecuteEnv() );
        automaticSchedulerEntityDO.setExecuteTag( entity.getExecuteTag() );
        automaticSchedulerEntityDO.setExecuteSpaceCode( entity.getExecuteSpaceCode() );
        automaticSchedulerEntityDO.setCoverageFlag( entity.getCoverageFlag() );
        automaticSchedulerEntityDO.setMessageFlag( entity.getMessageFlag() );
        automaticSchedulerEntityDO.setExecuteResult( entity.getExecuteResult() );
        automaticSchedulerEntityDO.setExecutorId( entity.getExecutorId() );
        automaticSchedulerEntityDO.setExecutor( entity.getExecutor() );
        automaticSchedulerEntityDO.setExecuteTime( entity.getExecuteTime() );
        automaticSchedulerEntityDO.setTaskId( entity.getTaskId() );

        return automaticSchedulerEntityDO;
    }

    @Override
    public AutomaticSchedulerEntity converter(AutomaticSchedulerAddedEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSchedulerEntity automaticSchedulerEntity = new AutomaticSchedulerEntity();

        automaticSchedulerEntity.setSchedulerCode( event.getSchedulerCode() );
        automaticSchedulerEntity.setSchedulerName( event.getSchedulerName() );
        automaticSchedulerEntity.setProductCode( event.getProductCode() );
        automaticSchedulerEntity.setCrontab( event.getCrontab() );
        automaticSchedulerEntity.setExecuteEnv( event.getExecuteEnv() );
        automaticSchedulerEntity.setExecuteTag( event.getExecuteTag() );
        automaticSchedulerEntity.setExecuteSpaceCode( event.getExecuteSpaceCode() );
        automaticSchedulerEntity.setCoverageFlag( event.getCoverageFlag() );
        automaticSchedulerEntity.setMessageFlag( event.getMessageFlag() );

        return automaticSchedulerEntity;
    }

    @Override
    public AutomaticSchedulerEntity converter(AutomaticSchedulerEditedEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSchedulerEntity automaticSchedulerEntity = new AutomaticSchedulerEntity();

        automaticSchedulerEntity.setSchedulerCode( event.getSchedulerCode() );
        automaticSchedulerEntity.setSchedulerName( event.getSchedulerName() );
        automaticSchedulerEntity.setProductCode( event.getProductCode() );
        automaticSchedulerEntity.setSwitchFlag( event.getSwitchFlag() );
        automaticSchedulerEntity.setCrontab( event.getCrontab() );
        automaticSchedulerEntity.setExecuteEnv( event.getExecuteEnv() );
        automaticSchedulerEntity.setExecuteTag( event.getExecuteTag() );
        automaticSchedulerEntity.setExecuteSpaceCode( event.getExecuteSpaceCode() );
        automaticSchedulerEntity.setCoverageFlag( event.getCoverageFlag() );
        automaticSchedulerEntity.setMessageFlag( event.getMessageFlag() );

        return automaticSchedulerEntity;
    }

    @Override
    public AutomaticSchedulerVO domainConverter(AutomaticSchedulerEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticSchedulerVO automaticSchedulerVO = new AutomaticSchedulerVO();

        automaticSchedulerVO.setSchedulerCode( entity.getSchedulerCode() );
        automaticSchedulerVO.setSchedulerName( entity.getSchedulerName() );
        automaticSchedulerVO.setSwitchFlag( entity.getSwitchFlag() );
        automaticSchedulerVO.setCrontab( entity.getCrontab() );
        automaticSchedulerVO.setExecuteEnv( entity.getExecuteEnv() );
        automaticSchedulerVO.setExecuteTag( entity.getExecuteTag() );
        automaticSchedulerVO.setExecuteSpaceCode( entity.getExecuteSpaceCode() );
        automaticSchedulerVO.setMessageFlag( entity.getMessageFlag() );
        automaticSchedulerVO.setExecutor( entity.getExecutor() );
        automaticSchedulerVO.setCreator( entity.getCreator() );
        automaticSchedulerVO.setExecuteTime( entity.getExecuteTime() );
        automaticSchedulerVO.setTaskId( entity.getTaskId() );

        return automaticSchedulerVO;
    }

    @Override
    public AutomaticPreExecutionEntityDO converter(AutomaticPreExecutionEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticPreExecutionEntityDO automaticPreExecutionEntityDO = new AutomaticPreExecutionEntityDO();

        automaticPreExecutionEntityDO.setEnable( entity.getEnable() );
        automaticPreExecutionEntityDO.setCreatorId( entity.getCreatorId() );
        automaticPreExecutionEntityDO.setCreator( entity.getCreator() );
        automaticPreExecutionEntityDO.setGmtCreate( entity.getGmtCreate() );
        automaticPreExecutionEntityDO.setModifierId( entity.getModifierId() );
        automaticPreExecutionEntityDO.setModifier( entity.getModifier() );
        automaticPreExecutionEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            automaticPreExecutionEntityDO.setId( entity.getId() );
        }
        automaticPreExecutionEntityDO.setPreCode( entity.getPreCode() );
        automaticPreExecutionEntityDO.setSchedulerCode( entity.getSchedulerCode() );
        automaticPreExecutionEntityDO.setSchedulerName( entity.getSchedulerName() );
        automaticPreExecutionEntityDO.setExecuteTime( entity.getExecuteTime() );
        automaticPreExecutionEntityDO.setPreStatus( entity.getPreStatus() );

        return automaticPreExecutionEntityDO;
    }

    @Override
    public List<AutomaticPreExecutionEntityDO> converterList(List<AutomaticPreExecutionEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AutomaticPreExecutionEntityDO> list = new ArrayList<AutomaticPreExecutionEntityDO>( entityList.size() );
        for ( AutomaticPreExecutionEntity automaticPreExecutionEntity : entityList ) {
            list.add( converter( automaticPreExecutionEntity ) );
        }

        return list;
    }
}
