package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.AutomaticSourceLogEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.AutomaticRecordLiteInfoVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordLogVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddAutomaticRecordLogEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditAutomaticRecordLogEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSourceLogEntityConverterImpl implements AutomaticSourceLogEntityConverter {

    @Override
    public AutomaticRecordLogVO convertVO(AutomaticSourceLogEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticRecordLogVO automaticRecordLogVO = new AutomaticRecordLogVO();

        automaticRecordLogVO.setStatus( entity.getStatus() );
        if ( entity.hasId() ) {
            automaticRecordLogVO.setId( entity.getId() );
        }
        automaticRecordLogVO.setCode( entity.getCode() );
        automaticRecordLogVO.setProductCode( entity.getProductCode() );
        automaticRecordLogVO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        automaticRecordLogVO.setCreatorId( entity.getCreatorId() );
        automaticRecordLogVO.setCreator( entity.getCreator() );
        automaticRecordLogVO.setGmtCreate( entity.getGmtCreate() );
        automaticRecordLogVO.setModifierId( entity.getModifierId() );
        automaticRecordLogVO.setModifier( entity.getModifier() );
        automaticRecordLogVO.setGmtModified( entity.getGmtModified() );
        automaticRecordLogVO.setAddress( entity.getAddress() );
        automaticRecordLogVO.setType( entity.getType() );
        automaticRecordLogVO.setFileName( entity.getFileName() );
        automaticRecordLogVO.setBucketName( entity.getBucketName() );
        automaticRecordLogVO.setFailInformation( entity.getFailInformation() );
        automaticRecordLogVO.setEnable( entity.getEnable() );
        automaticRecordLogVO.setCommitId( entity.getCommitId() );
        automaticRecordLogVO.setErrorLogFile( entity.getErrorLogFile() );
        automaticRecordLogVO.setAnalyticMethod( entity.getAnalyticMethod() );

        return automaticRecordLogVO;
    }

    @Override
    public List<AutomaticRecordLogVO> convertVOList(List<AutomaticSourceLogEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AutomaticRecordLogVO> list = new ArrayList<AutomaticRecordLogVO>( entityList.size() );
        for ( AutomaticSourceLogEntity automaticSourceLogEntity : entityList ) {
            list.add( convertVO( automaticSourceLogEntity ) );
        }

        return list;
    }

    @Override
    public AutomaticSourceLogEntity convert(AddAutomaticRecordLogEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSourceLogEntity automaticSourceLogEntity = new AutomaticSourceLogEntity();

        automaticSourceLogEntity.setCode( event.getCode() );
        automaticSourceLogEntity.setProductCode( event.getProductCode() );
        automaticSourceLogEntity.setAutomaticSourceCode( event.getAutomaticSourceCode() );
        automaticSourceLogEntity.setAddress( event.getAddress() );
        automaticSourceLogEntity.setType( event.getType() );
        automaticSourceLogEntity.setFileName( event.getFileName() );
        automaticSourceLogEntity.setBucketName( event.getBucketName() );
        automaticSourceLogEntity.setFailInformation( event.getFailInformation() );
        automaticSourceLogEntity.setStatus( event.getStatus() );
        automaticSourceLogEntity.setAnalyticMethod( event.getAnalyticMethod() );

        return automaticSourceLogEntity;
    }

    @Override
    public AutomaticSourceLogEntity convert(EditAutomaticRecordLogEvent event) {
        if ( event == null ) {
            return null;
        }

        AutomaticSourceLogEntity automaticSourceLogEntity = new AutomaticSourceLogEntity();

        automaticSourceLogEntity.setCode( event.getCode() );
        automaticSourceLogEntity.setAutomaticSourceCode( event.getAutomaticSourceCode() );
        automaticSourceLogEntity.setFailInformation( event.getFailInformation() );
        automaticSourceLogEntity.setStatus( event.getStatus() );

        return automaticSourceLogEntity;
    }

    @Override
    public AutomaticSourceLogEntity convert(AutomaticSourceLogEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        AutomaticSourceLogEntity automaticSourceLogEntity = new AutomaticSourceLogEntity();

        automaticSourceLogEntity.setId( entityDO.getId() );
        automaticSourceLogEntity.setCode( entityDO.getCode() );
        automaticSourceLogEntity.setProductCode( entityDO.getProductCode() );
        automaticSourceLogEntity.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        automaticSourceLogEntity.setCreatorId( entityDO.getCreatorId() );
        automaticSourceLogEntity.setCreator( entityDO.getCreator() );
        automaticSourceLogEntity.setGmtCreate( entityDO.getGmtCreate() );
        automaticSourceLogEntity.setModifierId( entityDO.getModifierId() );
        automaticSourceLogEntity.setModifier( entityDO.getModifier() );
        automaticSourceLogEntity.setGmtModified( entityDO.getGmtModified() );
        automaticSourceLogEntity.setAddress( entityDO.getAddress() );
        automaticSourceLogEntity.setType( entityDO.getType() );
        automaticSourceLogEntity.setFileName( entityDO.getFileName() );
        automaticSourceLogEntity.setBucketName( entityDO.getBucketName() );
        automaticSourceLogEntity.setFailInformation( entityDO.getFailInformation() );
        automaticSourceLogEntity.setEnable( entityDO.getEnable() );
        automaticSourceLogEntity.setStatus( entityDO.getStatus() );
        automaticSourceLogEntity.setCommitId( entityDO.getCommitId() );
        automaticSourceLogEntity.setErrorLogFile( entityDO.getErrorLogFile() );
        automaticSourceLogEntity.setAnalyticMethod( entityDO.getAnalyticMethod() );

        return automaticSourceLogEntity;
    }

    @Override
    public AutomaticSourceRecordEntityDO convert(AutomaticSourceRecordEntity selectByPrimaryKey) {
        if ( selectByPrimaryKey == null ) {
            return null;
        }

        AutomaticSourceRecordEntityDO automaticSourceRecordEntityDO = new AutomaticSourceRecordEntityDO();

        automaticSourceRecordEntityDO.setEnable( selectByPrimaryKey.getEnable() );
        automaticSourceRecordEntityDO.setCreatorId( selectByPrimaryKey.getCreatorId() );
        automaticSourceRecordEntityDO.setCreator( selectByPrimaryKey.getCreator() );
        automaticSourceRecordEntityDO.setGmtCreate( selectByPrimaryKey.getGmtCreate() );
        automaticSourceRecordEntityDO.setModifierId( selectByPrimaryKey.getModifierId() );
        automaticSourceRecordEntityDO.setModifier( selectByPrimaryKey.getModifier() );
        automaticSourceRecordEntityDO.setGmtModified( selectByPrimaryKey.getGmtModified() );
        if ( selectByPrimaryKey.hasId() ) {
            automaticSourceRecordEntityDO.setId( selectByPrimaryKey.getId() );
        }
        automaticSourceRecordEntityDO.setCode( selectByPrimaryKey.getCode() );
        automaticSourceRecordEntityDO.setProductCode( selectByPrimaryKey.getProductCode() );
        automaticSourceRecordEntityDO.setName( selectByPrimaryKey.getName() );
        automaticSourceRecordEntityDO.setComment( selectByPrimaryKey.getComment() );
        automaticSourceRecordEntityDO.setAddress( selectByPrimaryKey.getAddress() );
        automaticSourceRecordEntityDO.setType( selectByPrimaryKey.getType() );
        automaticSourceRecordEntityDO.setAddCaseNo( selectByPrimaryKey.getAddCaseNo() );
        automaticSourceRecordEntityDO.setUpdateCaseNo( selectByPrimaryKey.getUpdateCaseNo() );
        automaticSourceRecordEntityDO.setDeleteCaseNo( selectByPrimaryKey.getDeleteCaseNo() );
        automaticSourceRecordEntityDO.setStatus( selectByPrimaryKey.getStatus() );
        automaticSourceRecordEntityDO.setDataFileAddress( selectByPrimaryKey.getDataFileAddress() );
        automaticSourceRecordEntityDO.setExtendJarAddress( selectByPrimaryKey.getExtendJarAddress() );
        automaticSourceRecordEntityDO.setThirdJarAddress( selectByPrimaryKey.getThirdJarAddress() );
        automaticSourceRecordEntityDO.setFileName( selectByPrimaryKey.getFileName() );
        automaticSourceRecordEntityDO.setBucketName( selectByPrimaryKey.getBucketName() );
        automaticSourceRecordEntityDO.setFailInformation( selectByPrimaryKey.getFailInformation() );
        automaticSourceRecordEntityDO.setLastAutomaticSourceLogCode( selectByPrimaryKey.getLastAutomaticSourceLogCode() );
        automaticSourceRecordEntityDO.setPersonLiableId( selectByPrimaryKey.getPersonLiableId() );
        automaticSourceRecordEntityDO.setPersonLiable( selectByPrimaryKey.getPersonLiable() );
        automaticSourceRecordEntityDO.setTestcaseCode( selectByPrimaryKey.getTestcaseCode() );
        automaticSourceRecordEntityDO.setWorkSpace( selectByPrimaryKey.getWorkSpace() );
        automaticSourceRecordEntityDO.setBranch( selectByPrimaryKey.getBranch() );
        automaticSourceRecordEntityDO.setCommitId( selectByPrimaryKey.getCommitId() );
        automaticSourceRecordEntityDO.setScanDirectory( selectByPrimaryKey.getScanDirectory() );
        automaticSourceRecordEntityDO.setErrorLogFile( selectByPrimaryKey.getErrorLogFile() );
        automaticSourceRecordEntityDO.setAutoAnalysisFlag( selectByPrimaryKey.getAutoAnalysisFlag() );

        return automaticSourceRecordEntityDO;
    }

    @Override
    public List<AutomaticRecordVO> converterAutomaticSourceRecordList(List<AutomaticSourceRecordEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AutomaticRecordVO> list = new ArrayList<AutomaticRecordVO>( entityList.size() );
        for ( AutomaticSourceRecordEntity automaticSourceRecordEntity : entityList ) {
            list.add( automaticSourceRecordEntityToAutomaticRecordVO( automaticSourceRecordEntity ) );
        }

        return list;
    }

    @Override
    public List<AutomaticRecordLiteInfoVO> converterLiteInfoPage(List<AutomaticSourceRecordEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<AutomaticRecordLiteInfoVO> list1 = new ArrayList<AutomaticRecordLiteInfoVO>( list.size() );
        for ( AutomaticSourceRecordEntity automaticSourceRecordEntity : list ) {
            list1.add( automaticSourceRecordEntityToAutomaticRecordLiteInfoVO( automaticSourceRecordEntity ) );
        }

        return list1;
    }

    @Override
    public AutomaticSourceLogEntityDO convert(AutomaticSourceLogEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AutomaticSourceLogEntityDO automaticSourceLogEntityDO = new AutomaticSourceLogEntityDO();

        if ( entity.hasId() ) {
            automaticSourceLogEntityDO.setId( entity.getId() );
        }
        automaticSourceLogEntityDO.setCode( entity.getCode() );
        automaticSourceLogEntityDO.setProductCode( entity.getProductCode() );
        automaticSourceLogEntityDO.setAutomaticSourceCode( entity.getAutomaticSourceCode() );
        automaticSourceLogEntityDO.setCreatorId( entity.getCreatorId() );
        automaticSourceLogEntityDO.setCreator( entity.getCreator() );
        automaticSourceLogEntityDO.setGmtCreate( entity.getGmtCreate() );
        automaticSourceLogEntityDO.setModifierId( entity.getModifierId() );
        automaticSourceLogEntityDO.setModifier( entity.getModifier() );
        automaticSourceLogEntityDO.setGmtModified( entity.getGmtModified() );
        automaticSourceLogEntityDO.setAddress( entity.getAddress() );
        automaticSourceLogEntityDO.setType( entity.getType() );
        automaticSourceLogEntityDO.setFileName( entity.getFileName() );
        automaticSourceLogEntityDO.setBucketName( entity.getBucketName() );
        automaticSourceLogEntityDO.setFailInformation( entity.getFailInformation() );
        automaticSourceLogEntityDO.setEnable( entity.getEnable() );
        automaticSourceLogEntityDO.setStatus( entity.getStatus() );
        automaticSourceLogEntityDO.setCommitId( entity.getCommitId() );
        automaticSourceLogEntityDO.setErrorLogFile( entity.getErrorLogFile() );
        automaticSourceLogEntityDO.setAnalyticMethod( entity.getAnalyticMethod() );

        return automaticSourceLogEntityDO;
    }

    protected AutomaticRecordVO automaticSourceRecordEntityToAutomaticRecordVO(AutomaticSourceRecordEntity automaticSourceRecordEntity) {
        if ( automaticSourceRecordEntity == null ) {
            return null;
        }

        AutomaticRecordVO automaticRecordVO = new AutomaticRecordVO();

        automaticRecordVO.setCode( automaticSourceRecordEntity.getCode() );
        automaticRecordVO.setProductCode( automaticSourceRecordEntity.getProductCode() );
        automaticRecordVO.setName( automaticSourceRecordEntity.getName() );
        automaticRecordVO.setComment( automaticSourceRecordEntity.getComment() );
        automaticRecordVO.setAddress( automaticSourceRecordEntity.getAddress() );
        automaticRecordVO.setType( automaticSourceRecordEntity.getType() );
        automaticRecordVO.setCreatorId( automaticSourceRecordEntity.getCreatorId() );
        automaticRecordVO.setCreator( automaticSourceRecordEntity.getCreator() );
        automaticRecordVO.setGmtCreate( automaticSourceRecordEntity.getGmtCreate() );
        automaticRecordVO.setModifierId( automaticSourceRecordEntity.getModifierId() );
        automaticRecordVO.setModifier( automaticSourceRecordEntity.getModifier() );
        automaticRecordVO.setGmtModified( automaticSourceRecordEntity.getGmtModified() );
        automaticRecordVO.setFileName( automaticSourceRecordEntity.getFileName() );
        automaticRecordVO.setBucketName( automaticSourceRecordEntity.getBucketName() );
        automaticRecordVO.setDataFileAddress( automaticSourceRecordEntity.getDataFileAddress() );
        automaticRecordVO.setExtendJarAddress( automaticSourceRecordEntity.getExtendJarAddress() );
        automaticRecordVO.setThirdJarAddress( automaticSourceRecordEntity.getThirdJarAddress() );
        automaticRecordVO.setFailInformation( automaticSourceRecordEntity.getFailInformation() );
        automaticRecordVO.setStatus( automaticSourceRecordEntity.getStatus() );
        automaticRecordVO.setPersonLiableId( automaticSourceRecordEntity.getPersonLiableId() );
        automaticRecordVO.setPersonLiable( automaticSourceRecordEntity.getPersonLiable() );
        automaticRecordVO.setTestcaseCode( automaticSourceRecordEntity.getTestcaseCode() );
        automaticRecordVO.setWorkSpace( automaticSourceRecordEntity.getWorkSpace() );
        automaticRecordVO.setBranch( automaticSourceRecordEntity.getBranch() );
        automaticRecordVO.setCommitId( automaticSourceRecordEntity.getCommitId() );
        automaticRecordVO.setScanDirectory( automaticSourceRecordEntity.getScanDirectory() );
        automaticRecordVO.setErrorLogFile( automaticSourceRecordEntity.getErrorLogFile() );
        automaticRecordVO.setAutoAnalysisFlag( automaticSourceRecordEntity.getAutoAnalysisFlag() );

        return automaticRecordVO;
    }

    protected AutomaticRecordLiteInfoVO automaticSourceRecordEntityToAutomaticRecordLiteInfoVO(AutomaticSourceRecordEntity automaticSourceRecordEntity) {
        if ( automaticSourceRecordEntity == null ) {
            return null;
        }

        AutomaticRecordLiteInfoVO automaticRecordLiteInfoVO = new AutomaticRecordLiteInfoVO();

        automaticRecordLiteInfoVO.setCode( automaticSourceRecordEntity.getCode() );
        automaticRecordLiteInfoVO.setName( automaticSourceRecordEntity.getName() );

        return automaticRecordLiteInfoVO;
    }
}
