package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.infrastructure.dao.entity.CommentEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class CommentEntityConverterImpl implements CommentEntityConverter {

    @Override
    public CommentEntity convert(CommentVO vo) {
        if ( vo == null ) {
            return null;
        }

        CommentEntity commentEntity = new CommentEntity();

        commentEntity.setEnable( vo.getEnable() );
        commentEntity.setCreatorId( vo.getCreatorId() );
        commentEntity.setCreator( vo.getCreator() );
        commentEntity.setGmtCreate( vo.getGmtCreate() );
        commentEntity.setCode( vo.getCode() );
        commentEntity.setDomain( vo.getDomain() );
        commentEntity.setBusinessCode( vo.getBusinessCode() );
        commentEntity.setTopRepliedCode( vo.getTopRepliedCode() );
        commentEntity.setRepliedCode( vo.getRepliedCode() );
        commentEntity.setRepliedUserName( vo.getRepliedUserName() );
        commentEntity.setRepliedUserId( vo.getRepliedUserId() );
        commentEntity.setLevel( vo.getLevel() );
        commentEntity.setContent( vo.getContent() );

        return commentEntity;
    }

    @Override
    public List<CommentEntity> convert(Collection<CommentVO> vo) {
        if ( vo == null ) {
            return null;
        }

        List<CommentEntity> list = new ArrayList<CommentEntity>( vo.size() );
        for ( CommentVO commentVO : vo ) {
            list.add( convert( commentVO ) );
        }

        return list;
    }

    @Override
    public CommentVO convert(CommentEntity entity) {
        if ( entity == null ) {
            return null;
        }

        CommentVO commentVO = new CommentVO();

        commentVO.setDomain( entity.getDomain() );
        commentVO.setCode( entity.getCode() );
        commentVO.setBusinessCode( entity.getBusinessCode() );
        commentVO.setContent( entity.getContent() );
        commentVO.setCreatorId( entity.getCreatorId() );
        commentVO.setCreator( entity.getCreator() );
        commentVO.setRepliedCode( entity.getRepliedCode() );
        commentVO.setTopRepliedCode( entity.getTopRepliedCode() );
        commentVO.setRepliedUserId( entity.getRepliedUserId() );
        commentVO.setRepliedUserName( entity.getRepliedUserName() );
        commentVO.setLevel( entity.getLevel() );
        commentVO.setEnable( entity.getEnable() );
        commentVO.setGmtCreate( entity.getGmtCreate() );

        return commentVO;
    }

    @Override
    public List<CommentVO> converter(Collection<CommentEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<CommentVO> list = new ArrayList<CommentVO>( entityList.size() );
        for ( CommentEntity commentEntity : entityList ) {
            list.add( convert( commentEntity ) );
        }

        return list;
    }
}
