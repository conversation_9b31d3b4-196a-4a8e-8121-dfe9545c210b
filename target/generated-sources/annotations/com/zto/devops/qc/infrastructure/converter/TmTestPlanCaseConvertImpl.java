package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmTestPlanCaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmTestPlanCaseConvertImpl implements TmTestPlanCaseConvert {

    @Override
    public TmTestPlanCaseEntityDO covert(TmTestPlanCaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO = new TmTestPlanCaseEntityDO();

        tmTestPlanCaseEntityDO.setEnable( entity.getEnable() );
        tmTestPlanCaseEntityDO.setCreatorId( entity.getCreatorId() );
        tmTestPlanCaseEntityDO.setCreator( entity.getCreator() );
        tmTestPlanCaseEntityDO.setGmtCreate( entity.getGmtCreate() );
        tmTestPlanCaseEntityDO.setModifierId( entity.getModifierId() );
        tmTestPlanCaseEntityDO.setModifier( entity.getModifier() );
        tmTestPlanCaseEntityDO.setGmtModified( entity.getGmtModified() );
        if ( entity.hasId() ) {
            tmTestPlanCaseEntityDO.setId( entity.getId() );
        }
        tmTestPlanCaseEntityDO.setPlanCode( entity.getPlanCode() );
        tmTestPlanCaseEntityDO.setCaseType( entity.getCaseType() );
        tmTestPlanCaseEntityDO.setTestStage( entity.getTestStage() );
        tmTestPlanCaseEntityDO.setCaseCode( entity.getCaseCode() );
        tmTestPlanCaseEntityDO.setStatus( entity.getStatus() );
        tmTestPlanCaseEntityDO.setExecutorId( entity.getExecutorId() );
        tmTestPlanCaseEntityDO.setExecutor( entity.getExecutor() );
        tmTestPlanCaseEntityDO.setResultPath( entity.getResultPath() );
        tmTestPlanCaseEntityDO.setLogPath( entity.getLogPath() );
        tmTestPlanCaseEntityDO.setOperateCaseCode( entity.getOperateCaseCode() );
        tmTestPlanCaseEntityDO.setResultComment( entity.getResultComment() );

        return tmTestPlanCaseEntityDO;
    }

    @Override
    public List<TmTestPlanCaseEntityDO> covertList(List<TmTestPlanCaseEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmTestPlanCaseEntityDO> list = new ArrayList<TmTestPlanCaseEntityDO>( entityList.size() );
        for ( TmTestPlanCaseEntity tmTestPlanCaseEntity : entityList ) {
            list.add( covert( tmTestPlanCaseEntity ) );
        }

        return list;
    }

    @Override
    public TmTestPlanCaseEntityDO covertReversal(TmTestPlanCaseEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO = new TmTestPlanCaseEntityDO();

        tmTestPlanCaseEntityDO.setEnable( entityDO.getEnable() );
        tmTestPlanCaseEntityDO.setCreatorId( entityDO.getCreatorId() );
        tmTestPlanCaseEntityDO.setCreator( entityDO.getCreator() );
        tmTestPlanCaseEntityDO.setGmtCreate( entityDO.getGmtCreate() );
        tmTestPlanCaseEntityDO.setModifierId( entityDO.getModifierId() );
        tmTestPlanCaseEntityDO.setModifier( entityDO.getModifier() );
        tmTestPlanCaseEntityDO.setGmtModified( entityDO.getGmtModified() );
        tmTestPlanCaseEntityDO.setId( entityDO.getId() );
        tmTestPlanCaseEntityDO.setPlanCode( entityDO.getPlanCode() );
        tmTestPlanCaseEntityDO.setCaseType( entityDO.getCaseType() );
        tmTestPlanCaseEntityDO.setTestStage( entityDO.getTestStage() );
        tmTestPlanCaseEntityDO.setCaseCode( entityDO.getCaseCode() );
        tmTestPlanCaseEntityDO.setStatus( entityDO.getStatus() );
        tmTestPlanCaseEntityDO.setExecutorId( entityDO.getExecutorId() );
        tmTestPlanCaseEntityDO.setExecutor( entityDO.getExecutor() );
        tmTestPlanCaseEntityDO.setResultPath( entityDO.getResultPath() );
        tmTestPlanCaseEntityDO.setLogPath( entityDO.getLogPath() );
        tmTestPlanCaseEntityDO.setOperateCaseCode( entityDO.getOperateCaseCode() );
        tmTestPlanCaseEntityDO.setResultComment( entityDO.getResultComment() );

        return tmTestPlanCaseEntityDO;
    }

    @Override
    public List<TmTestPlanCaseEntity> covertListReversal(List<TmTestPlanCaseEntityDO> entityDOList) {
        if ( entityDOList == null ) {
            return null;
        }

        List<TmTestPlanCaseEntity> list = new ArrayList<TmTestPlanCaseEntity>( entityDOList.size() );
        for ( TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO : entityDOList ) {
            list.add( covert2Entity( tmTestPlanCaseEntityDO ) );
        }

        return list;
    }

    @Override
    public TmTestPlanCaseEntity covert2Entity(TmTestPlanCaseEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TmTestPlanCaseEntity tmTestPlanCaseEntity = new TmTestPlanCaseEntity();

        tmTestPlanCaseEntity.setEnable( entityDO.getEnable() );
        tmTestPlanCaseEntity.setCreatorId( entityDO.getCreatorId() );
        tmTestPlanCaseEntity.setCreator( entityDO.getCreator() );
        tmTestPlanCaseEntity.setGmtCreate( entityDO.getGmtCreate() );
        tmTestPlanCaseEntity.setModifierId( entityDO.getModifierId() );
        tmTestPlanCaseEntity.setModifier( entityDO.getModifier() );
        tmTestPlanCaseEntity.setGmtModified( entityDO.getGmtModified() );
        tmTestPlanCaseEntity.setId( entityDO.getId() );
        tmTestPlanCaseEntity.setPlanCode( entityDO.getPlanCode() );
        tmTestPlanCaseEntity.setCaseType( entityDO.getCaseType() );
        tmTestPlanCaseEntity.setTestStage( entityDO.getTestStage() );
        tmTestPlanCaseEntity.setCaseCode( entityDO.getCaseCode() );
        tmTestPlanCaseEntity.setStatus( entityDO.getStatus() );
        tmTestPlanCaseEntity.setExecutorId( entityDO.getExecutorId() );
        tmTestPlanCaseEntity.setExecutor( entityDO.getExecutor() );
        tmTestPlanCaseEntity.setResultPath( entityDO.getResultPath() );
        tmTestPlanCaseEntity.setLogPath( entityDO.getLogPath() );
        tmTestPlanCaseEntity.setOperateCaseCode( entityDO.getOperateCaseCode() );
        tmTestPlanCaseEntity.setResultComment( entityDO.getResultComment() );

        return tmTestPlanCaseEntity;
    }

    @Override
    public List<TmTestPlanCaseEntityDO> convert2DOList(List<TmTestPlanCaseEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TmTestPlanCaseEntityDO> list = new ArrayList<TmTestPlanCaseEntityDO>( entityList.size() );
        for ( TmTestPlanCaseEntity tmTestPlanCaseEntity : entityList ) {
            list.add( covert( tmTestPlanCaseEntity ) );
        }

        return list;
    }

    @Override
    public TestPlanCaseVO convert2VO(TmTestPlanCaseEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TestPlanCaseVO testPlanCaseVO = new TestPlanCaseVO();

        testPlanCaseVO.setType( entity.getCaseType() );
        if ( entity.hasId() ) {
            testPlanCaseVO.setId( entity.getId() );
        }
        testPlanCaseVO.setPlanCode( entity.getPlanCode() );
        testPlanCaseVO.setCaseCode( entity.getCaseCode() );
        testPlanCaseVO.setTestStage( entity.getTestStage() );
        testPlanCaseVO.setStatus( entity.getStatus() );
        testPlanCaseVO.setExecutorId( entity.getExecutorId() );
        testPlanCaseVO.setExecutor( entity.getExecutor() );
        testPlanCaseVO.setOperateCaseCode( entity.getOperateCaseCode() );
        testPlanCaseVO.setResultPath( entity.getResultPath() );
        testPlanCaseVO.setLogPath( entity.getLogPath() );
        testPlanCaseVO.setCreatorId( entity.getCreatorId() );
        testPlanCaseVO.setCreator( entity.getCreator() );
        testPlanCaseVO.setGmtCreate( entity.getGmtCreate() );
        testPlanCaseVO.setModifierId( entity.getModifierId() );
        testPlanCaseVO.setModifier( entity.getModifier() );
        testPlanCaseVO.setGmtModified( entity.getGmtModified() );
        testPlanCaseVO.setResultComment( entity.getResultComment() );

        testPlanCaseVO.setEnable( entity.getEnable() ? 1 : 0 );

        return testPlanCaseVO;
    }

    @Override
    public List<TestPlanCaseVO> convert2VOList(List<TmTestPlanCaseEntity> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TestPlanCaseVO> list = new ArrayList<TestPlanCaseVO>( entityList.size() );
        for ( TmTestPlanCaseEntity tmTestPlanCaseEntity : entityList ) {
            list.add( convert2VO( tmTestPlanCaseEntity ) );
        }

        return list;
    }
}
