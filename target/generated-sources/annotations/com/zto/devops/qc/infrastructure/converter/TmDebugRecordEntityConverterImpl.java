package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmDebugRecordEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TmDebugRecordEntity;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:27+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmDebugRecordEntityConverterImpl implements TmDebugRecordEntityConverter {

    @Override
    public TmDebugRecordEntity convertEntity(TmDebugRecordEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        TmDebugRecordEntity tmDebugRecordEntity = new TmDebugRecordEntity();

        tmDebugRecordEntity.setEnable( entityDO.getEnable() );
        tmDebugRecordEntity.setCreatorId( entityDO.getCreatorId() );
        tmDebugRecordEntity.setCreator( entityDO.getCreator() );
        tmDebugRecordEntity.setGmtCreate( entityDO.getGmtCreate() );
        tmDebugRecordEntity.setModifierId( entityDO.getModifierId() );
        tmDebugRecordEntity.setModifier( entityDO.getModifier() );
        tmDebugRecordEntity.setGmtModified( entityDO.getGmtModified() );
        tmDebugRecordEntity.setTaskId( entityDO.getTaskId() );
        tmDebugRecordEntity.setDebugCode( entityDO.getDebugCode() );
        tmDebugRecordEntity.setProductCode( entityDO.getProductCode() );
        tmDebugRecordEntity.setProductName( entityDO.getProductName() );
        tmDebugRecordEntity.setStatus( entityDO.getStatus() );
        tmDebugRecordEntity.setExecLogFile( entityDO.getExecLogFile() );
        tmDebugRecordEntity.setStartTime( entityDO.getStartTime() );
        tmDebugRecordEntity.setFinishTime( entityDO.getFinishTime() );

        return tmDebugRecordEntity;
    }

    @Override
    public TmDebugRecordEntityDO convertEntityDO(TmDebugRecordEntity entity) {
        if ( entity == null ) {
            return null;
        }

        TmDebugRecordEntityDO tmDebugRecordEntityDO = new TmDebugRecordEntityDO();

        tmDebugRecordEntityDO.setEnable( entity.getEnable() );
        tmDebugRecordEntityDO.setCreatorId( entity.getCreatorId() );
        tmDebugRecordEntityDO.setCreator( entity.getCreator() );
        tmDebugRecordEntityDO.setGmtCreate( entity.getGmtCreate() );
        tmDebugRecordEntityDO.setModifierId( entity.getModifierId() );
        tmDebugRecordEntityDO.setModifier( entity.getModifier() );
        tmDebugRecordEntityDO.setGmtModified( entity.getGmtModified() );
        tmDebugRecordEntityDO.setDebugCode( entity.getDebugCode() );
        tmDebugRecordEntityDO.setProductCode( entity.getProductCode() );
        tmDebugRecordEntityDO.setProductName( entity.getProductName() );
        tmDebugRecordEntityDO.setTaskId( entity.getTaskId() );
        tmDebugRecordEntityDO.setStatus( entity.getStatus() );
        tmDebugRecordEntityDO.setExecLogFile( entity.getExecLogFile() );
        tmDebugRecordEntityDO.setStartTime( entity.getStartTime() );
        tmDebugRecordEntityDO.setFinishTime( entity.getFinishTime() );

        return tmDebugRecordEntityDO;
    }
}
