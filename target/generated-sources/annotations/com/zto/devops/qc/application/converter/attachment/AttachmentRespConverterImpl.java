package com.zto.devops.qc.application.converter.attachment;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.service.attachment.model.AttachmentResp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AttachmentRespConverterImpl implements AttachmentRespConverter {

    @Override
    public AttachmentResp convert(AttachmentVO attachmentVO) {
        if ( attachmentVO == null ) {
            return null;
        }

        AttachmentResp attachmentResp = new AttachmentResp();

        if ( attachmentVO.getGmtCreate() != null ) {
            attachmentResp.setGmtCreate( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( attachmentVO.getGmtCreate() ) );
        }
        if ( attachmentVO.getGmtModified() != null ) {
            attachmentResp.setGmtModified( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( attachmentVO.getGmtModified() ) );
        }
        attachmentResp.setDomain( attachmentVO.getDomain() );
        attachmentResp.setBusinessCode( attachmentVO.getBusinessCode() );
        attachmentResp.setCode( attachmentVO.getCode() );
        attachmentResp.setUrl( attachmentVO.getUrl() );
        attachmentResp.setName( attachmentVO.getName() );
        attachmentResp.setRemoteFileId( attachmentVO.getRemoteFileId() );
        attachmentResp.setType( attachmentVO.getType() );
        attachmentResp.setDocumentType( attachmentVO.getDocumentType() );
        attachmentResp.setCreatorId( attachmentVO.getCreatorId() );
        attachmentResp.setCreator( attachmentVO.getCreator() );
        attachmentResp.setModifierId( attachmentVO.getModifierId() );
        attachmentResp.setModifier( attachmentVO.getModifier() );

        return attachmentResp;
    }

    @Override
    public List<AttachmentResp> convert(List<AttachmentVO> attachmentVOList) {
        if ( attachmentVOList == null ) {
            return null;
        }

        List<AttachmentResp> list = new ArrayList<AttachmentResp>( attachmentVOList.size() );
        for ( AttachmentVO attachmentVO : attachmentVOList ) {
            list.add( convert( attachmentVO ) );
        }

        return list;
    }
}
