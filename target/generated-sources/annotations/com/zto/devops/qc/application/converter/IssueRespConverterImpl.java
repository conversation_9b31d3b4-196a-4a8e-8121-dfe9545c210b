package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.CaseVO;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.model.issue.entity.Event;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.entity.TransitionNodeVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.IssueNumStatisticsVO;
import com.zto.devops.qc.client.service.issue.model.FindStatisticsIssueReq;
import com.zto.devops.qc.client.service.issue.model.IssueLegacyListResp;
import com.zto.devops.qc.client.service.issue.model.IssueNumStatisticsResp;
import com.zto.devops.qc.client.service.issue.model.IssueResp;
import com.zto.devops.qc.client.service.issue.model.IssueTagResp;
import com.zto.devops.qc.client.service.issue.model.StatisticsIssueResp;
import com.zto.devops.qc.client.service.issue.model.StatisticsProductIssueReq;
import com.zto.devops.qc.client.service.issue.model.StatisticsProductIssueResp;
import com.zto.devops.qc.client.service.issue.model.TransitionNodeResp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class IssueRespConverterImpl implements IssueRespConverter {

    @Override
    public IssueResp convert(IssueVO issueVO) {
        if ( issueVO == null ) {
            return null;
        }

        IssueResp issueResp = new IssueResp();

        if ( issueVO.getReopenTime() != null ) {
            issueResp.setReopenTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getReopenTime() ) );
        }
        if ( issueVO.getFindTime() != null ) {
            issueResp.setFindTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getFindTime() ) );
        }
        if ( issueVO.getStartFixTime() != null ) {
            issueResp.setStartFixTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getStartFixTime() ) );
        }
        if ( issueVO.getDelayFixTime() != null ) {
            issueResp.setDelayFixTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getDelayFixTime() ) );
        }
        if ( issueVO.getDeliverTime() != null ) {
            issueResp.setDeliverTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getDeliverTime() ) );
        }
        if ( issueVO.getRejectTime() != null ) {
            issueResp.setRejectTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getRejectTime() ) );
        }
        if ( issueVO.getCloseTime() != null ) {
            issueResp.setCloseTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getCloseTime() ) );
        }
        if ( issueVO.getUpdateTime() != null ) {
            issueResp.setUpdateTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getUpdateTime() ) );
        }
        if ( issueVO.getGmtCreate() != null ) {
            issueResp.setGmtCreate( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( issueVO.getGmtCreate() ) );
        }
        issueResp.setCode( issueVO.getCode() );
        issueResp.setTitle( issueVO.getTitle() );
        issueResp.setDescription( issueVO.getDescription() );
        issueResp.setStatus( issueVO.getStatus() );
        issueResp.setFindEnv( issueVO.getFindEnv() );
        issueResp.setFindStage( issueVO.getFindStage() );
        issueResp.setPriority( issueVO.getPriority() );
        issueResp.setRepetitionRate( issueVO.getRepetitionRate() );
        issueResp.setRootCause( issueVO.getRootCause() );
        issueResp.setTestMethod( issueVO.getTestMethod() );
        issueResp.setType( issueVO.getType() );
        issueResp.setProductCode( issueVO.getProductCode() );
        issueResp.setProductName( issueVO.getProductName() );
        issueResp.setSprintCode( issueVO.getSprintCode() );
        issueResp.setSprintName( issueVO.getSprintName() );
        issueResp.setRequirementCode( issueVO.getRequirementCode() );
        issueResp.setRequirementName( issueVO.getRequirementName() );
        issueResp.setRequirementLevel( issueVO.getRequirementLevel() );
        issueResp.setFindVersionCode( issueVO.getFindVersionCode() );
        issueResp.setFindVersionName( issueVO.getFindVersionName() );
        issueResp.setFindUserId( issueVO.getFindUserId() );
        issueResp.setFindUserName( issueVO.getFindUserName() );
        issueResp.setHandleUserId( issueVO.getHandleUserId() );
        issueResp.setHandleUserName( issueVO.getHandleUserName() );
        issueResp.setDevelopUserId( issueVO.getDevelopUserId() );
        issueResp.setDevelopUserName( issueVO.getDevelopUserName() );
        issueResp.setTestUserId( issueVO.getTestUserId() );
        issueResp.setTestUserName( issueVO.getTestUserName() );
        issueResp.setCreatorId( issueVO.getCreatorId() );
        issueResp.setCreator( issueVO.getCreator() );
        issueResp.setUpdateUserId( issueVO.getUpdateUserId() );
        issueResp.setUpdateUserName( issueVO.getUpdateUserName() );
        List<AttachmentVO> list = issueVO.getAttachments();
        if ( list != null ) {
            issueResp.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<CaseVO> list1 = issueVO.getCaseList();
        if ( list1 != null ) {
            issueResp.setCaseList( new ArrayList<CaseVO>( list1 ) );
        }
        List<TagVO> list2 = issueVO.getTags();
        if ( list2 != null ) {
            issueResp.setTags( new ArrayList<TagVO>( list2 ) );
        }
        issueResp.setTagName( issueVO.getTagName() );
        List<CommentVO> list3 = issueVO.getComments();
        if ( list3 != null ) {
            issueResp.setComments( new HashSet<CommentVO>( list3 ) );
        }
        issueResp.setContent( issueVO.getContent() );
        issueResp.setReason( issueVO.getReason() );
        issueResp.setReasonDesc( issueVO.getReasonDesc() );
        issueResp.setActualWorkingHours( issueVO.getActualWorkingHours() );
        issueResp.setFileCount( issueVO.getFileCount() );
        issueResp.setCaseCount( issueVO.getCaseCount() );
        Set<String> set = issueVO.getEditableFieldNames();
        if ( set != null ) {
            issueResp.setEditableFieldNames( new HashSet<String>( set ) );
        }
        List<Event> list4 = issueVO.getOperableEvents();
        if ( list4 != null ) {
            issueResp.setOperableEvents( new ArrayList<Event>( list4 ) );
        }
        issueResp.setTransitToDevelop( issueVO.getTransitToDevelop() );
        issueResp.setTransitToTest( issueVO.getTransitToTest() );
        issueResp.setVersionConfirm( issueVO.getVersionConfirm() );
        issueResp.setMorefixVersion( issueVO.getMorefixVersion() );
        issueResp.setExamination( issueVO.getExamination() );
        issueResp.setTestOmission( issueVO.getTestOmission() );
        issueResp.setCodeDefect( issueVO.getCodeDefect() );
        issueResp.setTestOmissionVersion( issueVO.getTestOmissionVersion() );
        issueResp.setCodeDefectVersion( issueVO.getCodeDefectVersion() );
        issueResp.setApplicationType( issueVO.getApplicationType() );
        issueResp.setRefuseReason( issueVO.getRefuseReason() );
        issueResp.setCcUserId( issueVO.getCcUserId() );
        issueResp.setCcUserName( issueVO.getCcUserName() );
        Set<RelevantUserVO> set1 = issueVO.getCcManList();
        if ( set1 != null ) {
            issueResp.setCcManList( new HashSet<RelevantUserVO>( set1 ) );
        }
        issueResp.setPlanStartDate( issueVO.getPlanStartDate() );
        issueResp.setPlanEndDate( issueVO.getPlanEndDate() );
        issueResp.setMsgCode( issueVO.getMsgCode() );
        issueResp.setWarn( issueVO.getWarn() );
        issueResp.setWarnDay( issueVO.getWarnDay() );
        issueResp.setValidFlag( issueVO.getValidFlag() );

        issueResp.setFixVersionCode( com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.isDefaultValue(issueVO.getFixVersionCode())?null:issueVO.getFixVersionCode() );
        issueResp.setFixVersionName( com.zto.devops.qc.client.enums.constants.DefaultValueEnum.BLANKSTRING.isDefaultValue(issueVO.getFixVersionName())?null:issueVO.getFixVersionName() );

        return issueResp;
    }

    @Override
    public List<IssueResp> convert(List<IssueVO> issueVOs) {
        if ( issueVOs == null ) {
            return null;
        }

        List<IssueResp> list = new ArrayList<IssueResp>( issueVOs.size() );
        for ( IssueVO issueVO : issueVOs ) {
            list.add( convert( issueVO ) );
        }

        return list;
    }

    @Override
    public List<IssueLegacyListResp> convertLegacyList(List<IssueLegacyVO> issueLegacyVOList) {
        if ( issueLegacyVOList == null ) {
            return null;
        }

        List<IssueLegacyListResp> list = new ArrayList<IssueLegacyListResp>( issueLegacyVOList.size() );
        for ( IssueLegacyVO issueLegacyVO : issueLegacyVOList ) {
            list.add( issueLegacyVOToIssueLegacyListResp( issueLegacyVO ) );
        }

        return list;
    }

    @Override
    public IssueNumStatisticsResp convertStatistic(IssueNumStatisticsVO issueNumStatisticsVO) {
        if ( issueNumStatisticsVO == null ) {
            return null;
        }

        IssueNumStatisticsResp issueNumStatisticsResp = new IssueNumStatisticsResp();

        issueNumStatisticsResp.setIssueCount( issueNumStatisticsVO.getIssueCount() );
        issueNumStatisticsResp.setValidIssueCount( issueNumStatisticsVO.getValidIssueCount() );
        issueNumStatisticsResp.setLegacyIssueCount( issueNumStatisticsVO.getLegacyIssueCount() );
        issueNumStatisticsResp.setLegacyIssueHigh( issueNumStatisticsVO.getLegacyIssueHigh() );

        return issueNumStatisticsResp;
    }

    @Override
    public List<IssueTagResp> convert(Collection<TagVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<IssueTagResp> list = new ArrayList<IssueTagResp>( vos.size() );
        for ( TagVO tagVO : vos ) {
            list.add( tagVOToIssueTagResp( tagVO ) );
        }

        return list;
    }

    @Override
    public List<IssueResp> convertIssueResp(Collection<IssueVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<IssueResp> list = new ArrayList<IssueResp>( vos.size() );
        for ( IssueVO issueVO : vos ) {
            list.add( convert( issueVO ) );
        }

        return list;
    }

    @Override
    public List<TransitionNodeResp> convertTransitionNodeResp(Collection<TransitionNodeVO> vos) {
        if ( vos == null ) {
            return null;
        }

        List<TransitionNodeResp> list = new ArrayList<TransitionNodeResp>( vos.size() );
        for ( TransitionNodeVO transitionNodeVO : vos ) {
            list.add( transitionNodeVOToTransitionNodeResp( transitionNodeVO ) );
        }

        return list;
    }

    @Override
    public FindStatisticsIssueReq convert(StatisticsProductIssueReq req) {
        if ( req == null ) {
            return null;
        }

        FindStatisticsIssueReq findStatisticsIssueReq = new FindStatisticsIssueReq();

        findStatisticsIssueReq.setStartDate( req.getStartDate() );
        findStatisticsIssueReq.setEndDate( req.getEndDate() );
        findStatisticsIssueReq.setProductCode( req.getProductCode() );

        return findStatisticsIssueReq;
    }

    @Override
    public StatisticsProductIssueResp convert(StatisticsIssueResp resp) {
        if ( resp == null ) {
            return null;
        }

        StatisticsProductIssueResp statisticsProductIssueResp = new StatisticsProductIssueResp();

        statisticsProductIssueResp.setProductCode( resp.getProductCode() );
        statisticsProductIssueResp.setUrgencyRepairTime( resp.getUrgencyRepairTime() );
        statisticsProductIssueResp.setHighRepairTime( resp.getHighRepairTime() );
        statisticsProductIssueResp.setMiddleRepairTime( resp.getMiddleRepairTime() );
        statisticsProductIssueResp.setUrgencyNum( resp.getUrgencyNum() );
        statisticsProductIssueResp.setHighNum( resp.getHighNum() );
        statisticsProductIssueResp.setMiddleNum( resp.getMiddleNum() );
        statisticsProductIssueResp.setLowNum( resp.getLowNum() );
        statisticsProductIssueResp.setUrgencyPunctualNum( resp.getUrgencyPunctualNum() );
        statisticsProductIssueResp.setHighPunctualNum( resp.getHighPunctualNum() );
        statisticsProductIssueResp.setMiddlePunctualNum( resp.getMiddlePunctualNum() );
        statisticsProductIssueResp.setUrgencyReopenNum( resp.getUrgencyReopenNum() );
        statisticsProductIssueResp.setHighReopenNum( resp.getHighReopenNum() );
        statisticsProductIssueResp.setMiddleReopenNum( resp.getMiddleReopenNum() );
        statisticsProductIssueResp.setLowReopenNum( resp.getLowReopenNum() );
        statisticsProductIssueResp.setUrgencyEffectiveNum( resp.getUrgencyEffectiveNum() );
        statisticsProductIssueResp.setHighEffectiveNum( resp.getHighEffectiveNum() );
        statisticsProductIssueResp.setMiddleEffectiveNum( resp.getMiddleEffectiveNum() );
        statisticsProductIssueResp.setLowEffectiveNum( resp.getLowEffectiveNum() );
        statisticsProductIssueResp.setRequirementIssueNum( resp.getRequirementIssueNum() );
        statisticsProductIssueResp.setProduceIssueNum( resp.getProduceIssueNum() );

        return statisticsProductIssueResp;
    }

    protected IssueLegacyListResp issueLegacyVOToIssueLegacyListResp(IssueLegacyVO issueLegacyVO) {
        if ( issueLegacyVO == null ) {
            return null;
        }

        IssueLegacyListResp issueLegacyListResp = new IssueLegacyListResp();

        issueLegacyListResp.setCode( issueLegacyVO.getCode() );
        issueLegacyListResp.setIssueCode( issueLegacyVO.getIssueCode() );
        issueLegacyListResp.setTitle( issueLegacyVO.getTitle() );
        issueLegacyListResp.setStatus( issueLegacyVO.getStatus() );
        issueLegacyListResp.setStatusDesc( issueLegacyVO.getStatusDesc() );
        issueLegacyListResp.setHandleUserId( issueLegacyVO.getHandleUserId() );
        issueLegacyListResp.setHandleUserName( issueLegacyVO.getHandleUserName() );
        issueLegacyListResp.setPriority( issueLegacyVO.getPriority() );
        issueLegacyListResp.setPriorityDesc( issueLegacyVO.getPriorityDesc() );
        issueLegacyListResp.setDevelopUserId( issueLegacyVO.getDevelopUserId() );
        issueLegacyListResp.setDevelopUserName( issueLegacyVO.getDevelopUserName() );
        issueLegacyListResp.setFindUserId( issueLegacyVO.getFindUserId() );
        issueLegacyListResp.setFindUserName( issueLegacyVO.getFindUserName() );
        issueLegacyListResp.setFindVersionCode( issueLegacyVO.getFindVersionCode() );
        issueLegacyListResp.setFindVersionName( issueLegacyVO.getFindVersionName() );

        return issueLegacyListResp;
    }

    protected IssueTagResp tagVOToIssueTagResp(TagVO tagVO) {
        if ( tagVO == null ) {
            return null;
        }

        IssueTagResp issueTagResp = new IssueTagResp();

        issueTagResp.setCode( tagVO.getCode() );
        if ( tagVO.getDomain() != null ) {
            issueTagResp.setDomain( tagVO.getDomain().name() );
        }
        issueTagResp.setBusinessCode( tagVO.getBusinessCode() );
        if ( tagVO.getType() != null ) {
            issueTagResp.setType( tagVO.getType().name() );
        }
        issueTagResp.setTagAlias( tagVO.getTagAlias() );
        issueTagResp.setTagCode( tagVO.getTagCode() );
        issueTagResp.setTagName( tagVO.getTagName() );
        issueTagResp.setEnable( tagVO.getEnable() );
        issueTagResp.setCreatorId( tagVO.getCreatorId() );

        return issueTagResp;
    }

    protected TransitionNodeResp transitionNodeVOToTransitionNodeResp(TransitionNodeVO transitionNodeVO) {
        if ( transitionNodeVO == null ) {
            return null;
        }

        TransitionNodeResp transitionNodeResp = new TransitionNodeResp();

        transitionNodeResp.setCode( transitionNodeVO.getCode() );
        if ( transitionNodeVO.getDomain() != null ) {
            transitionNodeResp.setDomain( transitionNodeVO.getDomain().name() );
        }
        transitionNodeResp.setBusinessCode( transitionNodeVO.getBusinessCode() );
        if ( transitionNodeVO.getCurStatus() != null ) {
            transitionNodeResp.setCurStatus( transitionNodeVO.getCurStatus().name() );
        }
        transitionNodeResp.setContent( transitionNodeVO.getContent() );
        if ( transitionNodeVO.getReason() != null ) {
            transitionNodeResp.setReason( transitionNodeVO.getReason().name() );
        }
        if ( transitionNodeVO.getNextStatus() != null ) {
            transitionNodeResp.setNextStatus( transitionNodeVO.getNextStatus().name() );
        }
        transitionNodeResp.setEnable( transitionNodeVO.getEnable() );
        transitionNodeResp.setCreatorId( transitionNodeVO.getCreatorId() );
        transitionNodeResp.setCreator( transitionNodeVO.getCreator() );
        transitionNodeResp.setGmtCreate( transitionNodeVO.getGmtCreate() );
        transitionNodeResp.setModifierId( transitionNodeVO.getModifierId() );
        transitionNodeResp.setModifier( transitionNodeVO.getModifier() );
        transitionNodeResp.setGmtModified( transitionNodeVO.getGmtModified() );

        return transitionNodeResp;
    }
}
