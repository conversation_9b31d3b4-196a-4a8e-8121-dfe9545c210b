package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.RelatedToMeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.CaseVO;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.model.issue.entity.Event;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindSortedPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.command.ChangeCaseExecuteResultCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.EditPlanStageStatusCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.EditPlanStatusCommand;
import com.zto.devops.qc.client.model.testmanager.plan.entity.PageTestPlanBaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.PlanPhaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.RelatedPlanReportVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.RelatedPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.RelatedReportVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestFunctionPointVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanStageVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmPlanCaseIssueVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanSendEmailVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.AssociatedTestPlanListQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.CurrentPersonPermissionInformationQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseCodeQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseModuleQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanPhaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PageListPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PagePlanIssueQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PageTestPlanQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PlanListQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.RelatedPlanReportQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.VerifyTestPassConditionQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.VersionPlanQuery;
import com.zto.devops.qc.client.service.plan.model.AddCommonTestPlanReq;
import com.zto.devops.qc.client.service.plan.model.AddMobileSpecialTestPlanReq;
import com.zto.devops.qc.client.service.plan.model.AddSafeTestPlanReq;
import com.zto.devops.qc.client.service.plan.model.AssociatedTestPlanListReq;
import com.zto.devops.qc.client.service.plan.model.ChangeCaseExecuteResultReq;
import com.zto.devops.qc.client.service.plan.model.CurrentPersonPermissionInformationReq;
import com.zto.devops.qc.client.service.plan.model.EditTestPlanStageReq;
import com.zto.devops.qc.client.service.plan.model.EditTestPlanStatusReq;
import com.zto.devops.qc.client.service.plan.model.FindSortedPlanCaseReq;
import com.zto.devops.qc.client.service.plan.model.ListPlanPhaseReq;
import com.zto.devops.qc.client.service.plan.model.PagePlanPhaseResp;
import com.zto.devops.qc.client.service.plan.model.PagePlanTestcaseResp;
import com.zto.devops.qc.client.service.plan.model.PageTestPlanCaseListReq;
import com.zto.devops.qc.client.service.plan.model.PageTestPlanReq;
import com.zto.devops.qc.client.service.plan.model.PageTestPlanResp;
import com.zto.devops.qc.client.service.plan.model.PlanIssueResp;
import com.zto.devops.qc.client.service.plan.model.PlanPageReq;
import com.zto.devops.qc.client.service.plan.model.RelatedPlanReportResp;
import com.zto.devops.qc.client.service.plan.model.RelatedTestReq;
import com.zto.devops.qc.client.service.plan.model.SendTestPlanReq;
import com.zto.devops.qc.client.service.plan.model.TestPlanCaseCodeListReq;
import com.zto.devops.qc.client.service.plan.model.TestPlanCaseListReq;
import com.zto.devops.qc.client.service.plan.model.TestPlanCaseModuleListReq;
import com.zto.devops.qc.client.service.plan.model.TestPlanIssueReq;
import com.zto.devops.qc.client.service.plan.model.TestPlanResp;
import com.zto.devops.qc.client.service.plan.model.VerifyTestPassConditionReq;
import com.zto.devops.qc.client.service.plan.model.VersionPlanReq;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TmTestPlanAdapterConvertorImpl implements TmTestPlanAdapterConvertor {

    @Override
    public RelatedPlanReportQuery convertor(RelatedTestReq req) {
        if ( req == null ) {
            return null;
        }

        RelatedPlanReportQuery relatedPlanReportQuery = new RelatedPlanReportQuery();

        relatedPlanReportQuery.setPlanCode( req.getPlanCode() );
        relatedPlanReportQuery.setReportCode( req.getReportCode() );
        relatedPlanReportQuery.setReportType( req.getReportType() );
        relatedPlanReportQuery.setPlanType( req.getPlanType() );

        return relatedPlanReportQuery;
    }

    @Override
    public RelatedPlanReportResp convertor(RelatedPlanReportVO relatedVO) {
        if ( relatedVO == null ) {
            return null;
        }

        RelatedPlanReportResp relatedPlanReportResp = new RelatedPlanReportResp();

        List<RelatedPlanVO> list = relatedVO.getTestPlan();
        if ( list != null ) {
            relatedPlanReportResp.setTestPlan( new ArrayList<RelatedPlanVO>( list ) );
        }
        List<RelatedReportVO> list1 = relatedVO.getTestReport();
        if ( list1 != null ) {
            relatedPlanReportResp.setTestReport( new ArrayList<RelatedReportVO>( list1 ) );
        }

        return relatedPlanReportResp;
    }

    @Override
    public PagePlanIssueQuery convertor(TestPlanIssueReq req) {
        if ( req == null ) {
            return null;
        }

        PagePlanIssueQuery pagePlanIssueQuery = new PagePlanIssueQuery();

        pagePlanIssueQuery.setTransactor( req.getTransactor() );
        pagePlanIssueQuery.setPage( req.getPage() );
        pagePlanIssueQuery.setSize( req.getSize() );
        pagePlanIssueQuery.setPlanCode( req.getPlanCode() );
        pagePlanIssueQuery.setCodeOrTitle( req.getCodeOrTitle() );
        List<IssueStatus> list = req.getStatusList();
        if ( list != null ) {
            pagePlanIssueQuery.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        List<Long> list1 = req.getHandleUserIdList();
        if ( list1 != null ) {
            pagePlanIssueQuery.setHandleUserIdList( new ArrayList<Long>( list1 ) );
        }
        List<Long> list2 = req.getDevelopUserIdList();
        if ( list2 != null ) {
            pagePlanIssueQuery.setDevelopUserIdList( new ArrayList<Long>( list2 ) );
        }
        List<Long> list3 = req.getTestUserIdList();
        if ( list3 != null ) {
            pagePlanIssueQuery.setTestUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<RelatedToMeEnum> list4 = req.getRelatedList();
        if ( list4 != null ) {
            pagePlanIssueQuery.setRelatedList( new ArrayList<RelatedToMeEnum>( list4 ) );
        }
        List<IssuePriority> list5 = req.getPriorityList();
        if ( list5 != null ) {
            pagePlanIssueQuery.setPriorityList( new ArrayList<IssuePriority>( list5 ) );
        }

        return pagePlanIssueQuery;
    }

    @Override
    public List<PlanIssueResp> convertor(List<TmPlanCaseIssueVO> list) {
        if ( list == null ) {
            return null;
        }

        List<PlanIssueResp> list1 = new ArrayList<PlanIssueResp>( list.size() );
        for ( TmPlanCaseIssueVO tmPlanCaseIssueVO : list ) {
            list1.add( convertor( tmPlanCaseIssueVO ) );
        }

        return list1;
    }

    @Override
    public PlanIssueResp convertor(TmPlanCaseIssueVO vo) {
        if ( vo == null ) {
            return null;
        }

        PlanIssueResp planIssueResp = new PlanIssueResp();

        if ( vo.getReopenTime() != null ) {
            planIssueResp.setReopenTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getReopenTime() ) );
        }
        if ( vo.getFindTime() != null ) {
            planIssueResp.setFindTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getFindTime() ) );
        }
        if ( vo.getStartFixTime() != null ) {
            planIssueResp.setStartFixTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getStartFixTime() ) );
        }
        if ( vo.getDelayFixTime() != null ) {
            planIssueResp.setDelayFixTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getDelayFixTime() ) );
        }
        if ( vo.getDeliverTime() != null ) {
            planIssueResp.setDeliverTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getDeliverTime() ) );
        }
        if ( vo.getRejectTime() != null ) {
            planIssueResp.setRejectTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getRejectTime() ) );
        }
        if ( vo.getCloseTime() != null ) {
            planIssueResp.setCloseTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getCloseTime() ) );
        }
        if ( vo.getUpdateTime() != null ) {
            planIssueResp.setUpdateTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getUpdateTime() ) );
        }
        if ( vo.getGmtCreate() != null ) {
            planIssueResp.setGmtCreate( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( vo.getGmtCreate() ) );
        }
        planIssueResp.setCode( vo.getCode() );
        planIssueResp.setTitle( vo.getTitle() );
        planIssueResp.setDescription( vo.getDescription() );
        planIssueResp.setStatus( vo.getStatus() );
        planIssueResp.setFindEnv( vo.getFindEnv() );
        planIssueResp.setFindStage( vo.getFindStage() );
        planIssueResp.setPriority( vo.getPriority() );
        planIssueResp.setRepetitionRate( vo.getRepetitionRate() );
        planIssueResp.setRootCause( vo.getRootCause() );
        planIssueResp.setTestMethod( vo.getTestMethod() );
        planIssueResp.setType( vo.getType() );
        planIssueResp.setProductCode( vo.getProductCode() );
        planIssueResp.setProductName( vo.getProductName() );
        planIssueResp.setSprintCode( vo.getSprintCode() );
        planIssueResp.setSprintName( vo.getSprintName() );
        planIssueResp.setRequirementCode( vo.getRequirementCode() );
        planIssueResp.setRequirementName( vo.getRequirementName() );
        planIssueResp.setRequirementLevel( vo.getRequirementLevel() );
        planIssueResp.setFindVersionCode( vo.getFindVersionCode() );
        planIssueResp.setFindVersionName( vo.getFindVersionName() );
        planIssueResp.setFindUserId( vo.getFindUserId() );
        planIssueResp.setFindUserName( vo.getFindUserName() );
        planIssueResp.setHandleUserId( vo.getHandleUserId() );
        planIssueResp.setHandleUserName( vo.getHandleUserName() );
        planIssueResp.setDevelopUserId( vo.getDevelopUserId() );
        planIssueResp.setDevelopUserName( vo.getDevelopUserName() );
        planIssueResp.setTestUserId( vo.getTestUserId() );
        planIssueResp.setTestUserName( vo.getTestUserName() );
        planIssueResp.setCreatorId( vo.getCreatorId() );
        planIssueResp.setCreator( vo.getCreator() );
        planIssueResp.setUpdateUserId( vo.getUpdateUserId() );
        planIssueResp.setUpdateUserName( vo.getUpdateUserName() );
        List<AttachmentVO> list = vo.getAttachments();
        if ( list != null ) {
            planIssueResp.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<CaseVO> list1 = vo.getCaseList();
        if ( list1 != null ) {
            planIssueResp.setCaseList( new ArrayList<CaseVO>( list1 ) );
        }
        List<TagVO> list2 = vo.getTags();
        if ( list2 != null ) {
            planIssueResp.setTags( new ArrayList<TagVO>( list2 ) );
        }
        planIssueResp.setTagName( vo.getTagName() );
        List<CommentVO> list3 = vo.getComments();
        if ( list3 != null ) {
            planIssueResp.setComments( new HashSet<CommentVO>( list3 ) );
        }
        planIssueResp.setContent( vo.getContent() );
        planIssueResp.setReason( vo.getReason() );
        planIssueResp.setReasonDesc( vo.getReasonDesc() );
        planIssueResp.setActualWorkingHours( vo.getActualWorkingHours() );
        planIssueResp.setFileCount( vo.getFileCount() );
        planIssueResp.setCaseCount( vo.getCaseCount() );
        Set<String> set = vo.getEditableFieldNames();
        if ( set != null ) {
            planIssueResp.setEditableFieldNames( new HashSet<String>( set ) );
        }
        List<Event> list4 = vo.getOperableEvents();
        if ( list4 != null ) {
            planIssueResp.setOperableEvents( new ArrayList<Event>( list4 ) );
        }
        planIssueResp.setTransitToDevelop( vo.getTransitToDevelop() );
        planIssueResp.setTransitToTest( vo.getTransitToTest() );
        planIssueResp.setVersionConfirm( vo.getVersionConfirm() );
        planIssueResp.setMorefixVersion( vo.getMorefixVersion() );
        planIssueResp.setExamination( vo.getExamination() );
        planIssueResp.setTestOmission( vo.getTestOmission() );
        planIssueResp.setCodeDefect( vo.getCodeDefect() );
        planIssueResp.setTestOmissionVersion( vo.getTestOmissionVersion() );
        planIssueResp.setCodeDefectVersion( vo.getCodeDefectVersion() );
        planIssueResp.setApplicationType( vo.getApplicationType() );
        planIssueResp.setRefuseReason( vo.getRefuseReason() );
        planIssueResp.setCcUserId( vo.getCcUserId() );
        planIssueResp.setCcUserName( vo.getCcUserName() );
        Set<RelevantUserVO> set1 = vo.getCcManList();
        if ( set1 != null ) {
            planIssueResp.setCcManList( new HashSet<RelevantUserVO>( set1 ) );
        }
        planIssueResp.setPlanStartDate( vo.getPlanStartDate() );
        planIssueResp.setPlanEndDate( vo.getPlanEndDate() );
        planIssueResp.setMsgCode( vo.getMsgCode() );
        planIssueResp.setWarn( vo.getWarn() );
        planIssueResp.setWarnDay( vo.getWarnDay() );
        planIssueResp.setValidFlag( vo.getValidFlag() );
        planIssueResp.setCaseCode( vo.getCaseCode() );
        planIssueResp.setCaseName( vo.getCaseName() );

        planIssueResp.setFixVersionCode( com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.isDefaultValue(vo.getFixVersionCode())?null:vo.getFixVersionCode() );
        planIssueResp.setFixVersionName( com.zto.devops.qc.client.enums.constants.DefaultValueEnum.BLANKSTRING.isDefaultValue(vo.getFixVersionName())?null:vo.getFixVersionName() );

        return planIssueResp;
    }

    @Override
    public PlanListQuery convertor(PlanPageReq req) {
        if ( req == null ) {
            return null;
        }

        PlanListQuery planListQuery = new PlanListQuery();

        planListQuery.setTransactor( req.getTransactor() );
        planListQuery.setPage( req.getPage() );
        planListQuery.setSize( req.getSize() );
        List<TestPlanStrategyEnum> list = req.getTestStrategyList();
        if ( list != null ) {
            planListQuery.setTestStrategyList( new ArrayList<TestPlanStrategyEnum>( list ) );
        }
        planListQuery.setPlanName( req.getPlanName() );
        List<String> list1 = req.getStatus();
        if ( list1 != null ) {
            planListQuery.setStatus( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = req.getType();
        if ( list2 != null ) {
            planListQuery.setType( new ArrayList<String>( list2 ) );
        }
        planListQuery.setProductCode( req.getProductCode() );
        planListQuery.setAccessTimeStart( req.getAccessTimeStart() );
        planListQuery.setAccessTimeEnd( req.getAccessTimeEnd() );
        planListQuery.setPermitTimeStart( req.getPermitTimeStart() );
        planListQuery.setPermitTimeEnd( req.getPermitTimeEnd() );
        List<String> list3 = req.getPlanDirectorId();
        if ( list3 != null ) {
            planListQuery.setPlanDirectorId( new ArrayList<String>( list3 ) );
        }
        planListQuery.setCreateTimeStart( req.getCreateTimeStart() );
        planListQuery.setCreateTimeEnd( req.getCreateTimeEnd() );
        planListQuery.setUpdateTimeStart( req.getUpdateTimeStart() );
        planListQuery.setUpdateTimeEnd( req.getUpdateTimeEnd() );
        List<Long> list4 = req.getModifierId();
        if ( list4 != null ) {
            planListQuery.setModifierId( new ArrayList<Long>( list4 ) );
        }
        if ( req.getOrderType() != null ) {
            planListQuery.setOrderType( req.getOrderType().name() );
        }

        planListQuery.setOrderField( com.zto.devops.qc.client.enums.testmanager.plan.PlanFieldEnum.getNameByCode(req.getOrderField()) );

        return planListQuery;
    }

    @Override
    public PageListPlanCaseQuery convertor(PageTestPlanCaseListReq req) {
        if ( req == null ) {
            return null;
        }

        PageListPlanCaseQuery pageListPlanCaseQuery = new PageListPlanCaseQuery();

        pageListPlanCaseQuery.setSearch( req.getCodeOrTitle() );
        pageListPlanCaseQuery.setPlanCode( req.getCode() );
        pageListPlanCaseQuery.setTransactor( req.getTransactor() );
        pageListPlanCaseQuery.setPage( req.getPage() );
        pageListPlanCaseQuery.setSize( req.getSize() );
        pageListPlanCaseQuery.setProductCode( req.getProductCode() );
        List<TestPlanStageEnum> list = req.getTestPlanStageList();
        if ( list != null ) {
            pageListPlanCaseQuery.setTestPlanStageList( new ArrayList<TestPlanStageEnum>( list ) );
        }
        pageListPlanCaseQuery.setParentCode( req.getParentCode() );
        List<TestcaseTypeEnum> list1 = req.getCaseTypeList();
        if ( list1 != null ) {
            pageListPlanCaseQuery.setCaseTypeList( new ArrayList<TestcaseTypeEnum>( list1 ) );
        }
        List<TestPlanCaseStatusEnum> list2 = req.getStatusList();
        if ( list2 != null ) {
            pageListPlanCaseQuery.setStatusList( new ArrayList<TestPlanCaseStatusEnum>( list2 ) );
        }
        List<Long> list3 = req.getExecutorIdList();
        if ( list3 != null ) {
            pageListPlanCaseQuery.setExecutorIdList( new ArrayList<Long>( list3 ) );
        }
        List<TestcasePriorityEnum> list4 = req.getPriorityList();
        if ( list4 != null ) {
            pageListPlanCaseQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list4 ) );
        }
        List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
        if ( list5 != null ) {
            pageListPlanCaseQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
        }
        List<AutomaticRecordTypeEnum> list6 = req.getAutomaticTypeList();
        if ( list6 != null ) {
            pageListPlanCaseQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list6 ) );
        }
        List<String> list7 = req.getTagList();
        if ( list7 != null ) {
            pageListPlanCaseQuery.setTagList( new ArrayList<String>( list7 ) );
        }
        List<Boolean> list8 = req.getSetCoreList();
        if ( list8 != null ) {
            pageListPlanCaseQuery.setSetCoreList( new ArrayList<Boolean>( list8 ) );
        }

        return pageListPlanCaseQuery;
    }

    @Override
    public List<PagePlanTestcaseResp> converterList(Collection<TestPlanCaseVO> list) {
        if ( list == null ) {
            return null;
        }

        List<PagePlanTestcaseResp> list1 = new ArrayList<PagePlanTestcaseResp>( list.size() );
        for ( TestPlanCaseVO testPlanCaseVO : list ) {
            list1.add( testPlanCaseVOToPagePlanTestcaseResp( testPlanCaseVO ) );
        }

        return list1;
    }

    @Override
    public ListPlanCaseCodeQuery convertor(TestPlanCaseCodeListReq req) {
        if ( req == null ) {
            return null;
        }

        ListPlanCaseCodeQuery listPlanCaseCodeQuery = new ListPlanCaseCodeQuery();

        listPlanCaseCodeQuery.setSearch( req.getCodeOrTitle() );
        listPlanCaseCodeQuery.setPlanCode( req.getCode() );
        listPlanCaseCodeQuery.setProductCode( req.getProductCode() );
        List<TestPlanStageEnum> list = req.getTestPlanStageList();
        if ( list != null ) {
            listPlanCaseCodeQuery.setTestPlanStageList( new ArrayList<TestPlanStageEnum>( list ) );
        }
        List<TestcaseTypeEnum> list1 = req.getCaseTypeList();
        if ( list1 != null ) {
            listPlanCaseCodeQuery.setCaseTypeList( new ArrayList<TestcaseTypeEnum>( list1 ) );
        }
        listPlanCaseCodeQuery.setParentCode( req.getParentCode() );
        List<TestPlanCaseStatusEnum> list2 = req.getStatusList();
        if ( list2 != null ) {
            listPlanCaseCodeQuery.setStatusList( new ArrayList<TestPlanCaseStatusEnum>( list2 ) );
        }
        List<Long> list3 = req.getExecutorIdList();
        if ( list3 != null ) {
            listPlanCaseCodeQuery.setExecutorIdList( new ArrayList<Long>( list3 ) );
        }
        List<TestcasePriorityEnum> list4 = req.getPriorityList();
        if ( list4 != null ) {
            listPlanCaseCodeQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list4 ) );
        }
        List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
        if ( list5 != null ) {
            listPlanCaseCodeQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
        }
        List<AutomaticRecordTypeEnum> list6 = req.getAutomaticTypeList();
        if ( list6 != null ) {
            listPlanCaseCodeQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list6 ) );
        }
        List<String> list7 = req.getTagList();
        if ( list7 != null ) {
            listPlanCaseCodeQuery.setTagList( new ArrayList<String>( list7 ) );
        }
        List<Boolean> list8 = req.getSetCoreList();
        if ( list8 != null ) {
            listPlanCaseCodeQuery.setSetCoreList( new ArrayList<Boolean>( list8 ) );
        }

        return listPlanCaseCodeQuery;
    }

    @Override
    public ListPlanPhaseQuery convertor(ListPlanPhaseReq req) {
        if ( req == null ) {
            return null;
        }

        ListPlanPhaseQuery listPlanPhaseQuery = new ListPlanPhaseQuery();

        listPlanPhaseQuery.setTransactor( req.getTransactor() );
        listPlanPhaseQuery.setPage( req.getPage() );
        listPlanPhaseQuery.setSize( req.getSize() );
        listPlanPhaseQuery.setProductCode( req.getProductCode() );
        listPlanPhaseQuery.setCaseType( req.getCaseType() );
        listPlanPhaseQuery.setVersionCode( req.getVersionCode() );
        listPlanPhaseQuery.setPlanName( req.getPlanName() );

        return listPlanPhaseQuery;
    }

    @Override
    public void convertor(FindSortedPlanCaseReq req, FindSortedPlanCaseQuery query) {
        if ( req == null ) {
            return;
        }

        query.setCaseCode( req.getCaseCode() );
        query.setPlanCode( req.getPlanCode() );
        query.setTestStage( req.getTestStage() );
        if ( query.getPriorityList() != null ) {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.getPriorityList().clear();
                query.getPriorityList().addAll( list );
            }
            else {
                query.setPriorityList( null );
            }
        }
        else {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
            }
        }
        if ( query.getTagList() != null ) {
            List<String> list1 = req.getTagList();
            if ( list1 != null ) {
                query.getTagList().clear();
                query.getTagList().addAll( list1 );
            }
            else {
                query.setTagList( null );
            }
        }
        else {
            List<String> list1 = req.getTagList();
            if ( list1 != null ) {
                query.setTagList( new ArrayList<String>( list1 ) );
            }
        }
        if ( query.getStatusList() != null ) {
            List<TestPlanCaseStatusEnum> list2 = req.getStatusList();
            if ( list2 != null ) {
                query.getStatusList().clear();
                query.getStatusList().addAll( list2 );
            }
            else {
                query.setStatusList( null );
            }
        }
        else {
            List<TestPlanCaseStatusEnum> list2 = req.getStatusList();
            if ( list2 != null ) {
                query.setStatusList( new ArrayList<TestPlanCaseStatusEnum>( list2 ) );
            }
        }
        if ( query.getExecutorIdList() != null ) {
            List<Long> list3 = req.getExecutorIdList();
            if ( list3 != null ) {
                query.getExecutorIdList().clear();
                query.getExecutorIdList().addAll( list3 );
            }
            else {
                query.setExecutorIdList( null );
            }
        }
        else {
            List<Long> list3 = req.getExecutorIdList();
            if ( list3 != null ) {
                query.setExecutorIdList( new ArrayList<Long>( list3 ) );
            }
        }
        query.setCodeOrTitle( req.getCodeOrTitle() );
        query.setTestcaseType( req.getTestcaseType() );
    }

    @Override
    public PageTestPlanQuery convertor(PageTestPlanReq req) {
        if ( req == null ) {
            return null;
        }

        PageTestPlanQuery pageTestPlanQuery = new PageTestPlanQuery();

        pageTestPlanQuery.setPlanName( req.getName() );
        pageTestPlanQuery.setTransactor( req.getTransactor() );
        pageTestPlanQuery.setPage( req.getPage() );
        pageTestPlanQuery.setSize( req.getSize() );
        pageTestPlanQuery.setProductCode( req.getProductCode() );

        return pageTestPlanQuery;
    }

    @Override
    public List<PageTestPlanResp> convertorList(List<PageTestPlanBaseVO> list) {
        if ( list == null ) {
            return null;
        }

        List<PageTestPlanResp> list1 = new ArrayList<PageTestPlanResp>( list.size() );
        for ( PageTestPlanBaseVO pageTestPlanBaseVO : list ) {
            list1.add( convertor( pageTestPlanBaseVO ) );
        }

        return list1;
    }

    @Override
    public PageTestPlanResp convertor(PageTestPlanBaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        PageTestPlanResp pageTestPlanResp = new PageTestPlanResp();

        pageTestPlanResp.setCode( vo.getPlanCode() );
        pageTestPlanResp.setName( vo.getPlanName() );

        return pageTestPlanResp;
    }

    @Override
    public VersionPlanQuery convertor(VersionPlanReq req) {
        if ( req == null ) {
            return null;
        }

        VersionPlanQuery versionPlanQuery = new VersionPlanQuery();

        versionPlanQuery.setVersionCode( req.getVersionCode() );

        return versionPlanQuery;
    }

    @Override
    public List<TestPlanResp> convertor(Collection<TmTestPlanVO> versionPlanVOList) {
        if ( versionPlanVOList == null ) {
            return null;
        }

        List<TestPlanResp> list = new ArrayList<TestPlanResp>( versionPlanVOList.size() );
        for ( TmTestPlanVO tmTestPlanVO : versionPlanVOList ) {
            list.add( tmTestPlanVOToTestPlanResp( tmTestPlanVO ) );
        }

        return list;
    }

    @Override
    public ListPlanCaseQuery convertor(TestPlanCaseListReq req) {
        if ( req == null ) {
            return null;
        }

        ListPlanCaseQuery listPlanCaseQuery = new ListPlanCaseQuery();

        listPlanCaseQuery.setSearch( req.getCodeOrTitle() );
        listPlanCaseQuery.setPlanCode( req.getCode() );
        listPlanCaseQuery.setProductCode( req.getProductCode() );
        List<TestPlanStageEnum> list = req.getTestPlanStageList();
        if ( list != null ) {
            listPlanCaseQuery.setTestPlanStageList( new ArrayList<TestPlanStageEnum>( list ) );
        }
        listPlanCaseQuery.setParentCode( req.getParentCode() );
        List<TestcaseTypeEnum> list1 = req.getCaseTypeList();
        if ( list1 != null ) {
            listPlanCaseQuery.setCaseTypeList( new ArrayList<TestcaseTypeEnum>( list1 ) );
        }
        List<TestPlanCaseStatusEnum> list2 = req.getStatusList();
        if ( list2 != null ) {
            listPlanCaseQuery.setStatusList( new ArrayList<TestPlanCaseStatusEnum>( list2 ) );
        }
        List<Long> list3 = req.getExecutorIdList();
        if ( list3 != null ) {
            listPlanCaseQuery.setExecutorIdList( new ArrayList<Long>( list3 ) );
        }
        List<TestcasePriorityEnum> list4 = req.getPriorityList();
        if ( list4 != null ) {
            listPlanCaseQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list4 ) );
        }
        List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
        if ( list5 != null ) {
            listPlanCaseQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
        }
        List<AutomaticRecordTypeEnum> list6 = req.getAutomaticTypeList();
        if ( list6 != null ) {
            listPlanCaseQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list6 ) );
        }
        List<String> list7 = req.getTagList();
        if ( list7 != null ) {
            listPlanCaseQuery.setTagList( new ArrayList<String>( list7 ) );
        }
        List<Boolean> list8 = req.getSetCoreList();
        if ( list8 != null ) {
            listPlanCaseQuery.setSetCoreList( new ArrayList<Boolean>( list8 ) );
        }

        return listPlanCaseQuery;
    }

    @Override
    public ListPlanCaseModuleQuery convertor(TestPlanCaseModuleListReq req) {
        if ( req == null ) {
            return null;
        }

        ListPlanCaseModuleQuery listPlanCaseModuleQuery = new ListPlanCaseModuleQuery();

        listPlanCaseModuleQuery.setPlanCode( req.getCode() );
        listPlanCaseModuleQuery.setProductCode( req.getProductCode() );
        List<TestPlanStageEnum> list = req.getTestPlanStageList();
        if ( list != null ) {
            listPlanCaseModuleQuery.setTestPlanStageList( new ArrayList<TestPlanStageEnum>( list ) );
        }
        List<TestcaseTypeEnum> list1 = req.getCaseTypeList();
        if ( list1 != null ) {
            listPlanCaseModuleQuery.setCaseTypeList( new ArrayList<TestcaseTypeEnum>( list1 ) );
        }
        listPlanCaseModuleQuery.setSetCore( req.getSetCore() );

        return listPlanCaseModuleQuery;
    }

    @Override
    public EditPlanStageStatusCommand convertor(EditTestPlanStageReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getPlanCode();

        EditPlanStageStatusCommand editPlanStageStatusCommand = new EditPlanStageStatusCommand( aggregateId );

        editPlanStageStatusCommand.setPlanCode( req.getPlanCode() );
        editPlanStageStatusCommand.setType( req.getType() );
        editPlanStageStatusCommand.setStage( req.getStage() );

        return editPlanStageStatusCommand;
    }

    @Override
    public EditPlanStatusCommand convertor(EditTestPlanStatusReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getPlanCode();

        EditPlanStatusCommand editPlanStatusCommand = new EditPlanStatusCommand( aggregateId );

        editPlanStatusCommand.setPlanCode( req.getPlanCode() );
        editPlanStatusCommand.setStatus( req.getStatus() );

        return editPlanStatusCommand;
    }

    @Override
    public TmTestPlanSendEmailVO convertor(SendTestPlanReq req) {
        if ( req == null ) {
            return null;
        }

        TmTestPlanSendEmailVO tmTestPlanSendEmailVO = new TmTestPlanSendEmailVO();

        tmTestPlanSendEmailVO.setEmailType( req.getEmailType() );
        tmTestPlanSendEmailVO.setBusinessCode( req.getBusinessCode() );
        tmTestPlanSendEmailVO.setBusinessName( req.getBusinessName() );
        tmTestPlanSendEmailVO.setRelationPlanCode( req.getRelationPlanCode() );
        tmTestPlanSendEmailVO.setRelationPlanName( req.getRelationPlanName() );
        tmTestPlanSendEmailVO.setProductCode( req.getProductCode() );
        tmTestPlanSendEmailVO.setProductName( req.getProductName() );
        tmTestPlanSendEmailVO.setVersionCode( req.getVersionCode() );
        tmTestPlanSendEmailVO.setVersionName( req.getVersionName() );
        tmTestPlanSendEmailVO.setAccessDate( req.getAccessDate() );
        tmTestPlanSendEmailVO.setAccessDatePartition( req.getAccessDatePartition() );
        tmTestPlanSendEmailVO.setPermitDate( req.getPermitDate() );
        tmTestPlanSendEmailVO.setPermitDatePartition( req.getPermitDatePartition() );
        List<SendUserInfoVO> list = req.getReceiveUsers();
        if ( list != null ) {
            tmTestPlanSendEmailVO.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = req.getCcUsers();
        if ( list1 != null ) {
            tmTestPlanSendEmailVO.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        tmTestPlanSendEmailVO.setPreview( req.getPreview() );

        return tmTestPlanSendEmailVO;
    }

    @Override
    public TmTestPlanVO convertor(AddMobileSpecialTestPlanReq req) {
        if ( req == null ) {
            return null;
        }

        TmTestPlanVO tmTestPlanVO = new TmTestPlanVO();

        tmTestPlanVO.setCode( req.getPlanCode() );
        tmTestPlanVO.setPlanName( req.getPlanName() );
        tmTestPlanVO.setRelationPlanCode( req.getRelationPlanCode() );
        tmTestPlanVO.setRelationPlanName( req.getRelationPlanName() );
        tmTestPlanVO.setDeptId( req.getDeptId() );
        tmTestPlanVO.setDeptName( req.getDeptName() );
        tmTestPlanVO.setProductCode( req.getProductCode() );
        tmTestPlanVO.setProductName( req.getProductName() );
        tmTestPlanVO.setVersionCode( req.getVersionCode() );
        tmTestPlanVO.setVersionName( req.getVersionName() );
        tmTestPlanVO.setAccessDate( req.getAccessDate() );
        tmTestPlanVO.setAccessDatePartition( req.getAccessDatePartition() );
        tmTestPlanVO.setPermitDate( req.getPermitDate() );
        tmTestPlanVO.setPermitDatePartition( req.getPermitDatePartition() );
        tmTestPlanVO.setComment( req.getComment() );
        tmTestPlanVO.setProductDirectorId( req.getProductDirectorId() );
        tmTestPlanVO.setProductDirectorName( req.getProductDirectorName() );
        List<TestFunctionPointVO> list = req.getPointList();
        if ( list != null ) {
            tmTestPlanVO.setPointList( new ArrayList<TestFunctionPointVO>( list ) );
        }

        return tmTestPlanVO;
    }

    @Override
    public AssociatedTestPlanListQuery convertor(AssociatedTestPlanListReq req) {
        if ( req == null ) {
            return null;
        }

        AssociatedTestPlanListQuery associatedTestPlanListQuery = new AssociatedTestPlanListQuery();

        associatedTestPlanListQuery.setPlanName( req.getPlanName() );
        associatedTestPlanListQuery.setProductCode( req.getProductCode() );

        return associatedTestPlanListQuery;
    }

    @Override
    public ChangeCaseExecuteResultCommand convertor(ChangeCaseExecuteResultReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getPlanCode();

        ChangeCaseExecuteResultCommand changeCaseExecuteResultCommand = new ChangeCaseExecuteResultCommand( aggregateId );

        changeCaseExecuteResultCommand.setExecuteStatus( req.getExecuteStatus() );
        List<String> list = req.getCaseIds();
        if ( list != null ) {
            changeCaseExecuteResultCommand.setCaseIds( new ArrayList<String>( list ) );
        }
        changeCaseExecuteResultCommand.setTestStage( req.getTestStage() );
        changeCaseExecuteResultCommand.setPlanCode( req.getPlanCode() );

        return changeCaseExecuteResultCommand;
    }

    @Override
    public CurrentPersonPermissionInformationQuery convertor(CurrentPersonPermissionInformationReq req) {
        if ( req == null ) {
            return null;
        }

        CurrentPersonPermissionInformationQuery currentPersonPermissionInformationQuery = new CurrentPersonPermissionInformationQuery();

        currentPersonPermissionInformationQuery.setPermissionType( req.getPermissionType() );
        currentPersonPermissionInformationQuery.setProductCode( req.getProductCode() );

        return currentPersonPermissionInformationQuery;
    }

    @Override
    public TmTestPlanVO convertor(AddCommonTestPlanReq req) {
        if ( req == null ) {
            return null;
        }

        TmTestPlanVO tmTestPlanVO = new TmTestPlanVO();

        tmTestPlanVO.setCode( req.getPlanCode() );
        tmTestPlanVO.setPlanName( req.getPlanName() );
        tmTestPlanVO.setTestStrategy( req.getTestStrategy() );
        tmTestPlanVO.setStatus( req.getStatus() );
        tmTestPlanVO.setProductCode( req.getProductCode() );
        tmTestPlanVO.setProductName( req.getProductName() );
        tmTestPlanVO.setVersionCode( req.getVersionCode() );
        tmTestPlanVO.setVersionName( req.getVersionName() );
        tmTestPlanVO.setStartDate( req.getStartDate() );
        tmTestPlanVO.setPublishDate( req.getPublishDate() );
        tmTestPlanVO.setAccessDate( req.getAccessDate() );
        tmTestPlanVO.setAccessDatePartition( req.getAccessDatePartition() );
        tmTestPlanVO.setPermitDate( req.getPermitDate() );
        tmTestPlanVO.setPermitDatePartition( req.getPermitDatePartition() );
        tmTestPlanVO.setDeveloperNum( req.getDeveloperNum() );
        tmTestPlanVO.setTesterNum( req.getTesterNum() );
        tmTestPlanVO.setComment( req.getComment() );
        tmTestPlanVO.setProductDirectorId( req.getProductDirectorId() );
        tmTestPlanVO.setProductDirectorName( req.getProductDirectorName() );
        tmTestPlanVO.setTestDirectorId( req.getTestDirectorId() );
        tmTestPlanVO.setTestDirectorName( req.getTestDirectorName() );
        tmTestPlanVO.setPlanDirectorId( req.getPlanDirectorId() );
        tmTestPlanVO.setPlanDirectorName( req.getPlanDirectorName() );
        tmTestPlanVO.setMobileSpecialTest( req.getMobileSpecialTest() );
        tmTestPlanVO.setSecurityScan( req.getSecurityScan() );
        tmTestPlanVO.setStaticAnalysis( req.getStaticAnalysis() );
        tmTestPlanVO.setStaticAnalysisTime( req.getStaticAnalysisTime() );
        tmTestPlanVO.setStaticAnalysisDirectorId( req.getStaticAnalysisDirectorId() );
        tmTestPlanVO.setStaticAnalysisDirectorName( req.getStaticAnalysisDirectorName() );
        tmTestPlanVO.setPerformanceTest( req.getPerformanceTest() );
        tmTestPlanVO.setPerformanceTestTime( req.getPerformanceTestTime() );
        tmTestPlanVO.setPerformanceTestDirectorId( req.getPerformanceTestDirectorId() );
        tmTestPlanVO.setPerformanceTestDirectorName( req.getPerformanceTestDirectorName() );
        tmTestPlanVO.setExploratoryTest( req.getExploratoryTest() );
        tmTestPlanVO.setExploratoryTestDirectorId( req.getExploratoryTestDirectorId() );
        tmTestPlanVO.setExploratoryTestDirectorName( req.getExploratoryTestDirectorName() );
        tmTestPlanVO.setExploratoryTestTime( req.getExploratoryTestTime() );
        List<TestFunctionPointVO> list = req.getPointList();
        if ( list != null ) {
            tmTestPlanVO.setPointList( new ArrayList<TestFunctionPointVO>( list ) );
        }

        return tmTestPlanVO;
    }

    @Override
    public TmTestPlanVO convertor(AddSafeTestPlanReq req) {
        if ( req == null ) {
            return null;
        }

        TmTestPlanVO tmTestPlanVO = new TmTestPlanVO();

        tmTestPlanVO.setCode( req.getPlanCode() );
        tmTestPlanVO.setPlanName( req.getPlanName() );
        tmTestPlanVO.setRelationPlanCode( req.getRelationPlanCode() );
        tmTestPlanVO.setRelationPlanName( req.getRelationPlanName() );
        tmTestPlanVO.setDeptId( req.getDeptId() );
        tmTestPlanVO.setDeptName( req.getDeptName() );
        tmTestPlanVO.setProductCode( req.getProductCode() );
        tmTestPlanVO.setProductName( req.getProductName() );
        tmTestPlanVO.setVersionCode( req.getVersionCode() );
        tmTestPlanVO.setVersionName( req.getVersionName() );
        tmTestPlanVO.setProductDirectorId( req.getProductDirectorId() );
        tmTestPlanVO.setProductDirectorName( req.getProductDirectorName() );
        tmTestPlanVO.setPermissionsTest( req.getPermissionsTest() );
        tmTestPlanVO.setPriority( req.getPriority() );
        tmTestPlanVO.setLastTestDate( req.getLastTestDate() );
        tmTestPlanVO.setTestInformation( req.getTestInformation() );
        tmTestPlanVO.setPermissionsTestInformation( req.getPermissionsTestInformation() );

        return tmTestPlanVO;
    }

    @Override
    public VerifyTestPassConditionQuery convertor(VerifyTestPassConditionReq req) {
        if ( req == null ) {
            return null;
        }

        VerifyTestPassConditionQuery verifyTestPassConditionQuery = new VerifyTestPassConditionQuery();

        verifyTestPassConditionQuery.setVersionCode( req.getVersionCode() );
        verifyTestPassConditionQuery.setEvent( req.getEvent() );

        return verifyTestPassConditionQuery;
    }

    @Override
    public List<PagePlanPhaseResp> convertorPlaseList(List<PlanPhaseVO> list) {
        if ( list == null ) {
            return null;
        }

        List<PagePlanPhaseResp> list1 = new ArrayList<PagePlanPhaseResp>( list.size() );
        for ( PlanPhaseVO planPhaseVO : list ) {
            list1.add( planPhaseVOToPagePlanPhaseResp( planPhaseVO ) );
        }

        return list1;
    }

    protected PagePlanTestcaseResp testPlanCaseVOToPagePlanTestcaseResp(TestPlanCaseVO testPlanCaseVO) {
        if ( testPlanCaseVO == null ) {
            return null;
        }

        PagePlanTestcaseResp pagePlanTestcaseResp = new PagePlanTestcaseResp();

        pagePlanTestcaseResp.setId( testPlanCaseVO.getId() );
        pagePlanTestcaseResp.setPlanCode( testPlanCaseVO.getPlanCode() );
        pagePlanTestcaseResp.setCaseCode( testPlanCaseVO.getCaseCode() );
        pagePlanTestcaseResp.setParentCode( testPlanCaseVO.getParentCode() );
        pagePlanTestcaseResp.setParentName( testPlanCaseVO.getParentName() );
        pagePlanTestcaseResp.setCaseName( testPlanCaseVO.getCaseName() );
        pagePlanTestcaseResp.setCaseAttribute( testPlanCaseVO.getCaseAttribute() );
        pagePlanTestcaseResp.setNodeType( testPlanCaseVO.getNodeType() );
        pagePlanTestcaseResp.setCasePath( testPlanCaseVO.getCasePath() );
        pagePlanTestcaseResp.setTestStage( testPlanCaseVO.getTestStage() );
        pagePlanTestcaseResp.setTestStageDesc( testPlanCaseVO.getTestStageDesc() );
        pagePlanTestcaseResp.setType( testPlanCaseVO.getType() );
        pagePlanTestcaseResp.setTypeDesc( testPlanCaseVO.getTypeDesc() );
        pagePlanTestcaseResp.setStatus( testPlanCaseVO.getStatus() );
        pagePlanTestcaseResp.setStatusDesc( testPlanCaseVO.getStatusDesc() );
        pagePlanTestcaseResp.setTestcaseStatus( testPlanCaseVO.getTestcaseStatus() );
        pagePlanTestcaseResp.setTestcaseStatusDesc( testPlanCaseVO.getTestcaseStatusDesc() );
        pagePlanTestcaseResp.setPriority( testPlanCaseVO.getPriority() );
        pagePlanTestcaseResp.setPriorityDesc( testPlanCaseVO.getPriorityDesc() );
        pagePlanTestcaseResp.setExecutorId( testPlanCaseVO.getExecutorId() );
        pagePlanTestcaseResp.setExecutor( testPlanCaseVO.getExecutor() );
        pagePlanTestcaseResp.setOperateCaseCode( testPlanCaseVO.getOperateCaseCode() );
        pagePlanTestcaseResp.setDutyUserId( testPlanCaseVO.getDutyUserId() );
        pagePlanTestcaseResp.setDutyUser( testPlanCaseVO.getDutyUser() );
        pagePlanTestcaseResp.setResultPath( testPlanCaseVO.getResultPath() );
        pagePlanTestcaseResp.setLogPath( testPlanCaseVO.getLogPath() );
        pagePlanTestcaseResp.setIssueCount( testPlanCaseVO.getIssueCount() );
        pagePlanTestcaseResp.setNotFixIssueCount( testPlanCaseVO.getNotFixIssueCount() );
        pagePlanTestcaseResp.setExecuteNum( testPlanCaseVO.getExecuteNum() );
        pagePlanTestcaseResp.setCreatorId( testPlanCaseVO.getCreatorId() );
        pagePlanTestcaseResp.setCreator( testPlanCaseVO.getCreator() );
        pagePlanTestcaseResp.setGmtCreate( testPlanCaseVO.getGmtCreate() );
        pagePlanTestcaseResp.setModifierId( testPlanCaseVO.getModifierId() );
        pagePlanTestcaseResp.setModifier( testPlanCaseVO.getModifier() );
        pagePlanTestcaseResp.setGmtModified( testPlanCaseVO.getGmtModified() );
        pagePlanTestcaseResp.setAutomaticRecordType( testPlanCaseVO.getAutomaticRecordType() );
        pagePlanTestcaseResp.setResultComment( testPlanCaseVO.getResultComment() );
        pagePlanTestcaseResp.setChildrenNum( testPlanCaseVO.getChildrenNum() );
        pagePlanTestcaseResp.setEnable( testPlanCaseVO.getEnable() );
        pagePlanTestcaseResp.setInterfaceName( testPlanCaseVO.getInterfaceName() );
        pagePlanTestcaseResp.setEnableChecked( testPlanCaseVO.getEnableChecked() );

        return pagePlanTestcaseResp;
    }

    protected TestPlanResp tmTestPlanVOToTestPlanResp(TmTestPlanVO tmTestPlanVO) {
        if ( tmTestPlanVO == null ) {
            return null;
        }

        TestPlanResp testPlanResp = new TestPlanResp();

        testPlanResp.setCode( tmTestPlanVO.getCode() );
        testPlanResp.setPlanName( tmTestPlanVO.getPlanName() );
        testPlanResp.setProductCode( tmTestPlanVO.getProductCode() );
        testPlanResp.setVersionCode( tmTestPlanVO.getVersionCode() );
        testPlanResp.setType( tmTestPlanVO.getType() );
        testPlanResp.setStatus( tmTestPlanVO.getStatus() );
        testPlanResp.setStatusDesc( tmTestPlanVO.getStatusDesc() );
        testPlanResp.setTypeDesc( tmTestPlanVO.getTypeDesc() );
        testPlanResp.setTestDirectorId( tmTestPlanVO.getTestDirectorId() );
        testPlanResp.setTestDirectorName( tmTestPlanVO.getTestDirectorName() );
        testPlanResp.setPlanDirectorId( tmTestPlanVO.getPlanDirectorId() );
        testPlanResp.setPlanDirectorName( tmTestPlanVO.getPlanDirectorName() );
        testPlanResp.setGmtCreate( tmTestPlanVO.getGmtCreate() );
        testPlanResp.setGmtModified( tmTestPlanVO.getGmtModified() );

        return testPlanResp;
    }

    protected PagePlanPhaseResp planPhaseVOToPagePlanPhaseResp(PlanPhaseVO planPhaseVO) {
        if ( planPhaseVO == null ) {
            return null;
        }

        PagePlanPhaseResp pagePlanPhaseResp = new PagePlanPhaseResp();

        pagePlanPhaseResp.setPlanCode( planPhaseVO.getPlanCode() );
        pagePlanPhaseResp.setPlanName( planPhaseVO.getPlanName() );
        pagePlanPhaseResp.setType( planPhaseVO.getType() );
        pagePlanPhaseResp.setTypeDesc( planPhaseVO.getTypeDesc() );
        pagePlanPhaseResp.setTestStrategy( planPhaseVO.getTestStrategy() );
        pagePlanPhaseResp.setTestStrategyDesc( planPhaseVO.getTestStrategyDesc() );
        pagePlanPhaseResp.setStatus( planPhaseVO.getStatus() );
        pagePlanPhaseResp.setStatusDesc( planPhaseVO.getStatusDesc() );
        List<TestPlanStageVO> list = planPhaseVO.getTestPlanStageVOList();
        if ( list != null ) {
            pagePlanPhaseResp.setTestPlanStageVOList( new ArrayList<TestPlanStageVO>( list ) );
        }

        return pagePlanPhaseResp;
    }
}
