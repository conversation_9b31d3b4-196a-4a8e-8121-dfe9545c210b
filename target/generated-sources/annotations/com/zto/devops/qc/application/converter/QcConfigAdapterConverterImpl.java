package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.testmanager.config.entity.QcConfigVO;
import com.zto.devops.qc.client.service.testmanager.config.model.QcConfigResp;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class QcConfigAdapterConverterImpl implements QcConfigAdapterConverter {

    @Override
    public QcConfigResp converter(QcConfigVO configVO) {
        if ( configVO == null ) {
            return null;
        }

        QcConfigResp qcConfigResp = new QcConfigResp();

        qcConfigResp.setUploadFileNum( configVO.getUploadFileNum() );
        qcConfigResp.setUploadFileSize( configVO.getUploadFileSize() );

        return qcConfigResp;
    }
}
