package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.enums.issue.IssueApplicationType;
import com.zto.devops.qc.client.enums.issue.IssueFindEnv;
import com.zto.devops.qc.client.enums.issue.IssueFindStage;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueRepetitionRate;
import com.zto.devops.qc.client.enums.issue.IssueRootCause;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.IssueTestMethod;
import com.zto.devops.qc.client.enums.issue.IssueType;
import com.zto.devops.qc.client.enums.issue.LaneStatusEnum;
import com.zto.devops.qc.client.enums.issue.RefuseReason;
import com.zto.devops.qc.client.enums.issue.RelatedToMeEnum;
import com.zto.devops.qc.client.model.issue.query.ExpIssueQuery;
import com.zto.devops.qc.client.model.issue.query.PageIssueQuery;
import com.zto.devops.qc.client.model.issue.query.PageIssueThingQuery;
import com.zto.devops.qc.client.model.issue.query.PageLaneIssueQuery;
import com.zto.devops.qc.client.service.issue.model.ExportIssueReq;
import com.zto.devops.qc.client.service.issue.model.PageIssueReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class PageIssueQueryConverterImpl implements PageIssueQueryConverter {

    @Override
    public PageIssueQuery convert(PageIssueReq pageIssueReq) {
        if ( pageIssueReq == null ) {
            return null;
        }

        PageIssueQuery pageIssueQuery = new PageIssueQuery();

        pageIssueQuery.setTransactor( pageIssueReq.getTransactor() );
        pageIssueQuery.setPage( pageIssueReq.getPage() );
        pageIssueQuery.setSize( pageIssueReq.getSize() );
        pageIssueQuery.setCodeOrTitle( pageIssueReq.getCodeOrTitle() );
        pageIssueQuery.setCurrentUserId( pageIssueReq.getCurrentUserId() );
        List<IssueStatus> list = pageIssueReq.getStatusList();
        if ( list != null ) {
            pageIssueQuery.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        List<IssuePriority> list1 = pageIssueReq.getPriorityList();
        if ( list1 != null ) {
            pageIssueQuery.setPriorityList( new ArrayList<IssuePriority>( list1 ) );
        }
        List<RelatedToMeEnum> list2 = pageIssueReq.getRelatedList();
        if ( list2 != null ) {
            pageIssueQuery.setRelatedList( new ArrayList<RelatedToMeEnum>( list2 ) );
        }
        List<Long> list3 = pageIssueReq.getHandleUserIdList();
        if ( list3 != null ) {
            pageIssueQuery.setHandleUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = pageIssueReq.getDevelopUserIdList();
        if ( list4 != null ) {
            pageIssueQuery.setDevelopUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = pageIssueReq.getTestUserIdList();
        if ( list5 != null ) {
            pageIssueQuery.setTestUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<String> list6 = pageIssueReq.getFindVersionList();
        if ( list6 != null ) {
            pageIssueQuery.setFindVersionList( new ArrayList<String>( list6 ) );
        }
        List<String> list7 = pageIssueReq.getFixVersionList();
        if ( list7 != null ) {
            pageIssueQuery.setFixVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = pageIssueReq.getRelatedRequireList();
        if ( list8 != null ) {
            pageIssueQuery.setRelatedRequireList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = pageIssueReq.getRelatedProductList();
        if ( list9 != null ) {
            pageIssueQuery.setRelatedProductList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = pageIssueReq.getSprintCode();
        if ( list10 != null ) {
            pageIssueQuery.setSprintCode( new ArrayList<String>( list10 ) );
        }
        List<IssueRootCause> list11 = pageIssueReq.getRootCauseList();
        if ( list11 != null ) {
            pageIssueQuery.setRootCauseList( new ArrayList<IssueRootCause>( list11 ) );
        }
        List<IssueType> list12 = pageIssueReq.getIssueTypeList();
        if ( list12 != null ) {
            pageIssueQuery.setIssueTypeList( new ArrayList<IssueType>( list12 ) );
        }
        List<IssueTestMethod> list13 = pageIssueReq.getTestMethodList();
        if ( list13 != null ) {
            pageIssueQuery.setTestMethodList( new ArrayList<IssueTestMethod>( list13 ) );
        }
        List<IssueRepetitionRate> list14 = pageIssueReq.getRepetitionRateList();
        if ( list14 != null ) {
            pageIssueQuery.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list14 ) );
        }
        List<IssueFindStage> list15 = pageIssueReq.getFindStageList();
        if ( list15 != null ) {
            pageIssueQuery.setFindStageList( new ArrayList<IssueFindStage>( list15 ) );
        }
        List<IssueFindEnv> list16 = pageIssueReq.getFindEnvList();
        if ( list16 != null ) {
            pageIssueQuery.setFindEnvList( new ArrayList<IssueFindEnv>( list16 ) );
        }
        List<Long> list17 = pageIssueReq.getFindUserIdList();
        if ( list17 != null ) {
            pageIssueQuery.setFindUserIdList( new ArrayList<Long>( list17 ) );
        }
        List<String> list18 = pageIssueReq.getTagName();
        if ( list18 != null ) {
            pageIssueQuery.setTagName( new ArrayList<String>( list18 ) );
        }
        pageIssueQuery.setCreateTimeStart( pageIssueReq.getCreateTimeStart() );
        pageIssueQuery.setCreateTimeEnd( pageIssueReq.getCreateTimeEnd() );
        pageIssueQuery.setCloseTimeStart( pageIssueReq.getCloseTimeStart() );
        pageIssueQuery.setCloseTimeEnd( pageIssueReq.getCloseTimeEnd() );
        List<Boolean> list19 = pageIssueReq.getExamination();
        if ( list19 != null ) {
            pageIssueQuery.setExamination( new ArrayList<Boolean>( list19 ) );
        }
        List<Boolean> list20 = pageIssueReq.getTestOmission();
        if ( list20 != null ) {
            pageIssueQuery.setTestOmission( new ArrayList<Boolean>( list20 ) );
        }
        List<Boolean> list21 = pageIssueReq.getCodeDefect();
        if ( list21 != null ) {
            pageIssueQuery.setCodeDefect( new ArrayList<Boolean>( list21 ) );
        }
        List<IssueApplicationType> list22 = pageIssueReq.getApplicationTypeList();
        if ( list22 != null ) {
            pageIssueQuery.setApplicationTypeList( new ArrayList<IssueApplicationType>( list22 ) );
        }
        pageIssueQuery.setVersionType( pageIssueReq.getVersionType() );
        List<RefuseReason> list23 = pageIssueReq.getRefuseReasonList();
        if ( list23 != null ) {
            pageIssueQuery.setRefuseReasonList( new ArrayList<RefuseReason>( list23 ) );
        }
        if ( pageIssueReq.getOrderType() != null ) {
            pageIssueQuery.setOrderType( pageIssueReq.getOrderType().name() );
        }
        List<Long> list24 = pageIssueReq.getCcUserIdList();
        if ( list24 != null ) {
            pageIssueQuery.setCcUserIdList( new ArrayList<Long>( list24 ) );
        }
        pageIssueQuery.setGroupId( pageIssueReq.getGroupId() );
        pageIssueQuery.setValidFlag( pageIssueReq.getValidFlag() );

        pageIssueQuery.setOrderField( com.zto.devops.qc.client.enums.issue.IssueFieldEnum.getNameByCode(pageIssueReq.getOrderField()) );

        return pageIssueQuery;
    }

    @Override
    public ExpIssueQuery convertExp(ExportIssueReq pageIssueReq) {
        if ( pageIssueReq == null ) {
            return null;
        }

        ExpIssueQuery expIssueQuery = new ExpIssueQuery();

        expIssueQuery.setTransactor( pageIssueReq.getTransactor() );
        List<String> list = pageIssueReq.getExcludeFields();
        if ( list != null ) {
            expIssueQuery.setExcludeFields( new ArrayList<String>( list ) );
        }
        expIssueQuery.setCodeOrTitle( pageIssueReq.getCodeOrTitle() );
        expIssueQuery.setCurrentUserId( pageIssueReq.getCurrentUserId() );
        List<IssueStatus> list1 = pageIssueReq.getStatusList();
        if ( list1 != null ) {
            expIssueQuery.setStatusList( new ArrayList<IssueStatus>( list1 ) );
        }
        List<IssuePriority> list2 = pageIssueReq.getPriorityList();
        if ( list2 != null ) {
            expIssueQuery.setPriorityList( new ArrayList<IssuePriority>( list2 ) );
        }
        List<RelatedToMeEnum> list3 = pageIssueReq.getRelatedList();
        if ( list3 != null ) {
            expIssueQuery.setRelatedList( new ArrayList<RelatedToMeEnum>( list3 ) );
        }
        List<Long> list4 = pageIssueReq.getHandleUserIdList();
        if ( list4 != null ) {
            expIssueQuery.setHandleUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = pageIssueReq.getDevelopUserIdList();
        if ( list5 != null ) {
            expIssueQuery.setDevelopUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<Long> list6 = pageIssueReq.getTestUserIdList();
        if ( list6 != null ) {
            expIssueQuery.setTestUserIdList( new ArrayList<Long>( list6 ) );
        }
        List<String> list7 = pageIssueReq.getFindVersionList();
        if ( list7 != null ) {
            expIssueQuery.setFindVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = pageIssueReq.getFixVersionList();
        if ( list8 != null ) {
            expIssueQuery.setFixVersionList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = pageIssueReq.getRelatedRequireList();
        if ( list9 != null ) {
            expIssueQuery.setRelatedRequireList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = pageIssueReq.getRelatedProductList();
        if ( list10 != null ) {
            expIssueQuery.setRelatedProductList( new ArrayList<String>( list10 ) );
        }
        List<String> list11 = pageIssueReq.getSprintCode();
        if ( list11 != null ) {
            expIssueQuery.setSprintCode( new ArrayList<String>( list11 ) );
        }
        List<IssueRootCause> list12 = pageIssueReq.getRootCauseList();
        if ( list12 != null ) {
            expIssueQuery.setRootCauseList( new ArrayList<IssueRootCause>( list12 ) );
        }
        List<IssueType> list13 = pageIssueReq.getIssueTypeList();
        if ( list13 != null ) {
            expIssueQuery.setIssueTypeList( new ArrayList<IssueType>( list13 ) );
        }
        List<IssueTestMethod> list14 = pageIssueReq.getTestMethodList();
        if ( list14 != null ) {
            expIssueQuery.setTestMethodList( new ArrayList<IssueTestMethod>( list14 ) );
        }
        List<IssueRepetitionRate> list15 = pageIssueReq.getRepetitionRateList();
        if ( list15 != null ) {
            expIssueQuery.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list15 ) );
        }
        List<IssueFindStage> list16 = pageIssueReq.getFindStageList();
        if ( list16 != null ) {
            expIssueQuery.setFindStageList( new ArrayList<IssueFindStage>( list16 ) );
        }
        List<IssueFindEnv> list17 = pageIssueReq.getFindEnvList();
        if ( list17 != null ) {
            expIssueQuery.setFindEnvList( new ArrayList<IssueFindEnv>( list17 ) );
        }
        List<Long> list18 = pageIssueReq.getFindUserIdList();
        if ( list18 != null ) {
            expIssueQuery.setFindUserIdList( new ArrayList<Long>( list18 ) );
        }
        List<String> list19 = pageIssueReq.getTagName();
        if ( list19 != null ) {
            expIssueQuery.setTagName( new ArrayList<String>( list19 ) );
        }
        expIssueQuery.setCreateTimeStart( pageIssueReq.getCreateTimeStart() );
        expIssueQuery.setCreateTimeEnd( pageIssueReq.getCreateTimeEnd() );
        expIssueQuery.setCloseTimeStart( pageIssueReq.getCloseTimeStart() );
        expIssueQuery.setCloseTimeEnd( pageIssueReq.getCloseTimeEnd() );
        List<Boolean> list20 = pageIssueReq.getExamination();
        if ( list20 != null ) {
            expIssueQuery.setExamination( new ArrayList<Boolean>( list20 ) );
        }
        List<Boolean> list21 = pageIssueReq.getTestOmission();
        if ( list21 != null ) {
            expIssueQuery.setTestOmission( new ArrayList<Boolean>( list21 ) );
        }
        List<Boolean> list22 = pageIssueReq.getCodeDefect();
        if ( list22 != null ) {
            expIssueQuery.setCodeDefect( new ArrayList<Boolean>( list22 ) );
        }
        List<IssueApplicationType> list23 = pageIssueReq.getApplicationTypeList();
        if ( list23 != null ) {
            expIssueQuery.setApplicationTypeList( new ArrayList<IssueApplicationType>( list23 ) );
        }
        expIssueQuery.setVersionType( pageIssueReq.getVersionType() );
        List<RefuseReason> list24 = pageIssueReq.getRefuseReasonList();
        if ( list24 != null ) {
            expIssueQuery.setRefuseReasonList( new ArrayList<RefuseReason>( list24 ) );
        }
        if ( pageIssueReq.getOrderType() != null ) {
            expIssueQuery.setOrderType( pageIssueReq.getOrderType().name() );
        }
        List<Long> list25 = pageIssueReq.getCcUserIdList();
        if ( list25 != null ) {
            expIssueQuery.setCcUserIdList( new ArrayList<Long>( list25 ) );
        }
        expIssueQuery.setGroupId( pageIssueReq.getGroupId() );
        expIssueQuery.setValidFlag( pageIssueReq.getValidFlag() );
        expIssueQuery.setPage( pageIssueReq.getPage() );
        expIssueQuery.setSize( pageIssueReq.getSize() );

        expIssueQuery.setOrderField( com.zto.devops.qc.client.enums.issue.IssueFieldEnum.getNameByCode(pageIssueReq.getOrderField()) );

        return expIssueQuery;
    }

    @Override
    public PageLaneIssueQuery convertLaneQuery(PageIssueReq pageIssueReq) {
        if ( pageIssueReq == null ) {
            return null;
        }

        PageLaneIssueQuery pageLaneIssueQuery = new PageLaneIssueQuery();

        pageLaneIssueQuery.setTransactor( pageIssueReq.getTransactor() );
        pageLaneIssueQuery.setPage( pageIssueReq.getPage() );
        pageLaneIssueQuery.setSize( pageIssueReq.getSize() );
        pageLaneIssueQuery.setCodeOrTitle( pageIssueReq.getCodeOrTitle() );
        pageLaneIssueQuery.setCurrentUserId( pageIssueReq.getCurrentUserId() );
        List<IssueStatus> list = pageIssueReq.getStatusList();
        if ( list != null ) {
            pageLaneIssueQuery.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        List<IssuePriority> list1 = pageIssueReq.getPriorityList();
        if ( list1 != null ) {
            pageLaneIssueQuery.setPriorityList( new ArrayList<IssuePriority>( list1 ) );
        }
        List<RelatedToMeEnum> list2 = pageIssueReq.getRelatedList();
        if ( list2 != null ) {
            pageLaneIssueQuery.setRelatedList( new ArrayList<RelatedToMeEnum>( list2 ) );
        }
        List<Long> list3 = pageIssueReq.getHandleUserIdList();
        if ( list3 != null ) {
            pageLaneIssueQuery.setHandleUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = pageIssueReq.getDevelopUserIdList();
        if ( list4 != null ) {
            pageLaneIssueQuery.setDevelopUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = pageIssueReq.getTestUserIdList();
        if ( list5 != null ) {
            pageLaneIssueQuery.setTestUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<String> list6 = pageIssueReq.getFindVersionList();
        if ( list6 != null ) {
            pageLaneIssueQuery.setFindVersionList( new ArrayList<String>( list6 ) );
        }
        List<String> list7 = pageIssueReq.getFixVersionList();
        if ( list7 != null ) {
            pageLaneIssueQuery.setFixVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = pageIssueReq.getRelatedRequireList();
        if ( list8 != null ) {
            pageLaneIssueQuery.setRelatedRequireList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = pageIssueReq.getRelatedProductList();
        if ( list9 != null ) {
            pageLaneIssueQuery.setRelatedProductList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = pageIssueReq.getSprintCode();
        if ( list10 != null ) {
            pageLaneIssueQuery.setSprintCode( new ArrayList<String>( list10 ) );
        }
        List<IssueRootCause> list11 = pageIssueReq.getRootCauseList();
        if ( list11 != null ) {
            pageLaneIssueQuery.setRootCauseList( new ArrayList<IssueRootCause>( list11 ) );
        }
        List<IssueType> list12 = pageIssueReq.getIssueTypeList();
        if ( list12 != null ) {
            pageLaneIssueQuery.setIssueTypeList( new ArrayList<IssueType>( list12 ) );
        }
        List<IssueTestMethod> list13 = pageIssueReq.getTestMethodList();
        if ( list13 != null ) {
            pageLaneIssueQuery.setTestMethodList( new ArrayList<IssueTestMethod>( list13 ) );
        }
        List<IssueRepetitionRate> list14 = pageIssueReq.getRepetitionRateList();
        if ( list14 != null ) {
            pageLaneIssueQuery.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list14 ) );
        }
        List<IssueFindStage> list15 = pageIssueReq.getFindStageList();
        if ( list15 != null ) {
            pageLaneIssueQuery.setFindStageList( new ArrayList<IssueFindStage>( list15 ) );
        }
        List<IssueFindEnv> list16 = pageIssueReq.getFindEnvList();
        if ( list16 != null ) {
            pageLaneIssueQuery.setFindEnvList( new ArrayList<IssueFindEnv>( list16 ) );
        }
        List<Long> list17 = pageIssueReq.getFindUserIdList();
        if ( list17 != null ) {
            pageLaneIssueQuery.setFindUserIdList( new ArrayList<Long>( list17 ) );
        }
        List<String> list18 = pageIssueReq.getTagName();
        if ( list18 != null ) {
            pageLaneIssueQuery.setTagName( new ArrayList<String>( list18 ) );
        }
        pageLaneIssueQuery.setCreateTimeStart( pageIssueReq.getCreateTimeStart() );
        pageLaneIssueQuery.setCreateTimeEnd( pageIssueReq.getCreateTimeEnd() );
        pageLaneIssueQuery.setCloseTimeStart( pageIssueReq.getCloseTimeStart() );
        pageLaneIssueQuery.setCloseTimeEnd( pageIssueReq.getCloseTimeEnd() );
        List<Boolean> list19 = pageIssueReq.getExamination();
        if ( list19 != null ) {
            pageLaneIssueQuery.setExamination( new ArrayList<Boolean>( list19 ) );
        }
        List<Boolean> list20 = pageIssueReq.getTestOmission();
        if ( list20 != null ) {
            pageLaneIssueQuery.setTestOmission( new ArrayList<Boolean>( list20 ) );
        }
        List<Boolean> list21 = pageIssueReq.getCodeDefect();
        if ( list21 != null ) {
            pageLaneIssueQuery.setCodeDefect( new ArrayList<Boolean>( list21 ) );
        }
        List<IssueApplicationType> list22 = pageIssueReq.getApplicationTypeList();
        if ( list22 != null ) {
            pageLaneIssueQuery.setApplicationTypeList( new ArrayList<IssueApplicationType>( list22 ) );
        }
        pageLaneIssueQuery.setVersionType( pageIssueReq.getVersionType() );
        List<RefuseReason> list23 = pageIssueReq.getRefuseReasonList();
        if ( list23 != null ) {
            pageLaneIssueQuery.setRefuseReasonList( new ArrayList<RefuseReason>( list23 ) );
        }
        if ( pageIssueReq.getOrderType() != null ) {
            pageLaneIssueQuery.setOrderType( pageIssueReq.getOrderType().name() );
        }
        List<Long> list24 = pageIssueReq.getCcUserIdList();
        if ( list24 != null ) {
            pageLaneIssueQuery.setCcUserIdList( new ArrayList<Long>( list24 ) );
        }
        pageLaneIssueQuery.setGroupId( pageIssueReq.getGroupId() );
        pageLaneIssueQuery.setValidFlag( pageIssueReq.getValidFlag() );
        List<LaneStatusEnum> list25 = pageIssueReq.getLane();
        if ( list25 != null ) {
            pageLaneIssueQuery.setLane( new ArrayList<LaneStatusEnum>( list25 ) );
        }

        pageLaneIssueQuery.setOrderField( com.zto.devops.qc.client.enums.issue.IssueFieldEnum.getNameByCode(pageIssueReq.getOrderField()) );

        return pageLaneIssueQuery;
    }

    @Override
    public PageIssueThingQuery convertThing(PageIssueReq pageIssueReq) {
        if ( pageIssueReq == null ) {
            return null;
        }

        PageIssueThingQuery pageIssueThingQuery = new PageIssueThingQuery();

        pageIssueThingQuery.setTransactor( pageIssueReq.getTransactor() );
        pageIssueThingQuery.setPage( pageIssueReq.getPage() );
        pageIssueThingQuery.setSize( pageIssueReq.getSize() );
        pageIssueThingQuery.setValidFlag( pageIssueReq.getValidFlag() );
        pageIssueThingQuery.setCodeOrTitle( pageIssueReq.getCodeOrTitle() );
        pageIssueThingQuery.setCurrentUserId( pageIssueReq.getCurrentUserId() );
        List<IssueStatus> list = pageIssueReq.getStatusList();
        if ( list != null ) {
            pageIssueThingQuery.setStatusList( new ArrayList<IssueStatus>( list ) );
        }
        List<IssuePriority> list1 = pageIssueReq.getPriorityList();
        if ( list1 != null ) {
            pageIssueThingQuery.setPriorityList( new ArrayList<IssuePriority>( list1 ) );
        }
        List<RelatedToMeEnum> list2 = pageIssueReq.getRelatedList();
        if ( list2 != null ) {
            pageIssueThingQuery.setRelatedList( new ArrayList<RelatedToMeEnum>( list2 ) );
        }
        List<Long> list3 = pageIssueReq.getHandleUserIdList();
        if ( list3 != null ) {
            pageIssueThingQuery.setHandleUserIdList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = pageIssueReq.getDevelopUserIdList();
        if ( list4 != null ) {
            pageIssueThingQuery.setDevelopUserIdList( new ArrayList<Long>( list4 ) );
        }
        List<Long> list5 = pageIssueReq.getTestUserIdList();
        if ( list5 != null ) {
            pageIssueThingQuery.setTestUserIdList( new ArrayList<Long>( list5 ) );
        }
        List<String> list6 = pageIssueReq.getFindVersionList();
        if ( list6 != null ) {
            pageIssueThingQuery.setFindVersionList( new ArrayList<String>( list6 ) );
        }
        List<String> list7 = pageIssueReq.getFixVersionList();
        if ( list7 != null ) {
            pageIssueThingQuery.setFixVersionList( new ArrayList<String>( list7 ) );
        }
        List<String> list8 = pageIssueReq.getRelatedRequireList();
        if ( list8 != null ) {
            pageIssueThingQuery.setRelatedRequireList( new ArrayList<String>( list8 ) );
        }
        List<String> list9 = pageIssueReq.getRelatedProductList();
        if ( list9 != null ) {
            pageIssueThingQuery.setRelatedProductList( new ArrayList<String>( list9 ) );
        }
        List<String> list10 = pageIssueReq.getSprintCode();
        if ( list10 != null ) {
            pageIssueThingQuery.setSprintCode( new ArrayList<String>( list10 ) );
        }
        List<IssueRootCause> list11 = pageIssueReq.getRootCauseList();
        if ( list11 != null ) {
            pageIssueThingQuery.setRootCauseList( new ArrayList<IssueRootCause>( list11 ) );
        }
        List<IssueType> list12 = pageIssueReq.getIssueTypeList();
        if ( list12 != null ) {
            pageIssueThingQuery.setIssueTypeList( new ArrayList<IssueType>( list12 ) );
        }
        List<IssueTestMethod> list13 = pageIssueReq.getTestMethodList();
        if ( list13 != null ) {
            pageIssueThingQuery.setTestMethodList( new ArrayList<IssueTestMethod>( list13 ) );
        }
        List<IssueRepetitionRate> list14 = pageIssueReq.getRepetitionRateList();
        if ( list14 != null ) {
            pageIssueThingQuery.setRepetitionRateList( new ArrayList<IssueRepetitionRate>( list14 ) );
        }
        List<IssueFindStage> list15 = pageIssueReq.getFindStageList();
        if ( list15 != null ) {
            pageIssueThingQuery.setFindStageList( new ArrayList<IssueFindStage>( list15 ) );
        }
        List<IssueFindEnv> list16 = pageIssueReq.getFindEnvList();
        if ( list16 != null ) {
            pageIssueThingQuery.setFindEnvList( new ArrayList<IssueFindEnv>( list16 ) );
        }
        List<Long> list17 = pageIssueReq.getFindUserIdList();
        if ( list17 != null ) {
            pageIssueThingQuery.setFindUserIdList( new ArrayList<Long>( list17 ) );
        }
        List<String> list18 = pageIssueReq.getTagName();
        if ( list18 != null ) {
            pageIssueThingQuery.setTagName( new ArrayList<String>( list18 ) );
        }
        pageIssueThingQuery.setCreateTimeStart( pageIssueReq.getCreateTimeStart() );
        pageIssueThingQuery.setCreateTimeEnd( pageIssueReq.getCreateTimeEnd() );
        pageIssueThingQuery.setCloseTimeStart( pageIssueReq.getCloseTimeStart() );
        pageIssueThingQuery.setCloseTimeEnd( pageIssueReq.getCloseTimeEnd() );
        List<IssueApplicationType> list19 = pageIssueReq.getApplicationTypeList();
        if ( list19 != null ) {
            pageIssueThingQuery.setApplicationTypeList( new ArrayList<IssueApplicationType>( list19 ) );
        }
        List<RefuseReason> list20 = pageIssueReq.getRefuseReasonList();
        if ( list20 != null ) {
            pageIssueThingQuery.setRefuseReasonList( new ArrayList<RefuseReason>( list20 ) );
        }
        pageIssueThingQuery.setOrderField( pageIssueReq.getOrderField() );
        if ( pageIssueReq.getOrderType() != null ) {
            pageIssueThingQuery.setOrderType( pageIssueReq.getOrderType().name() );
        }
        List<Long> list21 = pageIssueReq.getCcUserIdList();
        if ( list21 != null ) {
            pageIssueThingQuery.setCcUserIdList( new ArrayList<Long>( list21 ) );
        }

        return pageIssueThingQuery;
    }
}
