package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.testmanager.cases.command.MoveAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.query.MoveModuleReq;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSourceRecordConverterImpl implements AutomaticSourceRecordConverter {

    @Override
    public MoveAutomaticRecordCommand convertor(MoveModuleReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getCode();

        MoveAutomaticRecordCommand moveAutomaticRecordCommand = new MoveAutomaticRecordCommand( aggregateId );

        moveAutomaticRecordCommand.setCode( req.getCode() );
        moveAutomaticRecordCommand.setName( req.getName() );
        moveAutomaticRecordCommand.setProductCode( req.getProductCode() );
        moveAutomaticRecordCommand.setParentCode( req.getParentCode() );
        moveAutomaticRecordCommand.setParentName( req.getParentName() );
        moveAutomaticRecordCommand.setLayer( req.getLayer() );
        moveAutomaticRecordCommand.setOldPath( req.getOldPath() );
        moveAutomaticRecordCommand.setNewPath( req.getNewPath() );
        moveAutomaticRecordCommand.setType( req.getType() );
        moveAutomaticRecordCommand.setAttribute( req.getAttribute() );

        return moveAutomaticRecordCommand;
    }
}
