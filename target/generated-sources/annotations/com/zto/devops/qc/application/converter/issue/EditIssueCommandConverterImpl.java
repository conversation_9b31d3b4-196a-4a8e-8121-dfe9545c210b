package com.zto.devops.qc.application.converter.issue;

import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.model.issue.command.EditIssueCommand;
import com.zto.devops.qc.client.service.issue.model.EditIssueByLaneReq;
import com.zto.devops.qc.client.service.issue.model.EditIssueReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class EditIssueCommandConverterImpl implements EditIssueCommandConverter {

    @Override
    public EditIssueCommand convert(EditIssueReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        EditIssueCommand editIssueCommand = new EditIssueCommand( aggregateId );

        editIssueCommand.setDeveloper( editIssueReqToUser( req ) );
        editIssueCommand.setTester( editIssueReqToUser1( req ) );
        editIssueCommand.setRequirement( editIssueReqToRequirement( req ) );
        editIssueCommand.setFindVersion( editIssueReqToVersion( req ) );
        editIssueCommand.setFixVersion( editIssueReqToVersion1( req ) );
        editIssueCommand.setSprint( editIssueReqToSprint( req ) );
        editIssueCommand.setTitle( req.getTitle() );
        editIssueCommand.setDescription( req.getDescription() );
        editIssueCommand.setFindEnv( req.getFindEnv() );
        editIssueCommand.setFindStage( req.getFindStage() );
        editIssueCommand.setPriority( req.getPriority() );
        editIssueCommand.setRepetitionRate( req.getRepetitionRate() );
        editIssueCommand.setRootCause( req.getRootCause() );
        editIssueCommand.setTestMethod( req.getTestMethod() );
        editIssueCommand.setType( req.getType() );
        editIssueCommand.setRequirementLevel( req.getRequirementLevel() );
        editIssueCommand.setApplicationType( req.getApplicationType() );
        List<String> list = req.getTestcaseCodes();
        if ( list != null ) {
            editIssueCommand.setTestcaseCodes( new ArrayList<String>( list ) );
        }
        editIssueCommand.setPlanStartDate( req.getPlanStartDate() );
        editIssueCommand.setPlanEndDate( req.getPlanEndDate() );

        return editIssueCommand;
    }

    @Override
    public EditIssueCommand convert(EditIssueByLaneReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        EditIssueCommand editIssueCommand = new EditIssueCommand( aggregateId );

        editIssueCommand.setDeveloper( editIssueByLaneReqToUser( req ) );
        editIssueCommand.setTester( editIssueByLaneReqToUser1( req ) );
        editIssueCommand.setTitle( req.getTitle() );
        editIssueCommand.setPriority( req.getPriority() );
        editIssueCommand.setPlanStartDate( req.getPlanStartDate() );
        editIssueCommand.setPlanEndDate( req.getPlanEndDate() );

        return editIssueCommand;
    }

    protected User editIssueReqToUser(EditIssueReq editIssueReq) {
        if ( editIssueReq == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( editIssueReq.getDevelopUserId() );
        user.setUserName( editIssueReq.getDevelopUserName() );

        return user;
    }

    protected User editIssueReqToUser1(EditIssueReq editIssueReq) {
        if ( editIssueReq == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( editIssueReq.getTestUserId() );
        user.setUserName( editIssueReq.getTestUserName() );

        return user;
    }

    protected Requirement editIssueReqToRequirement(EditIssueReq editIssueReq) {
        if ( editIssueReq == null ) {
            return null;
        }

        Requirement requirement = new Requirement();

        requirement.setCode( editIssueReq.getRequirementCode() );
        requirement.setName( editIssueReq.getRequirementName() );

        return requirement;
    }

    protected Version editIssueReqToVersion(EditIssueReq editIssueReq) {
        if ( editIssueReq == null ) {
            return null;
        }

        Version version = new Version();

        version.setCode( editIssueReq.getFindVersionCode() );
        version.setName( editIssueReq.getFindVersionName() );

        return version;
    }

    protected Version editIssueReqToVersion1(EditIssueReq editIssueReq) {
        if ( editIssueReq == null ) {
            return null;
        }

        Version version = new Version();

        version.setCode( editIssueReq.getFixVersionCode() );
        version.setName( editIssueReq.getFixVersionName() );

        return version;
    }

    protected Sprint editIssueReqToSprint(EditIssueReq editIssueReq) {
        if ( editIssueReq == null ) {
            return null;
        }

        Sprint sprint = new Sprint();

        sprint.setCode( editIssueReq.getSprintCode() );
        sprint.setName( editIssueReq.getSprintName() );

        return sprint;
    }

    protected User editIssueByLaneReqToUser(EditIssueByLaneReq editIssueByLaneReq) {
        if ( editIssueByLaneReq == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( editIssueByLaneReq.getDevelopUserId() );
        user.setUserName( editIssueByLaneReq.getDevelopUserName() );

        return user;
    }

    protected User editIssueByLaneReqToUser1(EditIssueByLaneReq editIssueByLaneReq) {
        if ( editIssueByLaneReq == null ) {
            return null;
        }

        User user = new User();

        user.setUserId( editIssueByLaneReq.getTestUserId() );
        user.setUserName( editIssueByLaneReq.getTestUserName() );

        return user;
    }
}
