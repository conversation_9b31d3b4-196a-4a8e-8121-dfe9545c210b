package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.knowledgebase.command.RemoveKnowledgeBaseCommand;
import com.zto.devops.qc.client.model.knowledgebase.command.UpdateKnowledgeBaseCommand;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseVO;
import com.zto.devops.qc.client.service.knowledgebase.model.AddKnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.client.service.knowledgebase.model.KnowledgeBaseByProductCodeResp;
import com.zto.devops.qc.client.service.knowledgebase.model.RemoveKnowledgeBaseByProductCodeReq;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:16+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class KnowledgeBaseConverterImpl implements KnowledgeBaseConverter {

    @Override
    public KnowledgeBaseByProductCodeResp converter(KnowledgeBaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        KnowledgeBaseByProductCodeResp knowledgeBaseByProductCodeResp = new KnowledgeBaseByProductCodeResp();

        knowledgeBaseByProductCodeResp.setId( vo.getId() );
        knowledgeBaseByProductCodeResp.setSpaceId( vo.getSpaceId() );
        knowledgeBaseByProductCodeResp.setProductCode( vo.getProductCode() );
        knowledgeBaseByProductCodeResp.setUrl( vo.getUrl() );
        knowledgeBaseByProductCodeResp.setCreatorId( vo.getCreatorId() );
        knowledgeBaseByProductCodeResp.setCreator( vo.getCreator() );

        return knowledgeBaseByProductCodeResp;
    }

    @Override
    public UpdateKnowledgeBaseCommand converter(AddKnowledgeBaseByProductCodeReq req) {
        if ( req == null ) {
            return null;
        }

        UpdateKnowledgeBaseCommand updateKnowledgeBaseCommand = new UpdateKnowledgeBaseCommand();

        updateKnowledgeBaseCommand.setProductCode( req.getProductCode() );
        updateKnowledgeBaseCommand.setOperatedUserId( req.getOperatedUserId() );
        updateKnowledgeBaseCommand.setUserId( req.getUserId() );
        updateKnowledgeBaseCommand.setRoleType( req.getRoleType() );

        return updateKnowledgeBaseCommand;
    }

    @Override
    public RemoveKnowledgeBaseCommand converter(RemoveKnowledgeBaseByProductCodeReq req) {
        if ( req == null ) {
            return null;
        }

        RemoveKnowledgeBaseCommand removeKnowledgeBaseCommand = new RemoveKnowledgeBaseCommand();

        removeKnowledgeBaseCommand.setProductCode( req.getProductCode() );
        removeKnowledgeBaseCommand.setOperatedUserId( req.getOperatedUserId() );
        removeKnowledgeBaseCommand.setUserId( req.getUserId() );
        removeKnowledgeBaseCommand.setRoleType( req.getRoleType() );

        return removeKnowledgeBaseCommand;
    }
}
