package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.OperatedTestManageVO;
import com.zto.devops.qc.client.model.report.entity.OpertatedButton;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendExternalTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendMobileTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendReviewReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendSimpleTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendTmAccessTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendTmOnlineSmokeTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddAndSendTmPermitTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddExternalTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddMobileTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddReviewReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddSimpleTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddTmAccessReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddTmOnlineSmokeReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.AddTmPermitReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.ComputeMetricsCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendExternalTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendMobileTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendReviewReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendSimpleTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendTmAccessTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendTmOnlineSmokeTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditAndSendTmPermitTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditExternalTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditMobileTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditReviewReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditSimpleTestReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditTmAccessReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditTmOnlineSmokeReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.command.EditTmPermitReportCommand;
import com.zto.devops.qc.client.model.testmanager.report.entity.EmailMemberVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ExternalTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.MobileTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.PublishVersionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.QueryPermitResultVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewRenewalVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.SimpleTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmAccessReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmPermitReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmSmokeReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.query.AccessReportDetailQuery;
import com.zto.devops.qc.client.model.testmanager.report.query.OnlineSmokeReportDetailQuery;
import com.zto.devops.qc.client.model.testmanager.report.query.PageReportMqQuery;
import com.zto.devops.qc.client.model.testmanager.report.query.PermitReportDetailQuery;
import com.zto.devops.qc.client.service.report.model.AddAccessTestReportReq;
import com.zto.devops.qc.client.service.report.model.AddExternalTestReportReq;
import com.zto.devops.qc.client.service.report.model.AddMobileTestReportReq;
import com.zto.devops.qc.client.service.report.model.AddOnlineSmokeTestReportReq;
import com.zto.devops.qc.client.service.report.model.AddPermitTestReportReq;
import com.zto.devops.qc.client.service.report.model.ComputeMetricsReq;
import com.zto.devops.qc.client.service.report.model.EditAccessTestReportReq;
import com.zto.devops.qc.client.service.report.model.EditAndSendReviewReportReq;
import com.zto.devops.qc.client.service.report.model.EditExternalTestReportReq;
import com.zto.devops.qc.client.service.report.model.EditMobileTestReportReq;
import com.zto.devops.qc.client.service.report.model.EditOnlineSmokeTestReportReq;
import com.zto.devops.qc.client.service.report.model.EditPermitTestReportReq;
import com.zto.devops.qc.client.service.report.model.EditReviewReportReq;
import com.zto.devops.qc.client.service.report.model.ExternalTestReportResp;
import com.zto.devops.qc.client.service.report.model.MobileTestReportDetailResp;
import com.zto.devops.qc.client.service.report.model.OperatedTestManageResp;
import com.zto.devops.qc.client.service.report.model.PageReportReq;
import com.zto.devops.qc.client.service.report.model.QueryPermitResultResp;
import com.zto.devops.qc.client.service.report.model.QueryTestReportDetailReq;
import com.zto.devops.qc.client.service.report.model.ReviewReportDetailResp;
import com.zto.devops.qc.client.service.report.model.SaveSimpleTestReportReq;
import com.zto.devops.qc.client.service.report.model.SendSimpleTestReportReq;
import com.zto.devops.qc.client.service.report.model.SimpleTestReportDetailResp;
import com.zto.devops.qc.client.service.report.model.TmAccessReportDetailResp;
import com.zto.devops.qc.client.service.report.model.TmOnlineSmokeReportDetailResp;
import com.zto.devops.qc.client.service.report.model.TmPermitReportDetailResp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestReportConverterImpl implements TestReportConverter {

    @Override
    public OperatedTestManageResp convertor(OperatedTestManageVO vo) {
        if ( vo == null ) {
            return null;
        }

        OperatedTestManageResp operatedTestManageResp = new OperatedTestManageResp();

        List<OpertatedButton> list = vo.getTestPlanOperations();
        if ( list != null ) {
            operatedTestManageResp.setTestPlanOperations( new ArrayList<OpertatedButton>( list ) );
        }
        List<OpertatedButton> list1 = vo.getTestReportOperations();
        if ( list1 != null ) {
            operatedTestManageResp.setTestReportOperations( new ArrayList<OpertatedButton>( list1 ) );
        }

        return operatedTestManageResp;
    }

    @Override
    public AccessReportDetailQuery convertAccess(QueryTestReportDetailReq req) {
        if ( req == null ) {
            return null;
        }

        AccessReportDetailQuery accessReportDetailQuery = new AccessReportDetailQuery();

        accessReportDetailQuery.setReportCode( req.getReportCode() );
        accessReportDetailQuery.setPlanCode( req.getPlanCode() );

        return accessReportDetailQuery;
    }

    @Override
    public TmAccessReportDetailResp converter(TmAccessReportDetailVO vo) {
        if ( vo == null ) {
            return null;
        }

        TmAccessReportDetailResp tmAccessReportDetailResp = new TmAccessReportDetailResp();

        tmAccessReportDetailResp.setReportCode( vo.getReportCode() );
        tmAccessReportDetailResp.setReportName( vo.getReportName() );
        tmAccessReportDetailResp.setPlanCode( vo.getPlanCode() );
        tmAccessReportDetailResp.setPlanName( vo.getPlanName() );
        tmAccessReportDetailResp.setVersionCode( vo.getVersionCode() );
        tmAccessReportDetailResp.setVersionName( vo.getVersionName() );
        tmAccessReportDetailResp.setDeptId( vo.getDeptId() );
        tmAccessReportDetailResp.setDeptName( vo.getDeptName() );
        tmAccessReportDetailResp.setProductCode( vo.getProductCode() );
        tmAccessReportDetailResp.setProductName( vo.getProductName() );
        tmAccessReportDetailResp.setProductOwnerId( vo.getProductOwnerId() );
        tmAccessReportDetailResp.setProductOwnerName( vo.getProductOwnerName() );
        tmAccessReportDetailResp.setReportUserId( vo.getReportUserId() );
        tmAccessReportDetailResp.setReportUserName( vo.getReportUserName() );
        tmAccessReportDetailResp.setSummary( vo.getSummary() );
        tmAccessReportDetailResp.setSendTime( vo.getSendTime() );
        List<EmailMemberVO> list = vo.getReceiveUsers();
        if ( list != null ) {
            tmAccessReportDetailResp.setReceiveUsers( new ArrayList<EmailMemberVO>( list ) );
        }
        List<EmailMemberVO> list1 = vo.getCcUsers();
        if ( list1 != null ) {
            tmAccessReportDetailResp.setCcUsers( new ArrayList<EmailMemberVO>( list1 ) );
        }
        tmAccessReportDetailResp.setTestResult( vo.getTestResult() );
        tmAccessReportDetailResp.setNeedUiTest( vo.getNeedUiTest() );
        tmAccessReportDetailResp.setUiTestResult( vo.getUiTestResult() );
        tmAccessReportDetailResp.setReportType( vo.getReportType() );
        tmAccessReportDetailResp.setPresentationDate( vo.getPresentationDate() );
        tmAccessReportDetailResp.setAccessDatePartition( vo.getAccessDatePartition() );
        tmAccessReportDetailResp.setActualPresentationDate( vo.getActualPresentationDate() );

        return tmAccessReportDetailResp;
    }

    @Override
    public OnlineSmokeReportDetailQuery convertSmoke(QueryTestReportDetailReq req) {
        if ( req == null ) {
            return null;
        }

        OnlineSmokeReportDetailQuery onlineSmokeReportDetailQuery = new OnlineSmokeReportDetailQuery();

        onlineSmokeReportDetailQuery.setReportCode( req.getReportCode() );
        onlineSmokeReportDetailQuery.setPlanCode( req.getPlanCode() );

        return onlineSmokeReportDetailQuery;
    }

    @Override
    public TmOnlineSmokeReportDetailResp converter(TmSmokeReportDetailVO detailVO) {
        if ( detailVO == null ) {
            return null;
        }

        TmOnlineSmokeReportDetailResp tmOnlineSmokeReportDetailResp = new TmOnlineSmokeReportDetailResp();

        tmOnlineSmokeReportDetailResp.setReportCode( detailVO.getReportCode() );
        tmOnlineSmokeReportDetailResp.setReportName( detailVO.getReportName() );
        tmOnlineSmokeReportDetailResp.setPlanCode( detailVO.getPlanCode() );
        tmOnlineSmokeReportDetailResp.setPlanName( detailVO.getPlanName() );
        tmOnlineSmokeReportDetailResp.setVersionCode( detailVO.getVersionCode() );
        tmOnlineSmokeReportDetailResp.setVersionName( detailVO.getVersionName() );
        tmOnlineSmokeReportDetailResp.setDeptId( detailVO.getDeptId() );
        tmOnlineSmokeReportDetailResp.setDeptName( detailVO.getDeptName() );
        tmOnlineSmokeReportDetailResp.setProductCode( detailVO.getProductCode() );
        tmOnlineSmokeReportDetailResp.setProductName( detailVO.getProductName() );
        tmOnlineSmokeReportDetailResp.setProductOwnerId( detailVO.getProductOwnerId() );
        tmOnlineSmokeReportDetailResp.setProductOwnerName( detailVO.getProductOwnerName() );
        tmOnlineSmokeReportDetailResp.setReportUserId( detailVO.getReportUserId() );
        tmOnlineSmokeReportDetailResp.setReportUserName( detailVO.getReportUserName() );
        tmOnlineSmokeReportDetailResp.setSummary( detailVO.getSummary() );
        tmOnlineSmokeReportDetailResp.setSendTime( detailVO.getSendTime() );
        List<EmailMemberVO> list = detailVO.getReceiveUsers();
        if ( list != null ) {
            tmOnlineSmokeReportDetailResp.setReceiveUsers( new ArrayList<EmailMemberVO>( list ) );
        }
        List<EmailMemberVO> list1 = detailVO.getCcUsers();
        if ( list1 != null ) {
            tmOnlineSmokeReportDetailResp.setCcUsers( new ArrayList<EmailMemberVO>( list1 ) );
        }
        tmOnlineSmokeReportDetailResp.setTestResult( detailVO.getTestResult() );
        tmOnlineSmokeReportDetailResp.setNeedUiTest( detailVO.getNeedUiTest() );
        tmOnlineSmokeReportDetailResp.setUiTestResult( detailVO.getUiTestResult() );
        tmOnlineSmokeReportDetailResp.setZuiFlag( detailVO.getZuiFlag() );
        tmOnlineSmokeReportDetailResp.setZuiTestResult( detailVO.getZuiTestResult() );
        tmOnlineSmokeReportDetailResp.setReportType( detailVO.getReportType() );
        tmOnlineSmokeReportDetailResp.setAsPlanedOnline( detailVO.getAsPlanedOnline() );
        tmOnlineSmokeReportDetailResp.setActualPublishDate( detailVO.getActualPublishDate() );
        tmOnlineSmokeReportDetailResp.setDelay( detailVO.getDelay() );
        tmOnlineSmokeReportDetailResp.setDelayDesc( detailVO.getDelayDesc() );
        tmOnlineSmokeReportDetailResp.setPublishDate( detailVO.getPublishDate() );
        List<PublishVersionVO> list2 = detailVO.getPublishRecordList();
        if ( list2 != null ) {
            tmOnlineSmokeReportDetailResp.setPublishRecordList( new ArrayList<PublishVersionVO>( list2 ) );
        }

        return tmOnlineSmokeReportDetailResp;
    }

    @Override
    public PermitReportDetailQuery convertPermit(QueryTestReportDetailReq req) {
        if ( req == null ) {
            return null;
        }

        PermitReportDetailQuery permitReportDetailQuery = new PermitReportDetailQuery();

        permitReportDetailQuery.setReportCode( req.getReportCode() );
        permitReportDetailQuery.setPlanCode( req.getPlanCode() );

        return permitReportDetailQuery;
    }

    @Override
    public TmPermitReportDetailResp converter(TmPermitReportDetailVO permitReportDetailVO) {
        if ( permitReportDetailVO == null ) {
            return null;
        }

        TmPermitReportDetailResp tmPermitReportDetailResp = new TmPermitReportDetailResp();

        tmPermitReportDetailResp.setReportCode( permitReportDetailVO.getReportCode() );
        tmPermitReportDetailResp.setReportName( permitReportDetailVO.getReportName() );
        tmPermitReportDetailResp.setPlanCode( permitReportDetailVO.getPlanCode() );
        tmPermitReportDetailResp.setPlanName( permitReportDetailVO.getPlanName() );
        tmPermitReportDetailResp.setVersionCode( permitReportDetailVO.getVersionCode() );
        tmPermitReportDetailResp.setVersionName( permitReportDetailVO.getVersionName() );
        tmPermitReportDetailResp.setDeptId( permitReportDetailVO.getDeptId() );
        tmPermitReportDetailResp.setDeptName( permitReportDetailVO.getDeptName() );
        tmPermitReportDetailResp.setProductCode( permitReportDetailVO.getProductCode() );
        tmPermitReportDetailResp.setProductName( permitReportDetailVO.getProductName() );
        tmPermitReportDetailResp.setProductOwnerId( permitReportDetailVO.getProductOwnerId() );
        tmPermitReportDetailResp.setProductOwnerName( permitReportDetailVO.getProductOwnerName() );
        tmPermitReportDetailResp.setReportUserId( permitReportDetailVO.getReportUserId() );
        tmPermitReportDetailResp.setReportUserName( permitReportDetailVO.getReportUserName() );
        tmPermitReportDetailResp.setSummary( permitReportDetailVO.getSummary() );
        tmPermitReportDetailResp.setSendTime( permitReportDetailVO.getSendTime() );
        List<EmailMemberVO> list = permitReportDetailVO.getReceiveUsers();
        if ( list != null ) {
            tmPermitReportDetailResp.setReceiveUsers( new ArrayList<EmailMemberVO>( list ) );
        }
        List<EmailMemberVO> list1 = permitReportDetailVO.getCcUsers();
        if ( list1 != null ) {
            tmPermitReportDetailResp.setCcUsers( new ArrayList<EmailMemberVO>( list1 ) );
        }
        tmPermitReportDetailResp.setTestResult( permitReportDetailVO.getTestResult() );
        tmPermitReportDetailResp.setNeedUiTest( permitReportDetailVO.getNeedUiTest() );
        tmPermitReportDetailResp.setUiTestResult( permitReportDetailVO.getUiTestResult() );
        tmPermitReportDetailResp.setZuiFlag( permitReportDetailVO.getZuiFlag() );
        tmPermitReportDetailResp.setZuiTestResult( permitReportDetailVO.getZuiTestResult() );
        tmPermitReportDetailResp.setReportType( permitReportDetailVO.getReportType() );
        List<TmModuleTestVO> list2 = permitReportDetailVO.getModuleTestVOS();
        if ( list2 != null ) {
            tmPermitReportDetailResp.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list2 ) );
        }
        tmPermitReportDetailResp.setApprovalExitDate( permitReportDetailVO.getApprovalExitDate() );
        tmPermitReportDetailResp.setPermitDatePartition( permitReportDetailVO.getPermitDatePartition() );
        tmPermitReportDetailResp.setActualApprovalExitDate( permitReportDetailVO.getActualApprovalExitDate() );
        tmPermitReportDetailResp.setSafePlanCode( permitReportDetailVO.getSafePlanCode() );

        return tmPermitReportDetailResp;
    }

    @Override
    public ExternalTestReportResp convertor(ExternalTestReportDetailVO externalReportVO) {
        if ( externalReportVO == null ) {
            return null;
        }

        ExternalTestReportResp externalTestReportResp = new ExternalTestReportResp();

        externalTestReportResp.setPresentationDate( externalReportVO.getPlanPresentationDate() );
        externalTestReportResp.setPublishDate( externalReportVO.getPlanOnlineDate() );
        externalTestReportResp.setPublishDay( externalReportVO.getPlanOnlineDay() );
        externalTestReportResp.setReportCode( externalReportVO.getReportCode() );
        externalTestReportResp.setReportName( externalReportVO.getReportName() );
        externalTestReportResp.setPlanCode( externalReportVO.getPlanCode() );
        externalTestReportResp.setPlanName( externalReportVO.getPlanName() );
        externalTestReportResp.setVersionCode( externalReportVO.getVersionCode() );
        externalTestReportResp.setVersionName( externalReportVO.getVersionName() );
        externalTestReportResp.setDeptId( externalReportVO.getDeptId() );
        externalTestReportResp.setDeptName( externalReportVO.getDeptName() );
        externalTestReportResp.setProductCode( externalReportVO.getProductCode() );
        externalTestReportResp.setProductName( externalReportVO.getProductName() );
        externalTestReportResp.setProductOwnerId( externalReportVO.getProductOwnerId() );
        externalTestReportResp.setProductOwnerName( externalReportVO.getProductOwnerName() );
        externalTestReportResp.setReportUserId( externalReportVO.getReportUserId() );
        externalTestReportResp.setReportUserName( externalReportVO.getReportUserName() );
        externalTestReportResp.setSummary( externalReportVO.getSummary() );
        externalTestReportResp.setSendTime( externalReportVO.getSendTime() );
        List<EmailMemberVO> list = externalReportVO.getReceiveUsers();
        if ( list != null ) {
            externalTestReportResp.setReceiveUsers( new ArrayList<EmailMemberVO>( list ) );
        }
        List<EmailMemberVO> list1 = externalReportVO.getCcUsers();
        if ( list1 != null ) {
            externalTestReportResp.setCcUsers( new ArrayList<EmailMemberVO>( list1 ) );
        }
        externalTestReportResp.setTestResult( externalReportVO.getTestResult() );
        externalTestReportResp.setNeedUiTest( externalReportVO.getNeedUiTest() );
        externalTestReportResp.setUiTestResult( externalReportVO.getUiTestResult() );
        externalTestReportResp.setZuiFlag( externalReportVO.getZuiFlag() );
        externalTestReportResp.setZuiTestResult( externalReportVO.getZuiTestResult() );
        externalTestReportResp.setReportType( externalReportVO.getReportType() );
        externalTestReportResp.setReportTypeDesc( externalReportVO.getReportTypeDesc() );
        externalTestReportResp.setPresentationDay( externalReportVO.getPresentationDay() );
        externalTestReportResp.setCheckStartDate( externalReportVO.getCheckStartDate() );
        externalTestReportResp.setCheckEndDate( externalReportVO.getCheckEndDate() );
        externalTestReportResp.setActualPublishDate( externalReportVO.getActualPublishDate() );
        externalTestReportResp.setStartDate( externalReportVO.getStartDate() );
        externalTestReportResp.setDelay( externalReportVO.getDelay() );
        externalTestReportResp.setDelayDesc( externalReportVO.getDelayDesc() );
        externalTestReportResp.setProductSource( externalReportVO.getProductSource() );
        externalTestReportResp.setProductSourceDesc( externalReportVO.getProductSourceDesc() );
        List<AttachmentVO> list2 = externalReportVO.getAttachments();
        if ( list2 != null ) {
            externalTestReportResp.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        externalTestReportResp.setSafePlanCode( externalReportVO.getSafePlanCode() );

        return externalTestReportResp;
    }

    @Override
    public AddTmAccessReportCommand convertorAccessAdd(EditAccessTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT);

        AddTmAccessReportCommand addTmAccessReportCommand = new AddTmAccessReportCommand( aggregateId );

        addTmAccessReportCommand.setReportName( req.getReportName() );
        addTmAccessReportCommand.setReportCode( req.getReportCode() );
        addTmAccessReportCommand.setPlanCode( req.getPlanCode() );
        addTmAccessReportCommand.setPlanName( req.getPlanName() );
        addTmAccessReportCommand.setProductCode( req.getProductCode() );
        addTmAccessReportCommand.setProductName( req.getProductName() );
        addTmAccessReportCommand.setActualPresentationDate( req.getActualPresentationDate() );
        addTmAccessReportCommand.setSummary( req.getSummary() );

        addTmAccessReportCommand.setReportType( com.zto.devops.qc.client.enums.testmanager.report.ReportType.TEST_ACCESS );

        return addTmAccessReportCommand;
    }

    @Override
    public EditTmAccessReportCommand convertor(EditAccessTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditTmAccessReportCommand editTmAccessReportCommand = new EditTmAccessReportCommand( aggregateId );

        editTmAccessReportCommand.setReportCode( req.getReportCode() );
        editTmAccessReportCommand.setReportName( req.getReportName() );
        editTmAccessReportCommand.setPlanCode( req.getPlanCode() );
        editTmAccessReportCommand.setPlanName( req.getPlanName() );
        editTmAccessReportCommand.setProductCode( req.getProductCode() );
        editTmAccessReportCommand.setProductName( req.getProductName() );
        editTmAccessReportCommand.setActualPresentationDate( req.getActualPresentationDate() );
        editTmAccessReportCommand.setSummary( req.getSummary() );

        editTmAccessReportCommand.setReportType( com.zto.devops.qc.client.enums.testmanager.report.ReportType.TEST_ACCESS );

        return editTmAccessReportCommand;
    }

    @Override
    public AddTmOnlineSmokeReportCommand convertorOnlineSmokeAdd(EditOnlineSmokeTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT);

        AddTmOnlineSmokeReportCommand addTmOnlineSmokeReportCommand = new AddTmOnlineSmokeReportCommand( aggregateId );

        addTmOnlineSmokeReportCommand.setReportCode( req.getReportCode() );
        addTmOnlineSmokeReportCommand.setReportName( req.getReportName() );
        addTmOnlineSmokeReportCommand.setPlanCode( req.getPlanCode() );
        addTmOnlineSmokeReportCommand.setPlanName( req.getPlanName() );
        addTmOnlineSmokeReportCommand.setProductCode( req.getProductCode() );
        addTmOnlineSmokeReportCommand.setProductName( req.getProductName() );
        addTmOnlineSmokeReportCommand.setTestResult( req.getTestResult() );
        addTmOnlineSmokeReportCommand.setActualPublishDate( req.getActualPublishDate() );
        addTmOnlineSmokeReportCommand.setDelay( req.getDelay() );
        addTmOnlineSmokeReportCommand.setDelayDesc( req.getDelayDesc() );
        addTmOnlineSmokeReportCommand.setAsPlanedOnline( req.getAsPlanedOnline() );
        addTmOnlineSmokeReportCommand.setSummary( req.getSummary() );
        addTmOnlineSmokeReportCommand.setZuiTestResult( req.getZuiTestResult() );

        addTmOnlineSmokeReportCommand.setReportType( com.zto.devops.qc.client.enums.testmanager.report.ReportType.ONLINE_SMOKE );

        return addTmOnlineSmokeReportCommand;
    }

    @Override
    public EditTmOnlineSmokeReportCommand convertor(EditOnlineSmokeTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditTmOnlineSmokeReportCommand editTmOnlineSmokeReportCommand = new EditTmOnlineSmokeReportCommand( aggregateId );

        editTmOnlineSmokeReportCommand.setReportCode( req.getReportCode() );
        editTmOnlineSmokeReportCommand.setReportName( req.getReportName() );
        editTmOnlineSmokeReportCommand.setPlanCode( req.getPlanCode() );
        editTmOnlineSmokeReportCommand.setPlanName( req.getPlanName() );
        editTmOnlineSmokeReportCommand.setProductCode( req.getProductCode() );
        editTmOnlineSmokeReportCommand.setProductName( req.getProductName() );
        editTmOnlineSmokeReportCommand.setTestResult( req.getTestResult() );
        editTmOnlineSmokeReportCommand.setActualPublishDate( req.getActualPublishDate() );
        editTmOnlineSmokeReportCommand.setDelay( req.getDelay() );
        editTmOnlineSmokeReportCommand.setDelayDesc( req.getDelayDesc() );
        editTmOnlineSmokeReportCommand.setAsPlanedOnline( req.getAsPlanedOnline() );
        editTmOnlineSmokeReportCommand.setSummary( req.getSummary() );
        editTmOnlineSmokeReportCommand.setZuiTestResult( req.getZuiTestResult() );

        editTmOnlineSmokeReportCommand.setReportType( com.zto.devops.qc.client.enums.testmanager.report.ReportType.ONLINE_SMOKE );

        return editTmOnlineSmokeReportCommand;
    }

    @Override
    public EditAndSendReviewReportCommand editAndSendReviewConvert(EditAndSendReviewReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditAndSendReviewReportCommand editAndSendReviewReportCommand = new EditAndSendReviewReportCommand( aggregateId );

        editAndSendReviewReportCommand.setReportCode( req.getReportCode() );
        editAndSendReviewReportCommand.setReportName( req.getReportName() );
        editAndSendReviewReportCommand.setPlanCode( req.getPlanCode() );
        editAndSendReviewReportCommand.setPlanName( req.getPlanName() );
        editAndSendReviewReportCommand.setVersionCode( req.getVersionCode() );
        editAndSendReviewReportCommand.setVersionName( req.getVersionName() );
        editAndSendReviewReportCommand.setProductCode( req.getProductCode() );
        editAndSendReviewReportCommand.setProductName( req.getProductName() );
        editAndSendReviewReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            editAndSendReviewReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        editAndSendReviewReportCommand.setReviewInfo( req.getReviewInfo() );
        List<ReviewOpinionVO> list = req.getReviewOpinions();
        if ( list != null ) {
            editAndSendReviewReportCommand.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list ) );
        }
        List<ReviewRenewalVO> list1 = req.getReviewRenewals();
        if ( list1 != null ) {
            editAndSendReviewReportCommand.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list1 ) );
        }
        List<AttachmentVO> list2 = req.getAttachments();
        if ( list2 != null ) {
            editAndSendReviewReportCommand.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        List<SendUserInfoVO> list3 = req.getReceiveUsers();
        if ( list3 != null ) {
            editAndSendReviewReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list3 ) );
        }
        List<SendUserInfoVO> list4 = req.getCcUsers();
        if ( list4 != null ) {
            editAndSendReviewReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list4 ) );
        }
        editAndSendReviewReportCommand.setSummary( req.getSummary() );

        return editAndSendReviewReportCommand;
    }

    @Override
    public AddAndSendReviewReportCommand addAndSendReviewConvert(EditAndSendReviewReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT);

        AddAndSendReviewReportCommand addAndSendReviewReportCommand = new AddAndSendReviewReportCommand( aggregateId );

        addAndSendReviewReportCommand.setReportCode( req.getReportCode() );
        addAndSendReviewReportCommand.setReportName( req.getReportName() );
        addAndSendReviewReportCommand.setPlanCode( req.getPlanCode() );
        addAndSendReviewReportCommand.setPlanName( req.getPlanName() );
        addAndSendReviewReportCommand.setVersionCode( req.getVersionCode() );
        addAndSendReviewReportCommand.setVersionName( req.getVersionName() );
        addAndSendReviewReportCommand.setProductCode( req.getProductCode() );
        addAndSendReviewReportCommand.setProductName( req.getProductName() );
        addAndSendReviewReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            addAndSendReviewReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        addAndSendReviewReportCommand.setReviewInfo( req.getReviewInfo() );
        List<ReviewOpinionVO> list = req.getReviewOpinions();
        if ( list != null ) {
            addAndSendReviewReportCommand.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list ) );
        }
        List<ReviewRenewalVO> list1 = req.getReviewRenewals();
        if ( list1 != null ) {
            addAndSendReviewReportCommand.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list1 ) );
        }
        List<AttachmentVO> list2 = req.getAttachments();
        if ( list2 != null ) {
            addAndSendReviewReportCommand.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        List<SendUserInfoVO> list3 = req.getReceiveUsers();
        if ( list3 != null ) {
            addAndSendReviewReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list3 ) );
        }
        List<SendUserInfoVO> list4 = req.getCcUsers();
        if ( list4 != null ) {
            addAndSendReviewReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list4 ) );
        }
        addAndSendReviewReportCommand.setSummary( req.getSummary() );

        return addAndSendReviewReportCommand;
    }

    @Override
    public AddTmPermitReportCommand convertorPermitAdd(EditPermitTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT);

        AddTmPermitReportCommand addTmPermitReportCommand = new AddTmPermitReportCommand( aggregateId );

        addTmPermitReportCommand.setReportCode( req.getReportCode() );
        addTmPermitReportCommand.setReportName( req.getReportName() );
        addTmPermitReportCommand.setPlanCode( req.getPlanCode() );
        addTmPermitReportCommand.setPlanName( req.getPlanName() );
        addTmPermitReportCommand.setProductCode( req.getProductCode() );
        addTmPermitReportCommand.setProductName( req.getProductName() );
        addTmPermitReportCommand.setTestResult( req.getTestResult() );
        addTmPermitReportCommand.setActualApprovalExitDate( req.getActualApprovalExitDate() );
        addTmPermitReportCommand.setSummary( req.getSummary() );
        List<TmModuleTestVO> list = req.getModuleTestVOS();
        if ( list != null ) {
            addTmPermitReportCommand.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }
        addTmPermitReportCommand.setUiTestResult( req.getUiTestResult() );
        addTmPermitReportCommand.setZuiTestResult( req.getZuiTestResult() );

        addTmPermitReportCommand.setReportType( com.zto.devops.qc.client.enums.testmanager.report.ReportType.TEST_PERMIT );

        return addTmPermitReportCommand;
    }

    @Override
    public EditTmPermitReportCommand convertor(EditPermitTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditTmPermitReportCommand editTmPermitReportCommand = new EditTmPermitReportCommand( aggregateId );

        editTmPermitReportCommand.setReportCode( req.getReportCode() );
        editTmPermitReportCommand.setReportName( req.getReportName() );
        editTmPermitReportCommand.setPlanCode( req.getPlanCode() );
        editTmPermitReportCommand.setPlanName( req.getPlanName() );
        editTmPermitReportCommand.setProductCode( req.getProductCode() );
        editTmPermitReportCommand.setProductName( req.getProductName() );
        editTmPermitReportCommand.setTestResult( req.getTestResult() );
        editTmPermitReportCommand.setActualApprovalExitDate( req.getActualApprovalExitDate() );
        editTmPermitReportCommand.setSummary( req.getSummary() );
        List<TmModuleTestVO> list = req.getModuleTestVOS();
        if ( list != null ) {
            editTmPermitReportCommand.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }
        editTmPermitReportCommand.setUiTestResult( req.getUiTestResult() );
        editTmPermitReportCommand.setZuiTestResult( req.getZuiTestResult() );

        editTmPermitReportCommand.setReportType( com.zto.devops.qc.client.enums.testmanager.report.ReportType.TEST_PERMIT );

        return editTmPermitReportCommand;
    }

    @Override
    public AddAndSendSimpleTestReportCommand convert(SendSimpleTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT);

        AddAndSendSimpleTestReportCommand addAndSendSimpleTestReportCommand = new AddAndSendSimpleTestReportCommand( aggregateId );

        addAndSendSimpleTestReportCommand.setReportCode( req.getReportCode() );
        addAndSendSimpleTestReportCommand.setReportName( req.getReportName() );
        addAndSendSimpleTestReportCommand.setPlanCode( req.getPlanCode() );
        addAndSendSimpleTestReportCommand.setPlanName( req.getPlanName() );
        addAndSendSimpleTestReportCommand.setVersionCode( req.getVersionCode() );
        addAndSendSimpleTestReportCommand.setVersionName( req.getVersionName() );
        addAndSendSimpleTestReportCommand.setProductCode( req.getProductCode() );
        addAndSendSimpleTestReportCommand.setProductName( req.getProductName() );
        addAndSendSimpleTestReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            addAndSendSimpleTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        addAndSendSimpleTestReportCommand.setStartDate( req.getStartDate() );
        addAndSendSimpleTestReportCommand.setDeveloperCount( req.getDeveloperCount() );
        addAndSendSimpleTestReportCommand.setTesterCount( req.getTesterCount() );
        addAndSendSimpleTestReportCommand.setPlanPresentationDate( req.getPlanPresentationDate() );
        addAndSendSimpleTestReportCommand.setPlanPresentationDay( req.getPlanPresentationDay() );
        addAndSendSimpleTestReportCommand.setActualPresentationDate( req.getActualPresentationDate() );
        addAndSendSimpleTestReportCommand.setPlanApprovalExitDate( req.getPlanApprovalExitDate() );
        addAndSendSimpleTestReportCommand.setPlanApprovalExitDay( req.getPlanApprovalExitDay() );
        addAndSendSimpleTestReportCommand.setActualApprovalExitDate( req.getActualApprovalExitDate() );
        addAndSendSimpleTestReportCommand.setActualPresentationDay( req.getActualPresentationDay() );
        addAndSendSimpleTestReportCommand.setActualApprovalExitDay( req.getActualApprovalExitDay() );
        addAndSendSimpleTestReportCommand.setActualOnlineDate( req.getActualOnlineDate() );
        addAndSendSimpleTestReportCommand.setPlanOnlineDate( req.getPlanOnlineDate() );
        addAndSendSimpleTestReportCommand.setSummary( req.getSummary() );
        List<SendUserInfoVO> list = req.getReceiveUsers();
        if ( list != null ) {
            addAndSendSimpleTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = req.getCcUsers();
        if ( list1 != null ) {
            addAndSendSimpleTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = req.getAttachments();
        if ( list2 != null ) {
            addAndSendSimpleTestReportCommand.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        addAndSendSimpleTestReportCommand.setDelay( req.getDelay() );
        addAndSendSimpleTestReportCommand.setAsPlanedOnline( req.getAsPlanedOnline() );
        addAndSendSimpleTestReportCommand.setCaseExecuteResultVO( req.getCaseExecuteResultVO() );
        List<IssueInfoVO> list3 = req.getIssueInfoVOS();
        if ( list3 != null ) {
            addAndSendSimpleTestReportCommand.setIssueInfoVOS( new ArrayList<IssueInfoVO>( list3 ) );
        }
        addAndSendSimpleTestReportCommand.setUiTestResult( req.getUiTestResult() );
        addAndSendSimpleTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
        List<CoverageReasonVO> list4 = req.getCoverageReasonVOS();
        if ( list4 != null ) {
            addAndSendSimpleTestReportCommand.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list4 ) );
        }

        addAndSendSimpleTestReportCommand.setReportType( com.zto.devops.qc.client.enums.testmanager.report.ReportType.SIMPLE_PROCESS );

        return addAndSendSimpleTestReportCommand;
    }

    @Override
    public EditAndSendSimpleTestReportCommand convertEditAndSendSimpleTestReportCommand(SendSimpleTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditAndSendSimpleTestReportCommand editAndSendSimpleTestReportCommand = new EditAndSendSimpleTestReportCommand( aggregateId );

        editAndSendSimpleTestReportCommand.setReportCode( req.getReportCode() );
        editAndSendSimpleTestReportCommand.setReportName( req.getReportName() );
        editAndSendSimpleTestReportCommand.setPlanCode( req.getPlanCode() );
        editAndSendSimpleTestReportCommand.setPlanName( req.getPlanName() );
        editAndSendSimpleTestReportCommand.setVersionCode( req.getVersionCode() );
        editAndSendSimpleTestReportCommand.setVersionName( req.getVersionName() );
        editAndSendSimpleTestReportCommand.setProductCode( req.getProductCode() );
        editAndSendSimpleTestReportCommand.setProductName( req.getProductName() );
        editAndSendSimpleTestReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            editAndSendSimpleTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        editAndSendSimpleTestReportCommand.setStartDate( req.getStartDate() );
        editAndSendSimpleTestReportCommand.setDeveloperCount( req.getDeveloperCount() );
        editAndSendSimpleTestReportCommand.setTesterCount( req.getTesterCount() );
        editAndSendSimpleTestReportCommand.setPlanPresentationDate( req.getPlanPresentationDate() );
        editAndSendSimpleTestReportCommand.setPlanPresentationDay( req.getPlanPresentationDay() );
        editAndSendSimpleTestReportCommand.setActualPresentationDate( req.getActualPresentationDate() );
        editAndSendSimpleTestReportCommand.setPlanApprovalExitDate( req.getPlanApprovalExitDate() );
        editAndSendSimpleTestReportCommand.setPlanApprovalExitDay( req.getPlanApprovalExitDay() );
        editAndSendSimpleTestReportCommand.setActualApprovalExitDate( req.getActualApprovalExitDate() );
        editAndSendSimpleTestReportCommand.setActualPresentationDay( req.getActualPresentationDay() );
        editAndSendSimpleTestReportCommand.setActualApprovalExitDay( req.getActualApprovalExitDay() );
        editAndSendSimpleTestReportCommand.setActualOnlineDate( req.getActualOnlineDate() );
        editAndSendSimpleTestReportCommand.setPlanOnlineDate( req.getPlanOnlineDate() );
        editAndSendSimpleTestReportCommand.setSummary( req.getSummary() );
        List<SendUserInfoVO> list = req.getReceiveUsers();
        if ( list != null ) {
            editAndSendSimpleTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = req.getCcUsers();
        if ( list1 != null ) {
            editAndSendSimpleTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<AttachmentVO> list2 = req.getAttachments();
        if ( list2 != null ) {
            editAndSendSimpleTestReportCommand.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }
        editAndSendSimpleTestReportCommand.setDelay( req.getDelay() );
        editAndSendSimpleTestReportCommand.setAsPlanedOnline( req.getAsPlanedOnline() );
        editAndSendSimpleTestReportCommand.setCaseExecuteResultVO( req.getCaseExecuteResultVO() );
        List<IssueInfoVO> list3 = req.getIssueInfoVOS();
        if ( list3 != null ) {
            editAndSendSimpleTestReportCommand.setIssueInfoVOS( new ArrayList<IssueInfoVO>( list3 ) );
        }
        editAndSendSimpleTestReportCommand.setUiTestResult( req.getUiTestResult() );
        editAndSendSimpleTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
        List<CoverageReasonVO> list4 = req.getCoverageReasonVOS();
        if ( list4 != null ) {
            editAndSendSimpleTestReportCommand.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list4 ) );
        }

        editAndSendSimpleTestReportCommand.setReportType( com.zto.devops.qc.client.enums.testmanager.report.ReportType.SIMPLE_PROCESS );

        return editAndSendSimpleTestReportCommand;
    }

    @Override
    public ReviewReportDetailResp convertor(ReviewReportDetailVO reviewReportVO) {
        if ( reviewReportVO == null ) {
            return null;
        }

        ReviewReportDetailResp reviewReportDetailResp = new ReviewReportDetailResp();

        reviewReportDetailResp.setReportCode( reviewReportVO.getReportCode() );
        reviewReportDetailResp.setReportName( reviewReportVO.getReportName() );
        reviewReportDetailResp.setPlanCode( reviewReportVO.getPlanCode() );
        reviewReportDetailResp.setPlanName( reviewReportVO.getPlanName() );
        reviewReportDetailResp.setVersionCode( reviewReportVO.getVersionCode() );
        reviewReportDetailResp.setVersionName( reviewReportVO.getVersionName() );
        reviewReportDetailResp.setDeptId( reviewReportVO.getDeptId() );
        reviewReportDetailResp.setDeptName( reviewReportVO.getDeptName() );
        reviewReportDetailResp.setProductCode( reviewReportVO.getProductCode() );
        reviewReportDetailResp.setProductName( reviewReportVO.getProductName() );
        reviewReportDetailResp.setProductOwnerId( reviewReportVO.getProductOwnerId() );
        reviewReportDetailResp.setProductOwnerName( reviewReportVO.getProductOwnerName() );
        reviewReportDetailResp.setReportUserId( reviewReportVO.getReportUserId() );
        reviewReportDetailResp.setReportUserName( reviewReportVO.getReportUserName() );
        reviewReportDetailResp.setSummary( reviewReportVO.getSummary() );
        reviewReportDetailResp.setSendTime( reviewReportVO.getSendTime() );
        List<EmailMemberVO> list = reviewReportVO.getReceiveUsers();
        if ( list != null ) {
            reviewReportDetailResp.setReceiveUsers( new ArrayList<EmailMemberVO>( list ) );
        }
        List<EmailMemberVO> list1 = reviewReportVO.getCcUsers();
        if ( list1 != null ) {
            reviewReportDetailResp.setCcUsers( new ArrayList<EmailMemberVO>( list1 ) );
        }
        reviewReportDetailResp.setTestResult( reviewReportVO.getTestResult() );
        reviewReportDetailResp.setNeedUiTest( reviewReportVO.getNeedUiTest() );
        reviewReportDetailResp.setUiTestResult( reviewReportVO.getUiTestResult() );
        reviewReportDetailResp.setReportType( reviewReportVO.getReportType() );
        reviewReportDetailResp.setReportTypeDesc( reviewReportVO.getReportTypeDesc() );
        reviewReportDetailResp.setReviewInfo( reviewReportVO.getReviewInfo() );
        List<ReviewOpinionVO> list2 = reviewReportVO.getReviewOpinions();
        if ( list2 != null ) {
            reviewReportDetailResp.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list2 ) );
        }
        List<ReviewRenewalVO> list3 = reviewReportVO.getReviewRenewals();
        if ( list3 != null ) {
            reviewReportDetailResp.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list3 ) );
        }
        List<AttachmentVO> list4 = reviewReportVO.getAttachments();
        if ( list4 != null ) {
            reviewReportDetailResp.setAttachments( new ArrayList<AttachmentVO>( list4 ) );
        }

        return reviewReportDetailResp;
    }

    @Override
    public MobileTestReportDetailResp convert(MobileTestReportDetailVO vo) {
        if ( vo == null ) {
            return null;
        }

        MobileTestReportDetailResp mobileTestReportDetailResp = new MobileTestReportDetailResp();

        mobileTestReportDetailResp.setReportCode( vo.getReportCode() );
        mobileTestReportDetailResp.setReportName( vo.getReportName() );
        mobileTestReportDetailResp.setPlanCode( vo.getPlanCode() );
        mobileTestReportDetailResp.setPlanName( vo.getPlanName() );
        mobileTestReportDetailResp.setVersionCode( vo.getVersionCode() );
        mobileTestReportDetailResp.setVersionName( vo.getVersionName() );
        mobileTestReportDetailResp.setDeptId( vo.getDeptId() );
        mobileTestReportDetailResp.setDeptName( vo.getDeptName() );
        mobileTestReportDetailResp.setProductCode( vo.getProductCode() );
        mobileTestReportDetailResp.setProductName( vo.getProductName() );
        mobileTestReportDetailResp.setProductOwnerId( vo.getProductOwnerId() );
        mobileTestReportDetailResp.setProductOwnerName( vo.getProductOwnerName() );
        mobileTestReportDetailResp.setReportUserId( vo.getReportUserId() );
        mobileTestReportDetailResp.setReportUserName( vo.getReportUserName() );
        mobileTestReportDetailResp.setSummary( vo.getSummary() );
        mobileTestReportDetailResp.setSendTime( vo.getSendTime() );
        List<EmailMemberVO> list = vo.getReceiveUsers();
        if ( list != null ) {
            mobileTestReportDetailResp.setReceiveUsers( new ArrayList<EmailMemberVO>( list ) );
        }
        List<EmailMemberVO> list1 = vo.getCcUsers();
        if ( list1 != null ) {
            mobileTestReportDetailResp.setCcUsers( new ArrayList<EmailMemberVO>( list1 ) );
        }
        mobileTestReportDetailResp.setTestResult( vo.getTestResult() );
        mobileTestReportDetailResp.setNeedUiTest( vo.getNeedUiTest() );
        mobileTestReportDetailResp.setUiTestResult( vo.getUiTestResult() );
        mobileTestReportDetailResp.setReportType( vo.getReportType() );
        mobileTestReportDetailResp.setReportTypeDesc( vo.getReportTypeDesc() );
        mobileTestReportDetailResp.setActualTestStart( vo.getActualTestStart() );
        mobileTestReportDetailResp.setActualTestStartDay( vo.getActualTestStartDay() );
        mobileTestReportDetailResp.setActualTestEnd( vo.getActualTestEnd() );
        mobileTestReportDetailResp.setActualTestEndDay( vo.getActualTestEndDay() );
        mobileTestReportDetailResp.setRelationPlanCode( vo.getRelationPlanCode() );
        List<TmModuleTestVO> list2 = vo.getModuleTestVOS();
        if ( list2 != null ) {
            mobileTestReportDetailResp.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list2 ) );
        }

        return mobileTestReportDetailResp;
    }

    @Override
    public AddReviewReportCommand addReviewConvert(EditReviewReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        AddReviewReportCommand addReviewReportCommand = new AddReviewReportCommand( aggregateId );

        addReviewReportCommand.setReportCode( req.getReportCode() );
        addReviewReportCommand.setReportName( req.getReportName() );
        addReviewReportCommand.setPlanCode( req.getPlanCode() );
        addReviewReportCommand.setPlanName( req.getPlanName() );
        addReviewReportCommand.setProductCode( req.getProductCode() );
        addReviewReportCommand.setProductName( req.getProductName() );
        addReviewReportCommand.setReviewInfo( req.getReviewInfo() );
        List<ReviewOpinionVO> list = req.getReviewOpinions();
        if ( list != null ) {
            addReviewReportCommand.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list ) );
        }
        List<ReviewRenewalVO> list1 = req.getReviewRenewals();
        if ( list1 != null ) {
            addReviewReportCommand.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list1 ) );
        }
        List<AttachmentVO> list2 = req.getAttachments();
        if ( list2 != null ) {
            addReviewReportCommand.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return addReviewReportCommand;
    }

    @Override
    public EditReviewReportCommand editReviewConvert(EditReviewReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditReviewReportCommand editReviewReportCommand = new EditReviewReportCommand( aggregateId );

        editReviewReportCommand.setReportCode( req.getReportCode() );
        editReviewReportCommand.setReportName( req.getReportName() );
        editReviewReportCommand.setPlanCode( req.getPlanCode() );
        editReviewReportCommand.setPlanName( req.getPlanName() );
        editReviewReportCommand.setProductCode( req.getProductCode() );
        editReviewReportCommand.setProductName( req.getProductName() );
        editReviewReportCommand.setReviewInfo( req.getReviewInfo() );
        List<ReviewOpinionVO> list = req.getReviewOpinions();
        if ( list != null ) {
            editReviewReportCommand.setReviewOpinions( new ArrayList<ReviewOpinionVO>( list ) );
        }
        List<ReviewRenewalVO> list1 = req.getReviewRenewals();
        if ( list1 != null ) {
            editReviewReportCommand.setReviewRenewals( new ArrayList<ReviewRenewalVO>( list1 ) );
        }
        List<AttachmentVO> list2 = req.getAttachments();
        if ( list2 != null ) {
            editReviewReportCommand.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return editReviewReportCommand;
    }

    @Override
    public AddExternalTestReportCommand addExternalConvert(EditExternalTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        AddExternalTestReportCommand addExternalTestReportCommand = new AddExternalTestReportCommand( aggregateId );

        addExternalTestReportCommand.setReportCode( req.getReportCode() );
        addExternalTestReportCommand.setReportName( req.getReportName() );
        addExternalTestReportCommand.setPlanCode( req.getPlanCode() );
        addExternalTestReportCommand.setPlanName( req.getPlanName() );
        addExternalTestReportCommand.setProductCode( req.getProductCode() );
        addExternalTestReportCommand.setProductName( req.getProductName() );
        addExternalTestReportCommand.setTestResult( req.getTestResult() );
        addExternalTestReportCommand.setUiTestResult( req.getUiTestResult() );
        addExternalTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
        addExternalTestReportCommand.setCheckStartDate( req.getCheckStartDate() );
        addExternalTestReportCommand.setCheckEndDate( req.getCheckEndDate() );
        addExternalTestReportCommand.setActualPublishDate( req.getActualPublishDate() );
        addExternalTestReportCommand.setDelay( req.getDelay() );
        addExternalTestReportCommand.setDelayDesc( req.getDelayDesc() );
        List<AttachmentVO> list = req.getAttachments();
        if ( list != null ) {
            addExternalTestReportCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        addExternalTestReportCommand.setSummary( req.getSummary() );

        return addExternalTestReportCommand;
    }

    @Override
    public EditExternalTestReportCommand editExternalConvert(EditExternalTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditExternalTestReportCommand editExternalTestReportCommand = new EditExternalTestReportCommand( aggregateId );

        editExternalTestReportCommand.setReportCode( req.getReportCode() );
        editExternalTestReportCommand.setReportName( req.getReportName() );
        editExternalTestReportCommand.setPlanCode( req.getPlanCode() );
        editExternalTestReportCommand.setPlanName( req.getPlanName() );
        editExternalTestReportCommand.setProductCode( req.getProductCode() );
        editExternalTestReportCommand.setProductName( req.getProductName() );
        editExternalTestReportCommand.setTestResult( req.getTestResult() );
        editExternalTestReportCommand.setUiTestResult( req.getUiTestResult() );
        editExternalTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
        editExternalTestReportCommand.setCheckStartDate( req.getCheckStartDate() );
        editExternalTestReportCommand.setCheckEndDate( req.getCheckEndDate() );
        editExternalTestReportCommand.setActualPublishDate( req.getActualPublishDate() );
        editExternalTestReportCommand.setDelay( req.getDelay() );
        editExternalTestReportCommand.setDelayDesc( req.getDelayDesc() );
        List<AttachmentVO> list = req.getAttachments();
        if ( list != null ) {
            editExternalTestReportCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        editExternalTestReportCommand.setSummary( req.getSummary() );

        return editExternalTestReportCommand;
    }

    @Override
    public AddMobileTestReportCommand addMobileConvert(EditMobileTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        AddMobileTestReportCommand addMobileTestReportCommand = new AddMobileTestReportCommand( aggregateId );

        addMobileTestReportCommand.setReportCode( req.getReportCode() );
        addMobileTestReportCommand.setReportName( req.getReportName() );
        addMobileTestReportCommand.setPlanCode( req.getPlanCode() );
        addMobileTestReportCommand.setPlanName( req.getPlanName() );
        addMobileTestReportCommand.setProductCode( req.getProductCode() );
        addMobileTestReportCommand.setProductName( req.getProductName() );
        addMobileTestReportCommand.setTestResult( req.getTestResult() );
        addMobileTestReportCommand.setActualTestStart( req.getActualTestStart() );
        addMobileTestReportCommand.setActualTestStartDay( req.getActualTestStartDay() );
        addMobileTestReportCommand.setActualTestEnd( req.getActualTestEnd() );
        addMobileTestReportCommand.setActualTestEndDay( req.getActualTestEndDay() );
        List<TmModuleTestVO> list = req.getModuleTestVOS();
        if ( list != null ) {
            addMobileTestReportCommand.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }
        addMobileTestReportCommand.setSummary( req.getSummary() );

        return addMobileTestReportCommand;
    }

    @Override
    public EditMobileTestReportCommand editMobileConvert(EditMobileTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditMobileTestReportCommand editMobileTestReportCommand = new EditMobileTestReportCommand( aggregateId );

        editMobileTestReportCommand.setReportCode( req.getReportCode() );
        editMobileTestReportCommand.setReportName( req.getReportName() );
        editMobileTestReportCommand.setPlanCode( req.getPlanCode() );
        editMobileTestReportCommand.setPlanName( req.getPlanName() );
        editMobileTestReportCommand.setProductCode( req.getProductCode() );
        editMobileTestReportCommand.setProductName( req.getProductName() );
        editMobileTestReportCommand.setTestResult( req.getTestResult() );
        editMobileTestReportCommand.setActualTestStart( req.getActualTestStart() );
        editMobileTestReportCommand.setActualTestStartDay( req.getActualTestStartDay() );
        editMobileTestReportCommand.setActualTestEnd( req.getActualTestEnd() );
        editMobileTestReportCommand.setActualTestEndDay( req.getActualTestEndDay() );
        List<TmModuleTestVO> list = req.getModuleTestVOS();
        if ( list != null ) {
            editMobileTestReportCommand.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }
        editMobileTestReportCommand.setSummary( req.getSummary() );

        return editMobileTestReportCommand;
    }

    @Override
    public SimpleTestReportDetailResp convert(SimpleTestReportDetailVO vo) {
        if ( vo == null ) {
            return null;
        }

        SimpleTestReportDetailResp simpleTestReportDetailResp = new SimpleTestReportDetailResp();

        simpleTestReportDetailResp.setPlanPresentationDay( vo.getPresentationDay() );
        simpleTestReportDetailResp.setReportCode( vo.getReportCode() );
        simpleTestReportDetailResp.setReportName( vo.getReportName() );
        simpleTestReportDetailResp.setPlanCode( vo.getPlanCode() );
        simpleTestReportDetailResp.setPlanName( vo.getPlanName() );
        simpleTestReportDetailResp.setVersionCode( vo.getVersionCode() );
        simpleTestReportDetailResp.setVersionName( vo.getVersionName() );
        simpleTestReportDetailResp.setDeptId( vo.getDeptId() );
        simpleTestReportDetailResp.setDeptName( vo.getDeptName() );
        simpleTestReportDetailResp.setProductCode( vo.getProductCode() );
        simpleTestReportDetailResp.setProductName( vo.getProductName() );
        simpleTestReportDetailResp.setProductOwnerId( vo.getProductOwnerId() );
        simpleTestReportDetailResp.setProductOwnerName( vo.getProductOwnerName() );
        simpleTestReportDetailResp.setReportUserId( vo.getReportUserId() );
        simpleTestReportDetailResp.setReportUserName( vo.getReportUserName() );
        simpleTestReportDetailResp.setSummary( vo.getSummary() );
        simpleTestReportDetailResp.setSendTime( vo.getSendTime() );
        List<EmailMemberVO> list = vo.getReceiveUsers();
        if ( list != null ) {
            simpleTestReportDetailResp.setReceiveUsers( new ArrayList<EmailMemberVO>( list ) );
        }
        List<EmailMemberVO> list1 = vo.getCcUsers();
        if ( list1 != null ) {
            simpleTestReportDetailResp.setCcUsers( new ArrayList<EmailMemberVO>( list1 ) );
        }
        simpleTestReportDetailResp.setTestResult( vo.getTestResult() );
        simpleTestReportDetailResp.setNeedUiTest( vo.getNeedUiTest() );
        simpleTestReportDetailResp.setUiTestResult( vo.getUiTestResult() );
        simpleTestReportDetailResp.setTestStrategy( vo.getTestStrategy() );
        simpleTestReportDetailResp.setZuiFlag( vo.getZuiFlag() );
        simpleTestReportDetailResp.setZuiTestResult( vo.getZuiTestResult() );
        simpleTestReportDetailResp.setReportType( vo.getReportType() );
        simpleTestReportDetailResp.setReportTypeDesc( vo.getReportTypeDesc() );
        simpleTestReportDetailResp.setPlanPresentationDate( vo.getPlanPresentationDate() );
        simpleTestReportDetailResp.setPlanOnlineDate( vo.getPlanOnlineDate() );
        simpleTestReportDetailResp.setStartDate( vo.getStartDate() );
        simpleTestReportDetailResp.setAsPlanedOnline( vo.getAsPlanedOnline() );
        simpleTestReportDetailResp.setDelay( vo.getDelay() );
        simpleTestReportDetailResp.setDeveloperCount( vo.getDeveloperCount() );
        simpleTestReportDetailResp.setTesterCount( vo.getTesterCount() );
        simpleTestReportDetailResp.setCaseExecuteResultVO( vo.getCaseExecuteResultVO() );
        List<IssueInfoVO> list2 = vo.getIssueInfoVOS();
        if ( list2 != null ) {
            simpleTestReportDetailResp.setIssueInfoVOS( new ArrayList<IssueInfoVO>( list2 ) );
        }
        List<IssueLegacyVO> list3 = vo.getIssueLegacyVOS();
        if ( list3 != null ) {
            simpleTestReportDetailResp.setIssueLegacyVOS( new ArrayList<IssueLegacyVO>( list3 ) );
        }
        List<AttachmentVO> list4 = vo.getAttachments();
        if ( list4 != null ) {
            simpleTestReportDetailResp.setAttachments( new ArrayList<AttachmentVO>( list4 ) );
        }
        simpleTestReportDetailResp.setActualOnlineDate( vo.getActualOnlineDate() );
        simpleTestReportDetailResp.setActualPresentationDate( vo.getActualPresentationDate() );
        simpleTestReportDetailResp.setActualPresentationDay( vo.getActualPresentationDay() );
        simpleTestReportDetailResp.setPlanApprovalExitDate( vo.getPlanApprovalExitDate() );
        simpleTestReportDetailResp.setPlanApprovalExitDay( vo.getPlanApprovalExitDay() );
        simpleTestReportDetailResp.setActualApprovalExitDate( vo.getActualApprovalExitDate() );
        simpleTestReportDetailResp.setActualApprovalExitDay( vo.getActualApprovalExitDay() );
        List<PublishVersionVO> list5 = vo.getPublishRecordList();
        if ( list5 != null ) {
            simpleTestReportDetailResp.setPublishRecordList( new ArrayList<PublishVersionVO>( list5 ) );
        }

        return simpleTestReportDetailResp;
    }

    @Override
    public AddSimpleTestReportCommand convert(SaveSimpleTestReportReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddSimpleTestReportCommand addSimpleTestReportCommand = new AddSimpleTestReportCommand( aggregateId1 );

        if ( req != null ) {
            addSimpleTestReportCommand.setReportCode( req.getReportCode() );
            addSimpleTestReportCommand.setReportName( req.getReportName() );
            addSimpleTestReportCommand.setPlanCode( req.getPlanCode() );
            addSimpleTestReportCommand.setPlanName( req.getPlanName() );
            addSimpleTestReportCommand.setProductCode( req.getProductCode() );
            addSimpleTestReportCommand.setProductName( req.getProductName() );
            if ( req.getTestResult() != null ) {
                addSimpleTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
            }
            addSimpleTestReportCommand.setDeveloperCount( req.getDeveloperCount() );
            addSimpleTestReportCommand.setTesterCount( req.getTesterCount() );
            addSimpleTestReportCommand.setPlanPresentationDay( req.getPlanPresentationDay() );
            addSimpleTestReportCommand.setActualPresentationDate( req.getActualPresentationDate() );
            addSimpleTestReportCommand.setPlanApprovalExitDay( req.getPlanApprovalExitDay() );
            addSimpleTestReportCommand.setActualApprovalExitDate( req.getActualApprovalExitDate() );
            addSimpleTestReportCommand.setActualPresentationDay( req.getActualPresentationDay() );
            addSimpleTestReportCommand.setActualApprovalExitDay( req.getActualApprovalExitDay() );
            addSimpleTestReportCommand.setActualOnlineDate( req.getActualOnlineDate() );
            addSimpleTestReportCommand.setSummary( req.getSummary() );
            List<AttachmentVO> list = req.getAttachments();
            if ( list != null ) {
                addSimpleTestReportCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
            }
            addSimpleTestReportCommand.setDelay( req.getDelay() );
            addSimpleTestReportCommand.setAsPlanedOnline( req.getAsPlanedOnline() );
            addSimpleTestReportCommand.setUiTestResult( req.getUiTestResult() );
            addSimpleTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
        }

        return addSimpleTestReportCommand;
    }

    @Override
    public EditSimpleTestReportCommand convert(SaveSimpleTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditSimpleTestReportCommand editSimpleTestReportCommand = new EditSimpleTestReportCommand( aggregateId );

        editSimpleTestReportCommand.setReportCode( req.getReportCode() );
        editSimpleTestReportCommand.setReportName( req.getReportName() );
        editSimpleTestReportCommand.setPlanCode( req.getPlanCode() );
        editSimpleTestReportCommand.setPlanName( req.getPlanName() );
        editSimpleTestReportCommand.setProductCode( req.getProductCode() );
        editSimpleTestReportCommand.setProductName( req.getProductName() );
        if ( req.getTestResult() != null ) {
            editSimpleTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        editSimpleTestReportCommand.setDeveloperCount( req.getDeveloperCount() );
        editSimpleTestReportCommand.setTesterCount( req.getTesterCount() );
        editSimpleTestReportCommand.setPlanPresentationDay( req.getPlanPresentationDay() );
        editSimpleTestReportCommand.setActualPresentationDate( req.getActualPresentationDate() );
        editSimpleTestReportCommand.setPlanApprovalExitDay( req.getPlanApprovalExitDay() );
        editSimpleTestReportCommand.setActualApprovalExitDate( req.getActualApprovalExitDate() );
        editSimpleTestReportCommand.setActualPresentationDay( req.getActualPresentationDay() );
        editSimpleTestReportCommand.setActualApprovalExitDay( req.getActualApprovalExitDay() );
        editSimpleTestReportCommand.setActualOnlineDate( req.getActualOnlineDate() );
        editSimpleTestReportCommand.setSummary( req.getSummary() );
        List<AttachmentVO> list = req.getAttachments();
        if ( list != null ) {
            editSimpleTestReportCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        editSimpleTestReportCommand.setDelay( req.getDelay() );
        editSimpleTestReportCommand.setAsPlanedOnline( req.getAsPlanedOnline() );
        editSimpleTestReportCommand.setUiTestResult( req.getUiTestResult() );
        editSimpleTestReportCommand.setZuiTestResult( req.getZuiTestResult() );

        return editSimpleTestReportCommand;
    }

    @Override
    public AddAndSendTmAccessTestReportCommand convert(AddAccessTestReportReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddAndSendTmAccessTestReportCommand addAndSendTmAccessTestReportCommand = new AddAndSendTmAccessTestReportCommand( aggregateId1 );

        if ( req != null ) {
            addAndSendTmAccessTestReportCommand.setReportCode( req.getReportCode() );
            addAndSendTmAccessTestReportCommand.setReportName( req.getReportName() );
            addAndSendTmAccessTestReportCommand.setPlanCode( req.getPlanCode() );
            addAndSendTmAccessTestReportCommand.setPlanName( req.getPlanName() );
            addAndSendTmAccessTestReportCommand.setVersionCode( req.getVersionCode() );
            addAndSendTmAccessTestReportCommand.setVersionName( req.getVersionName() );
            addAndSendTmAccessTestReportCommand.setProductCode( req.getProductCode() );
            addAndSendTmAccessTestReportCommand.setProductName( req.getProductName() );
            addAndSendTmAccessTestReportCommand.setPreview( req.getPreview() );
            if ( req.getTestResult() != null ) {
                addAndSendTmAccessTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
            }
            addAndSendTmAccessTestReportCommand.setActualPresentationDate( req.getActualPresentationDate() );
            addAndSendTmAccessTestReportCommand.setDelayDays( req.getDelayDays() );
            addAndSendTmAccessTestReportCommand.setSummary( req.getSummary() );
            List<SendUserInfoVO> list = req.getReceiveUsers();
            if ( list != null ) {
                addAndSendTmAccessTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
            }
            List<SendUserInfoVO> list1 = req.getCcUsers();
            if ( list1 != null ) {
                addAndSendTmAccessTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
            }
        }

        return addAndSendTmAccessTestReportCommand;
    }

    @Override
    public EditAndSendTmAccessTestReportCommand convert(AddAccessTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditAndSendTmAccessTestReportCommand editAndSendTmAccessTestReportCommand = new EditAndSendTmAccessTestReportCommand( aggregateId );

        editAndSendTmAccessTestReportCommand.setReportCode( req.getReportCode() );
        editAndSendTmAccessTestReportCommand.setReportName( req.getReportName() );
        editAndSendTmAccessTestReportCommand.setPlanCode( req.getPlanCode() );
        editAndSendTmAccessTestReportCommand.setPlanName( req.getPlanName() );
        editAndSendTmAccessTestReportCommand.setVersionCode( req.getVersionCode() );
        editAndSendTmAccessTestReportCommand.setVersionName( req.getVersionName() );
        editAndSendTmAccessTestReportCommand.setProductCode( req.getProductCode() );
        editAndSendTmAccessTestReportCommand.setProductName( req.getProductName() );
        editAndSendTmAccessTestReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            editAndSendTmAccessTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        editAndSendTmAccessTestReportCommand.setActualPresentationDate( req.getActualPresentationDate() );
        editAndSendTmAccessTestReportCommand.setDelayDays( req.getDelayDays() );
        editAndSendTmAccessTestReportCommand.setSummary( req.getSummary() );
        List<SendUserInfoVO> list = req.getReceiveUsers();
        if ( list != null ) {
            editAndSendTmAccessTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = req.getCcUsers();
        if ( list1 != null ) {
            editAndSendTmAccessTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return editAndSendTmAccessTestReportCommand;
    }

    @Override
    public AddAndSendExternalTestReportCommand convert(AddExternalTestReportReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddAndSendExternalTestReportCommand addAndSendExternalTestReportCommand = new AddAndSendExternalTestReportCommand( aggregateId1 );

        if ( req != null ) {
            addAndSendExternalTestReportCommand.setReportCode( req.getReportCode() );
            addAndSendExternalTestReportCommand.setReportName( req.getReportName() );
            addAndSendExternalTestReportCommand.setPlanCode( req.getPlanCode() );
            addAndSendExternalTestReportCommand.setPlanName( req.getPlanName() );
            addAndSendExternalTestReportCommand.setVersionCode( req.getVersionCode() );
            addAndSendExternalTestReportCommand.setVersionName( req.getVersionName() );
            addAndSendExternalTestReportCommand.setProductCode( req.getProductCode() );
            addAndSendExternalTestReportCommand.setProductName( req.getProductName() );
            addAndSendExternalTestReportCommand.setPreview( req.getPreview() );
            if ( req.getTestResult() != null ) {
                addAndSendExternalTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
            }
            addAndSendExternalTestReportCommand.setUiTestResult( req.getUiTestResult() );
            addAndSendExternalTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
            addAndSendExternalTestReportCommand.setPresentationDate( req.getPresentationDate() );
            addAndSendExternalTestReportCommand.setPresentationDay( req.getPresentationDay() );
            addAndSendExternalTestReportCommand.setPublishDate( req.getPublishDate() );
            addAndSendExternalTestReportCommand.setPublishDay( req.getPublishDay() );
            addAndSendExternalTestReportCommand.setCheckStartDate( req.getCheckStartDate() );
            addAndSendExternalTestReportCommand.setCheckEndDate( req.getCheckEndDate() );
            addAndSendExternalTestReportCommand.setActualPublishDate( req.getActualPublishDate() );
            addAndSendExternalTestReportCommand.setDelay( req.getDelay() );
            addAndSendExternalTestReportCommand.setDelayDesc( req.getDelayDesc() );
            List<AttachmentVO> list = req.getAttachments();
            if ( list != null ) {
                addAndSendExternalTestReportCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
            }
            List<SendUserInfoVO> list1 = req.getReceiveUsers();
            if ( list1 != null ) {
                addAndSendExternalTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list1 ) );
            }
            List<SendUserInfoVO> list2 = req.getCcUsers();
            if ( list2 != null ) {
                addAndSendExternalTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list2 ) );
            }
            addAndSendExternalTestReportCommand.setSummary( req.getSummary() );
        }

        return addAndSendExternalTestReportCommand;
    }

    @Override
    public EditAndSendExternalTestReportCommand convert(AddExternalTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditAndSendExternalTestReportCommand editAndSendExternalTestReportCommand = new EditAndSendExternalTestReportCommand( aggregateId );

        editAndSendExternalTestReportCommand.setReportCode( req.getReportCode() );
        editAndSendExternalTestReportCommand.setReportName( req.getReportName() );
        editAndSendExternalTestReportCommand.setPlanCode( req.getPlanCode() );
        editAndSendExternalTestReportCommand.setPlanName( req.getPlanName() );
        editAndSendExternalTestReportCommand.setVersionCode( req.getVersionCode() );
        editAndSendExternalTestReportCommand.setVersionName( req.getVersionName() );
        editAndSendExternalTestReportCommand.setProductCode( req.getProductCode() );
        editAndSendExternalTestReportCommand.setProductName( req.getProductName() );
        editAndSendExternalTestReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            editAndSendExternalTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        editAndSendExternalTestReportCommand.setUiTestResult( req.getUiTestResult() );
        editAndSendExternalTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
        editAndSendExternalTestReportCommand.setPresentationDate( req.getPresentationDate() );
        editAndSendExternalTestReportCommand.setPresentationDay( req.getPresentationDay() );
        editAndSendExternalTestReportCommand.setPublishDate( req.getPublishDate() );
        editAndSendExternalTestReportCommand.setPublishDay( req.getPublishDay() );
        editAndSendExternalTestReportCommand.setCheckStartDate( req.getCheckStartDate() );
        editAndSendExternalTestReportCommand.setCheckEndDate( req.getCheckEndDate() );
        editAndSendExternalTestReportCommand.setActualPublishDate( req.getActualPublishDate() );
        editAndSendExternalTestReportCommand.setDelay( req.getDelay() );
        editAndSendExternalTestReportCommand.setDelayDesc( req.getDelayDesc() );
        List<AttachmentVO> list = req.getAttachments();
        if ( list != null ) {
            editAndSendExternalTestReportCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<SendUserInfoVO> list1 = req.getReceiveUsers();
        if ( list1 != null ) {
            editAndSendExternalTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<SendUserInfoVO> list2 = req.getCcUsers();
        if ( list2 != null ) {
            editAndSendExternalTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list2 ) );
        }
        editAndSendExternalTestReportCommand.setSummary( req.getSummary() );

        return editAndSendExternalTestReportCommand;
    }

    @Override
    public AddAndSendMobileTestReportCommand convert(AddMobileTestReportReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddAndSendMobileTestReportCommand addAndSendMobileTestReportCommand = new AddAndSendMobileTestReportCommand( aggregateId1 );

        if ( req != null ) {
            addAndSendMobileTestReportCommand.setReportCode( req.getReportCode() );
            addAndSendMobileTestReportCommand.setReportName( req.getReportName() );
            addAndSendMobileTestReportCommand.setPlanCode( req.getPlanCode() );
            addAndSendMobileTestReportCommand.setPlanName( req.getPlanName() );
            addAndSendMobileTestReportCommand.setVersionCode( req.getVersionCode() );
            addAndSendMobileTestReportCommand.setVersionName( req.getVersionName() );
            addAndSendMobileTestReportCommand.setProductCode( req.getProductCode() );
            addAndSendMobileTestReportCommand.setProductName( req.getProductName() );
            addAndSendMobileTestReportCommand.setPreview( req.getPreview() );
            if ( req.getTestResult() != null ) {
                addAndSendMobileTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
            }
            addAndSendMobileTestReportCommand.setActualTestStart( req.getActualTestStart() );
            addAndSendMobileTestReportCommand.setActualTestStartDay( req.getActualTestStartDay() );
            addAndSendMobileTestReportCommand.setActualTestEnd( req.getActualTestEnd() );
            addAndSendMobileTestReportCommand.setActualTestEndDay( req.getActualTestEndDay() );
            List<TmModuleTestVO> list = req.getModuleTestVOS();
            if ( list != null ) {
                addAndSendMobileTestReportCommand.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
            }
            List<SendUserInfoVO> list1 = req.getReceiveUsers();
            if ( list1 != null ) {
                addAndSendMobileTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list1 ) );
            }
            List<SendUserInfoVO> list2 = req.getCcUsers();
            if ( list2 != null ) {
                addAndSendMobileTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list2 ) );
            }
        }

        return addAndSendMobileTestReportCommand;
    }

    @Override
    public EditAndSendMobileTestReportCommand convert(AddMobileTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditAndSendMobileTestReportCommand editAndSendMobileTestReportCommand = new EditAndSendMobileTestReportCommand( aggregateId );

        editAndSendMobileTestReportCommand.setReportCode( req.getReportCode() );
        editAndSendMobileTestReportCommand.setReportName( req.getReportName() );
        editAndSendMobileTestReportCommand.setPlanCode( req.getPlanCode() );
        editAndSendMobileTestReportCommand.setPlanName( req.getPlanName() );
        editAndSendMobileTestReportCommand.setVersionCode( req.getVersionCode() );
        editAndSendMobileTestReportCommand.setVersionName( req.getVersionName() );
        editAndSendMobileTestReportCommand.setProductCode( req.getProductCode() );
        editAndSendMobileTestReportCommand.setProductName( req.getProductName() );
        editAndSendMobileTestReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            editAndSendMobileTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        editAndSendMobileTestReportCommand.setActualTestStart( req.getActualTestStart() );
        editAndSendMobileTestReportCommand.setActualTestStartDay( req.getActualTestStartDay() );
        editAndSendMobileTestReportCommand.setActualTestEnd( req.getActualTestEnd() );
        editAndSendMobileTestReportCommand.setActualTestEndDay( req.getActualTestEndDay() );
        List<SendUserInfoVO> list = req.getReceiveUsers();
        if ( list != null ) {
            editAndSendMobileTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = req.getCcUsers();
        if ( list1 != null ) {
            editAndSendMobileTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return editAndSendMobileTestReportCommand;
    }

    @Override
    public AddAndSendTmOnlineSmokeTestReportCommand convert(AddOnlineSmokeTestReportReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddAndSendTmOnlineSmokeTestReportCommand addAndSendTmOnlineSmokeTestReportCommand = new AddAndSendTmOnlineSmokeTestReportCommand( aggregateId1 );

        if ( req != null ) {
            addAndSendTmOnlineSmokeTestReportCommand.setReportCode( req.getReportCode() );
            addAndSendTmOnlineSmokeTestReportCommand.setReportName( req.getReportName() );
            addAndSendTmOnlineSmokeTestReportCommand.setPlanCode( req.getPlanCode() );
            addAndSendTmOnlineSmokeTestReportCommand.setPlanName( req.getPlanName() );
            addAndSendTmOnlineSmokeTestReportCommand.setVersionCode( req.getVersionCode() );
            addAndSendTmOnlineSmokeTestReportCommand.setVersionName( req.getVersionName() );
            addAndSendTmOnlineSmokeTestReportCommand.setProductCode( req.getProductCode() );
            addAndSendTmOnlineSmokeTestReportCommand.setProductName( req.getProductName() );
            addAndSendTmOnlineSmokeTestReportCommand.setPreview( req.getPreview() );
            if ( req.getTestResult() != null ) {
                addAndSendTmOnlineSmokeTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
            }
            addAndSendTmOnlineSmokeTestReportCommand.setActualPublishDate( req.getActualPublishDate() );
            addAndSendTmOnlineSmokeTestReportCommand.setDelay( req.getDelay() );
            addAndSendTmOnlineSmokeTestReportCommand.setDelayDesc( req.getDelayDesc() );
            addAndSendTmOnlineSmokeTestReportCommand.setAsPlanedOnline( req.getAsPlanedOnline() );
            addAndSendTmOnlineSmokeTestReportCommand.setSummary( req.getSummary() );
            addAndSendTmOnlineSmokeTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
            List<SendUserInfoVO> list = req.getReceiveUsers();
            if ( list != null ) {
                addAndSendTmOnlineSmokeTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
            }
            List<SendUserInfoVO> list1 = req.getCcUsers();
            if ( list1 != null ) {
                addAndSendTmOnlineSmokeTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
            }
        }

        return addAndSendTmOnlineSmokeTestReportCommand;
    }

    @Override
    public EditAndSendTmOnlineSmokeTestReportCommand convert(AddOnlineSmokeTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditAndSendTmOnlineSmokeTestReportCommand editAndSendTmOnlineSmokeTestReportCommand = new EditAndSendTmOnlineSmokeTestReportCommand( aggregateId );

        editAndSendTmOnlineSmokeTestReportCommand.setReportCode( req.getReportCode() );
        editAndSendTmOnlineSmokeTestReportCommand.setReportName( req.getReportName() );
        editAndSendTmOnlineSmokeTestReportCommand.setPlanCode( req.getPlanCode() );
        editAndSendTmOnlineSmokeTestReportCommand.setPlanName( req.getPlanName() );
        editAndSendTmOnlineSmokeTestReportCommand.setVersionCode( req.getVersionCode() );
        editAndSendTmOnlineSmokeTestReportCommand.setVersionName( req.getVersionName() );
        editAndSendTmOnlineSmokeTestReportCommand.setProductCode( req.getProductCode() );
        editAndSendTmOnlineSmokeTestReportCommand.setProductName( req.getProductName() );
        editAndSendTmOnlineSmokeTestReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            editAndSendTmOnlineSmokeTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        editAndSendTmOnlineSmokeTestReportCommand.setActualPublishDate( req.getActualPublishDate() );
        editAndSendTmOnlineSmokeTestReportCommand.setDelay( req.getDelay() );
        editAndSendTmOnlineSmokeTestReportCommand.setDelayDesc( req.getDelayDesc() );
        editAndSendTmOnlineSmokeTestReportCommand.setAsPlanedOnline( req.getAsPlanedOnline() );
        editAndSendTmOnlineSmokeTestReportCommand.setSummary( req.getSummary() );
        editAndSendTmOnlineSmokeTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
        List<SendUserInfoVO> list = req.getReceiveUsers();
        if ( list != null ) {
            editAndSendTmOnlineSmokeTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = req.getCcUsers();
        if ( list1 != null ) {
            editAndSendTmOnlineSmokeTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }

        return editAndSendTmOnlineSmokeTestReportCommand;
    }

    @Override
    public AddAndSendTmPermitTestReportCommand convert(AddPermitTestReportReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddAndSendTmPermitTestReportCommand addAndSendTmPermitTestReportCommand = new AddAndSendTmPermitTestReportCommand( aggregateId1 );

        if ( req != null ) {
            addAndSendTmPermitTestReportCommand.setReportCode( req.getReportCode() );
            addAndSendTmPermitTestReportCommand.setReportName( req.getReportName() );
            addAndSendTmPermitTestReportCommand.setPlanCode( req.getPlanCode() );
            addAndSendTmPermitTestReportCommand.setPlanName( req.getPlanName() );
            addAndSendTmPermitTestReportCommand.setVersionCode( req.getVersionCode() );
            addAndSendTmPermitTestReportCommand.setVersionName( req.getVersionName() );
            addAndSendTmPermitTestReportCommand.setProductCode( req.getProductCode() );
            addAndSendTmPermitTestReportCommand.setProductName( req.getProductName() );
            addAndSendTmPermitTestReportCommand.setPreview( req.getPreview() );
            if ( req.getTestResult() != null ) {
                addAndSendTmPermitTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
            }
            addAndSendTmPermitTestReportCommand.setActualApprovalExitDate( req.getActualApprovalExitDate() );
            addAndSendTmPermitTestReportCommand.setDelayDays( req.getDelayDays() );
            addAndSendTmPermitTestReportCommand.setSecurityUserId( req.getSecurityUserId() );
            addAndSendTmPermitTestReportCommand.setSecurityUserName( req.getSecurityUserName() );
            addAndSendTmPermitTestReportCommand.setSecurityTestResultCode( req.getSecurityTestResultCode() );
            addAndSendTmPermitTestReportCommand.setSecurityTestResultDesc( req.getSecurityTestResultDesc() );
            addAndSendTmPermitTestReportCommand.setSummary( req.getSummary() );
            List<TmModuleTestVO> list = req.getModuleTestVOS();
            if ( list != null ) {
                addAndSendTmPermitTestReportCommand.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
            }
            addAndSendTmPermitTestReportCommand.setUiTestResult( req.getUiTestResult() );
            addAndSendTmPermitTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
            List<SendUserInfoVO> list1 = req.getReceiveUsers();
            if ( list1 != null ) {
                addAndSendTmPermitTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list1 ) );
            }
            List<SendUserInfoVO> list2 = req.getCcUsers();
            if ( list2 != null ) {
                addAndSendTmPermitTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list2 ) );
            }
            List<CoverageReasonVO> list3 = req.getCoverageReasonVOS();
            if ( list3 != null ) {
                addAndSendTmPermitTestReportCommand.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list3 ) );
            }
        }

        return addAndSendTmPermitTestReportCommand;
    }

    @Override
    public EditAndSendTmPermitTestReportCommand convert(AddPermitTestReportReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getReportCode();

        EditAndSendTmPermitTestReportCommand editAndSendTmPermitTestReportCommand = new EditAndSendTmPermitTestReportCommand( aggregateId );

        editAndSendTmPermitTestReportCommand.setReportCode( req.getReportCode() );
        editAndSendTmPermitTestReportCommand.setReportName( req.getReportName() );
        editAndSendTmPermitTestReportCommand.setPlanCode( req.getPlanCode() );
        editAndSendTmPermitTestReportCommand.setPlanName( req.getPlanName() );
        editAndSendTmPermitTestReportCommand.setVersionCode( req.getVersionCode() );
        editAndSendTmPermitTestReportCommand.setVersionName( req.getVersionName() );
        editAndSendTmPermitTestReportCommand.setProductCode( req.getProductCode() );
        editAndSendTmPermitTestReportCommand.setProductName( req.getProductName() );
        editAndSendTmPermitTestReportCommand.setPreview( req.getPreview() );
        if ( req.getTestResult() != null ) {
            editAndSendTmPermitTestReportCommand.setTestResult( Enum.valueOf( TmTestResultEnum.class, req.getTestResult() ) );
        }
        editAndSendTmPermitTestReportCommand.setActualApprovalExitDate( req.getActualApprovalExitDate() );
        editAndSendTmPermitTestReportCommand.setDelayDays( req.getDelayDays() );
        editAndSendTmPermitTestReportCommand.setSecurityUserId( req.getSecurityUserId() );
        editAndSendTmPermitTestReportCommand.setSecurityUserName( req.getSecurityUserName() );
        editAndSendTmPermitTestReportCommand.setSecurityTestResultCode( req.getSecurityTestResultCode() );
        editAndSendTmPermitTestReportCommand.setSecurityTestResultDesc( req.getSecurityTestResultDesc() );
        editAndSendTmPermitTestReportCommand.setSummary( req.getSummary() );
        List<TmModuleTestVO> list = req.getModuleTestVOS();
        if ( list != null ) {
            editAndSendTmPermitTestReportCommand.setModuleTestVOS( new ArrayList<TmModuleTestVO>( list ) );
        }
        editAndSendTmPermitTestReportCommand.setUiTestResult( req.getUiTestResult() );
        editAndSendTmPermitTestReportCommand.setZuiTestResult( req.getZuiTestResult() );
        List<SendUserInfoVO> list1 = req.getReceiveUsers();
        if ( list1 != null ) {
            editAndSendTmPermitTestReportCommand.setReceiveUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        List<SendUserInfoVO> list2 = req.getCcUsers();
        if ( list2 != null ) {
            editAndSendTmPermitTestReportCommand.setCcUsers( new ArrayList<SendUserInfoVO>( list2 ) );
        }
        List<CoverageReasonVO> list3 = req.getCoverageReasonVOS();
        if ( list3 != null ) {
            editAndSendTmPermitTestReportCommand.setCoverageReasonVOS( new ArrayList<CoverageReasonVO>( list3 ) );
        }

        return editAndSendTmPermitTestReportCommand;
    }

    @Override
    public PageReportMqQuery convert(PageReportReq req) {
        if ( req == null ) {
            return null;
        }

        PageReportMqQuery pageReportMqQuery = new PageReportMqQuery();

        pageReportMqQuery.setGmtCreateStart( req.getGmtCreateStart() );
        pageReportMqQuery.setGmtCreateEnd( req.getGmtCreateEnd() );
        pageReportMqQuery.setVersionCode( req.getVersionCode() );
        pageReportMqQuery.setTransactor( req.getTransactor() );

        return pageReportMqQuery;
    }

    @Override
    public QueryPermitResultResp converter(QueryPermitResultVO vo) {
        if ( vo == null ) {
            return null;
        }

        QueryPermitResultResp queryPermitResultResp = new QueryPermitResultResp();

        queryPermitResultResp.setTestResult( vo.getTestResult() );
        queryPermitResultResp.setSentFlag( vo.getSentFlag() );

        return queryPermitResultResp;
    }

    @Override
    public ComputeMetricsCommand convert(ComputeMetricsReq vo) {
        if ( vo == null ) {
            return null;
        }

        String aggregateId = null;

        ComputeMetricsCommand computeMetricsCommand = new ComputeMetricsCommand( aggregateId );

        List<String> list = vo.getVersionCodes();
        if ( list != null ) {
            computeMetricsCommand.setVersionCodes( new ArrayList<String>( list ) );
        }

        return computeMetricsCommand;
    }
}
