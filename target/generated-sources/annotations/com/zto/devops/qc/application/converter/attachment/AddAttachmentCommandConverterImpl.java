package com.zto.devops.qc.application.converter.attachment;

import com.zto.devops.qc.client.model.issue.command.AddAttachmentCommand;
import com.zto.devops.qc.client.service.attachment.model.AddAttachmentReq;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AddAttachmentCommandConverterImpl implements AddAttachmentCommandConverter {

    @Override
    public AddAttachmentCommand convert(AddAttachmentReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        AddAttachmentCommand addAttachmentCommand = new AddAttachmentCommand( aggregateId );

        addAttachmentCommand.setBusinessCode( req.getBusinessCode() );
        addAttachmentCommand.setUrl( req.getUrl() );
        addAttachmentCommand.setName( req.getName() );
        addAttachmentCommand.setRemoteFileId( req.getRemoteFileId() );
        addAttachmentCommand.setType( req.getType() );

        return addAttachmentCommand;
    }
}
