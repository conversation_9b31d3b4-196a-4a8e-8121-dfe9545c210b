package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.EmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.VersionEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.query.DetailEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.PageEmailQuery;
import com.zto.devops.qc.client.service.testmanager.email.model.DetailEmailReq;
import com.zto.devops.qc.client.service.testmanager.email.model.DetailEmailResp;
import com.zto.devops.qc.client.service.testmanager.email.model.EmailResp;
import com.zto.devops.qc.client.service.testmanager.email.model.PageEmailReq;
import com.zto.devops.qc.client.service.testmanager.email.model.PageEmailResp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class EmailQueryConverterImpl implements EmailQueryConverter {

    @Override
    public DetailEmailQuery convert(DetailEmailReq req) {
        if ( req == null ) {
            return null;
        }

        DetailEmailQuery detailEmailQuery = new DetailEmailQuery();

        detailEmailQuery.setEmailCode( req.getEmailCode() );
        detailEmailQuery.setBusinessCode( req.getBusinessCode() );
        detailEmailQuery.setRelatePlanCode( req.getRelatePlanCode() );
        detailEmailQuery.setEmailType( req.getEmailType() );

        return detailEmailQuery;
    }

    @Override
    public List<EmailResp> convertor(Collection<VersionEmailVO> versionEmailVOS) {
        if ( versionEmailVOS == null ) {
            return null;
        }

        List<EmailResp> list = new ArrayList<EmailResp>( versionEmailVOS.size() );
        for ( VersionEmailVO versionEmailVO : versionEmailVOS ) {
            list.add( versionEmailVOToEmailResp( versionEmailVO ) );
        }

        return list;
    }

    @Override
    public DetailEmailResp convert(DetailEmailVO vo) {
        if ( vo == null ) {
            return null;
        }

        DetailEmailResp detailEmailResp = new DetailEmailResp();

        detailEmailResp.setEmailCode( vo.getEmailCode() );
        detailEmailResp.setPreview( vo.getPreview() );
        List<SendUserInfoVO> list = vo.getReceiveUsers();
        if ( list != null ) {
            detailEmailResp.setReceiveUsers( new ArrayList<SendUserInfoVO>( list ) );
        }
        List<SendUserInfoVO> list1 = vo.getCcUsers();
        if ( list1 != null ) {
            detailEmailResp.setCcUsers( new ArrayList<SendUserInfoVO>( list1 ) );
        }
        detailEmailResp.setButtonVOS( vo.getButtonVOS() );
        detailEmailResp.setEmailSource( vo.getEmailSource() );
        detailEmailResp.setBusinessCode( vo.getBusinessCode() );
        detailEmailResp.setProductCode( vo.getProductCode() );
        detailEmailResp.setVersionCode( vo.getVersionCode() );
        detailEmailResp.setRelatePlanCode( vo.getRelatePlanCode() );
        detailEmailResp.setSendTime( vo.getSendTime() );
        detailEmailResp.setSendUserName( vo.getSendUserName() );
        detailEmailResp.setSendUserId( vo.getSendUserId() );
        List<AttachmentVO> list2 = vo.getAttachments();
        if ( list2 != null ) {
            detailEmailResp.setAttachments( new ArrayList<AttachmentVO>( list2 ) );
        }

        return detailEmailResp;
    }

    @Override
    public PageEmailQuery convert(PageEmailReq req) {
        if ( req == null ) {
            return null;
        }

        PageEmailQuery pageEmailQuery = new PageEmailQuery();

        pageEmailQuery.setTransactor( req.getTransactor() );
        pageEmailQuery.setPage( req.getPage() );
        pageEmailQuery.setSize( req.getSize() );
        List<String> list = req.getTypeList();
        if ( list != null ) {
            pageEmailQuery.setTypeList( new ArrayList<String>( list ) );
        }
        List<Long> list1 = req.getSendUserIdList();
        if ( list1 != null ) {
            pageEmailQuery.setSendUserIdList( new ArrayList<Long>( list1 ) );
        }
        List<String> list2 = req.getVersionCodeList();
        if ( list2 != null ) {
            pageEmailQuery.setVersionCodeList( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = req.getPlanCodeList();
        if ( list3 != null ) {
            pageEmailQuery.setPlanCodeList( new ArrayList<String>( list3 ) );
        }
        pageEmailQuery.setSendTimeStart( req.getSendTimeStart() );
        pageEmailQuery.setSendTimeEnd( req.getSendTimeEnd() );
        pageEmailQuery.setNameOrCode( req.getNameOrCode() );
        pageEmailQuery.setEmailSourceEnum( req.getEmailSourceEnum() );
        pageEmailQuery.setIsPersonal( req.getIsPersonal() );
        pageEmailQuery.setProductCode( req.getProductCode() );

        return pageEmailQuery;
    }

    @Override
    public List<PageEmailResp> convert(List<EmailVO> emailVOS) {
        if ( emailVOS == null ) {
            return null;
        }

        List<PageEmailResp> list = new ArrayList<PageEmailResp>( emailVOS.size() );
        for ( EmailVO emailVO : emailVOS ) {
            list.add( emailVOToPageEmailResp( emailVO ) );
        }

        return list;
    }

    protected EmailResp versionEmailVOToEmailResp(VersionEmailVO versionEmailVO) {
        if ( versionEmailVO == null ) {
            return null;
        }

        EmailResp emailResp = new EmailResp();

        emailResp.setEmailCode( versionEmailVO.getEmailCode() );
        emailResp.setEmailName( versionEmailVO.getEmailName() );
        emailResp.setBusinessCode( versionEmailVO.getBusinessCode() );
        emailResp.setEmailType( versionEmailVO.getEmailType() );
        emailResp.setEmailTypeDesc( versionEmailVO.getEmailTypeDesc() );
        emailResp.setVersionName( versionEmailVO.getVersionName() );
        emailResp.setVersionCode( versionEmailVO.getVersionCode() );
        emailResp.setProductCode( versionEmailVO.getProductCode() );
        emailResp.setProductName( versionEmailVO.getProductName() );
        emailResp.setGmtCreate( versionEmailVO.getGmtCreate() );
        emailResp.setGmtModified( versionEmailVO.getGmtModified() );
        emailResp.setCreator( versionEmailVO.getCreator() );
        emailResp.setCreatorId( versionEmailVO.getCreatorId() );
        emailResp.setModifier( versionEmailVO.getModifier() );
        emailResp.setModifierId( versionEmailVO.getModifierId() );
        emailResp.setSender( versionEmailVO.getSender() );
        emailResp.setSenderId( versionEmailVO.getSenderId() );
        emailResp.setSendDate( versionEmailVO.getSendDate() );
        emailResp.setSource( versionEmailVO.getSource() );

        return emailResp;
    }

    protected PageEmailResp emailVOToPageEmailResp(EmailVO emailVO) {
        if ( emailVO == null ) {
            return null;
        }

        PageEmailResp pageEmailResp = new PageEmailResp();

        pageEmailResp.setEmailCode( emailVO.getEmailCode() );
        pageEmailResp.setEmailName( emailVO.getEmailName() );
        pageEmailResp.setBusinessCode( emailVO.getBusinessCode() );
        pageEmailResp.setEmailTypeEnum( emailVO.getEmailTypeEnum() );
        pageEmailResp.setVersionDesc( emailVO.getVersionDesc() );
        pageEmailResp.setVersionCode( emailVO.getVersionCode() );
        pageEmailResp.setRelatePlanCode( emailVO.getRelatePlanCode() );
        pageEmailResp.setRelatePlanName( emailVO.getRelatePlanName() );
        pageEmailResp.setSendTime( emailVO.getSendTime() );
        pageEmailResp.setSendUserName( emailVO.getSendUserName() );
        pageEmailResp.setSendUserId( emailVO.getSendUserId() );

        return pageEmailResp;
    }
}
