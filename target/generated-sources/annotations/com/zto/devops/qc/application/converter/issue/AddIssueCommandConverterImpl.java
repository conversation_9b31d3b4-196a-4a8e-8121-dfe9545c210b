package com.zto.devops.qc.application.converter.issue;

import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.model.issue.command.AddIssueCommand;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.service.issue.model.AddIssueReq;
import com.zto.devops.qc.client.service.issue.model.SlowSqlToIssueReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AddIssueCommandConverterImpl implements AddIssueCommandConverter {

    @Override
    public void convert(AddIssueReq addIssueReq, AddIssueCommand command) {
        if ( addIssueReq == null ) {
            return;
        }

        if ( command.getHandler() == null ) {
            command.setHandler( new User() );
        }
        addIssueReqToUser( addIssueReq, command.getHandler() );
        if ( command.getDeveloper() == null ) {
            command.setDeveloper( new User() );
        }
        addIssueReqToUser1( addIssueReq, command.getDeveloper() );
        if ( command.getProduct() == null ) {
            command.setProduct( new Product() );
        }
        addIssueReqToProduct( addIssueReq, command.getProduct() );
        if ( command.getRequirement() == null ) {
            command.setRequirement( new Requirement() );
        }
        addIssueReqToRequirement( addIssueReq, command.getRequirement() );
        if ( command.getFindVersion() == null ) {
            command.setFindVersion( new Version() );
        }
        addIssueReqToVersion( addIssueReq, command.getFindVersion() );
        if ( command.getSprint() == null ) {
            command.setSprint( new Sprint() );
        }
        addIssueReqToSprint( addIssueReq, command.getSprint() );
        command.setTitle( addIssueReq.getTitle() );
        command.setDescription( addIssueReq.getDescription() );
        command.setFindEnv( addIssueReq.getFindEnv() );
        command.setFindStage( addIssueReq.getFindStage() );
        command.setPriority( addIssueReq.getPriority() );
        command.setRepetitionRate( addIssueReq.getRepetitionRate() );
        command.setRootCause( addIssueReq.getRootCause() );
        command.setTestMethod( addIssueReq.getTestMethod() );
        command.setType( addIssueReq.getType() );
        command.setRequirementLevel( addIssueReq.getRequirementLevel() );
        if ( command.getAttachments() != null ) {
            List<AttachmentVO> list = addIssueReq.getAttachments();
            if ( list != null ) {
                command.getAttachments().clear();
                command.getAttachments().addAll( list );
            }
            else {
                command.setAttachments( null );
            }
        }
        else {
            List<AttachmentVO> list = addIssueReq.getAttachments();
            if ( list != null ) {
                command.setAttachments( new ArrayList<AttachmentVO>( list ) );
            }
        }
        if ( command.getTags() != null ) {
            List<TagVO> list1 = addIssueReq.getTags();
            if ( list1 != null ) {
                command.getTags().clear();
                command.getTags().addAll( list1 );
            }
            else {
                command.setTags( null );
            }
        }
        else {
            List<TagVO> list1 = addIssueReq.getTags();
            if ( list1 != null ) {
                command.setTags( new ArrayList<TagVO>( list1 ) );
            }
        }
        command.setApplicationType( addIssueReq.getApplicationType() );
        if ( command.getTestcaseCodes() != null ) {
            List<String> list2 = addIssueReq.getTestcaseCodes();
            if ( list2 != null ) {
                command.getTestcaseCodes().clear();
                command.getTestcaseCodes().addAll( list2 );
            }
            else {
                command.setTestcaseCodes( null );
            }
        }
        else {
            List<String> list2 = addIssueReq.getTestcaseCodes();
            if ( list2 != null ) {
                command.setTestcaseCodes( new ArrayList<String>( list2 ) );
            }
        }
        command.setMsgCode( addIssueReq.getMsgCode() );
        command.setProductCode( addIssueReq.getProductCode() );
        command.setProductName( addIssueReq.getProductName() );
        command.setTestUserId( addIssueReq.getTestUserId() );
        command.setTestUserName( addIssueReq.getTestUserName() );
    }

    @Override
    public void convert(SlowSqlToIssueReq req, AddIssueCommand command) {
        if ( req == null ) {
            return;
        }

        if ( command.getProduct() == null ) {
            command.setProduct( new Product() );
        }
        slowSqlToIssueReqToProduct( req, command.getProduct() );
        command.setTitle( req.getTitle() );
        command.setDescription( req.getDescription() );
        command.setFindEnv( req.getFindEnv() );
        command.setFindStage( req.getFindStage() );
        command.setPriority( req.getPriority() );
        command.setRepetitionRate( req.getRepetitionRate() );
        command.setRootCause( req.getRootCause() );
        command.setTestMethod( req.getTestMethod() );
        command.setType( req.getType() );
        command.setProductCode( req.getProductCode() );
        command.setProductName( req.getProductName() );
        command.setTestUserId( req.getTestUserId() );
        command.setTestUserName( req.getTestUserName() );
        command.setStartAt( req.getStartAt() );
        command.setLocation( req.getLocation() );
        command.setAppId( req.getAppId() );
    }

    protected void addIssueReqToUser(AddIssueReq addIssueReq, User mappingTarget) {
        if ( addIssueReq == null ) {
            return;
        }

        mappingTarget.setUserId( addIssueReq.getDevelopUserId() );
        mappingTarget.setUserName( addIssueReq.getDevelopUserName() );
    }

    protected void addIssueReqToUser1(AddIssueReq addIssueReq, User mappingTarget) {
        if ( addIssueReq == null ) {
            return;
        }

        mappingTarget.setUserId( addIssueReq.getDevelopUserId() );
        mappingTarget.setUserName( addIssueReq.getDevelopUserName() );
    }

    protected void addIssueReqToProduct(AddIssueReq addIssueReq, Product mappingTarget) {
        if ( addIssueReq == null ) {
            return;
        }

        mappingTarget.setCode( addIssueReq.getProductCode() );
        mappingTarget.setName( addIssueReq.getProductName() );
    }

    protected void addIssueReqToRequirement(AddIssueReq addIssueReq, Requirement mappingTarget) {
        if ( addIssueReq == null ) {
            return;
        }

        mappingTarget.setCode( addIssueReq.getRequirementCode() );
        mappingTarget.setName( addIssueReq.getRequirementName() );
    }

    protected void addIssueReqToVersion(AddIssueReq addIssueReq, Version mappingTarget) {
        if ( addIssueReq == null ) {
            return;
        }

        mappingTarget.setCode( addIssueReq.getFindVersionCode() );
        mappingTarget.setName( addIssueReq.getFindVersionName() );
    }

    protected void addIssueReqToSprint(AddIssueReq addIssueReq, Sprint mappingTarget) {
        if ( addIssueReq == null ) {
            return;
        }

        mappingTarget.setName( addIssueReq.getSprintName() );
        mappingTarget.setCode( addIssueReq.getSprintCode() );
    }

    protected void slowSqlToIssueReqToProduct(SlowSqlToIssueReq slowSqlToIssueReq, Product mappingTarget) {
        if ( slowSqlToIssueReq == null ) {
            return;
        }

        mappingTarget.setCode( slowSqlToIssueReq.getProductCode() );
        mappingTarget.setName( slowSqlToIssueReq.getProductName() );
    }
}
