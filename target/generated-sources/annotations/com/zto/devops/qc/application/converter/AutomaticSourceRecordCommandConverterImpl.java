package com.zto.devops.qc.application.converter;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddAutomaticRecordLogCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditAutomaticPersonLiableCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordLogVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.service.testmanager.cases.model.AddAutomaticRecordReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.AutomaticRecordResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.EditAutomaticPersonLiableReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.EditAutomaticRecordReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class AutomaticSourceRecordCommandConverterImpl implements AutomaticSourceRecordCommandConverter {

    @Override
    public AddAutomaticRecordCommand convert(AddAutomaticRecordReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddAutomaticRecordCommand addAutomaticRecordCommand = new AddAutomaticRecordCommand( aggregateId1 );

        if ( req != null ) {
            addAutomaticRecordCommand.setAutoAnalysisFlag( req.getAutoAnalysisFlag() );
            addAutomaticRecordCommand.setProductCode( req.getProductCode() );
            addAutomaticRecordCommand.setName( req.getName() );
            addAutomaticRecordCommand.setComment( req.getComment() );
            addAutomaticRecordCommand.setAddress( req.getAddress() );
            addAutomaticRecordCommand.setType( req.getType() );
            addAutomaticRecordCommand.setFileName( req.getFileName() );
            addAutomaticRecordCommand.setDataFileAddress( req.getDataFileAddress() );
            addAutomaticRecordCommand.setExtendJarAddress( req.getExtendJarAddress() );
            addAutomaticRecordCommand.setThirdJarAddress( req.getThirdJarAddress() );
            addAutomaticRecordCommand.setBucketName( req.getBucketName() );
            addAutomaticRecordCommand.setTestcaseCode( req.getTestcaseCode() );
            addAutomaticRecordCommand.setPath( req.getPath() );
            addAutomaticRecordCommand.setWorkSpace( req.getWorkSpace() );
            addAutomaticRecordCommand.setBranch( req.getBranch() );
            addAutomaticRecordCommand.setScanDirectory( req.getScanDirectory() );
            addAutomaticRecordCommand.setCommitId( req.getCommitId() );
        }

        return addAutomaticRecordCommand;
    }

    @Override
    public EditAutomaticRecordCommand convert(EditAutomaticRecordReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getCode();

        EditAutomaticRecordCommand editAutomaticRecordCommand = new EditAutomaticRecordCommand( aggregateId );

        editAutomaticRecordCommand.setAutoAnalysisFlag( req.getAutoAnalysisFlag() );
        editAutomaticRecordCommand.setType( req.getType() );
        editAutomaticRecordCommand.setProductCode( req.getProductCode() );
        editAutomaticRecordCommand.setName( req.getName() );
        editAutomaticRecordCommand.setComment( req.getComment() );
        editAutomaticRecordCommand.setAddress( req.getAddress() );
        editAutomaticRecordCommand.setFileName( req.getFileName() );
        editAutomaticRecordCommand.setDataFileAddress( req.getDataFileAddress() );
        editAutomaticRecordCommand.setExtendJarAddress( req.getExtendJarAddress() );
        editAutomaticRecordCommand.setThirdJarAddress( req.getThirdJarAddress() );
        editAutomaticRecordCommand.setBucketName( req.getBucketName() );
        editAutomaticRecordCommand.setTestcaseCode( req.getTestcaseCode() );
        editAutomaticRecordCommand.setPath( req.getPath() );
        editAutomaticRecordCommand.setCode( req.getCode() );
        editAutomaticRecordCommand.setWorkSpace( req.getWorkSpace() );
        editAutomaticRecordCommand.setBranch( req.getBranch() );
        editAutomaticRecordCommand.setScanDirectory( req.getScanDirectory() );
        editAutomaticRecordCommand.setCommitId( req.getCommitId() );

        return editAutomaticRecordCommand;
    }

    @Override
    public AddAutomaticRecordLogCommand convert(EditAutomaticRecordCommand command) {
        if ( command == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = command.getAggregateId();

        AddAutomaticRecordLogCommand addAutomaticRecordLogCommand = new AddAutomaticRecordLogCommand( aggregateId );

        addAutomaticRecordLogCommand.setTransactor( command.getTransactor() );
        addAutomaticRecordLogCommand.setAutoAnalysisFlag( command.getAutoAnalysisFlag() );
        addAutomaticRecordLogCommand.setProductCode( command.getProductCode() );
        addAutomaticRecordLogCommand.setName( command.getName() );
        addAutomaticRecordLogCommand.setComment( command.getComment() );
        addAutomaticRecordLogCommand.setAddress( command.getAddress() );
        addAutomaticRecordLogCommand.setType( command.getType() );
        addAutomaticRecordLogCommand.setFileName( command.getFileName() );
        addAutomaticRecordLogCommand.setDataFileAddress( command.getDataFileAddress() );
        addAutomaticRecordLogCommand.setExtendJarAddress( command.getExtendJarAddress() );
        addAutomaticRecordLogCommand.setThirdJarAddress( command.getThirdJarAddress() );
        addAutomaticRecordLogCommand.setBucketName( command.getBucketName() );
        addAutomaticRecordLogCommand.setLastAutomaticSourceLogCode( command.getLastAutomaticSourceLogCode() );
        addAutomaticRecordLogCommand.setTestcaseCode( command.getTestcaseCode() );
        addAutomaticRecordLogCommand.setPath( command.getPath() );
        addAutomaticRecordLogCommand.setWorkSpace( command.getWorkSpace() );
        addAutomaticRecordLogCommand.setBranch( command.getBranch() );
        addAutomaticRecordLogCommand.setScanDirectory( command.getScanDirectory() );
        addAutomaticRecordLogCommand.setCommitId( command.getCommitId() );
        if ( command.getStatus() != null ) {
            addAutomaticRecordLogCommand.setStatus( command.getStatus().name() );
        }

        return addAutomaticRecordLogCommand;
    }

    @Override
    public EditAutomaticRecordCommand convert(AutomaticRecordVO vo) {
        if ( vo == null ) {
            return null;
        }

        String aggregateId = null;

        EditAutomaticRecordCommand editAutomaticRecordCommand = new EditAutomaticRecordCommand( aggregateId );

        editAutomaticRecordCommand.setAutoAnalysisFlag( vo.getAutoAnalysisFlag() );
        editAutomaticRecordCommand.setType( vo.getType() );
        editAutomaticRecordCommand.setProductCode( vo.getProductCode() );
        editAutomaticRecordCommand.setName( vo.getName() );
        editAutomaticRecordCommand.setComment( vo.getComment() );
        editAutomaticRecordCommand.setAddress( vo.getAddress() );
        editAutomaticRecordCommand.setFileName( vo.getFileName() );
        editAutomaticRecordCommand.setDataFileAddress( vo.getDataFileAddress() );
        editAutomaticRecordCommand.setExtendJarAddress( vo.getExtendJarAddress() );
        editAutomaticRecordCommand.setThirdJarAddress( vo.getThirdJarAddress() );
        editAutomaticRecordCommand.setBucketName( vo.getBucketName() );
        editAutomaticRecordCommand.setTestcaseCode( vo.getTestcaseCode() );
        editAutomaticRecordCommand.setCode( vo.getCode() );
        editAutomaticRecordCommand.setStatus( vo.getStatus() );
        editAutomaticRecordCommand.setWorkSpace( vo.getWorkSpace() );
        editAutomaticRecordCommand.setBranch( vo.getBranch() );
        editAutomaticRecordCommand.setScanDirectory( vo.getScanDirectory() );
        editAutomaticRecordCommand.setCommitId( vo.getCommitId() );

        return editAutomaticRecordCommand;
    }

    @Override
    public EditAutomaticPersonLiableCommand convertor(EditAutomaticPersonLiableReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getCode();

        EditAutomaticPersonLiableCommand editAutomaticPersonLiableCommand = new EditAutomaticPersonLiableCommand( aggregateId );

        editAutomaticPersonLiableCommand.setCode( req.getCode() );
        editAutomaticPersonLiableCommand.setPersonLiableId( req.getPersonLiableId() );
        editAutomaticPersonLiableCommand.setPersonLiable( req.getPersonLiable() );

        return editAutomaticPersonLiableCommand;
    }

    @Override
    public AutomaticRecordResp convertor(AutomaticRecordVO vo) {
        if ( vo == null ) {
            return null;
        }

        AutomaticRecordResp automaticRecordResp = new AutomaticRecordResp();

        automaticRecordResp.setCode( vo.getCode() );
        automaticRecordResp.setProductCode( vo.getProductCode() );
        automaticRecordResp.setName( vo.getName() );
        automaticRecordResp.setComment( vo.getComment() );
        automaticRecordResp.setAddress( vo.getAddress() );
        automaticRecordResp.setType( vo.getType() );
        automaticRecordResp.setCreatorId( vo.getCreatorId() );
        automaticRecordResp.setCreator( vo.getCreator() );
        automaticRecordResp.setGmtCreate( vo.getGmtCreate() );
        automaticRecordResp.setModifierId( vo.getModifierId() );
        automaticRecordResp.setModifier( vo.getModifier() );
        automaticRecordResp.setGmtModified( vo.getGmtModified() );
        automaticRecordResp.setFileName( vo.getFileName() );
        automaticRecordResp.setBucketName( vo.getBucketName() );
        automaticRecordResp.setDataFileAddress( vo.getDataFileAddress() );
        automaticRecordResp.setExtendJarAddress( vo.getExtendJarAddress() );
        automaticRecordResp.setThirdJarAddress( vo.getThirdJarAddress() );
        automaticRecordResp.setFailInformation( vo.getFailInformation() );
        automaticRecordResp.setStatus( vo.getStatus() );
        automaticRecordResp.setPersonLiableId( vo.getPersonLiableId() );
        automaticRecordResp.setPersonLiable( vo.getPersonLiable() );
        List<Button> list = vo.getButtons();
        if ( list != null ) {
            automaticRecordResp.setButtons( new ArrayList<Button>( list ) );
        }
        List<AutomaticRecordLogVO> list1 = vo.getLogList();
        if ( list1 != null ) {
            automaticRecordResp.setLogList( new ArrayList<AutomaticRecordLogVO>( list1 ) );
        }
        automaticRecordResp.setTestcaseCode( vo.getTestcaseCode() );
        automaticRecordResp.setTestcaseVO( vo.getTestcaseVO() );
        automaticRecordResp.setScanDirectory( vo.getScanDirectory() );
        automaticRecordResp.setWorkSpace( vo.getWorkSpace() );
        automaticRecordResp.setBranch( vo.getBranch() );
        automaticRecordResp.setCommitId( vo.getCommitId() );
        automaticRecordResp.setErrorLogFile( vo.getErrorLogFile() );
        automaticRecordResp.setDataDir( vo.getDataDir() );
        automaticRecordResp.setLibDir( vo.getLibDir() );
        automaticRecordResp.setAutoAnalysisFlag( vo.getAutoAnalysisFlag() );

        return automaticRecordResp;
    }
}
