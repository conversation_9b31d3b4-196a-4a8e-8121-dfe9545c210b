package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.testmanager.apitest.command.SceneBatchAddToTestPlanCommand;
import com.zto.devops.qc.client.model.testmanager.plan.entity.BatchTestPlanVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneBatchToTestPlanReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class SceneConvertorImpl implements SceneConvertor {

    @Override
    public SceneBatchAddToTestPlanCommand convertor(SceneBatchToTestPlanReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        SceneBatchAddToTestPlanCommand sceneBatchAddToTestPlanCommand = new SceneBatchAddToTestPlanCommand( aggregateId );

        List<String> list = req.getSceneCodeList();
        if ( list != null ) {
            sceneBatchAddToTestPlanCommand.setSceneCodeList( new ArrayList<String>( list ) );
        }
        List<BatchTestPlanVO> list1 = req.getTestPlanList();
        if ( list1 != null ) {
            sceneBatchAddToTestPlanCommand.setTestPlanList( new ArrayList<BatchTestPlanVO>( list1 ) );
        }
        sceneBatchAddToTestPlanCommand.setProductCode( req.getProductCode() );

        return sceneBatchAddToTestPlanCommand;
    }
}
