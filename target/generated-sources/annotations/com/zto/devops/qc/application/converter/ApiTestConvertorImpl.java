package com.zto.devops.qc.application.converter;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddApiGlobalConfigurationCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddApiTestCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddApiTestVariableCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddPreDataModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddSceneModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddSceneTagCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.ApiTestRevokeCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.BatchAddPreDataVariableCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.BatchAddVariableCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.BatchChangeApiCaseStatusCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.BatchDeleteSceneCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.BatchMoveSceneCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.BatchPublishApiTestCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.ChangeApiCaseStatusCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.CheckUserPermisionCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.CopyApiTestCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.CopySceneInfoCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.DbAuthorizeCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditApiTestCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditApiTestVariableCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditPreDataModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.EditSceneModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.ExecuteApiCallBackCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.ExecuteApiCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.ExecuteApiTestCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.GenerateApiCaseExceptionCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.GetApiTestVariableResultCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.JmxFileUploadCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.MovePreDataModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.MoveSceneModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.PublishApiTestCaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SaveDebugInfoCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.UpdateApiCaseExceptionCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.UpdateApiTestVariableStatusCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.UpdateSceneStepRecordCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.VerifyBatchOperationCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiFieldConfigVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiGlobalConfigurationVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeDbVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeProductVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.DebugNodeInfoVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneTagVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.AddApiGlobalConfigurationReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.AddApiTestCaseReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.AddApiTestVariableReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.AddPreDataModuleReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.AddSceneModuleReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.AddSceneTagReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ApiTestAuthorizeReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ApiTestRevokeReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BaseApiTestVariableReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BasePreDataVariableReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BatchAddPreDataVariableReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BatchAddVariableReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BatchChangeApiCaseStatusReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BatchDeleteSceneReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BatchMoveSceneReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BatchPublishApiTestCaseReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ChangeApiCaseStatusReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.CheckUserPermisionReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.CopyApiTestCaseReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.CopySceneInfoReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.EditApiTestCaseReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.EditApiTestVariableReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.EditPreDataModuleReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.EditSceneModuleReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ExecuteApiCallBackReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.GenerateApiCaseExceptionReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.GetApiTestVariableResultReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.JmxFileUploadReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.MovePreDataModuleReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.MoveSceneModuleReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiTestVariableResp;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PublishApiTestCaseReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SaveDebugLinkReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneInfoResp;
import com.zto.devops.qc.client.service.testmanager.apitest.model.UpdateApiCaseExceptionReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.UpdateApiTestVariableStatusReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.UpdateSceneStepRecordReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.VerifyBatchOperationReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteApiCaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteApiTestReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:16+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class ApiTestConvertorImpl implements ApiTestConvertor {

    @Override
    public AddApiTestVariableCommand convertor(AddApiTestVariableReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddApiTestVariableCommand addApiTestVariableCommand = new AddApiTestVariableCommand( aggregateId1 );

        if ( req != null ) {
            addApiTestVariableCommand.setProductCode( req.getProductCode() );
            addApiTestVariableCommand.setLinkCode( req.getLinkCode() );
            addApiTestVariableCommand.setVariableName( req.getVariableName() );
            addApiTestVariableCommand.setVariableKey( req.getVariableKey() );
            addApiTestVariableCommand.setVariableValue( req.getVariableValue() );
            addApiTestVariableCommand.setType( req.getType() );
            addApiTestVariableCommand.setLoginValidTime( req.getLoginValidTime() );
        }
        addApiTestVariableCommand.setSubVariableType( com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum.valueOf(req.getSubVariableType()) );

        return addApiTestVariableCommand;
    }

    @Override
    public EditApiTestVariableCommand convertor(EditApiTestVariableReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getVariableCode();

        EditApiTestVariableCommand editApiTestVariableCommand = new EditApiTestVariableCommand( aggregateId );

        editApiTestVariableCommand.setProductCode( req.getProductCode() );
        editApiTestVariableCommand.setProductName( req.getProductName() );
        editApiTestVariableCommand.setLinkCode( req.getLinkCode() );
        editApiTestVariableCommand.setVariableCode( req.getVariableCode() );
        editApiTestVariableCommand.setVariableName( req.getVariableName() );
        editApiTestVariableCommand.setVariableKey( req.getVariableKey() );
        editApiTestVariableCommand.setVariableValue( req.getVariableValue() );
        editApiTestVariableCommand.setType( req.getType() );
        editApiTestVariableCommand.setLoginValidTime( req.getLoginValidTime() );

        editApiTestVariableCommand.setSubVariableType( com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum.valueOf(req.getSubVariableType()) );

        return editApiTestVariableCommand;
    }

    @Override
    public List<PageApiTestVariableResp> convertor(List<ApiTestVariableVO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<PageApiTestVariableResp> list = new ArrayList<PageApiTestVariableResp>( voList.size() );
        for ( ApiTestVariableVO apiTestVariableVO : voList ) {
            list.add( apiTestVariableVOToPageApiTestVariableResp( apiTestVariableVO ) );
        }

        return list;
    }

    @Override
    public UpdateApiTestVariableStatusCommand convertor(UpdateApiTestVariableStatusReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getVariableCode();

        UpdateApiTestVariableStatusCommand updateApiTestVariableStatusCommand = new UpdateApiTestVariableStatusCommand( aggregateId );

        updateApiTestVariableStatusCommand.setVariableCode( req.getVariableCode() );
        updateApiTestVariableStatusCommand.setVariableStatus( req.getVariableStatus() );

        return updateApiTestVariableStatusCommand;
    }

    @Override
    public GetApiTestVariableResultCommand convertor(GetApiTestVariableResultReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        GetApiTestVariableResultCommand getApiTestVariableResultCommand = new GetApiTestVariableResultCommand( aggregateId );

        getApiTestVariableResultCommand.setVariableKey( req.getVariableKey() );

        return getApiTestVariableResultCommand;
    }

    @Override
    public ExecuteApiTestCommand convertor(ExecuteApiTestReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        ExecuteApiTestCommand executeApiTestCommand = new ExecuteApiTestCommand( aggregateId );

        executeApiTestCommand.setProductCode( req.getProductCode() );
        executeApiTestCommand.setTrigMode( req.getTrigMode() );
        executeApiTestCommand.setEnv( req.getEnv() );
        executeApiTestCommand.setEnvName( req.getEnvName() );
        executeApiTestCommand.setIsDeploy( req.getIsDeploy() );
        executeApiTestCommand.setAutoEnv( req.getAutoEnv() );
        List<String> list = req.getApiCodes();
        if ( list != null ) {
            executeApiTestCommand.setApiCodes( new ArrayList<String>( list ) );
        }

        return executeApiTestCommand;
    }

    @Override
    public ExecuteApiCallBackCommand convertor(ExecuteApiCallBackReq req) {
        if ( req == null ) {
            return null;
        }

        ExecuteApiCallBackCommand executeApiCallBackCommand = new ExecuteApiCallBackCommand();

        executeApiCallBackCommand.setTaskCode( req.getTaskCode() );
        executeApiCallBackCommand.setCaseCode( req.getCaseCode() );
        executeApiCallBackCommand.setResult( req.getResult() );
        executeApiCallBackCommand.setPath( req.getPath() );

        return executeApiCallBackCommand;
    }

    @Override
    public ExecuteApiCaseCommand convertor(ExecuteApiCaseReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        ExecuteApiCaseCommand executeApiCaseCommand = new ExecuteApiCaseCommand( aggregateId );

        executeApiCaseCommand.setProductCode( req.getProductCode() );
        executeApiCaseCommand.setTrigMode( req.getTrigMode() );
        executeApiCaseCommand.setEnv( req.getEnv() );
        executeApiCaseCommand.setEnvName( req.getEnvName() );
        executeApiCaseCommand.setIsDeploy( req.getIsDeploy() );
        executeApiCaseCommand.setAutoEnv( req.getAutoEnv() );
        List<String> list = req.getCaseCodes();
        if ( list != null ) {
            executeApiCaseCommand.setCaseCodes( new ArrayList<String>( list ) );
        }

        return executeApiCaseCommand;
    }

    @Override
    public SceneInfoResp convertorSceneInfoVO(SceneInfoEntityDO entityDO) {
        if ( entityDO == null ) {
            return null;
        }

        SceneInfoResp sceneInfoResp = new SceneInfoResp();

        sceneInfoResp.setId( entityDO.getId() );
        sceneInfoResp.setProductCode( entityDO.getProductCode() );
        sceneInfoResp.setSceneCode( entityDO.getSceneCode() );
        sceneInfoResp.setSceneName( entityDO.getSceneName() );
        sceneInfoResp.setAutomaticSourceCode( entityDO.getAutomaticSourceCode() );
        if ( entityDO.getSceneVersion() != null ) {
            sceneInfoResp.setSceneVersion( String.valueOf( entityDO.getSceneVersion() ) );
        }
        sceneInfoResp.setSceneInfoDesc( entityDO.getSceneInfoDesc() );
        sceneInfoResp.setSceneOssPath( entityDO.getSceneOssPath() );
        sceneInfoResp.setSceneOssFile( entityDO.getSceneOssFile() );
        sceneInfoResp.setStepRecord( entityDO.getStepRecord() );
        sceneInfoResp.setStatus( entityDO.getStatus() );
        sceneInfoResp.setEnable( entityDO.getEnable() );
        sceneInfoResp.setModifier( entityDO.getModifier() );
        sceneInfoResp.setGmtModified( entityDO.getGmtModified() );
        sceneInfoResp.setEnableDesc( entityDO.getEnableDesc() );
        sceneInfoResp.setIsModified( entityDO.getIsModified() );
        sceneInfoResp.setPublishErrorMsg( entityDO.getPublishErrorMsg() );
        sceneInfoResp.setModifierId( entityDO.getModifierId() );
        sceneInfoResp.setSceneIndexType( entityDO.getSceneIndexType() );
        sceneInfoResp.setParentCode( entityDO.getParentCode() );
        sceneInfoResp.setCreator( entityDO.getCreator() );
        List<SceneTagVO> list = entityDO.getTagVOList();
        if ( list != null ) {
            sceneInfoResp.setTagVOList( new ArrayList<SceneTagVO>( list ) );
        }
        List<Button> list1 = entityDO.getButtonList();
        if ( list1 != null ) {
            sceneInfoResp.setButtonList( new ArrayList<Button>( list1 ) );
        }
        List<Button> list2 = entityDO.getCollapseButtonList();
        if ( list2 != null ) {
            sceneInfoResp.setCollapseButtonList( new ArrayList<Button>( list2 ) );
        }
        sceneInfoResp.setIsCheck( entityDO.getIsCheck() );
        sceneInfoResp.setShareStatus( entityDO.getShareStatus() );
        sceneInfoResp.setScenePath( entityDO.getScenePath() );

        sceneInfoResp.setTags( (entityDO.getSceneTagData() != null && !entityDO.getSceneTagData().isEmpty()) ? java.util.Arrays.stream(entityDO.getSceneTagData().split(",")).map(String::trim).collect(java.util.stream.Collectors.toList()) : java.util.Collections.emptyList() );

        return sceneInfoResp;
    }

    @Override
    public List<SceneInfoResp> convertorSceneInfoVOLsit(List<SceneInfoEntityDO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<SceneInfoResp> list = new ArrayList<SceneInfoResp>( voList.size() );
        for ( SceneInfoEntityDO sceneInfoEntityDO : voList ) {
            list.add( convertorSceneInfoVO( sceneInfoEntityDO ) );
        }

        return list;
    }

    @Override
    public CopySceneInfoCommand convertor(CopySceneInfoReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getSceneCode();

        CopySceneInfoCommand copySceneInfoCommand = new CopySceneInfoCommand( aggregateId );

        copySceneInfoCommand.setSceneCode( req.getSceneCode() );
        copySceneInfoCommand.setSceneName( req.getSceneName() );
        copySceneInfoCommand.setSceneInfoDesc( req.getSceneInfoDesc() );
        copySceneInfoCommand.setParentCode( req.getParentCode() );
        copySceneInfoCommand.setSceneType( req.getSceneType() );

        return copySceneInfoCommand;
    }

    @Override
    public AddSceneModuleCommand convertor(AddSceneModuleReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddSceneModuleCommand addSceneModuleCommand = new AddSceneModuleCommand( aggregateId1 );

        if ( req != null ) {
            addSceneModuleCommand.setProductCode( req.getProductCode() );
            addSceneModuleCommand.setParentCode( req.getParentCode() );
            addSceneModuleCommand.setSceneIndexName( req.getSceneIndexName() );
            addSceneModuleCommand.setSceneType( req.getSceneType() );
        }

        return addSceneModuleCommand;
    }

    @Override
    public AddPreDataModuleCommand convertor(AddPreDataModuleReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddPreDataModuleCommand addPreDataModuleCommand = new AddPreDataModuleCommand( aggregateId1 );

        if ( req != null ) {
            addPreDataModuleCommand.setProductCode( req.getProductCode() );
            addPreDataModuleCommand.setParentCode( req.getParentCode() );
            addPreDataModuleCommand.setSceneIndexName( req.getSceneIndexName() );
        }

        return addPreDataModuleCommand;
    }

    @Override
    public EditSceneModuleCommand convertor(EditSceneModuleReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        EditSceneModuleCommand editSceneModuleCommand = new EditSceneModuleCommand( aggregateId );

        editSceneModuleCommand.setProductCode( req.getProductCode() );
        editSceneModuleCommand.setCode( req.getCode() );
        editSceneModuleCommand.setSceneIndexName( req.getSceneIndexName() );

        return editSceneModuleCommand;
    }

    @Override
    public EditPreDataModuleCommand convertor(EditPreDataModuleReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        EditPreDataModuleCommand editPreDataModuleCommand = new EditPreDataModuleCommand( aggregateId );

        editPreDataModuleCommand.setProductCode( req.getProductCode() );
        editPreDataModuleCommand.setCode( req.getCode() );
        editPreDataModuleCommand.setSceneIndexName( req.getSceneIndexName() );

        return editPreDataModuleCommand;
    }

    @Override
    public MoveSceneModuleCommand convertor(MoveSceneModuleReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        MoveSceneModuleCommand moveSceneModuleCommand = new MoveSceneModuleCommand( aggregateId );

        moveSceneModuleCommand.setProductCode( req.getProductCode() );
        moveSceneModuleCommand.setCode( req.getCode() );
        moveSceneModuleCommand.setParentCode( req.getParentCode() );

        return moveSceneModuleCommand;
    }

    @Override
    public MovePreDataModuleCommand convertor(MovePreDataModuleReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        MovePreDataModuleCommand movePreDataModuleCommand = new MovePreDataModuleCommand( aggregateId );

        movePreDataModuleCommand.setProductCode( req.getProductCode() );
        movePreDataModuleCommand.setCode( req.getCode() );
        movePreDataModuleCommand.setParentCode( req.getParentCode() );

        return movePreDataModuleCommand;
    }

    @Override
    public BatchDeleteSceneCommand convertor(BatchDeleteSceneReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        BatchDeleteSceneCommand batchDeleteSceneCommand = new BatchDeleteSceneCommand( aggregateId );

        batchDeleteSceneCommand.setProductCode( req.getProductCode() );
        List<String> list = req.getSceneCodeList();
        if ( list != null ) {
            batchDeleteSceneCommand.setSceneCodeList( new ArrayList<String>( list ) );
        }
        batchDeleteSceneCommand.setSceneType( req.getSceneType() );

        return batchDeleteSceneCommand;
    }

    @Override
    public BatchMoveSceneCommand convertor(BatchMoveSceneReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        BatchMoveSceneCommand batchMoveSceneCommand = new BatchMoveSceneCommand( aggregateId );

        batchMoveSceneCommand.setProductCode( req.getProductCode() );
        List<String> list = req.getSceneCodeList();
        if ( list != null ) {
            batchMoveSceneCommand.setSceneCodeList( new ArrayList<String>( list ) );
        }
        batchMoveSceneCommand.setParentCode( req.getParentCode() );
        batchMoveSceneCommand.setSceneType( req.getSceneType() );

        return batchMoveSceneCommand;
    }

    @Override
    public BatchAddVariableCommand convertor(BatchAddVariableReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        BatchAddVariableCommand batchAddVariableCommand = new BatchAddVariableCommand( aggregateId );

        batchAddVariableCommand.setProductCode( req.getProductCode() );
        batchAddVariableCommand.setProductName( req.getProductName() );
        List<BaseApiTestVariableReq> list = req.getApiTestVariableList();
        if ( list != null ) {
            batchAddVariableCommand.setApiTestVariableList( new ArrayList<BaseApiTestVariableReq>( list ) );
        }
        batchAddVariableCommand.setType( req.getType() );
        batchAddVariableCommand.setLinkCode( req.getLinkCode() );
        List<BaseApiTestVariableReq> list1 = req.getApiTestHeaderList();
        if ( list1 != null ) {
            batchAddVariableCommand.setApiTestHeaderList( new ArrayList<BaseApiTestVariableReq>( list1 ) );
        }
        batchAddVariableCommand.setSceneType( req.getSceneType() );

        return batchAddVariableCommand;
    }

    @Override
    public UpdateSceneStepRecordCommand convertor(UpdateSceneStepRecordReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        UpdateSceneStepRecordCommand updateSceneStepRecordCommand = new UpdateSceneStepRecordCommand( aggregateId1 );

        if ( req != null ) {
            updateSceneStepRecordCommand.setProductCode( req.getProductCode() );
            updateSceneStepRecordCommand.setSceneId( req.getSceneId() );
            updateSceneStepRecordCommand.setStepRecord( req.getStepRecord() );
        }

        return updateSceneStepRecordCommand;
    }

    @Override
    public BatchAddPreDataVariableCommand convertor(BatchAddPreDataVariableReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        BatchAddPreDataVariableCommand batchAddPreDataVariableCommand = new BatchAddPreDataVariableCommand( aggregateId );

        batchAddPreDataVariableCommand.setProductCode( req.getProductCode() );
        batchAddPreDataVariableCommand.setProductName( req.getProductName() );
        List<BasePreDataVariableReq> list = req.getInputParameter();
        if ( list != null ) {
            batchAddPreDataVariableCommand.setInputParameter( new ArrayList<BasePreDataVariableReq>( list ) );
        }
        List<BasePreDataVariableReq> list1 = req.getOutputParameter();
        if ( list1 != null ) {
            batchAddPreDataVariableCommand.setOutputParameter( new ArrayList<BasePreDataVariableReq>( list1 ) );
        }
        batchAddPreDataVariableCommand.setType( req.getType() );
        batchAddPreDataVariableCommand.setLinkCode( req.getLinkCode() );

        return batchAddPreDataVariableCommand;
    }

    @Override
    public CheckUserPermisionCommand convertor(CheckUserPermisionReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        CheckUserPermisionCommand checkUserPermisionCommand = new CheckUserPermisionCommand( aggregateId );

        checkUserPermisionCommand.setProductCode( req.getProductCode() );

        return checkUserPermisionCommand;
    }

    @Override
    public DbAuthorizeCommand convertor(ApiTestAuthorizeReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        DbAuthorizeCommand dbAuthorizeCommand = new DbAuthorizeCommand( aggregateId );

        dbAuthorizeCommand.setProductCode( req.getProductCode() );
        List<ApiTestAuthorizeProductVO> list = req.getAuthorizeProductList();
        if ( list != null ) {
            dbAuthorizeCommand.setAuthorizeProductList( new ArrayList<ApiTestAuthorizeProductVO>( list ) );
        }
        List<ApiTestAuthorizeDbVO> list1 = req.getAuthorizeDbList();
        if ( list1 != null ) {
            dbAuthorizeCommand.setAuthorizeDbList( new ArrayList<ApiTestAuthorizeDbVO>( list1 ) );
        }

        return dbAuthorizeCommand;
    }

    @Override
    public ApiTestRevokeCommand convertor(ApiTestRevokeReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        ApiTestRevokeCommand apiTestRevokeCommand = new ApiTestRevokeCommand( aggregateId );

        apiTestRevokeCommand.setProductCode( req.getProductCode() );
        apiTestRevokeCommand.setAuthorizeProductCode( req.getAuthorizeProductCode() );

        return apiTestRevokeCommand;
    }

    @Override
    public SaveDebugInfoCommand convertor(SaveDebugLinkReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        SaveDebugInfoCommand saveDebugInfoCommand = new SaveDebugInfoCommand( aggregateId );

        saveDebugInfoCommand.setProductCode( req.getProductCode() );
        saveDebugInfoCommand.setSceneCode( req.getSceneCode() );
        saveDebugInfoCommand.setTaskId( req.getTaskId() );
        saveDebugInfoCommand.setDebugType( req.getDebugType() );
        saveDebugInfoCommand.setStatus( req.getStatus() );
        List<DebugNodeInfoVO> list = req.getLinkBaseInfo();
        if ( list != null ) {
            saveDebugInfoCommand.setLinkBaseInfo( new ArrayList<DebugNodeInfoVO>( list ) );
        }

        return saveDebugInfoCommand;
    }

    @Override
    public AddApiGlobalConfigurationCommand convertor(AddApiGlobalConfigurationReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        AddApiGlobalConfigurationCommand addApiGlobalConfigurationCommand = new AddApiGlobalConfigurationCommand( aggregateId );

        addApiGlobalConfigurationCommand.setProductCode( req.getProductCode() );
        List<ApiGlobalConfigurationVO> list = req.getApiGlobalConfigurationVOList();
        if ( list != null ) {
            addApiGlobalConfigurationCommand.setApiGlobalConfigurationVOList( new ArrayList<ApiGlobalConfigurationVO>( list ) );
        }

        return addApiGlobalConfigurationCommand;
    }

    @Override
    public AddApiTestCaseCommand convertor(AddApiTestCaseReq req, String aggregateId) {
        if ( req == null && aggregateId == null ) {
            return null;
        }

        String aggregateId1 = null;
        if ( aggregateId != null ) {
            aggregateId1 = aggregateId;
        }

        AddApiTestCaseCommand addApiTestCaseCommand = new AddApiTestCaseCommand( aggregateId1 );

        if ( req != null ) {
            addApiTestCaseCommand.setProductCode( req.getProductCode() );
            addApiTestCaseCommand.setCaseName( req.getCaseName() );
            addApiTestCaseCommand.setApiCode( req.getApiCode() );
            addApiTestCaseCommand.setStatus( req.getStatus() );
            List<Integer> list = req.getDbIds();
            if ( list != null ) {
                addApiTestCaseCommand.setDbIds( new ArrayList<Integer>( list ) );
            }
        }
        addApiTestCaseCommand.setCaseReqData( mapJSONObjectToString(req.getCaseReqData()) );
        addApiTestCaseCommand.setCaseType( com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum.getCode(req.getCaseType()) );

        return addApiTestCaseCommand;
    }

    @Override
    public EditApiTestCaseCommand convertor(EditApiTestCaseReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        EditApiTestCaseCommand editApiTestCaseCommand = new EditApiTestCaseCommand( aggregateId );

        editApiTestCaseCommand.setProductCode( req.getProductCode() );
        editApiTestCaseCommand.setCaseCode( req.getCaseCode() );
        editApiTestCaseCommand.setCaseName( req.getCaseName() );
        editApiTestCaseCommand.setApiCode( req.getApiCode() );
        editApiTestCaseCommand.setStatus( req.getStatus() );
        List<Integer> list = req.getDbIds();
        if ( list != null ) {
            editApiTestCaseCommand.setDbIds( new ArrayList<Integer>( list ) );
        }

        editApiTestCaseCommand.setCaseReqData( mapJSONObjectToString(req.getCaseReqData()) );
        editApiTestCaseCommand.setCaseType( com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum.getCode(req.getCaseType()) );

        return editApiTestCaseCommand;
    }

    @Override
    public PublishApiTestCaseCommand convertor(PublishApiTestCaseReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        PublishApiTestCaseCommand publishApiTestCaseCommand = new PublishApiTestCaseCommand( aggregateId );

        publishApiTestCaseCommand.setProductCode( req.getProductCode() );
        publishApiTestCaseCommand.setCaseCode( req.getCaseCode() );

        return publishApiTestCaseCommand;
    }

    @Override
    public BatchPublishApiTestCaseCommand convertor(BatchPublishApiTestCaseReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        BatchPublishApiTestCaseCommand batchPublishApiTestCaseCommand = new BatchPublishApiTestCaseCommand( aggregateId );

        batchPublishApiTestCaseCommand.setProductCode( req.getProductCode() );
        List<String> list = req.getCaseCodeList();
        if ( list != null ) {
            batchPublishApiTestCaseCommand.setCaseCodeList( new ArrayList<String>( list ) );
        }
        List<String> list1 = req.getApiCodeList();
        if ( list1 != null ) {
            batchPublishApiTestCaseCommand.setApiCodeList( new ArrayList<String>( list1 ) );
        }
        batchPublishApiTestCaseCommand.setPublishCaseFlag( req.getPublishCaseFlag() );

        return batchPublishApiTestCaseCommand;
    }

    @Override
    public UpdateApiCaseExceptionCommand convert(UpdateApiCaseExceptionReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getCaseCode();

        UpdateApiCaseExceptionCommand updateApiCaseExceptionCommand = new UpdateApiCaseExceptionCommand( aggregateId );

        updateApiCaseExceptionCommand.setKey( req.getKey() );
        updateApiCaseExceptionCommand.setValue( req.getValue() );
        updateApiCaseExceptionCommand.setAsserts( req.getAsserts() );
        updateApiCaseExceptionCommand.setApiConfigType( req.getApiConfigType() );

        return updateApiCaseExceptionCommand;
    }

    @Override
    public GenerateApiCaseExceptionCommand convert(GenerateApiCaseExceptionReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getParentCaseCode();

        GenerateApiCaseExceptionCommand generateApiCaseExceptionCommand = new GenerateApiCaseExceptionCommand( aggregateId );

        generateApiCaseExceptionCommand.setParentCaseCode( req.getParentCaseCode() );
        generateApiCaseExceptionCommand.setStatus( req.getStatus() );
        List<ApiFieldConfigVO> list = req.getApiFieldConfigList();
        if ( list != null ) {
            generateApiCaseExceptionCommand.setApiFieldConfigList( new ArrayList<ApiFieldConfigVO>( list ) );
        }

        return generateApiCaseExceptionCommand;
    }

    @Override
    public VerifyBatchOperationCommand convertor(VerifyBatchOperationReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        VerifyBatchOperationCommand verifyBatchOperationCommand = new VerifyBatchOperationCommand( aggregateId );

        verifyBatchOperationCommand.setProductCode( req.getProductCode() );
        List<String> list = req.getApiCaseCodeList();
        if ( list != null ) {
            verifyBatchOperationCommand.setApiCaseCodeList( new ArrayList<String>( list ) );
        }
        verifyBatchOperationCommand.setOperation( req.getOperation() );

        return verifyBatchOperationCommand;
    }

    @Override
    public ChangeApiCaseStatusCommand convertor(ChangeApiCaseStatusReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        ChangeApiCaseStatusCommand changeApiCaseStatusCommand = new ChangeApiCaseStatusCommand( aggregateId );

        changeApiCaseStatusCommand.setApiCaseCode( req.getApiCaseCode() );
        changeApiCaseStatusCommand.setStatus( req.getStatus() );
        changeApiCaseStatusCommand.setOperation( req.getOperation() );

        return changeApiCaseStatusCommand;
    }

    @Override
    public BatchChangeApiCaseStatusCommand convertor(BatchChangeApiCaseStatusReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        BatchChangeApiCaseStatusCommand batchChangeApiCaseStatusCommand = new BatchChangeApiCaseStatusCommand( aggregateId );

        batchChangeApiCaseStatusCommand.setProductCode( req.getProductCode() );
        List<String> list = req.getApiCaseCodeList();
        if ( list != null ) {
            batchChangeApiCaseStatusCommand.setApiCaseCodeList( new ArrayList<String>( list ) );
        }
        batchChangeApiCaseStatusCommand.setOperation( req.getOperation() );

        return batchChangeApiCaseStatusCommand;
    }

    @Override
    public JmxFileUploadCommand convertor(JmxFileUploadReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        JmxFileUploadCommand jmxFileUploadCommand = new JmxFileUploadCommand( aggregateId );

        jmxFileUploadCommand.setBucketName( req.getBucketName() );
        jmxFileUploadCommand.setAddress( req.getAddress() );
        jmxFileUploadCommand.setFileName( req.getFileName() );
        jmxFileUploadCommand.setProductCode( req.getProductCode() );
        jmxFileUploadCommand.setProductName( req.getProductName() );
        jmxFileUploadCommand.setSceneCode( req.getSceneCode() );
        jmxFileUploadCommand.setType( req.getType() );

        return jmxFileUploadCommand;
    }

    @Override
    public AddSceneTagCommand convertor(AddSceneTagReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        AddSceneTagCommand addSceneTagCommand = new AddSceneTagCommand( aggregateId );

        List<String> list = req.getBusinessCodeList();
        if ( list != null ) {
            addSceneTagCommand.setBusinessCodeList( new ArrayList<String>( list ) );
        }
        addSceneTagCommand.setDomain( req.getDomain() );
        List<String> list1 = req.getTagNameList();
        if ( list1 != null ) {
            addSceneTagCommand.setTagNameList( new ArrayList<String>( list1 ) );
        }
        addSceneTagCommand.setProductCode( req.getProductCode() );

        return addSceneTagCommand;
    }

    @Override
    public CopyApiTestCaseCommand convertor(CopyApiTestCaseReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        CopyApiTestCaseCommand copyApiTestCaseCommand = new CopyApiTestCaseCommand( aggregateId );

        copyApiTestCaseCommand.setCaseCode( req.getCaseCode() );
        copyApiTestCaseCommand.setCaseName( req.getCaseName() );

        return copyApiTestCaseCommand;
    }

    protected PageApiTestVariableResp apiTestVariableVOToPageApiTestVariableResp(ApiTestVariableVO apiTestVariableVO) {
        if ( apiTestVariableVO == null ) {
            return null;
        }

        PageApiTestVariableResp pageApiTestVariableResp = new PageApiTestVariableResp();

        pageApiTestVariableResp.setId( apiTestVariableVO.getId() );
        pageApiTestVariableResp.setProductCode( apiTestVariableVO.getProductCode() );
        pageApiTestVariableResp.setProductName( apiTestVariableVO.getProductName() );
        pageApiTestVariableResp.setLinkCode( apiTestVariableVO.getLinkCode() );
        pageApiTestVariableResp.setVariableCode( apiTestVariableVO.getVariableCode() );
        pageApiTestVariableResp.setVariableName( apiTestVariableVO.getVariableName() );
        pageApiTestVariableResp.setVariableKey( apiTestVariableVO.getVariableKey() );
        pageApiTestVariableResp.setVariableValue( apiTestVariableVO.getVariableValue() );
        if ( apiTestVariableVO.getType() != null ) {
            pageApiTestVariableResp.setType( apiTestVariableVO.getType().name() );
        }
        if ( apiTestVariableVO.getVariableStatus() != null ) {
            pageApiTestVariableResp.setVariableStatus( String.valueOf( apiTestVariableVO.getVariableStatus() ) );
        }
        pageApiTestVariableResp.setSubVariableType( apiTestVariableVO.getSubVariableType() );
        pageApiTestVariableResp.setLoginValidTime( apiTestVariableVO.getLoginValidTime() );
        pageApiTestVariableResp.setCreatorId( apiTestVariableVO.getCreatorId() );
        pageApiTestVariableResp.setCreator( apiTestVariableVO.getCreator() );
        pageApiTestVariableResp.setGmtCreate( apiTestVariableVO.getGmtCreate() );
        pageApiTestVariableResp.setModifierId( apiTestVariableVO.getModifierId() );
        pageApiTestVariableResp.setModifier( apiTestVariableVO.getModifier() );
        pageApiTestVariableResp.setGmtModified( apiTestVariableVO.getGmtModified() );

        return pageApiTestVariableResp;
    }
}
