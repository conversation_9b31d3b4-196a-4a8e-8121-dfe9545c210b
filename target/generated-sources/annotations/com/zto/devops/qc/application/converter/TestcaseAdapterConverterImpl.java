package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.HeartCaseFilterEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseButtonEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseEditFieldEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditTestcaseCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditTestcaseTitleCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.MoveModuleCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.XmindCaseAddCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.XmindCaseEditCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ButtonVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.CheckCreatorOrDutyUserVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.CheckTestcaseStatusVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseResultContentVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.FieldVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListExecuteCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseModuleVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListXmindDetailVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseIssueVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRequirementVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListExecuteCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListExecuteEnvQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseCodeQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseExpQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseModuleQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.MoveModuleReq;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageAutomaticTaskGroupQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageExecuteRecordQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageTestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageXmindDetailQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.TestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.XmindFilterQuery;
import com.zto.devops.qc.client.service.testmanager.cases.model.AddTestcaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.CheckCreatorOrDutyUserResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.CheckTestcaseStatusResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.EditTestcaseTitleReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteCaseResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListExecuteCaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListExecuteCaseResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListExecuteEnvReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseCodeReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseExecuteRecordReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseModuleReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseModuleResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListXmindDetailReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListXmindDetailResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.PageAutomaticTaskGroupReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.PlanCaseResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.TestcaseIssueResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.TestcaseRequirementResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.TestcaseResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.XmindCaseAddReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.XmindCaseEditReq;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:12:17+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class TestcaseAdapterConverterImpl implements TestcaseAdapterConverter {

    @Override
    public ExecuteCaseResp converter(ExecuteCaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        ExecuteCaseResp executeCaseResp = new ExecuteCaseResp();

        executeCaseResp.setPriority( vo.getPriority() );
        executeCaseResp.setResult( vo.getResult() );
        executeCaseResp.setType( vo.getType() );
        executeCaseResp.setCode( vo.getCode() );
        executeCaseResp.setAutomaticTaskCode( vo.getAutomaticTaskCode() );
        executeCaseResp.setAutomaticTaskId( vo.getAutomaticTaskId() );
        executeCaseResp.setProductCode( vo.getProductCode() );
        executeCaseResp.setProductName( vo.getProductName() );
        executeCaseResp.setVersionCode( vo.getVersionCode() );
        executeCaseResp.setVersionName( vo.getVersionName() );
        executeCaseResp.setTestPlanCode( vo.getTestPlanCode() );
        executeCaseResp.setTestPlanName( vo.getTestPlanName() );
        executeCaseResp.setParentCode( vo.getParentCode() );
        List<String> list = vo.getParentFullName();
        if ( list != null ) {
            executeCaseResp.setParentFullName( new ArrayList<String>( list ) );
        }
        executeCaseResp.setName( vo.getName() );
        executeCaseResp.setNodeType( vo.getNodeType() );
        executeCaseResp.setExecutorId( vo.getExecutorId() );
        executeCaseResp.setExecutor( vo.getExecutor() );
        executeCaseResp.setComment( vo.getComment() );
        executeCaseResp.setEnable( vo.getEnable() );
        executeCaseResp.setModifierId( vo.getModifierId() );
        executeCaseResp.setModifier( vo.getModifier() );
        executeCaseResp.setGmtModified( vo.getGmtModified() );
        executeCaseResp.setResultFile( vo.getResultFile() );
        List<ExecuteCaseResultContentVO> list1 = vo.getResultContent();
        if ( list1 != null ) {
            executeCaseResp.setResultContent( new ArrayList<ExecuteCaseResultContentVO>( list1 ) );
        }
        executeCaseResp.setReportFile( vo.getReportFile() );
        executeCaseResp.setExecLogFile( vo.getExecLogFile() );
        executeCaseResp.setAutomaticRecordType( vo.getAutomaticRecordType() );
        executeCaseResp.setInterfaceName( vo.getInterfaceName() );
        executeCaseResp.setPassedCount( vo.getPassedCount() );
        executeCaseResp.setFailedCount( vo.getFailedCount() );

        return executeCaseResp;
    }

    @Override
    public PageXmindDetailQuery converterPageXmindDetailQuery(ListXmindDetailReq req) {
        if ( req == null ) {
            return null;
        }

        PageXmindDetailQuery pageXmindDetailQuery = new PageXmindDetailQuery();

        pageXmindDetailQuery.setTransactor( req.getTransactor() );
        pageXmindDetailQuery.setPage( req.getPage() );
        pageXmindDetailQuery.setSize( req.getSize() );
        pageXmindDetailQuery.setGroupType( req.getGroupType() );
        pageXmindDetailQuery.setProductCode( req.getProductCode() );
        pageXmindDetailQuery.setType( req.getType() );
        pageXmindDetailQuery.setParentCode( req.getParentCode() );
        pageXmindDetailQuery.setCodeOrTitle( req.getCodeOrTitle() );
        List<TestcasePriorityEnum> list = req.getPriorityList();
        if ( list != null ) {
            pageXmindDetailQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
        }
        List<TestcaseStatusEnum> list1 = req.getStatusList();
        if ( list1 != null ) {
            pageXmindDetailQuery.setStatusList( new ArrayList<TestcaseStatusEnum>( list1 ) );
        }
        List<String> list2 = req.getTagList();
        if ( list2 != null ) {
            pageXmindDetailQuery.setTagList( new ArrayList<String>( list2 ) );
        }
        List<Long> list3 = req.getDutyUserList();
        if ( list3 != null ) {
            pageXmindDetailQuery.setDutyUserList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = req.getCreatorList();
        if ( list4 != null ) {
            pageXmindDetailQuery.setCreatorList( new ArrayList<Long>( list4 ) );
        }
        pageXmindDetailQuery.setCreateTimeBegin( req.getCreateTimeBegin() );
        pageXmindDetailQuery.setCreateTimeEnd( req.getCreateTimeEnd() );
        pageXmindDetailQuery.setModifyTimeBegin( req.getModifyTimeBegin() );
        pageXmindDetailQuery.setModifyTimeEnd( req.getModifyTimeEnd() );
        pageXmindDetailQuery.setTestStage( req.getTestStage() );
        pageXmindDetailQuery.setPlanCode( req.getPlanCode() );
        pageXmindDetailQuery.setPlanPattern( req.getPlanPattern() );
        pageXmindDetailQuery.setAutomaticSourceCode( req.getAutomaticSourceCode() );
        List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
        if ( list5 != null ) {
            pageXmindDetailQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
        }
        List<HeartCaseFilterEnum> list6 = req.getIsHeart();
        if ( list6 != null ) {
            pageXmindDetailQuery.setIsHeart( new ArrayList<HeartCaseFilterEnum>( list6 ) );
        }
        List<AutomaticRecordTypeEnum> list7 = req.getAutomaticTypeList();
        if ( list7 != null ) {
            pageXmindDetailQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list7 ) );
        }
        pageXmindDetailQuery.setSetCore( req.getSetCore() );
        pageXmindDetailQuery.setVersionCode( req.getVersionCode() );
        pageXmindDetailQuery.setFactoryPattern( req.getFactoryPattern() );
        List<String> list8 = req.getPlanCaseVersionCodeList();
        if ( list8 != null ) {
            pageXmindDetailQuery.setPlanCaseVersionCodeList( new ArrayList<String>( list8 ) );
        }
        pageXmindDetailQuery.setCaseCode( req.getCaseCode() );
        pageXmindDetailQuery.setIsRoot( req.getIsRoot() );

        return pageXmindDetailQuery;
    }

    @Override
    public void converter(PageAutomaticTaskGroupReq req, PageAutomaticTaskGroupQuery query) {
        if ( req == null ) {
            return;
        }

        query.setTransactor( req.getTransactor() );
        query.setPage( req.getPage() );
        query.setSize( req.getSize() );
        if ( query.getExecuteEnvList() != null ) {
            List<String> list = req.getExecuteEnvList();
            if ( list != null ) {
                query.getExecuteEnvList().clear();
                query.getExecuteEnvList().addAll( list );
            }
            else {
                query.setExecuteEnvList( null );
            }
        }
        else {
            List<String> list = req.getExecuteEnvList();
            if ( list != null ) {
                query.setExecuteEnvList( new ArrayList<String>( list ) );
            }
        }
        if ( query.getAutomaticCodeList() != null ) {
            List<String> list1 = req.getAutomaticCodeList();
            if ( list1 != null ) {
                query.getAutomaticCodeList().clear();
                query.getAutomaticCodeList().addAll( list1 );
            }
            else {
                query.setAutomaticCodeList( null );
            }
        }
        else {
            List<String> list1 = req.getAutomaticCodeList();
            if ( list1 != null ) {
                query.setAutomaticCodeList( new ArrayList<String>( list1 ) );
            }
        }
        query.setSchedulerCode( req.getSchedulerCode() );
        query.setProductCode( req.getProductCode() );
        if ( query.getStatusList() != null ) {
            List<AutomaticStatusEnum> list2 = req.getStatusList();
            if ( list2 != null ) {
                query.getStatusList().clear();
                query.getStatusList().addAll( list2 );
            }
            else {
                query.setStatusList( null );
            }
        }
        else {
            List<AutomaticStatusEnum> list2 = req.getStatusList();
            if ( list2 != null ) {
                query.setStatusList( new ArrayList<AutomaticStatusEnum>( list2 ) );
            }
        }
        if ( query.getVersionCodeList() != null ) {
            List<String> list3 = req.getVersionCodeList();
            if ( list3 != null ) {
                query.getVersionCodeList().clear();
                query.getVersionCodeList().addAll( list3 );
            }
            else {
                query.setVersionCodeList( null );
            }
        }
        else {
            List<String> list3 = req.getVersionCodeList();
            if ( list3 != null ) {
                query.setVersionCodeList( new ArrayList<String>( list3 ) );
            }
        }
        if ( query.getTestPlanCodeList() != null ) {
            List<String> list4 = req.getTestPlanCodeList();
            if ( list4 != null ) {
                query.getTestPlanCodeList().clear();
                query.getTestPlanCodeList().addAll( list4 );
            }
            else {
                query.setTestPlanCodeList( null );
            }
        }
        else {
            List<String> list4 = req.getTestPlanCodeList();
            if ( list4 != null ) {
                query.setTestPlanCodeList( new ArrayList<String>( list4 ) );
            }
        }
        if ( query.getTrigModeList() != null ) {
            List<AutomaticTaskTrigModeEnum> list5 = req.getTrigModeList();
            if ( list5 != null ) {
                query.getTrigModeList().clear();
                query.getTrigModeList().addAll( list5 );
            }
            else {
                query.setTrigModeList( null );
            }
        }
        else {
            List<AutomaticTaskTrigModeEnum> list5 = req.getTrigModeList();
            if ( list5 != null ) {
                query.setTrigModeList( new ArrayList<AutomaticTaskTrigModeEnum>( list5 ) );
            }
        }
        if ( query.getExecutorIdList() != null ) {
            List<Long> list6 = req.getExecutorIdList();
            if ( list6 != null ) {
                query.getExecutorIdList().clear();
                query.getExecutorIdList().addAll( list6 );
            }
            else {
                query.setExecutorIdList( null );
            }
        }
        else {
            List<Long> list6 = req.getExecutorIdList();
            if ( list6 != null ) {
                query.setExecutorIdList( new ArrayList<Long>( list6 ) );
            }
        }
        query.setStartTimeBegin( req.getStartTimeBegin() );
        query.setStartTimeEnd( req.getStartTimeEnd() );
        query.setFinishTimeBegin( req.getFinishTimeBegin() );
        query.setFinishTimeEnd( req.getFinishTimeEnd() );
        query.setTaskId( req.getTaskId() );
    }

    @Override
    public void converter(ListTestcaseCodeReq req, ListTestcaseCodeQuery query) {
        if ( req == null ) {
            return;
        }

        query.setProductCode( req.getProductCode() );
        query.setType( req.getType() );
        query.setParentCode( req.getParentCode() );
        query.setCodeOrTitle( req.getCodeOrTitle() );
        if ( query.getPriorityList() != null ) {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.getPriorityList().clear();
                query.getPriorityList().addAll( list );
            }
            else {
                query.setPriorityList( null );
            }
        }
        else {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
            }
        }
        if ( query.getStatusList() != null ) {
            List<TestcaseStatusEnum> list1 = req.getStatusList();
            if ( list1 != null ) {
                query.getStatusList().clear();
                query.getStatusList().addAll( list1 );
            }
            else {
                query.setStatusList( null );
            }
        }
        else {
            List<TestcaseStatusEnum> list1 = req.getStatusList();
            if ( list1 != null ) {
                query.setStatusList( new ArrayList<TestcaseStatusEnum>( list1 ) );
            }
        }
        if ( query.getTagList() != null ) {
            List<String> list2 = req.getTagList();
            if ( list2 != null ) {
                query.getTagList().clear();
                query.getTagList().addAll( list2 );
            }
            else {
                query.setTagList( null );
            }
        }
        else {
            List<String> list2 = req.getTagList();
            if ( list2 != null ) {
                query.setTagList( new ArrayList<String>( list2 ) );
            }
        }
        if ( query.getDutyUserList() != null ) {
            List<Long> list3 = req.getDutyUserList();
            if ( list3 != null ) {
                query.getDutyUserList().clear();
                query.getDutyUserList().addAll( list3 );
            }
            else {
                query.setDutyUserList( null );
            }
        }
        else {
            List<Long> list3 = req.getDutyUserList();
            if ( list3 != null ) {
                query.setDutyUserList( new ArrayList<Long>( list3 ) );
            }
        }
        if ( query.getCreatorList() != null ) {
            List<Long> list4 = req.getCreatorList();
            if ( list4 != null ) {
                query.getCreatorList().clear();
                query.getCreatorList().addAll( list4 );
            }
            else {
                query.setCreatorList( null );
            }
        }
        else {
            List<Long> list4 = req.getCreatorList();
            if ( list4 != null ) {
                query.setCreatorList( new ArrayList<Long>( list4 ) );
            }
        }
        query.setCreateTimeBegin( req.getCreateTimeBegin() );
        query.setCreateTimeEnd( req.getCreateTimeEnd() );
        query.setModifyTimeBegin( req.getModifyTimeBegin() );
        query.setModifyTimeEnd( req.getModifyTimeEnd() );
        query.setTestStage( req.getTestStage() );
        query.setPlanCode( req.getPlanCode() );
        query.setPlanPattern( req.getPlanPattern() );
        if ( query.getNodeTypeList() != null ) {
            List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
            if ( list5 != null ) {
                query.getNodeTypeList().clear();
                query.getNodeTypeList().addAll( list5 );
            }
            else {
                query.setNodeTypeList( null );
            }
        }
        else {
            List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
            if ( list5 != null ) {
                query.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
            }
        }
        if ( query.getIsHeart() != null ) {
            List<HeartCaseFilterEnum> list6 = req.getIsHeart();
            if ( list6 != null ) {
                query.getIsHeart().clear();
                query.getIsHeart().addAll( list6 );
            }
            else {
                query.setIsHeart( null );
            }
        }
        else {
            List<HeartCaseFilterEnum> list6 = req.getIsHeart();
            if ( list6 != null ) {
                query.setIsHeart( new ArrayList<HeartCaseFilterEnum>( list6 ) );
            }
        }
        if ( query.getAutomaticTypeList() != null ) {
            List<AutomaticRecordTypeEnum> list7 = req.getAutomaticTypeList();
            if ( list7 != null ) {
                query.getAutomaticTypeList().clear();
                query.getAutomaticTypeList().addAll( list7 );
            }
            else {
                query.setAutomaticTypeList( null );
            }
        }
        else {
            List<AutomaticRecordTypeEnum> list7 = req.getAutomaticTypeList();
            if ( list7 != null ) {
                query.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list7 ) );
            }
        }
        if ( query.getSetCoreList() != null ) {
            List<Boolean> list8 = req.getSetCoreList();
            if ( list8 != null ) {
                query.getSetCoreList().clear();
                query.getSetCoreList().addAll( list8 );
            }
            else {
                query.setSetCoreList( null );
            }
        }
        else {
            List<Boolean> list8 = req.getSetCoreList();
            if ( list8 != null ) {
                query.setSetCoreList( new ArrayList<Boolean>( list8 ) );
            }
        }
        query.setVersionCode( req.getVersionCode() );
    }

    @Override
    public List<ListXmindDetailResp> convertList(Collection<ListXmindDetailVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ListXmindDetailResp> list1 = new ArrayList<ListXmindDetailResp>( list.size() );
        for ( ListXmindDetailVO listXmindDetailVO : list ) {
            list1.add( listXmindDetailVOToListXmindDetailResp( listXmindDetailVO ) );
        }

        return list1;
    }

    @Override
    public void converter(ListTestcaseModuleReq req, ListTestcaseModuleQuery query) {
        if ( req == null ) {
            return;
        }

        query.setType( req.getType() );
        query.setProductCode( req.getProductCode() );
        query.setPlanPattern( req.getPlanPattern() );
        query.setPlanCode( req.getPlanCode() );
        query.setTestStage( req.getTestStage() );
        query.setMenuPattern( req.getMenuPattern() );
        query.setVersionCode( req.getVersionCode() );
        query.setSetCore( req.getSetCore() );
        query.setFactoryPattern( req.getFactoryPattern() );
    }

    @Override
    public ListTestcaseModuleResp converter(ListTestcaseModuleVO vo) {
        if ( vo == null ) {
            return null;
        }

        ListTestcaseModuleResp listTestcaseModuleResp = new ListTestcaseModuleResp();

        listTestcaseModuleResp.setCode( vo.getCode() );
        listTestcaseModuleResp.setName( vo.getName() );
        listTestcaseModuleResp.setProductCode( vo.getProductCode() );
        listTestcaseModuleResp.setParentCode( vo.getParentCode() );
        listTestcaseModuleResp.setType( vo.getType() );
        listTestcaseModuleResp.setStatus( vo.getStatus() );
        listTestcaseModuleResp.setAutomaticSourceCode( vo.getAutomaticSourceCode() );
        listTestcaseModuleResp.setNodeType( vo.getNodeType() );
        listTestcaseModuleResp.setSort( vo.getSort() );
        listTestcaseModuleResp.setPath( vo.getPath() );
        listTestcaseModuleResp.setLayer( vo.getLayer() );
        listTestcaseModuleResp.setTestcaseCount( vo.getTestcaseCount() );
        listTestcaseModuleResp.setAutomaticSourceStatus( vo.getAutomaticSourceStatus() );
        listTestcaseModuleResp.setAttribute( vo.getAttribute() );
        listTestcaseModuleResp.setComment( vo.getComment() );
        listTestcaseModuleResp.setAutomaticSourceType( vo.getAutomaticSourceType() );
        listTestcaseModuleResp.setChildren( listTestcaseModuleVOListToListTestcaseModuleRespList( vo.getChildren() ) );
        listTestcaseModuleResp.setSceneFlag( vo.getSceneFlag() );

        return listTestcaseModuleResp;
    }

    @Override
    public void converterPage(ListTestcaseReq req, PageTestcaseQuery query) {
        if ( req == null ) {
            return;
        }

        query.setTransactor( req.getTransactor() );
        query.setPage( req.getPage() );
        query.setSize( req.getSize() );
        query.setGroupType( req.getGroupType() );
        query.setProductCode( req.getProductCode() );
        query.setType( req.getType() );
        query.setParentCode( req.getParentCode() );
        query.setCodeOrTitle( req.getCodeOrTitle() );
        if ( query.getPriorityList() != null ) {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.getPriorityList().clear();
                query.getPriorityList().addAll( list );
            }
            else {
                query.setPriorityList( null );
            }
        }
        else {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
            }
        }
        if ( query.getStatusList() != null ) {
            List<TestcaseStatusEnum> list1 = req.getStatusList();
            if ( list1 != null ) {
                query.getStatusList().clear();
                query.getStatusList().addAll( list1 );
            }
            else {
                query.setStatusList( null );
            }
        }
        else {
            List<TestcaseStatusEnum> list1 = req.getStatusList();
            if ( list1 != null ) {
                query.setStatusList( new ArrayList<TestcaseStatusEnum>( list1 ) );
            }
        }
        if ( query.getTagList() != null ) {
            List<String> list2 = req.getTagList();
            if ( list2 != null ) {
                query.getTagList().clear();
                query.getTagList().addAll( list2 );
            }
            else {
                query.setTagList( null );
            }
        }
        else {
            List<String> list2 = req.getTagList();
            if ( list2 != null ) {
                query.setTagList( new ArrayList<String>( list2 ) );
            }
        }
        if ( query.getDutyUserList() != null ) {
            List<Long> list3 = req.getDutyUserList();
            if ( list3 != null ) {
                query.getDutyUserList().clear();
                query.getDutyUserList().addAll( list3 );
            }
            else {
                query.setDutyUserList( null );
            }
        }
        else {
            List<Long> list3 = req.getDutyUserList();
            if ( list3 != null ) {
                query.setDutyUserList( new ArrayList<Long>( list3 ) );
            }
        }
        if ( query.getCreatorList() != null ) {
            List<Long> list4 = req.getCreatorList();
            if ( list4 != null ) {
                query.getCreatorList().clear();
                query.getCreatorList().addAll( list4 );
            }
            else {
                query.setCreatorList( null );
            }
        }
        else {
            List<Long> list4 = req.getCreatorList();
            if ( list4 != null ) {
                query.setCreatorList( new ArrayList<Long>( list4 ) );
            }
        }
        query.setCreateTimeBegin( req.getCreateTimeBegin() );
        query.setCreateTimeEnd( req.getCreateTimeEnd() );
        query.setModifyTimeBegin( req.getModifyTimeBegin() );
        query.setModifyTimeEnd( req.getModifyTimeEnd() );
        query.setTestStage( req.getTestStage() );
        query.setPlanCode( req.getPlanCode() );
        query.setPlanPattern( req.getPlanPattern() );
        query.setAutomaticSourceCode( req.getAutomaticSourceCode() );
        if ( query.getNodeTypeList() != null ) {
            List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
            if ( list5 != null ) {
                query.getNodeTypeList().clear();
                query.getNodeTypeList().addAll( list5 );
            }
            else {
                query.setNodeTypeList( null );
            }
        }
        else {
            List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
            if ( list5 != null ) {
                query.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
            }
        }
        if ( query.getIsHeart() != null ) {
            List<HeartCaseFilterEnum> list6 = req.getIsHeart();
            if ( list6 != null ) {
                query.getIsHeart().clear();
                query.getIsHeart().addAll( list6 );
            }
            else {
                query.setIsHeart( null );
            }
        }
        else {
            List<HeartCaseFilterEnum> list6 = req.getIsHeart();
            if ( list6 != null ) {
                query.setIsHeart( new ArrayList<HeartCaseFilterEnum>( list6 ) );
            }
        }
        if ( query.getAutomaticTypeList() != null ) {
            List<AutomaticRecordTypeEnum> list7 = req.getAutomaticTypeList();
            if ( list7 != null ) {
                query.getAutomaticTypeList().clear();
                query.getAutomaticTypeList().addAll( list7 );
            }
            else {
                query.setAutomaticTypeList( null );
            }
        }
        else {
            List<AutomaticRecordTypeEnum> list7 = req.getAutomaticTypeList();
            if ( list7 != null ) {
                query.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list7 ) );
            }
        }
        query.setSetCore( req.getSetCore() );
        query.setVersionCode( req.getVersionCode() );
        query.setFactoryPattern( req.getFactoryPattern() );
    }

    @Override
    public void converterPage(ListTestcaseReq req, TestcaseQuery query) {
        if ( req == null ) {
            return;
        }

        query.setTransactor( req.getTransactor() );
        query.setGroupType( req.getGroupType() );
        query.setProductCode( req.getProductCode() );
        query.setType( req.getType() );
        query.setParentCode( req.getParentCode() );
        query.setCodeOrTitle( req.getCodeOrTitle() );
        if ( query.getPriorityList() != null ) {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.getPriorityList().clear();
                query.getPriorityList().addAll( list );
            }
            else {
                query.setPriorityList( null );
            }
        }
        else {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
            }
        }
        if ( query.getStatusList() != null ) {
            List<TestcaseStatusEnum> list1 = req.getStatusList();
            if ( list1 != null ) {
                query.getStatusList().clear();
                query.getStatusList().addAll( list1 );
            }
            else {
                query.setStatusList( null );
            }
        }
        else {
            List<TestcaseStatusEnum> list1 = req.getStatusList();
            if ( list1 != null ) {
                query.setStatusList( new ArrayList<TestcaseStatusEnum>( list1 ) );
            }
        }
        if ( query.getTagList() != null ) {
            List<String> list2 = req.getTagList();
            if ( list2 != null ) {
                query.getTagList().clear();
                query.getTagList().addAll( list2 );
            }
            else {
                query.setTagList( null );
            }
        }
        else {
            List<String> list2 = req.getTagList();
            if ( list2 != null ) {
                query.setTagList( new ArrayList<String>( list2 ) );
            }
        }
        if ( query.getDutyUserList() != null ) {
            List<Long> list3 = req.getDutyUserList();
            if ( list3 != null ) {
                query.getDutyUserList().clear();
                query.getDutyUserList().addAll( list3 );
            }
            else {
                query.setDutyUserList( null );
            }
        }
        else {
            List<Long> list3 = req.getDutyUserList();
            if ( list3 != null ) {
                query.setDutyUserList( new ArrayList<Long>( list3 ) );
            }
        }
        if ( query.getCreatorList() != null ) {
            List<Long> list4 = req.getCreatorList();
            if ( list4 != null ) {
                query.getCreatorList().clear();
                query.getCreatorList().addAll( list4 );
            }
            else {
                query.setCreatorList( null );
            }
        }
        else {
            List<Long> list4 = req.getCreatorList();
            if ( list4 != null ) {
                query.setCreatorList( new ArrayList<Long>( list4 ) );
            }
        }
        query.setCreateTimeBegin( req.getCreateTimeBegin() );
        query.setCreateTimeEnd( req.getCreateTimeEnd() );
        query.setModifyTimeBegin( req.getModifyTimeBegin() );
        query.setModifyTimeEnd( req.getModifyTimeEnd() );
        query.setTestStage( req.getTestStage() );
        query.setPlanCode( req.getPlanCode() );
        query.setPlanPattern( req.getPlanPattern() );
        query.setAutomaticSourceCode( req.getAutomaticSourceCode() );
        if ( query.getNodeTypeList() != null ) {
            List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
            if ( list5 != null ) {
                query.getNodeTypeList().clear();
                query.getNodeTypeList().addAll( list5 );
            }
            else {
                query.setNodeTypeList( null );
            }
        }
        else {
            List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
            if ( list5 != null ) {
                query.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
            }
        }
        if ( query.getIsHeart() != null ) {
            List<HeartCaseFilterEnum> list6 = req.getIsHeart();
            if ( list6 != null ) {
                query.getIsHeart().clear();
                query.getIsHeart().addAll( list6 );
            }
            else {
                query.setIsHeart( null );
            }
        }
        else {
            List<HeartCaseFilterEnum> list6 = req.getIsHeart();
            if ( list6 != null ) {
                query.setIsHeart( new ArrayList<HeartCaseFilterEnum>( list6 ) );
            }
        }
        if ( query.getAutomaticTypeList() != null ) {
            List<AutomaticRecordTypeEnum> list7 = req.getAutomaticTypeList();
            if ( list7 != null ) {
                query.getAutomaticTypeList().clear();
                query.getAutomaticTypeList().addAll( list7 );
            }
            else {
                query.setAutomaticTypeList( null );
            }
        }
        else {
            List<AutomaticRecordTypeEnum> list7 = req.getAutomaticTypeList();
            if ( list7 != null ) {
                query.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list7 ) );
            }
        }
        query.setSetCore( req.getSetCore() );
        query.setVersionCode( req.getVersionCode() );
    }

    @Override
    public List<ListTestcaseResp> converterList(Collection<ListTestcaseVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ListTestcaseResp> list1 = new ArrayList<ListTestcaseResp>( list.size() );
        for ( ListTestcaseVO listTestcaseVO : list ) {
            list1.add( converter( listTestcaseVO ) );
        }

        return list1;
    }

    @Override
    public ListTestcaseResp converter(ListTestcaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        ListTestcaseResp listTestcaseResp = new ListTestcaseResp();

        listTestcaseResp.setPriority( vo.getPriority() );
        listTestcaseResp.setStatus( vo.getStatus() );
        listTestcaseResp.setType( vo.getType() );
        listTestcaseResp.setCode( vo.getCode() );
        listTestcaseResp.setProductCode( vo.getProductCode() );
        listTestcaseResp.setParentCode( vo.getParentCode() );
        List<String> list = vo.getParentFullName();
        if ( list != null ) {
            listTestcaseResp.setParentFullName( new ArrayList<String>( list ) );
        }
        listTestcaseResp.setName( vo.getName() );
        listTestcaseResp.setAttribute( vo.getAttribute() );
        listTestcaseResp.setTypeDesc( vo.getTypeDesc() );
        listTestcaseResp.setPriorityDesc( vo.getPriorityDesc() );
        listTestcaseResp.setStatusDesc( vo.getStatusDesc() );
        listTestcaseResp.setPrecondition( vo.getPrecondition() );
        listTestcaseResp.setDutyUserId( vo.getDutyUserId() );
        listTestcaseResp.setDutyUser( vo.getDutyUser() );
        listTestcaseResp.setComment( vo.getComment() );
        listTestcaseResp.setSort( vo.getSort() );
        listTestcaseResp.setNodeType( vo.getNodeType() );
        listTestcaseResp.setCreatorId( vo.getCreatorId() );
        listTestcaseResp.setCreator( vo.getCreator() );
        listTestcaseResp.setGmtCreate( vo.getGmtCreate() );
        listTestcaseResp.setModifierId( vo.getModifierId() );
        listTestcaseResp.setModifier( vo.getModifier() );
        listTestcaseResp.setGmtModified( vo.getGmtModified() );
        List<TestcaseStepVO> list1 = vo.getTestSteps();
        if ( list1 != null ) {
            listTestcaseResp.setTestSteps( new ArrayList<TestcaseStepVO>( list1 ) );
        }
        List<TagVO> list2 = vo.getTags();
        if ( list2 != null ) {
            listTestcaseResp.setTags( new ArrayList<TagVO>( list2 ) );
        }
        listTestcaseResp.setFields( testcaseEditFieldEnumListToFieldVOList( vo.getFields() ) );
        listTestcaseResp.setButtons( testcaseButtonEnumListToButtonVOList( vo.getButtons() ) );
        listTestcaseResp.setSetHeart( vo.getSetHeart() );
        listTestcaseResp.setAutomaticSourceCode( vo.getAutomaticSourceCode() );
        listTestcaseResp.setInterfaceName( vo.getInterfaceName() );
        listTestcaseResp.setAutomaticSourceType( vo.getAutomaticSourceType() );
        listTestcaseResp.setSetCore( vo.getSetCore() );
        listTestcaseResp.setVersionCode( vo.getVersionCode() );
        listTestcaseResp.setVersionName( vo.getVersionName() );
        listTestcaseResp.setExecuteNum( vo.getExecuteNum() );
        listTestcaseResp.setTestcaseCount( vo.getTestcaseCount() );
        listTestcaseResp.setChildren( converterList( vo.getChildren() ) );
        listTestcaseResp.setSwitchStatus( vo.getSwitchStatus() );
        listTestcaseResp.setEnableChecked( vo.getEnableChecked() );
        listTestcaseResp.setSceneFlag( vo.getSceneFlag() );

        return listTestcaseResp;
    }

    @Override
    public ButtonVO converter(TestcaseButtonEnum button) {
        if ( button == null ) {
            return null;
        }

        ButtonVO buttonVO = new ButtonVO();

        buttonVO.setCode( button.name() );
        buttonVO.setName( button.getDesc() );

        return buttonVO;
    }

    @Override
    public PageExecuteRecordQuery converter(ListTestcaseExecuteRecordReq req) {
        if ( req == null ) {
            return null;
        }

        String code = null;

        code = req.getCode();

        PageExecuteRecordQuery pageExecuteRecordQuery = new PageExecuteRecordQuery( code );

        pageExecuteRecordQuery.setTransactor( req.getTransactor() );
        pageExecuteRecordQuery.setPage( req.getPage() );
        pageExecuteRecordQuery.setSize( req.getSize() );
        pageExecuteRecordQuery.setType( req.getType() );

        return pageExecuteRecordQuery;
    }

    @Override
    public AddTestcaseCommand convertor(AddTestcaseReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId("TEST_CASE");

        AddTestcaseCommand addTestcaseCommand = new AddTestcaseCommand( aggregateId );

        addTestcaseCommand.setProductCode( req.getProductCode() );
        addTestcaseCommand.setParentCode( req.getParentCode() );
        addTestcaseCommand.setName( req.getName() );
        addTestcaseCommand.setAttribute( req.getAttribute() );
        addTestcaseCommand.setType( req.getType() );
        addTestcaseCommand.setPriority( req.getPriority() );
        addTestcaseCommand.setPrecondition( req.getPrecondition() );
        addTestcaseCommand.setDutyUserId( req.getDutyUserId() );
        addTestcaseCommand.setDutyUser( req.getDutyUser() );
        addTestcaseCommand.setComment( req.getComment() );
        addTestcaseCommand.setLayer( req.getLayer() );
        addTestcaseCommand.setPath( req.getPath() );
        List<AttachmentVO> list = req.getAttachments();
        if ( list != null ) {
            addTestcaseCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<TagVO> list1 = req.getTags();
        if ( list1 != null ) {
            addTestcaseCommand.setTags( new ArrayList<TagVO>( list1 ) );
        }
        List<TestcaseRelationVO> list2 = req.getVos();
        if ( list2 != null ) {
            addTestcaseCommand.setVos( new ArrayList<TestcaseRelationVO>( list2 ) );
        }
        List<TestcaseStepVO> list3 = req.getTestSteps();
        if ( list3 != null ) {
            addTestcaseCommand.setTestSteps( new ArrayList<TestcaseStepVO>( list3 ) );
        }
        addTestcaseCommand.setSetCore( req.getSetCore() );
        addTestcaseCommand.setVersionCode( req.getVersionCode() );

        return addTestcaseCommand;
    }

    @Override
    public XmindCaseAddCommand converter(XmindCaseAddReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        XmindCaseAddCommand xmindCaseAddCommand = new XmindCaseAddCommand( aggregateId );

        xmindCaseAddCommand.setAttribute( req.getAttribute() );
        xmindCaseAddCommand.setTopic( req.getTopic() );
        xmindCaseAddCommand.setLayer( req.getLayer() );
        xmindCaseAddCommand.setParentCode( req.getParentCode() );
        xmindCaseAddCommand.setProductCode( req.getProductCode() );
        xmindCaseAddCommand.setType( req.getType() );
        xmindCaseAddCommand.setPriority( req.getPriority() );
        xmindCaseAddCommand.setSetCore( req.getSetCore() );
        xmindCaseAddCommand.setVersionCode( req.getVersionCode() );

        return xmindCaseAddCommand;
    }

    @Override
    public XmindCaseEditCommand converter(XmindCaseEditReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        XmindCaseEditCommand xmindCaseEditCommand = new XmindCaseEditCommand( aggregateId );

        xmindCaseEditCommand.setId( req.getId() );
        xmindCaseEditCommand.setTopic( req.getTopic() );
        xmindCaseEditCommand.setParentCode( req.getParentCode() );
        xmindCaseEditCommand.setProductCode( req.getProductCode() );
        xmindCaseEditCommand.setTag( req.getTag() );
        xmindCaseEditCommand.setOperation( req.getOperation() );
        xmindCaseEditCommand.setAttribute( req.getAttribute() );
        xmindCaseEditCommand.setOldParentCode( req.getOldParentCode() );
        xmindCaseEditCommand.setOldPath( req.getOldPath() );
        xmindCaseEditCommand.setNewPath( req.getNewPath() );
        xmindCaseEditCommand.setVersionCode( req.getVersionCode() );
        xmindCaseEditCommand.setSetCore( req.getSetCore() );

        return xmindCaseEditCommand;
    }

    @Override
    public XmindFilterQuery convert(ListXmindDetailReq req) {
        if ( req == null ) {
            return null;
        }

        XmindFilterQuery xmindFilterQuery = new XmindFilterQuery();

        xmindFilterQuery.setTransactor( req.getTransactor() );
        xmindFilterQuery.setProductCode( req.getProductCode() );
        xmindFilterQuery.setType( req.getType() );
        xmindFilterQuery.setParentCode( req.getParentCode() );
        xmindFilterQuery.setCodeOrTitle( req.getCodeOrTitle() );
        List<TestcasePriorityEnum> list = req.getPriorityList();
        if ( list != null ) {
            xmindFilterQuery.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
        }
        List<TestcaseStatusEnum> list1 = req.getStatusList();
        if ( list1 != null ) {
            xmindFilterQuery.setStatusList( new ArrayList<TestcaseStatusEnum>( list1 ) );
        }
        List<String> list2 = req.getTagList();
        if ( list2 != null ) {
            xmindFilterQuery.setTagList( new ArrayList<String>( list2 ) );
        }
        List<Long> list3 = req.getDutyUserList();
        if ( list3 != null ) {
            xmindFilterQuery.setDutyUserList( new ArrayList<Long>( list3 ) );
        }
        List<Long> list4 = req.getCreatorList();
        if ( list4 != null ) {
            xmindFilterQuery.setCreatorList( new ArrayList<Long>( list4 ) );
        }
        xmindFilterQuery.setCreateTimeBegin( req.getCreateTimeBegin() );
        xmindFilterQuery.setCreateTimeEnd( req.getCreateTimeEnd() );
        xmindFilterQuery.setModifyTimeBegin( req.getModifyTimeBegin() );
        xmindFilterQuery.setModifyTimeEnd( req.getModifyTimeEnd() );
        xmindFilterQuery.setTestStage( req.getTestStage() );
        xmindFilterQuery.setPlanCode( req.getPlanCode() );
        xmindFilterQuery.setPlanPattern( req.getPlanPattern() );
        List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
        if ( list5 != null ) {
            xmindFilterQuery.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
        }
        List<HeartCaseFilterEnum> list6 = req.getIsHeart();
        if ( list6 != null ) {
            xmindFilterQuery.setIsHeart( new ArrayList<HeartCaseFilterEnum>( list6 ) );
        }
        List<AutomaticRecordTypeEnum> list7 = req.getAutomaticTypeList();
        if ( list7 != null ) {
            xmindFilterQuery.setAutomaticTypeList( new ArrayList<AutomaticRecordTypeEnum>( list7 ) );
        }
        xmindFilterQuery.setVersionCode( req.getVersionCode() );
        xmindFilterQuery.setSetCore( req.getSetCore() );
        List<TestPlanCaseStatusEnum> list8 = req.getResultList();
        if ( list8 != null ) {
            xmindFilterQuery.setResultList( new ArrayList<TestPlanCaseStatusEnum>( list8 ) );
        }
        List<Long> list9 = req.getExecutorIdList();
        if ( list9 != null ) {
            xmindFilterQuery.setExecutorIdList( new ArrayList<Long>( list9 ) );
        }

        return xmindFilterQuery;
    }

    @Override
    public void converterQuery(ListTestcaseReq req, ListTestcaseExpQuery query) {
        if ( req == null ) {
            return;
        }

        query.setTransactor( req.getTransactor() );
        query.setPage( req.getPage() );
        query.setSize( req.getSize() );
        query.setGroupType( req.getGroupType() );
        query.setProductCode( req.getProductCode() );
        query.setType( req.getType() );
        query.setParentCode( req.getParentCode() );
        query.setCodeOrTitle( req.getCodeOrTitle() );
        if ( query.getPriorityList() != null ) {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.getPriorityList().clear();
                query.getPriorityList().addAll( list );
            }
            else {
                query.setPriorityList( null );
            }
        }
        else {
            List<TestcasePriorityEnum> list = req.getPriorityList();
            if ( list != null ) {
                query.setPriorityList( new ArrayList<TestcasePriorityEnum>( list ) );
            }
        }
        if ( query.getStatusList() != null ) {
            List<TestcaseStatusEnum> list1 = req.getStatusList();
            if ( list1 != null ) {
                query.getStatusList().clear();
                query.getStatusList().addAll( list1 );
            }
            else {
                query.setStatusList( null );
            }
        }
        else {
            List<TestcaseStatusEnum> list1 = req.getStatusList();
            if ( list1 != null ) {
                query.setStatusList( new ArrayList<TestcaseStatusEnum>( list1 ) );
            }
        }
        if ( query.getTagList() != null ) {
            List<String> list2 = req.getTagList();
            if ( list2 != null ) {
                query.getTagList().clear();
                query.getTagList().addAll( list2 );
            }
            else {
                query.setTagList( null );
            }
        }
        else {
            List<String> list2 = req.getTagList();
            if ( list2 != null ) {
                query.setTagList( new ArrayList<String>( list2 ) );
            }
        }
        if ( query.getDutyUserList() != null ) {
            List<Long> list3 = req.getDutyUserList();
            if ( list3 != null ) {
                query.getDutyUserList().clear();
                query.getDutyUserList().addAll( list3 );
            }
            else {
                query.setDutyUserList( null );
            }
        }
        else {
            List<Long> list3 = req.getDutyUserList();
            if ( list3 != null ) {
                query.setDutyUserList( new ArrayList<Long>( list3 ) );
            }
        }
        if ( query.getCreatorList() != null ) {
            List<Long> list4 = req.getCreatorList();
            if ( list4 != null ) {
                query.getCreatorList().clear();
                query.getCreatorList().addAll( list4 );
            }
            else {
                query.setCreatorList( null );
            }
        }
        else {
            List<Long> list4 = req.getCreatorList();
            if ( list4 != null ) {
                query.setCreatorList( new ArrayList<Long>( list4 ) );
            }
        }
        query.setCreateTimeBegin( req.getCreateTimeBegin() );
        query.setCreateTimeEnd( req.getCreateTimeEnd() );
        query.setModifyTimeBegin( req.getModifyTimeBegin() );
        query.setModifyTimeEnd( req.getModifyTimeEnd() );
        query.setTestStage( req.getTestStage() );
        query.setPlanCode( req.getPlanCode() );
        query.setPlanPattern( req.getPlanPattern() );
        query.setAutomaticSourceCode( req.getAutomaticSourceCode() );
        if ( query.getNodeTypeList() != null ) {
            List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
            if ( list5 != null ) {
                query.getNodeTypeList().clear();
                query.getNodeTypeList().addAll( list5 );
            }
            else {
                query.setNodeTypeList( null );
            }
        }
        else {
            List<AutomaticNodeTypeEnum> list5 = req.getNodeTypeList();
            if ( list5 != null ) {
                query.setNodeTypeList( new ArrayList<AutomaticNodeTypeEnum>( list5 ) );
            }
        }
        query.setSetCore( req.getSetCore() );
        query.setVersionCode( req.getVersionCode() );
        if ( query.getCheckCodeList() != null ) {
            List<String> list6 = req.getCheckCodeList();
            if ( list6 != null ) {
                query.getCheckCodeList().clear();
                query.getCheckCodeList().addAll( list6 );
            }
            else {
                query.setCheckCodeList( null );
            }
        }
        else {
            List<String> list6 = req.getCheckCodeList();
            if ( list6 != null ) {
                query.setCheckCodeList( new ArrayList<String>( list6 ) );
            }
        }
        query.setExportType( req.getExportType() );
    }

    @Override
    public EditTestcaseCommand convertEdit(AddTestcaseReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getCode();

        EditTestcaseCommand editTestcaseCommand = new EditTestcaseCommand( aggregateId );

        editTestcaseCommand.setProductCode( req.getProductCode() );
        editTestcaseCommand.setParentCode( req.getParentCode() );
        editTestcaseCommand.setName( req.getName() );
        editTestcaseCommand.setAttribute( req.getAttribute() );
        editTestcaseCommand.setType( req.getType() );
        editTestcaseCommand.setPriority( req.getPriority() );
        editTestcaseCommand.setPrecondition( req.getPrecondition() );
        editTestcaseCommand.setDutyUserId( req.getDutyUserId() );
        editTestcaseCommand.setDutyUser( req.getDutyUser() );
        editTestcaseCommand.setComment( req.getComment() );
        editTestcaseCommand.setLayer( req.getLayer() );
        editTestcaseCommand.setPath( req.getPath() );
        List<AttachmentVO> list = req.getAttachments();
        if ( list != null ) {
            editTestcaseCommand.setAttachments( new ArrayList<AttachmentVO>( list ) );
        }
        List<TagVO> list1 = req.getTags();
        if ( list1 != null ) {
            editTestcaseCommand.setTags( new ArrayList<TagVO>( list1 ) );
        }
        List<TestcaseRelationVO> list2 = req.getVos();
        if ( list2 != null ) {
            editTestcaseCommand.setVos( new ArrayList<TestcaseRelationVO>( list2 ) );
        }
        List<TestcaseStepVO> list3 = req.getTestSteps();
        if ( list3 != null ) {
            editTestcaseCommand.setTestSteps( new ArrayList<TestcaseStepVO>( list3 ) );
        }
        editTestcaseCommand.setVersionCode( req.getVersionCode() );

        return editTestcaseCommand;
    }

    @Override
    public PlanCaseResp converter(PlanCaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        PlanCaseResp planCaseResp = new PlanCaseResp();

        planCaseResp.setPriority( vo.getPriority() );
        planCaseResp.setStatus( vo.getStatus() );
        planCaseResp.setType( vo.getType() );
        planCaseResp.setPlanCode( vo.getPlanCode() );
        planCaseResp.setCaseCode( vo.getCaseCode() );
        planCaseResp.setProductCode( vo.getProductCode() );
        planCaseResp.setProductName( vo.getProductName() );
        planCaseResp.setVersionCode( vo.getVersionCode() );
        planCaseResp.setVersionName( vo.getVersionName() );
        planCaseResp.setParentCode( vo.getParentCode() );
        List<String> list = vo.getParentFullName();
        if ( list != null ) {
            planCaseResp.setParentFullName( new ArrayList<String>( list ) );
        }
        planCaseResp.setName( vo.getName() );
        planCaseResp.setPrecondition( vo.getPrecondition() );
        planCaseResp.setExecutorId( vo.getExecutorId() );
        planCaseResp.setExecutor( vo.getExecutor() );
        planCaseResp.setComment( vo.getComment() );
        planCaseResp.setEnable( vo.getEnable() );
        List<TestcaseStepVO> list1 = vo.getTestSteps();
        if ( list1 != null ) {
            planCaseResp.setTestSteps( new ArrayList<TestcaseStepVO>( list1 ) );
        }
        List<TagVO> list2 = vo.getTags();
        if ( list2 != null ) {
            planCaseResp.setTags( new ArrayList<TagVO>( list2 ) );
        }
        planCaseResp.setFields( testcaseEditFieldEnumListToFieldVOList( vo.getFields() ) );
        planCaseResp.setButtons( testcaseButtonEnumListToButtonVOList( vo.getButtons() ) );
        planCaseResp.setNodeType( vo.getNodeType() );
        planCaseResp.setModifierId( vo.getModifierId() );
        planCaseResp.setModifier( vo.getModifier() );
        planCaseResp.setGmtModified( vo.getGmtModified() );
        planCaseResp.setTestStage( vo.getTestStage() );
        planCaseResp.setResultFile( vo.getResultFile() );
        List<ExecuteCaseResultContentVO> list5 = vo.getResultContent();
        if ( list5 != null ) {
            planCaseResp.setResultContent( new ArrayList<ExecuteCaseResultContentVO>( list5 ) );
        }
        planCaseResp.setOperateCaseCode( vo.getOperateCaseCode() );
        planCaseResp.setAutomaticTaskId( vo.getAutomaticTaskId() );
        planCaseResp.setReportFile( vo.getReportFile() );
        planCaseResp.setExecLogFile( vo.getExecLogFile() );
        planCaseResp.setTestcaseStatus( vo.getTestcaseStatus() );
        planCaseResp.setAutomaticRecordType( vo.getAutomaticRecordType() );
        planCaseResp.setInterfaceName( vo.getInterfaceName() );
        planCaseResp.setResultComment( vo.getResultComment() );

        return planCaseResp;
    }

    @Override
    public FieldVO converter(TestcaseEditFieldEnum editField) {
        if ( editField == null ) {
            return null;
        }

        FieldVO fieldVO = new FieldVO();

        fieldVO.setCode( editField.name() );
        fieldVO.setName( editField.getDesc() );

        return fieldVO;
    }

    @Override
    public CheckCreatorOrDutyUserResp converter(CheckCreatorOrDutyUserVO vo) {
        if ( vo == null ) {
            return null;
        }

        CheckCreatorOrDutyUserResp checkCreatorOrDutyUserResp = new CheckCreatorOrDutyUserResp();

        checkCreatorOrDutyUserResp.setTotal( vo.getTotal() );
        checkCreatorOrDutyUserResp.setPassed( vo.getPassed() );
        checkCreatorOrDutyUserResp.setRefused( vo.getRefused() );

        return checkCreatorOrDutyUserResp;
    }

    @Override
    public CheckTestcaseStatusResp converter(CheckTestcaseStatusVO vo) {
        if ( vo == null ) {
            return null;
        }

        CheckTestcaseStatusResp checkTestcaseStatusResp = new CheckTestcaseStatusResp();

        checkTestcaseStatusResp.setTotal( vo.getTotal() );
        checkTestcaseStatusResp.setNormal( vo.getNormal() );
        checkTestcaseStatusResp.setDisable( vo.getDisable() );
        checkTestcaseStatusResp.setEdit( vo.getEdit() );

        return checkTestcaseStatusResp;
    }

    @Override
    public TestcaseIssueResp converter(TestcaseIssueVO vo) {
        if ( vo == null ) {
            return null;
        }

        TestcaseIssueResp testcaseIssueResp = new TestcaseIssueResp();

        testcaseIssueResp.setStatus( vo.getStatus() );
        testcaseIssueResp.setCode( vo.getCode() );
        testcaseIssueResp.setTitle( vo.getTitle() );
        testcaseIssueResp.setHandleUserId( vo.getHandleUserId() );
        testcaseIssueResp.setHandleUserName( vo.getHandleUserName() );

        return testcaseIssueResp;
    }

    @Override
    public TestcaseRequirementResp converter(TestcaseRequirementVO vo) {
        if ( vo == null ) {
            return null;
        }

        TestcaseRequirementResp testcaseRequirementResp = new TestcaseRequirementResp();

        testcaseRequirementResp.setCode( vo.getCode() );
        testcaseRequirementResp.setTitle( vo.getTitle() );
        testcaseRequirementResp.setStatus( vo.getStatus() );
        testcaseRequirementResp.setStatusDesc( vo.getStatusDesc() );
        testcaseRequirementResp.setDockingUserId( vo.getDockingUserId() );
        testcaseRequirementResp.setDockingUserName( vo.getDockingUserName() );

        return testcaseRequirementResp;
    }

    @Override
    public TestcaseResp converter(TestcaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        TestcaseResp testcaseResp = new TestcaseResp();

        testcaseResp.setPriority( vo.getPriority() );
        testcaseResp.setStatus( vo.getStatus() );
        testcaseResp.setType( vo.getType() );
        testcaseResp.setCode( vo.getCode() );
        testcaseResp.setProductCode( vo.getProductCode() );
        testcaseResp.setParentCode( vo.getParentCode() );
        List<String> list = vo.getParentFullName();
        if ( list != null ) {
            testcaseResp.setParentFullName( new ArrayList<String>( list ) );
        }
        testcaseResp.setName( vo.getName() );
        testcaseResp.setAttribute( vo.getAttribute() );
        testcaseResp.setTypeDesc( vo.getTypeDesc() );
        testcaseResp.setPriorityDesc( vo.getPriorityDesc() );
        testcaseResp.setStatusDesc( vo.getStatusDesc() );
        testcaseResp.setPrecondition( vo.getPrecondition() );
        testcaseResp.setDutyUserId( vo.getDutyUserId() );
        testcaseResp.setDutyUser( vo.getDutyUser() );
        testcaseResp.setComment( vo.getComment() );
        testcaseResp.setSort( vo.getSort() );
        testcaseResp.setNodeType( vo.getNodeType() );
        testcaseResp.setCreatorId( vo.getCreatorId() );
        testcaseResp.setCreator( vo.getCreator() );
        testcaseResp.setGmtCreate( vo.getGmtCreate() );
        testcaseResp.setModifierId( vo.getModifierId() );
        testcaseResp.setModifier( vo.getModifier() );
        testcaseResp.setGmtModified( vo.getGmtModified() );
        List<TestcaseStepVO> list1 = vo.getTestSteps();
        if ( list1 != null ) {
            testcaseResp.setTestSteps( new ArrayList<TestcaseStepVO>( list1 ) );
        }
        List<TagVO> list2 = vo.getTags();
        if ( list2 != null ) {
            testcaseResp.setTags( new ArrayList<TagVO>( list2 ) );
        }
        testcaseResp.setFields( testcaseEditFieldEnumListToFieldVOList( vo.getFields() ) );
        testcaseResp.setButtons( testcaseButtonEnumListToButtonVOList( vo.getButtons() ) );
        testcaseResp.setSetHeart( vo.getSetHeart() );
        testcaseResp.setAutomaticSourceCode( vo.getAutomaticSourceCode() );
        testcaseResp.setInterfaceName( vo.getInterfaceName() );
        testcaseResp.setAutomaticSourceType( vo.getAutomaticSourceType() );
        testcaseResp.setSetCore( vo.getSetCore() );
        testcaseResp.setVersionCode( vo.getVersionCode() );
        testcaseResp.setVersionName( vo.getVersionName() );

        return testcaseResp;
    }

    @Override
    public void converter(ListExecuteCaseReq req, ListExecuteCaseQuery query) {
        if ( req == null ) {
            return;
        }

        query.setAutomaticTaskCode( req.getCode() );
        query.setTransactor( req.getTransactor() );
        if ( query.getExecutorIdList() != null ) {
            List<String> list = req.getExecutorIdList();
            if ( list != null ) {
                query.getExecutorIdList().clear();
                query.getExecutorIdList().addAll( list );
            }
            else {
                query.setExecutorIdList( null );
            }
        }
        else {
            List<String> list = req.getExecutorIdList();
            if ( list != null ) {
                query.setExecutorIdList( new ArrayList<String>( list ) );
            }
        }
        if ( query.getStatus() != null ) {
            List<TestPlanCaseStatusEnum> list1 = req.getStatus();
            if ( list1 != null ) {
                query.getStatus().clear();
                query.getStatus().addAll( list1 );
            }
            else {
                query.setStatus( null );
            }
        }
        else {
            List<TestPlanCaseStatusEnum> list1 = req.getStatus();
            if ( list1 != null ) {
                query.setStatus( new ArrayList<TestPlanCaseStatusEnum>( list1 ) );
            }
        }
        query.setStartTimeBegin( req.getStartTimeBegin() );
        query.setStartTimeEnd( req.getStartTimeEnd() );
        query.setFinishTimeBegin( req.getFinishTimeBegin() );
        query.setFinishTimeEnd( req.getFinishTimeEnd() );
    }

    @Override
    public ListExecuteCaseResp converter(ListExecuteCaseVO vo) {
        if ( vo == null ) {
            return null;
        }

        ListExecuteCaseResp listExecuteCaseResp = new ListExecuteCaseResp();

        listExecuteCaseResp.setResult( vo.getResult() );
        listExecuteCaseResp.setCode( vo.getCode() );
        listExecuteCaseResp.setAutomaticTaskCode( vo.getAutomaticTaskCode() );
        listExecuteCaseResp.setName( vo.getName() );
        listExecuteCaseResp.setAttribute( vo.getAttribute() );
        listExecuteCaseResp.setParentCode( vo.getParentCode() );
        listExecuteCaseResp.setSort( vo.getSort() );
        listExecuteCaseResp.setEnable( vo.getEnable() );
        listExecuteCaseResp.setExecutorId( vo.getExecutorId() );
        listExecuteCaseResp.setExecutor( vo.getExecutor() );
        listExecuteCaseResp.setStartTime( vo.getStartTime() );
        listExecuteCaseResp.setFinishTime( vo.getFinishTime() );
        listExecuteCaseResp.setReportFile( vo.getReportFile() );
        listExecuteCaseResp.setExecLogFile( vo.getExecLogFile() );
        listExecuteCaseResp.setTestcaseCount( vo.getTestcaseCount() );
        listExecuteCaseResp.setChildren( listExecuteCaseVOListToListExecuteCaseRespList( vo.getChildren() ) );

        return listExecuteCaseResp;
    }

    @Override
    public EditTestcaseTitleCommand convertor(EditTestcaseTitleReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getCode();

        EditTestcaseTitleCommand editTestcaseTitleCommand = new EditTestcaseTitleCommand( aggregateId );

        editTestcaseTitleCommand.setCode( req.getCode() );
        editTestcaseTitleCommand.setName( req.getName() );
        editTestcaseTitleCommand.setPriority( req.getPriority() );
        editTestcaseTitleCommand.setVersionCode( req.getVersionCode() );

        return editTestcaseTitleCommand;
    }

    @Override
    public MoveModuleCommand convertor(MoveModuleReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        aggregateId = req.getCode();

        MoveModuleCommand moveModuleCommand = new MoveModuleCommand( aggregateId );

        moveModuleCommand.setCode( req.getCode() );
        moveModuleCommand.setName( req.getName() );
        moveModuleCommand.setProductCode( req.getProductCode() );
        moveModuleCommand.setParentCode( req.getParentCode() );
        moveModuleCommand.setParentName( req.getParentName() );
        moveModuleCommand.setLayer( req.getLayer() );
        moveModuleCommand.setOldPath( req.getOldPath() );
        moveModuleCommand.setNewPath( req.getNewPath() );
        moveModuleCommand.setType( req.getType() );
        moveModuleCommand.setAttribute( req.getAttribute() );

        return moveModuleCommand;
    }

    @Override
    public void converter(ListExecuteEnvReq req, ListExecuteEnvQuery query) {
        if ( req == null ) {
            return;
        }

        query.setProductCode( req.getProductCode() );
        query.setKeyWord( req.getKeyWord() );
    }

    protected ListXmindDetailResp listXmindDetailVOToListXmindDetailResp(ListXmindDetailVO listXmindDetailVO) {
        if ( listXmindDetailVO == null ) {
            return null;
        }

        ListXmindDetailResp listXmindDetailResp = new ListXmindDetailResp();

        listXmindDetailResp.setId( listXmindDetailVO.getId() );
        listXmindDetailResp.setTopic( listXmindDetailVO.getTopic() );
        listXmindDetailResp.setParentCode( listXmindDetailVO.getParentCode() );
        listXmindDetailResp.setDirection( listXmindDetailVO.getDirection() );
        listXmindDetailResp.setDisabled( listXmindDetailVO.getDisabled() );
        listXmindDetailResp.setExpanded( listXmindDetailVO.getExpanded() );
        listXmindDetailResp.setTagName( listXmindDetailVO.getTagName() );
        listXmindDetailResp.setTagValue( listXmindDetailVO.getTagValue() );
        listXmindDetailResp.setTagEdit( listXmindDetailVO.getTagEdit() );
        List<ListXmindDetailVO> list = listXmindDetailVO.getChildren();
        if ( list != null ) {
            listXmindDetailResp.setChildren( new ArrayList<ListXmindDetailVO>( list ) );
        }
        listXmindDetailResp.setAttribute( listXmindDetailVO.getAttribute() );
        listXmindDetailResp.setHasChilds( listXmindDetailVO.getHasChilds() );
        listXmindDetailResp.setPriority( listXmindDetailVO.getPriority() );
        listXmindDetailResp.setPriorityDesc( listXmindDetailVO.getPriorityDesc() );
        listXmindDetailResp.setTestcaseCount( listXmindDetailVO.getTestcaseCount() );
        listXmindDetailResp.setPath( listXmindDetailVO.getPath() );
        listXmindDetailResp.setExecutionStatus( listXmindDetailVO.getExecutionStatus() );
        listXmindDetailResp.setExecutionStatusDesc( listXmindDetailVO.getExecutionStatusDesc() );
        listXmindDetailResp.setTestcaseStatus( listXmindDetailVO.getTestcaseStatus() );
        listXmindDetailResp.setTestcaseStatusDesc( listXmindDetailVO.getTestcaseStatusDesc() );

        return listXmindDetailResp;
    }

    protected List<ListTestcaseModuleResp> listTestcaseModuleVOListToListTestcaseModuleRespList(List<ListTestcaseModuleVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ListTestcaseModuleResp> list1 = new ArrayList<ListTestcaseModuleResp>( list.size() );
        for ( ListTestcaseModuleVO listTestcaseModuleVO : list ) {
            list1.add( converter( listTestcaseModuleVO ) );
        }

        return list1;
    }

    protected List<FieldVO> testcaseEditFieldEnumListToFieldVOList(List<TestcaseEditFieldEnum> list) {
        if ( list == null ) {
            return null;
        }

        List<FieldVO> list1 = new ArrayList<FieldVO>( list.size() );
        for ( TestcaseEditFieldEnum testcaseEditFieldEnum : list ) {
            list1.add( converter( testcaseEditFieldEnum ) );
        }

        return list1;
    }

    protected List<ButtonVO> testcaseButtonEnumListToButtonVOList(List<TestcaseButtonEnum> list) {
        if ( list == null ) {
            return null;
        }

        List<ButtonVO> list1 = new ArrayList<ButtonVO>( list.size() );
        for ( TestcaseButtonEnum testcaseButtonEnum : list ) {
            list1.add( converter( testcaseButtonEnum ) );
        }

        return list1;
    }

    protected List<ListExecuteCaseResp> listExecuteCaseVOListToListExecuteCaseRespList(List<ListExecuteCaseVO> list) {
        if ( list == null ) {
            return null;
        }

        List<ListExecuteCaseResp> list1 = new ArrayList<ListExecuteCaseResp>( list.size() );
        for ( ListExecuteCaseVO listExecuteCaseVO : list ) {
            list1.add( converter( listExecuteCaseVO ) );
        }

        return list1;
    }
}
