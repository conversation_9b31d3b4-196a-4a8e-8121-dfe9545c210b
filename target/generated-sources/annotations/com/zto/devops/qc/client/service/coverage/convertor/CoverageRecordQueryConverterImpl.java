package com.zto.devops.qc.client.service.coverage.convertor;

import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageRecordPageQuery;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageRecordPageReq;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:11:09+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class CoverageRecordQueryConverterImpl implements CoverageRecordQueryConverter {

    @Override
    public CoverageRecordPageQuery convert(CoverageRecordPageReq coverageRecordPageReq) {
        if ( coverageRecordPageReq == null ) {
            return null;
        }

        CoverageRecordPageQuery coverageRecordPageQuery = new CoverageRecordPageQuery();

        coverageRecordPageQuery.setTransactor( coverageRecordPageReq.getTransactor() );
        coverageRecordPageQuery.setPage( coverageRecordPageReq.getPage() );
        coverageRecordPageQuery.setSize( coverageRecordPageReq.getSize() );
        coverageRecordPageQuery.setVersionCode( coverageRecordPageReq.getVersionCode() );
        coverageRecordPageQuery.setAppId( coverageRecordPageReq.getAppId() );
        List<String> list = coverageRecordPageReq.getBranchStatus();
        if ( list != null ) {
            coverageRecordPageQuery.setBranchStatus( new ArrayList<String>( list ) );
        }
        coverageRecordPageQuery.setCreatorId( coverageRecordPageReq.getCreatorId() );
        coverageRecordPageQuery.setGmtCreateStart( coverageRecordPageReq.getGmtCreateStart() );
        coverageRecordPageQuery.setGmtCreateEnd( coverageRecordPageReq.getGmtCreateEnd() );
        coverageRecordPageQuery.setDiffType( coverageRecordPageReq.getDiffType() );

        coverageRecordPageQuery.setOrderField( com.zto.devops.qc.client.enums.testmanager.coverage.CoverageFieldEnum.getNameByCode(coverageRecordPageReq.getOrderField()) );
        coverageRecordPageQuery.setOrderType( com.zto.devops.qc.client.enums.testmanager.coverage.OrderTypeEnum.getNameByCode(coverageRecordPageReq.getOrderType()) );

        return coverageRecordPageQuery;
    }
}
