package com.zto.devops.qc.client.service.relevantUser.model;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.relevantUser.query.MyIssueQuery;
import com.zto.devops.qc.client.model.relevantUser.query.MyTaskVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:11:09+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class RelevantConverterImpl implements RelevantConverter {

    @Override
    public MyIssueQuery convertTask(MyTaskReq req) {
        if ( req == null ) {
            return null;
        }

        MyIssueQuery myIssueQuery = new MyIssueQuery();

        myIssueQuery.setTransactor( req.getTransactor() );
        myIssueQuery.setPage( req.getPage() );
        myIssueQuery.setSize( req.getSize() );
        List<RelevantUserTypeEnum> list = req.getRelevantUserTypes();
        if ( list != null ) {
            myIssueQuery.setRelevantUserTypes( new ArrayList<RelevantUserTypeEnum>( list ) );
        }
        myIssueQuery.setDomain( req.getDomain() );
        myIssueQuery.setOrderField( req.getOrderField() );
        if ( req.getOrderType() != null ) {
            myIssueQuery.setOrderType( req.getOrderType().name() );
        }
        myIssueQuery.setUserId( req.getUserId() );
        myIssueQuery.setModifierId( req.getModifierId() );
        List<String> list1 = req.getStatus();
        if ( list1 != null ) {
            myIssueQuery.setStatus( new ArrayList<String>( list1 ) );
        }
        myIssueQuery.setName( req.getName() );
        myIssueQuery.setBusinessCode( req.getBusinessCode() );
        List<String> list2 = req.getProjectLevel();
        if ( list2 != null ) {
            myIssueQuery.setProjectLevel( new ArrayList<String>( list2 ) );
        }
        List<String> list3 = req.getProjectScale();
        if ( list3 != null ) {
            myIssueQuery.setProjectScale( new ArrayList<String>( list3 ) );
        }
        List<String> list4 = req.getWorthLevel();
        if ( list4 != null ) {
            myIssueQuery.setWorthLevel( new ArrayList<String>( list4 ) );
        }
        List<String> list5 = req.getVersionCodeList();
        if ( list5 != null ) {
            myIssueQuery.setVersionCodeList( new ArrayList<String>( list5 ) );
        }
        myIssueQuery.setGroupId( req.getGroupId() );

        return myIssueQuery;
    }

    @Override
    public MyTaskResp convert(MyTaskVO req) {
        if ( req == null ) {
            return null;
        }

        MyTaskResp myTaskResp = new MyTaskResp();

        myTaskResp.setProductName( reqProductName( req ) );
        myTaskResp.setProductCode( reqProductCode( req ) );
        myTaskResp.setFindVersionCode( reqFindVersionCode( req ) );
        myTaskResp.setFindVersionName( reqFindVersionName( req ) );
        myTaskResp.setDeveloperUserName( reqDeveloperUserName( req ) );
        myTaskResp.setDeveloperUserId( reqDeveloperUserId( req ) );
        myTaskResp.setTesterUserName( reqTesterUserName( req ) );
        myTaskResp.setTesterUserId( reqDockingUserUserId( req ) );
        myTaskResp.setAssociatedVersionName( reqAssociatedVersionName( req ) );
        myTaskResp.setAssociatedVersionCode( reqAssociatedVersionCode( req ) );
        myTaskResp.setStatus( req.getStatus() );
        myTaskResp.setStatusDesc( req.getStatusDesc() );
        myTaskResp.setName( req.getName() );
        myTaskResp.setPriority( req.getPriority() );
        myTaskResp.setPriorityDesc( req.getPriorityDesc() );
        myTaskResp.setSpannedTime( req.getSpannedTime() );
        myTaskResp.setPlanWorkHours( req.getPlanWorkHours() );
        myTaskResp.setWarn( req.getWarn() );
        myTaskResp.setWarnDay( req.getWarnDay() );
        myTaskResp.setProduct( req.getProduct() );
        myTaskResp.setLevel( req.getLevel() );
        myTaskResp.setFindVersion( req.getFindVersion() );
        myTaskResp.setDeveloper( req.getDeveloper() );
        myTaskResp.setTester( req.getTester() );
        myTaskResp.setStartDate( req.getStartDate() );
        myTaskResp.setPublishDate( req.getPublishDate() );
        myTaskResp.setEndTime( req.getEndTime() );
        myTaskResp.setProjectScale( req.getProjectScale() );
        myTaskResp.setProjectType( req.getProjectType() );
        myTaskResp.setAssociatedVersion( req.getAssociatedVersion() );
        myTaskResp.setBusinessCode( req.getBusinessCode() );
        List<Button> list = req.getButtonVOs();
        if ( list != null ) {
            myTaskResp.setButtonVOs( new ArrayList<Button>( list ) );
        }
        myTaskResp.setGmtCreate( req.getGmtCreate() );

        myTaskResp.setDomain( com.zto.devops.framework.client.enums.DomainEnum.getNameByEnum(req.getDomainEnum()) );
        myTaskResp.setDomainDesc( com.zto.devops.framework.client.enums.DomainEnum.getValueByEnum(req.getDomainEnum()) );

        return myTaskResp;
    }

    @Override
    public List<MyTaskResp> convert(List<MyTaskVO> req) {
        if ( req == null ) {
            return null;
        }

        List<MyTaskResp> list = new ArrayList<MyTaskResp>( req.size() );
        for ( MyTaskVO myTaskVO : req ) {
            list.add( convert( myTaskVO ) );
        }

        return list;
    }

    private String reqProductName(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        Product product = myTaskVO.getProduct();
        if ( product == null ) {
            return null;
        }
        String name = product.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String reqProductCode(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        Product product = myTaskVO.getProduct();
        if ( product == null ) {
            return null;
        }
        String code = product.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String reqFindVersionCode(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        Version findVersion = myTaskVO.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String code = findVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }

    private String reqFindVersionName(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        Version findVersion = myTaskVO.getFindVersion();
        if ( findVersion == null ) {
            return null;
        }
        String name = findVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String reqDeveloperUserName(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        User developer = myTaskVO.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        String userName = developer.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long reqDeveloperUserId(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        User developer = myTaskVO.getDeveloper();
        if ( developer == null ) {
            return null;
        }
        Long userId = developer.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String reqTesterUserName(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        User tester = myTaskVO.getTester();
        if ( tester == null ) {
            return null;
        }
        String userName = tester.getUserName();
        if ( userName == null ) {
            return null;
        }
        return userName;
    }

    private Long reqDockingUserUserId(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        User dockingUser = myTaskVO.getDockingUser();
        if ( dockingUser == null ) {
            return null;
        }
        Long userId = dockingUser.getUserId();
        if ( userId == null ) {
            return null;
        }
        return userId;
    }

    private String reqAssociatedVersionName(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        Version associatedVersion = myTaskVO.getAssociatedVersion();
        if ( associatedVersion == null ) {
            return null;
        }
        String name = associatedVersion.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String reqAssociatedVersionCode(MyTaskVO myTaskVO) {
        if ( myTaskVO == null ) {
            return null;
        }
        Version associatedVersion = myTaskVO.getAssociatedVersion();
        if ( associatedVersion == null ) {
            return null;
        }
        String code = associatedVersion.getCode();
        if ( code == null ) {
            return null;
        }
        return code;
    }
}
