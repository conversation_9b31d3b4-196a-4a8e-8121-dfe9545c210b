package com.zto.devops.qc.client.service.coverage.convertor;

import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.parameter.CoverageRecordEditParameter;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.testmanager.coverage.command.BatchCoverageCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.CoverageNotStandardReasonEditCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.EditCoverageCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.GenerateCoverageCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.VerifyCoverageConditionCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageAppInfoVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageResultVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageTaskVO;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageResultListQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageResultQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageTaskQuery;
import com.zto.devops.qc.client.service.coverage.model.req.BatchCoverageReq;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageReasonEditReq;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageTaskReq;
import com.zto.devops.qc.client.service.coverage.model.req.EditCoverageReq;
import com.zto.devops.qc.client.service.coverage.model.req.GenerateCoverageReq;
import com.zto.devops.qc.client.service.coverage.model.req.TmCodeCoverResultListReq;
import com.zto.devops.qc.client.service.coverage.model.req.TmCodeCoverResultReq;
import com.zto.devops.qc.client.service.coverage.model.req.VerifyGenerateConditionReq;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageTaskResp;
import com.zto.devops.qc.client.service.coverage.model.resp.TmCodeCoverResultResp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-08T13:11:09+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_92 (Oracle Corporation)"
)
@Component
public class CoverageAdapterConverterImpl implements CoverageAdapterConverter {

    @Override
    public GenerateCoverageCommand convertor(GenerateCoverageReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        GenerateCoverageCommand generateCoverageCommand = new GenerateCoverageCommand( aggregateId );

        generateCoverageCommand.setProductCode( req.getProductCode() );
        generateCoverageCommand.setAppId( req.getAppId() );
        generateCoverageCommand.setRecordType( req.getRecordType() );
        generateCoverageCommand.setVersionCode( req.getVersionCode() );
        generateCoverageCommand.setVersionName( req.getVersionName() );
        generateCoverageCommand.setDiffType( req.getDiffType() );

        return generateCoverageCommand;
    }

    @Override
    public BatchCoverageCommand convertor(BatchCoverageReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        BatchCoverageCommand batchCoverageCommand = new BatchCoverageCommand( aggregateId );

        batchCoverageCommand.setProductCode( req.getProductCode() );
        batchCoverageCommand.setVersionCode( req.getVersionCode() );
        batchCoverageCommand.setVersionName( req.getVersionName() );
        List<String> list = req.getAppIdList();
        if ( list != null ) {
            batchCoverageCommand.setAppIdList( new ArrayList<String>( list ) );
        }
        batchCoverageCommand.setRecordType( req.getRecordType() );
        batchCoverageCommand.setDiffType( req.getDiffType() );

        return batchCoverageCommand;
    }

    @Override
    public TmCodeCoverResultResp convertCodeCoverResult(CoverageResultVO vo) {
        if ( vo == null ) {
            return null;
        }

        TmCodeCoverResultResp tmCodeCoverResultResp = new TmCodeCoverResultResp();

        tmCodeCoverResultResp.setCoverageReasonVOS( coverageAppInfoVOListToCoverageReasonVOList( vo.getAppList() ) );
        tmCodeCoverResultResp.setCodeCoverResult( vo.getCodeCoverResult() );
        tmCodeCoverResultResp.setCodeCoverResultDesc( vo.getCodeCoverResultDesc() );
        List<String> list1 = vo.getReasonList();
        if ( list1 != null ) {
            tmCodeCoverResultResp.setReasonList( new ArrayList<String>( list1 ) );
        }

        return tmCodeCoverResultResp;
    }

    @Override
    public CoverageReasonVO convertReason(CoverageAppInfoVO infoVO) {
        if ( infoVO == null ) {
            return null;
        }

        CoverageReasonVO coverageReasonVO = new CoverageReasonVO();

        coverageReasonVO.setAppId( infoVO.getAppId() );
        coverageReasonVO.setRecordRate( infoVO.getRecordRate() );
        List<String> list = infoVO.getReasonList();
        if ( list != null ) {
            coverageReasonVO.setReasonList( new ArrayList<String>( list ) );
        }
        coverageReasonVO.setCustomReason( infoVO.getCustomReason() );
        coverageReasonVO.setVersionCode( infoVO.getVersionCode() );
        coverageReasonVO.setVersionName( infoVO.getVersionName() );

        return coverageReasonVO;
    }

    @Override
    public CoverageResultQuery convertResultQuery(TmCodeCoverResultReq req) {
        if ( req == null ) {
            return null;
        }

        CoverageResultQuery coverageResultQuery = new CoverageResultQuery();

        coverageResultQuery.setVersionCode( req.getVersionCode() );
        coverageResultQuery.setReportType( req.getReportType() );

        return coverageResultQuery;
    }

    @Override
    public CoverageRecordGenerateParameter converter(GenerateCoverageReq req) {
        if ( req == null ) {
            return null;
        }

        CoverageRecordGenerateParameter coverageRecordGenerateParameter = new CoverageRecordGenerateParameter();

        coverageRecordGenerateParameter.setProductCode( req.getProductCode() );
        coverageRecordGenerateParameter.setVersionCode( req.getVersionCode() );
        coverageRecordGenerateParameter.setVersionName( req.getVersionName() );
        coverageRecordGenerateParameter.setRecordType( req.getRecordType() );
        coverageRecordGenerateParameter.setDiffType( req.getDiffType() );

        coverageRecordGenerateParameter.setAppIdList( java.util.Arrays.asList(req.getAppId()) );

        return coverageRecordGenerateParameter;
    }

    @Override
    public CoverageRecordGenerateParameter converter(BatchCoverageReq req) {
        if ( req == null ) {
            return null;
        }

        CoverageRecordGenerateParameter coverageRecordGenerateParameter = new CoverageRecordGenerateParameter();

        coverageRecordGenerateParameter.setProductCode( req.getProductCode() );
        coverageRecordGenerateParameter.setVersionCode( req.getVersionCode() );
        coverageRecordGenerateParameter.setVersionName( req.getVersionName() );
        List<String> list = req.getAppIdList();
        if ( list != null ) {
            coverageRecordGenerateParameter.setAppIdList( new ArrayList<String>( list ) );
        }
        coverageRecordGenerateParameter.setRecordType( req.getRecordType() );
        coverageRecordGenerateParameter.setDiffType( req.getDiffType() );

        return coverageRecordGenerateParameter;
    }

    @Override
    public CoverageRecordEditParameter converter(EditCoverageReq req) {
        if ( req == null ) {
            return null;
        }

        CoverageRecordEditParameter coverageRecordEditParameter = new CoverageRecordEditParameter();

        coverageRecordEditParameter.setVersionCode( req.getVersionCode() );
        coverageRecordEditParameter.setAppId( req.getAppId() );
        coverageRecordEditParameter.setComment( req.getComment() );
        coverageRecordEditParameter.setProductCode( req.getProductCode() );
        coverageRecordEditParameter.setDiffType( req.getDiffType() );

        return coverageRecordEditParameter;
    }

    @Override
    public EditCoverageCommand convert(EditCoverageReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        EditCoverageCommand editCoverageCommand = new EditCoverageCommand( aggregateId );

        editCoverageCommand.setVersionCode( req.getVersionCode() );
        editCoverageCommand.setComment( req.getComment() );
        editCoverageCommand.setAppId( req.getAppId() );
        editCoverageCommand.setProductCode( req.getProductCode() );
        editCoverageCommand.setDiffType( req.getDiffType() );

        return editCoverageCommand;
    }

    @Override
    public CoverageNotStandardReasonEditCommand convert(CoverageReasonEditReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        CoverageNotStandardReasonEditCommand coverageNotStandardReasonEditCommand = new CoverageNotStandardReasonEditCommand( aggregateId );

        coverageNotStandardReasonEditCommand.setVersionCode( req.getVersionCode() );
        coverageNotStandardReasonEditCommand.setAppId( req.getAppId() );
        coverageNotStandardReasonEditCommand.setProductCode( req.getProductCode() );
        List<String> list = req.getReasonList();
        if ( list != null ) {
            coverageNotStandardReasonEditCommand.setReasonList( new ArrayList<String>( list ) );
        }
        coverageNotStandardReasonEditCommand.setCustomReason( req.getCustomReason() );

        return coverageNotStandardReasonEditCommand;
    }

    @Override
    public CoverageTaskQuery converter(CoverageTaskReq req) {
        if ( req == null ) {
            return null;
        }

        CoverageTaskQuery coverageTaskQuery = new CoverageTaskQuery();

        coverageTaskQuery.setTransactor( req.getTransactor() );
        coverageTaskQuery.setPage( req.getPage() );
        coverageTaskQuery.setSize( req.getSize() );
        coverageTaskQuery.setProductCode( req.getProductCode() );
        coverageTaskQuery.setAppId( req.getAppId() );
        List<RecordStatusEnum> list = req.getStatusList();
        if ( list != null ) {
            coverageTaskQuery.setStatusList( new ArrayList<RecordStatusEnum>( list ) );
        }
        List<RecordTypeEnum> list1 = req.getRecordTypeList();
        if ( list1 != null ) {
            coverageTaskQuery.setRecordTypeList( new ArrayList<RecordTypeEnum>( list1 ) );
        }
        List<DiffTypeEnum> list2 = req.getDiffTypeList();
        if ( list2 != null ) {
            coverageTaskQuery.setDiffTypeList( new ArrayList<DiffTypeEnum>( list2 ) );
        }
        List<String> list3 = req.getVersionCodeList();
        if ( list3 != null ) {
            coverageTaskQuery.setVersionCodeList( new ArrayList<String>( list3 ) );
        }
        List<Long> list4 = req.getCreatorIdList();
        if ( list4 != null ) {
            coverageTaskQuery.setCreatorIdList( new ArrayList<Long>( list4 ) );
        }
        coverageTaskQuery.setRecordCreateStart( req.getRecordCreateStart() );
        coverageTaskQuery.setRecordCreateEnd( req.getRecordCreateEnd() );
        coverageTaskQuery.setRecordModifiedStart( req.getRecordModifiedStart() );
        coverageTaskQuery.setRecordModifiedEnd( req.getRecordModifiedEnd() );

        return coverageTaskQuery;
    }

    @Override
    public List<CoverageTaskResp> convertList(List<CoverageTaskVO> list) {
        if ( list == null ) {
            return null;
        }

        List<CoverageTaskResp> list1 = new ArrayList<CoverageTaskResp>( list.size() );
        for ( CoverageTaskVO coverageTaskVO : list ) {
            list1.add( converter( coverageTaskVO ) );
        }

        return list1;
    }

    @Override
    public CoverageTaskResp converter(CoverageTaskVO vo) {
        if ( vo == null ) {
            return null;
        }

        CoverageTaskResp coverageTaskResp = new CoverageTaskResp();

        coverageTaskResp.setTaskId( vo.getTaskId() );
        coverageTaskResp.setAppId( vo.getAppId() );
        coverageTaskResp.setStandardRate( vo.getStandardRate() );
        coverageTaskResp.setRecordRate( vo.getRecordRate() );
        coverageTaskResp.setRecordStatus( vo.getRecordStatus() );
        coverageTaskResp.setRecordStatusDesc( vo.getRecordStatusDesc() );
        coverageTaskResp.setAppNums( vo.getAppNums() );
        coverageTaskResp.setRecordType( vo.getRecordType() );
        coverageTaskResp.setRecordTypeDesc( vo.getRecordTypeDesc() );
        coverageTaskResp.setDiffType( vo.getDiffType() );
        coverageTaskResp.setDiffTypeDesc( vo.getDiffTypeDesc() );
        coverageTaskResp.setRecordErrorMsg( vo.getRecordErrorMsg() );
        coverageTaskResp.setVersionCode( vo.getVersionCode() );
        coverageTaskResp.setVersionName( vo.getVersionName() );
        coverageTaskResp.setEnvName( vo.getEnvName() );
        coverageTaskResp.setRecordCreator( vo.getRecordCreator() );
        coverageTaskResp.setRecordCreate( vo.getRecordCreate() );
        coverageTaskResp.setRecordModified( vo.getRecordModified() );
        coverageTaskResp.setComment( vo.getComment() );
        List<CoverageTaskVO> list = vo.getChildren();
        if ( list != null ) {
            coverageTaskResp.setChildren( new ArrayList<CoverageTaskVO>( list ) );
        }
        coverageTaskResp.setBucketName( vo.getBucketName() );
        coverageTaskResp.setFileName( vo.getFileName() );

        return coverageTaskResp;
    }

    @Override
    public CoverageResultListQuery convertResultListQuery(TmCodeCoverResultListReq req) {
        if ( req == null ) {
            return null;
        }

        CoverageResultListQuery coverageResultListQuery = new CoverageResultListQuery();

        List<String> list = req.getVersionCodeList();
        if ( list != null ) {
            coverageResultListQuery.setVersionCodeList( new ArrayList<String>( list ) );
        }

        return coverageResultListQuery;
    }

    @Override
    public VerifyCoverageConditionCommand convertor(VerifyGenerateConditionReq req) {
        if ( req == null ) {
            return null;
        }

        String aggregateId = null;

        VerifyCoverageConditionCommand verifyCoverageConditionCommand = new VerifyCoverageConditionCommand( aggregateId );

        verifyCoverageConditionCommand.setProductCode( req.getProductCode() );
        verifyCoverageConditionCommand.setVersionCode( req.getVersionCode() );
        verifyCoverageConditionCommand.setVersionName( req.getVersionName() );
        List<String> list = req.getAppIdList();
        if ( list != null ) {
            verifyCoverageConditionCommand.setAppIdList( new ArrayList<String>( list ) );
        }
        verifyCoverageConditionCommand.setRecordType( req.getRecordType() );
        verifyCoverageConditionCommand.setDiffType( req.getDiffType() );

        return verifyCoverageConditionCommand;
    }

    protected List<CoverageReasonVO> coverageAppInfoVOListToCoverageReasonVOList(List<CoverageAppInfoVO> list) {
        if ( list == null ) {
            return null;
        }

        List<CoverageReasonVO> list1 = new ArrayList<CoverageReasonVO>( list.size() );
        for ( CoverageAppInfoVO coverageAppInfoVO : list ) {
            list1.add( convertReason( coverageAppInfoVO ) );
        }

        return list1;
    }
}
