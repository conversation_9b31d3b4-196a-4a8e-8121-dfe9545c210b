package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.devops.qc.client.model.dto.TmEmailEntityDO;
import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.PageEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.VersionEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.query.DetailEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.PageEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.TestPlanToSendMailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.VersionEmailQuery;
import com.zto.devops.qc.client.model.testmanager.report.entity.EmailMemberVO;
import com.zto.devops.qc.client.model.testmanager.report.event.ReportAddedEvent;

import java.util.Date;
import java.util.List;

public interface IEmailRepository {

    PageEmailVO pageEmail(PageEmailQuery query);

    Boolean testPlanToSendMail(TestPlanToSendMailQuery query);

    List<VersionEmailVO> getVersionEmail(VersionEmailQuery query);

    DetailEmailVO findEmailDetail(DetailEmailQuery query);

    List<TmEmailEntityDO> selectEnableByRelatePlanCodeAndEmailType(String relatePlanCode, EmailTypeEnum emailType);

    List<EmailMemberVO> queryEmailMembers(String reportCode);

    List<TmEmailEntityDO> selectByBusinessCode(String businessCode);

    List<TmEmailEntityDO> getRelatedPlanList(String versionCode, TestPlanNewTypeEnum planType);

    List<TmEmailEntityDO> getRelatedReportList(String versionCode, TestReportTypeEnum reportType);

    void addEmail(ReportAddedEvent event);

    void transferEmail(Date begin, Date end);

    List<TmEmailEntityDO> selectByBusinessCodeAndEmailType(String businessCode, EmailTypeEnum emailType);

    void insertSelective(TmEmailEntityDO entityDO);
}
