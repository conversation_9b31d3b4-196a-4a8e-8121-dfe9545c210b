package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneTagEnum;
import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.event.TagAddedEvent;
import com.zto.devops.qc.client.model.issue.event.TagRemovedSimpleEvent;

import java.util.List;

public interface TagRepository {

    List<TagVO> listTagsByBusinessCode(String businessCode);

    void addTag(TagAddedEvent event);

    TagVO findTagByCode(String code);

    void removeTag(TagRemovedSimpleEvent event);

    List<TagVO> getTagsByCodeList(List<String> codeList);

    void addTagList(List<TagVO> tags, String businessCode, BaseEvent event);

    TagVO findTestcaseTagQuery(String code);

    Long tagTestcaseNo(String tagName, String domain, String productCode);

    void deleteTestcaseTag(List<String> codeList);

    List<String> findCodesByNameAndProCode(String tagName, String businessCode);

    Integer saveBatch(List<TagEntityDO> list);

    List<TagVO> queryBusinessTagWithFixVersionCode(String versionCode);

    void saveSceneTag(String sceneCode, SceneTagEnum sceneTag);

    void removeSceneTag(String sceneCode, List<SceneTagEnum> sceneTags);

    List<String> findBusinessCodeByTag(SceneTagEnum sceneTag);

    List<TagVO> listTagsByBusinessCodeAndDomain(String businessCode, DomainEnum domain);

    List<TagVO> selectByIssueCodeList(List<String> issueCodeList);
}
