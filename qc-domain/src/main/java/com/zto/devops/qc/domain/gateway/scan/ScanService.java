package com.zto.devops.qc.domain.gateway.scan;

import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoveragePublishVO;
import com.zto.devops.qc.domain.model.scan.ScanObject;

public interface ScanService {

    Boolean staticScanAndSave(ScanObject scanObject, CoveragePublishVO coveragePublishVO, CoverageRecordGenerateParameter parameter) throws Exception;

    void batchDeleteHistoryDataByVersionCode(String versionCode);
}