package com.zto.devops.qc.domain.gateway.metadata;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata.*;

import java.util.List;

public interface MetaDataService {

    String getMetaDataProductCode(String productCode);

    List<MetaDataDocHttpVO> getMetaDataDocHttp(String productCode);

    MetaDataDocHttpVO getMetaDataDocDetail(Long id);

    List<GatewayNamespaceVO> getGatewayNamespace();

    List<GatewayApiVO> getGatewayApi(String namespace);

    List<GlobalEnvVO> getDocGlobalEnv(String productCode);

    String getLubanProductCode(String productCode);

    List<MetaDataDocHttpVO> getMetaDataDocDubbo(String productCode);

    GatewayApiSettingVO getGatewayApiSetting(String productCode, String serviceInfo);
}
