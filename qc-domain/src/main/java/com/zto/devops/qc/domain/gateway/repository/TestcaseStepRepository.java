package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseStepQuery;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.model.dto.TestcaseStepEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;

import java.util.List;

public interface TestcaseStepRepository {

    List<TestcaseStepVO> listTestcaseStepQuery(ListTestcaseStepQuery query);

    List<TestcaseStepVO> selectStepByCaseCodes(List<String> caseCodes);

    List<TestcaseStepVO> getTestcaseStepVOList(TestcaseEntityDO vo);

    List<TestcaseStepEntityDO> selectTestcaseStepByCaseCodeList(List<String> caseCodes);

    void saveBatch(List<TestcaseStepEntityDO> entityDOList);

    void addTestcaseStepList(List<TestcaseStepVO> testSteps, String testcaseCode, BaseEvent event);

    List<TestcaseStepVO> handle(ListTestcaseStepQuery query);
}
