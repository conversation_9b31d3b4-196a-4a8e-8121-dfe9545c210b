package com.zto.devops.qc.domain.gateway.gitlab;

import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;
import com.zto.devops.qc.domain.model.coverage.CommitInfo;

import java.util.List;

public interface GitlabService {

    /**
     * 目标分支是否包含源分支
     *
     * @param projectId
     * @param sourceBranch 源分支
     * @param targetBranch 目标分支
     * @return
     */
    boolean targetContainSource(Integer projectId, String sourceBranch, String targetBranch);

    /**
     * 获取分支最新commitId
     *
     * @param projectId
     * @param branch
     * @return
     */
    String getLatestCommitId(Integer projectId, String branch);

    String getBranchCommitId(Integer gitProjectId, String branchName);

    void clone(CoverageRecordGenerateVO entity, String sourcePath);

    List<CommitInfo> getLastCommitInfo(String filePath, String sourcePath);

}
