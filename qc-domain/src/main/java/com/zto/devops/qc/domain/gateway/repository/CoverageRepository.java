package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.model.dto.CoverageRecordBasicEntityDO;
import com.zto.devops.qc.client.model.dto.CoverageRecordEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordEditParameter;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.*;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageRecordPageQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageResultQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageTaskQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmTestReportVO;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageRecordReq;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageRecordResp;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface CoverageRepository {

    List<CoveragePublishVO> getLatestPublishRecord(CoveragePublishQuery query);

    void updateCoverageRecordById(Long id, String status, String recordErrorMsg, String recordUrl, String commitId,
                                  String basicCommitId, String basicBranchName, BigDecimal recordRate,
                                  String remark, Integer codeCoverNum, Integer codeSum, String envName, String gitCompareUrl,
                                  String bucketName, String fileName);

    List<CoverageRecordBasicVO> getInitialCoverageRecords(CoverageRecordGenerateParameter parameter);

    void updateByPrimaryKeySelective(CoverageRecordBasicVO vo);

    List<CoverageRecordVO> getCoverageRecordList(CoverageRecordPageQuery coverageRecordPageQuery);

    CoverageRecordResp getLatestCoverageReport(CoverageRecordReq req);

    CoveragePublishVO getLatestPublishRecordByEntity(CoveragePublishVO vo);

    CoveragePublishVO getFirstBranchPublish(CoveragePublishVO vo);

    void delCoverageRecord(CoverageRecordBasicVO vo);

    CoverageRecordBasicVO insertCoverageRecordBasic(CoverageRecordBasicVO basicVO);

    void insertSelective(CoveragePublishVO publishVO);

    CoverageRecordGenerateVO selectCoverageRecords(CoverageRecordBasicVO basicVO);

    List<CoverageExecVO> getExecList(String versionCode, String appId, String commitId, String flowLaneType, DiffTypeEnum diffTypeEnum);

    List<CoveragePublishVO> getMiddlePublishEntity(CoverageRecordGenerateVO generateVO);

    List<CoverageExecVO> getMiddleExecList(Set<String> middleCommitIdList, String appId, String versionCode, String branchName, String flowLaneType, DiffTypeEnum diffTypeEnum);

    List<CoveragePublishVO> getLatestPublishRecordByProductCode(CoveragePublishQuery query);

    void insertSelective(CoverageExecVO execVO);

    CoverageVersionRateVO getVersionRecordRate(String versionCode);

    void editCoverage(CoverageRecordEditParameter parameter);

    List<TmTestPlanVO> getTestPlan(CoverageRecordPageQuery query);

    List<CoverageTaskVO> selectTaskIdFromCoverage(CoverageTaskQuery query);

    List<CoverageTaskVO> selectCoverageByTaskId(CoverageTaskQuery query);

    List<CoverageAppInfoVO> selectListByVersionCode(String versionCode, List<String> list);

    List<CoverageAppInfoVO> selectListByVersionCodeList(List<String> versionCodeList, List<String> list);

    TmTestReportVO getTmTestReport(CoverageResultQuery query);

    List<CoverageRecordBasicVO> getExistCoverageRecords(String versionCode, List<String> branchNames, String appType);

    void batchInsert(List<CoverageRecordBasicVO> coverageRecordBasicList);

    int selectBranchBasicCount(CoverageBranchBasicVO basicVO);

    void batchInsertBranchBasic(List<CoverageBranchBasicVO> coverageRecordBasicList);

    void updateBatch(CoverageReasonEditEvent event);

    void updateCoverageRemark(String versionCode, String appId, CoverageRecordBasicEntityDO entityDO);

    String selectCoverageRemark(String versionCode, String appId);

    void resetCoverageReason(String versionCode, String appId, User user);

    List<CoverageRecordBasicVO> getCoverageRunningList(String timeoutDate);

    List<CoverageRecordBasicEntityDO> getLastRecordEntity(CoverageRecordGenerateVO vo);

    List<CoveragePublishVO> getPublishRecordByCommitId(CoveragePublishVO publishVO);

    List<CoveragePublishVO> getPublishRecordLastDay(Date startTime, Date endTime);

    String getGitCompareUrl(String versionCode, String appId, String branchName);

    List<CoverageRecordBasicVO> getCoverageRecordListByLimit();

    /**
     * 根据应用id，查询已存在覆盖率记录
     *
     * @param versionCode 版本code
     * @param branchNames 分支名称集合
     * @param appType     应用类型
     * @param appIdList   应用id集合
     * @return
     */
    List<CoverageRecordBasicVO> getExistCoverageRecordsByAppIdList(String versionCode,
                                                                   List<String> branchNames,
                                                                   String appType,
                                                                   List<String> appIdList);

    CoverageRecordBasicVO getInitialCoverageRecordByAppId(CoverageRecordGenerateParameter parameter, String appId);

    /**
     * 根据taskId，查询自动生成的覆盖率结果
     *
     * @param taskId
     * @return
     */
    List<CoverageRecordBasicEntityDO> selectAutoGenerateResultByTaskId(String productCode, String taskId, List<RecordStatusEnum> statusList);

    /**
     * 根据版本code 和 appid 查询当前批次覆盖率生成情况
     *
     * @param versionCode 版本code
     * @param appIdList   应用id集合
     * @return
     */
    List<CoverageRecordBasicEntityDO> selectAutoGenerateResultByAppIdList(String versionCode, List<String> appIdList);

    CoverageRecordEntityDO queryCoverageRecordById(Long id);

    /**
     * 根据版本批量查询覆盖率
     * @param versionCode 版本编码
     * @return 覆盖率
     */
    List<CoverageVersionRateVO> getVersionRecordRateList(List<String> versionCode);

    /**
     * 根据版本code和应用id，查询basic数据
     *
     * @param versionCodes 版本号集合
     * @param appId        应用id
     * @return {@link CoverageBranchBasicVO}
     */
    List<CoverageBranchBasicVO> getCoverageBasicByVersionCodesAndAppId(Set<String> versionCodes, String appId);

    /**
     * 更新basic
     *
     * @param toUpdateList {@link CoverageBranchBasicVO}
     */
    void batchUpdateBranchBasic(List<CoverageBranchBasicVO> toUpdateList);

    /**
     * 查同版本同应用，相同commitId的部署记录
     *
     * @param versionCode 版本号
     * @param commitId    提交记录编号
     * @param appId       应用id
     * @return {@link CoveragePublishVO}
     */
    List<CoveragePublishVO> getByCommitId(String versionCode, String commitId, String appId);
}
