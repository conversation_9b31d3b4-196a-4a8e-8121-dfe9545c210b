package com.zto.devops.qc.domain.gateway.impexp;

import com.zto.devops.qc.client.model.report.entity.PlanFunctionPointResp;
import com.zto.devops.qc.client.model.report.entity.PlanMobileResp;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/5/17
 * @Version 1.0
 */
public interface IExcelProcess {

    List<PlanFunctionPointResp> ExcelWriterPFP(String url);

    List<PlanMobileResp> ExcelWriterPM(String url);
}
