package com.zto.devops.qc.domain.gateway.util;

import com.zto.devops.qc.client.service.testmanager.apitest.model.ApiDebugLogResp;

import java.util.concurrent.ScheduledExecutorService;

public interface ApiDebugLogService {

    ApiDebugLogResp queryApiDebugLog(String taskId);

    void setApiDebugLog(String taskId, String logContent, boolean isDocument);

//    void abortApiDebugLog(String taskId);

//    void readJmeterLogExecutor(String taskId, ScheduledExecutorService executor);

}
