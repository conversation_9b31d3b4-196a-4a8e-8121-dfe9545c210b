package com.zto.devops.qc.domain.gateway.repository;

import com.github.pagehelper.PageInfo;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.testmanager.apitest.command.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.event.*;
import com.zto.devops.qc.client.model.testmanager.apitest.query.ApiTestCaseExecuteDetailQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiCaseQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiExceptionCaseQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiTestCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ApiTestCaseExecuteDetailTiledVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ApiTestRepository {

    ApiTestVariableVO getApiTestVariableByVariableCode(String variableCode);

    ApiTestVariableVO getUniqueApiTestVariable(String variableCode, String productCode, String linkCode, String variableKey);

    void addApiTestVariable(AddApiTestVariableEvent event);

    void editApiTestVariable(EditApiTestVariableEvent event);

    List<ApiTestVariableVO> queryApiTestVariablePage(PageApiTestVariableReq req);

    void updateApiTestVariableStatus(UpdateApiTestVariableStatusEvent event);

    void deleteApiTestVariable(DeleteApiTestVariableEvent event);

    void deleteApiTestByProductAndType(String productCode, List<ApiTypeEnum> typeList);

    void insertApiTest(ApiTestEntityDO entityDO);

    void updateApiTest(ApiTestEntityDO entityDO);

    void deleteApiTest(ApiTestEntityDO entityDO);

    List<ApiTestEntityDO> queryApiDetailByCodeList(List<String> codeLst);

    List<ApiTestEntityDO> queryApiDetailByMainCodeListAndEnable(List<String> codeList, List<ApiTestEnableEnum> enableList);

    ApiTestEntityDO queryApiDetailByCode(String apiCode);

    ApiCaseEntityDO queryApiCaseByCode(String caseCode);

    ApiTestCaseEntityDO queryApiCaseByCodeAndStatus(String caseCode, ApiCaseStatusEnum status);

    List<ApiTestVariableVO> queryVariableListByProductCode(String productCode, String type);

    void updateApiCaseSelective(ApiCaseEntityDO entityDO);

    void deleteApiCaseByApiCode(String apiCode);

    List<ApiTestCaseEntityDO> getApiCaseByCodes(List<String> apiCaseCodes);

    /**
     * 查询接口列表
     *
     * @param req {@link PageApiReq}
     * @return {@link ApiVO}
     */
    List<ApiVO> queryApiPage(PageApiReq req);

    /**
     * 查询接口文档版本code
     *
     * @param req {@link PageApiDocVersionReq}
     * @return {@link ApiDocVersionVO}
     */
    List<ApiDocVersionVO> queryApiDocVersionPage(PageApiDocVersionReq req);

    /**
     * 查询接口基础信息，供下拉选择
     *
     * @param req {@link PageApiLiteInfoReq}
     * @return {@link ApiLiteInfoVO}
     */
    List<ApiLiteInfoVO> queryApiLiteInfoPage(PageApiLiteInfoReq req);

    /**
     * 根据父用例code 和 用例类型 查询用例集合 （用于自动化用例列表执行用例）
     *
     * @param parentCode 父用例code
     * @param sourceType 用例类型
     * @return {@link ApiCaseEntityDO}
     */
    List<ApiCaseEntityDO> getApiCaseByParentCodeAndSourceType(String parentCode, ApiCaseSourceTypeEnum sourceType);

    /**
     * 根据接口code集合和用例类型，查询用例集合（用于接口列表批量执行用例）
     *
     * @param apiCodes   接口code结合
     * @param sourceType 用例类型
     * @return {@link ApiCaseEntityDO}
     */
    List<ApiCaseEntityDO> getApiCaseByApiCodesAndSourceType(List<String> apiCodes, ApiCaseSourceTypeEnum sourceType);

    /**
     * 查询用例列表
     *
     * @param query {@link PageApiCaseQuery}
     * @return {@link ApiCaseVO}
     */
    List<ApiCaseVO> queryApiCasePage(PageApiCaseQuery query);

    List<ApiCaseChildVO> queryApiCaseChildrenPage(PageApiCaseChildrenReq req);

    /**
     * 统计用例数，用于执行弹框
     *
     * @param req {@link CountCaseReq}
     * @return
     */
    Integer countCase(CountCaseReq req);

    void updateApiCaseUserByParent(UpdateApiCaseUserCommand command);

    List<ApiTestEntityDO> getApiTestByDocId(Long docId, String productCode);

    List<ApiTestEntityDO> getApiTestByDocIds(List<Long> docIds, String productCode);

    List<ApiTestCaseEntityDO> getApiTestCaseByMainApiCodes(List<String> mainApiCodes, String productCode);

    List<ApiTestEntityDO> queryApiTestByProductAndType(String productCode, List<ApiTypeEnum> typeList);

    List<ApiTestEntityDO> getAllVersionApiTest(ApiTestEntityDO entityDO);

    void updateApiCaseUserByVariableCode(ApiCaseEntityDO entityDO);

    Boolean checkSceneName(String sceneName, String productCode, String sceneCode, Integer sceneType);

    void insertSceneInfoSelective(SceneInfoEntityDO entityDO);

    void updateSceneInfoByCode(SceneInfoEntityDO entityDO);

    void updateSceneInfoByPrimaryKey(SceneInfoEntityDO entityDO);

    SceneInfoEntityDO querySceneInfoByPrimaryKey(SceneInfoEntityDO entityDO);

    SceneInfoEntityDO querySceneInfoByCodeAndVersion(String sceneCode, Integer sceneVersion);

    SceneInfoEntityDO queryLatestSceneInfo(String sceneCode, SceneInfoStatusEnum status);

    List<SceneInfoEntityDO> pageQuerySceneList(PageSceneInfoReq req);

    SceneInfoEntityDO queryLatestEditSceneInfo(QueryLatestEditSceneInfoReq req);

    List<ApiCaseEntityDO> queryNodeCaseDataList(String sceneCode, List<String> nodeList);

    PageInfo<ApiSampleCaseVO> queryPageApiTest(PageApiInfoReq req);

    /**
     * 根据父用例code 和 任务code 查执行结果
     *
     * @param parentCode 父用例code
     * @param taskCode   任务code
     * @return 执行结果
     */
    String queryExecuteResultByParentCodeAndTaskCode(String parentCode, String taskCode);

    /**
     * 更新父用例执行结果
     *
     * @param parentCode    父用例code
     * @param taskCode      任务code
     * @param executeResult 执行结果
     */
    void updateApiCaseExecuteResult(String parentCode, String taskCode, TestPlanCaseStatusEnum executeResult);

    /**
     * 统计父用例执行结果
     *
     * @param taskCode 任务code
     * @return {@link StatApiCaseExecuteResultVO}
     */
    List<StatApiCaseExecuteResultVO> statApiCaseExecuteResult(String taskCode);

    /**
     * 根据登记库code，查最近发布的场景信息
     *
     * @param automaticSourceCode 登记库code
     * @return {@link SceneInfoEntityDO}
     */
    SceneInfoEntityDO queryLatestSceneInfoByAutomaticSourceCode(String automaticSourceCode);

    List<SceneInfoEntityDO> querySceneInfoByCode(String sceneCode);

    List<SceneInfoEntityDO> querySceneByAutoSourceCode(List<String> autoSourceList);

    List<SceneInfoEntityDO> querySceneByProductCodeAndSceneType(String productCode, Integer sceneType);

    void deleteAllModuleScene(String productCode);

    void insertAllModuleScene(List<SceneIndexVO> indexList);

    List<SceneIndexVO> queryAllSceneModule(SceneModuleQueryReq req);

    List<SceneIndexVO> queryModuleByParentCode(String parentCode);

    void insertSceneIndexSelective(SceneIndexVO indexVO);

    SceneIndexVO querySceneIndexByCode(String sceneIndexCode);

    List<SceneIndexVO> querySceneIndexModuleInfo(String productCode);

    void updateSceneIndexByCode(SceneIndexVO sceneIndex);

    SceneIndexVO queryUniqueSceneModule(String productCode, String parentCode, String sceneIndexName, SceneIndexTypeEnum type, UseCaseFactoryTypeEnum sceneType);

    void updateSceneIndexSelective(SceneIndexVO indexVO);

    void updateByExampleSelective(SceneIndexVO indexVO);

    SceneIndexVO querySceneModuleById(Long id);

    List<SceneIndexVO> querySceneIndexByParentCode(String parentCode);

    void batchMoveScene(BatchMoveSceneEvent event);

    List<SceneInfoEntityDO> queryPublishSceneAutoSource(List<String> sceneCodes);

    List<ApiTestVariableVO> querySceneVariable(String productCode, String linkCode, VariableTypeEnum type, SubVariableTypeEnum subVariableType, List<VariableUsageTypeEnum> variableUsageTypes);

    void batchDeleteVariable(String productCode, String linkCode, VariableTypeEnum type, List<VariableUsageTypeEnum> variableUsageTypes);

    void batchAddVariable(BatchAddVariableCommand command);

    void batchInsertVariable(List<ApiTestVariableVO> list);

    void batchUpdateVariable(List<ApiTestVariableVO> list);

    /**
     * 查询场景变量，排除同名变量
     *
     * @param sceneCode      产品code
     * @param variableKeySet 待排除变量名集合
     * @param type           类型
     * @return {@link ApiTestVariableVO}
     */
    List<ApiTestVariableVO> querySceneVariableExcludeKeySet(String sceneCode, Set<String> variableKeySet, VariableTypeEnum type);

    List<SceneIndexVO> querySimpleSceneIndex(SceneModuleQueryReq req);

    /**
     * 根据登记库code，统计未删除场景个数
     *
     * @param automaticSourceCode 登记库code
     * @return
     */
    int countEnableBySourceCode(String automaticSourceCode);


    /**
     * 根据登记库code，统计未删除Apicase个数
     *
     * @param automaticSourceCode 登记库code
     * @return
     */
    int countApiEnableBySourceCode(String automaticSourceCode);

    /**
     * 批量保存造数变量
     *
     * @param command
     */
    void batchAddPreDataVariable(BatchAddPreDataVariableCommand command);

    /**
     * 查询唯一db变量
     *
     * @param productCode 产品code
     * @param variableKey
     * @param sceneType   场景类型
     * @return {@link ApiTestVariableVO}
     */
    ApiTestVariableVO getUniqueDbVariable(String productCode, String variableKey, Integer sceneType, VariableTypeEnum variableType, String businessCode);

    /**
     * 查询共享造数
     *
     * @param req
     * @return
     */
    List<SceneIndexVO> querySharedPreData(SharedSceneModuleQueryReq req);

    /**
     * 查询共享造数所有模块
     *
     * @param productList
     * @return
     */
    List<SceneIndexVO> querySharedPreDataModuleByProduct(List<String> productList);

    List<ApiTestEntityDO> queryApiTestByDocVersion(String docVersion);

    PageInfo<PageApiLiteResp> queryPageSceneApi(PageApiLiteReq req);

    void deleteSceneApiRelationBySceneCode(String sceneCode);

    void insertSceneApiRelation(SceneApiRelationEntityDO entityDO);

    List<String> querySceneApiRelationAllSceneCode(String productCode);

    void addSceneDbAuthorize(List<SceneDatabaseAuthorizeEntityDO> list);

    void dbRevoke(String productCode, String authorizeProductCode);

    List<SceneDatabaseAuthorizeEntityDO> queryAuthorizeList(String productCode);

    List<SceneDatabaseAuthorizeEntityDO> queryAuthorizeInfo(String authorizeProductCode);

    List<String> queryPageApiCode(PageApiInfoReq req);

    void addApiTestCase(AddApiTestCaseEvent event);

    List<TmApiTestCaseVO> queryUniqueApiTestCase(String apiCode, String caseCode, String caseName);

    void editApiTestCase(EditApiTestCaseEvent event);

    TmApiTestCaseVO getApiTestCase(String caseCode, ApiCaseStatusEnum status);

    List<ApiTestCaseEntityDO> queryExceptionCaseByParentCaseCode(String parentCaseCode, ApiCaseStatusEnum status, boolean sorted, List<Integer> caseTypeList);

    void updateExceptionCaseByCode(ApiTestCaseEntityDO entityDO);

    void batchDeleteApiCaseExceptionByCodeList(List<String> caseCodeList, User transactor);

    void updateApiTestCaseSelective(ApiTestCaseEntityDO entityDO);

    void insertApiTestCaseSelective(ApiTestCaseEntityDO entityDO);

    List<PageApiTestCaseVO> queryParentApiTestCaseList(PageApiTestCaseQuery query);

    List<PageApiTestCaseVO> queryApiTestCaseList(PageApiTestCaseQuery query, String parentCode);

    void deleteApiCaseByParentCaseCode(String parentCaseCode, ApiCaseStatusEnum status, User transactor);

    void batchInsertApiTestCase(List<ApiTestCaseEntityDO> list);

    ApiTestCaseEntityDO selectApiTestCaseByCodeAndStatus(String apiCaseCode, ApiCaseStatusEnum status);

    void updateApiTestCaseEnable(ChangeApiCaseStatusCommand command);

    List<ApiTestCaseEnableVO> queryApiCaseEnable(String productCode, List<String> codeList, String status);

    void batchChangeApiTestCaseEnable(BatchChangeApiCaseStatusCommand command);

    void batchDeleteApiTestCase(BatchChangeApiCaseStatusCommand command);

    List<ApiTestCaseEntityDO> queryExceptionCaseByCodeList(List<String> list);

    List<ApiTestCaseEntityDO> queryApiCaseDataByApiCode(List<String> list);

    List<ApiTestCaseEntityDO> queryApiCaseDataByParentCode(List<String> list);

    /**
     * 发布以后，草稿数据与发布数据状态保持一致
     *
     * @param caseCode 用例code
     * @param enabled  {@link ApiCaseStatusEnum}
     */
    void editDraftApiTestCaseStatus(String caseCode, ApiCaseEnableEnum enabled, User transactor);

    List<CountApiTestCaseChildrenVO> selectApiTestCaseListByCode(Set<String> codeSet, String productCode);

    /**
     * 查询接口用例执行明细
     *
     * @param query {@link ApiTestCaseExecuteDetailQuery}
     * @return {@link ApiTestCaseExecuteDetailTiledVO}
     */
    List<ApiTestCaseExecuteDetailTiledVO> queryApiTestCaseExecuteDetail(ApiTestCaseExecuteDetailQuery query);


    void updateApiTestCaseSelectiveByCaseCodeAndStatus(ApiTestCaseEntityDO entityDO);

    int countApiTestCaseByAutomaticSourceCode(String automaticSourceCode);

    List<String> selectAutomaticSourceCodeList(List<String> apiCaseCodes, ApiCaseStatusEnum status);

    List<ApiTestCaseEntityDO> querySubApiCasesByParentCaseCodes(List<String> parentCaseCodes, ApiCaseStatusEnum status);

    List<String> queryApiCaseCodeByApiCode(String apiCode);

    String selectApiCodeByAutomaticSourceCode(String automaticSourceCode);

    ApiTestCaseEntityDO queryNormalCaseByParentCode(String parentCaseCode);

    int countSystemCaseByApiCode(String apiCode, ApiCaseStatusEnum status);

    void addApiTestCaseTag(String caseCode, SceneTagEnum tagEnum, User transactor);

    void deleteApiTestCaseTag(String caseCode, List<SceneTagEnum> tagEnumList, User transactor);

    List<ApiTestCaseEntityDO> selectApiTestCaseListByCaseType(List<Integer> caseTypeList, Set<String> apiCodeSet);

    List<ApiTestEntityDO> selectApiTestByCodeList(Set<String> apiCodeSet);

    void updateCaseApiInfo(ApiTestEntityDO apiTest);

    void updateApiCaseExecuteResultByTaskId(AutomaticTaskEntityDO taskEntityDO);

    void updateApiTestCasePublishFlag(Set<String> caseCodeSet);

    void updateApiTestCaseTag(String caseCode, String tagValue);

    List<String> queryApiCaseTagByApiCode(String apiCode);

    List<ApiCaseExceptionVO> queryExceptionCaseList(PageApiExceptionCaseQuery query);

    List<ApiTestCaseEntityDO> queryApiExceptionCase(List<String> caseCodes, Integer caseType);

    void batchUpdateApiExceptionCase(List<ApiTestCaseEntityDO> list);

    /**
     * 查询已删除场景code,3.682.0历史数据刷新
     *
     * @return {@link String}
     */
    Set<String> selectDisableSceneCode();

    /**
     * 批量删除场景和接口关联关系
     *
     * @param codes 场景code集合,3.682.0历史数据刷新
     */
    void batchDeleteSceneApiRelation(List<String> codes);

    /**
     * 所有包含造数用例，草稿态，手工+系统，没有删除，有用例变更标识,3.682.0历史数据刷新
     *
     * @return {@link ApiTestCaseEntityDO}
     */
    List<ApiTestCaseEntityDO> selectApiTestCaseContainPreData();

    List<ApiTestEntityDO> queryApiTestCodeByMainApiCode(String mainApiCode);

    void deleteApiTestByMainApiCode(String mainApiCode);

    List<ApiTestEntityDO> queryApiTestDocVersionByMainApiCode(List<String> list);

    List<String> queryApiCaseAllApiCode(String productCode);

    void updateApiCaseMainApiCode(String apiCode, String mainApiCode);

    ApiTestEntityDO queryLatestApiTestByMainCode(String mainApiCode);

    void updateApiTestTagByMainApiCode(String mainApiCode, String tagValue);

    boolean refreshApiTestModifyFlag(String mainApiCode);

    List<String> queryApiCodeByCaseCode(List<String> list);

    List<String> queryRelatedApiCodeBySceneCode(String sceneCode);

    int countSceneApiRelationByMainApiCode(String mainApiCode);

    void updateApiTestRelatedSceneFlagByMainApiCode(String mainApiCode, Integer relatedSceneFlag);

    void updateApiCaseRelatedByApiCode(String apiCode);

    List<SceneInfoEntityDO> querySceneInfoTagList(List<String> sceneCodes);

    List<ApiTestEntityDO> queryApiDocId(ApiTestEntityDO entityDO, boolean isPreciseSearch);

    /**
     * 校验用例名称是否唯一，不排除任何用例，针对复制用例场景 (手工用例&&未删除态）
     *
     * @param apiCode  接口code
     * @param caseName 用例名称
     * @return {@link TmApiTestCaseVO}
     */
    boolean isUniqueCaseName(String apiCode, String caseName);
}
