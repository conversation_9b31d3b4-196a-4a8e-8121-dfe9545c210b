package com.zto.devops.qc.domain.gateway.rpc;

import com.zto.devops.qc.client.model.rpc.pipeline.*;
import com.zto.devops.qc.client.model.rpc.pipeline.query.*;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackCDEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/4/12 10:41
 */
public interface IPipelineRpcService {


    String downloadUrl(FindOssFileUrlQuery query);

    /**
     * 回归通过后，查询流程关联的版本信息推送给jacoco //devops-qc
     *
     * @param query
     * @return
     */
    JacocoVersionVO getJacocoVersion(ListFlowVersionJacocoQuery query);

    PageApplicationVO getPageApplicationVO(PageApplicationQuery query);

    List<JacocoApplicationVO> listDeployApplicationQuery(String flowCode, String planCode);

    VersionFlowDateVO findVersionFlowDateQuery(FindVersionFlowDateQuery query);

    NamespaceResp findBaseNamespace(String productCode);

    List<ApplicationResp> getApplicationList(String productCode);


    /**
     * 根据版本code，查询关联应用ID
     *
     * @param versionCode
     * @return
     */
    List<String> findVersionAppIdQuery(String versionCode);

    void handleExecuteCallbackCDEvent(ExecuteCallbackCDEvent event);

    /**
     * 根据示例name，查询podIp列表
     *
     * @param instanceNameList 实例名集合
     * @return podIp集合
     */
    List<String> getPodIpListByInstanceNameList(List<String> instanceNameList);

    FlowBranchVO queryBranchByVersionCode(String versionCode);

    List<FeatureVO> listFeatureConfig(String productCode);

    /**
     * 流程详情
     *
     * @param flowCode 流程code
     * @return {@link FlowBaseDetailVO}
     */
    FlowBaseDetailVO flowDetail(String flowCode);

    String getVersionSimpleVO(String appId, String tag);


    /**
     * 根据appId和空间名称，查询版本
     *
     * @param query {@link FindVersionByNamespaceQuery}
     * @return {@link FindVersionByNamespaceVO}
     */
    FindVersionByNamespaceVO queryVersionByNamespace(FindVersionByNamespaceQuery query);

    List<ApplicationVO> listApplicationByAppIds(List<String> appIds);

    VersionContainSubVO getVersionByVersionCodeAndAppId(String versionCode, String appId);

    String queryVersionReleaseBranchCommit(VersionReleaseBranchCommitQuery query);

}
