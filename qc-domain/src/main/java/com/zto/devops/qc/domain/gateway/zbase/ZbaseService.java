package com.zto.devops.qc.domain.gateway.zbase;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.service.agent.model.DependencyServiceReq;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ListESQueryVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ListRedisQueryVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.listESQueryReq;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.TopicSimpleQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.TopicSimpleVO;

/**
 * <AUTHOR>
 * @create 2023/7/31 10:15
 */
public interface ZbaseService {

    JSONArray queryProductDb(String productCode);

    JSONObject queryPhysicalDbInfo(String id);

    JSONObject queryDbAccountInfo(JSONObject reqObject);

    List<ListRedisQueryVO> queryRedisList(String productCode, String search);

    List<ListESQueryVO> queryESList(listESQueryReq req);

//    String generateEsSign(ProxyExecDTO dto);

    PageInfo<TopicSimpleVO> topicSimpleQuery(TopicSimpleQuery query);

    Integer getPhysicsDbId(String databaseUrl, String productCode, User user,
                           UseCaseFactoryTypeEnum type, String businessCode);


//    JSONObject queryDependencyPageListByAppId(String appid,Integer pageNum,Integer pageSize);
//    JSONArray  queryMethodsByDependService(DependencyServiceReq dependencyServiceReq);

}
