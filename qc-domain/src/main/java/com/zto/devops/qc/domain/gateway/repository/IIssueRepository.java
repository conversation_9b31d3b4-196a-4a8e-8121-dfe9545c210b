package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.dto.Page;
import com.zto.devops.qc.client.enums.issue.IssueFindStage;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.entity.*;
import com.zto.devops.qc.client.model.issue.event.*;
import com.zto.devops.qc.client.model.issue.query.*;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.IssueNumStatisticsVO;
import com.zto.devops.qc.domain.model.Issue;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/3/17
 * @Version 1.0
 */
public interface IIssueRepository {

    Integer selectCountByRequirementCode(String requirementCode);

    Page<IssueVO> pageIssueQuery(PageIssueQuery query);

    List<RelatedMatterStatusCountVO> selectRelatedMatterStatusCount(List<String> requirementCodeList);

    List<IssueVO> listIssueForVersionQuery(ListIssueForVersionQuery query);

    List<IssueBaseVO> listBySprintQuery(ListBySprintQuery query);

    Page<String> pageIssueCodeThingQuery(PageIssueThingQuery query);

//    Issue loadFormDb(String aggregateIdentifier);

    List<IssueVO> selectByDefinedQuery(IssueQueryParameter parameter);

    List<IssueEntityDO> selectBySimpleIssueQuery(SimpleIssueQuery query);

    List<IssueEntityDO> selectByProductCodeAndCode(String productCode, String sprintCode);

    void updateBySprintInIssueEditedEvent(SprintInIssueEditedEvent event);

    void updateBySprintIssueLinkedEvent(SprintIssueLinkedEvent event);

    IssueEntityDO findIssueByCode(String code);

    List<IssueVO> listUnClosedIssueByVersionCodes(List<String> versionCodes);

    List<IssueVO> listUnClosedIssueByFixedVersionCodes(List<String> versionCodes);

    List<IssueLegacyVO> listIssueLegacy(IssueLegacyListQuery query);

    IssueNumStatisticsVO getIssueNumStatistic(IssueNumStatisticQuery query);

    IssueNumStatisticsVO getUiTestIssueNumStatistic(IssueNumStatisticQuery query);

    /**
     * ISSUE回溯接口
     *
     * @param aggregateIdentifier
     * @return
     */
    Issue loadFormDb(String aggregateIdentifier);

    /**
     * 添加缺陷
     *
     * @param event
     */
    void addIssue(IssueAddedEvent event);

    /**
     * 更新缺陷 - 延期修复
     *
     * @param event
     */
    void updateIssue(IssueDelayFixedEvent event);

    /**
     * 更新缺陷 - 退回开发修复
     *
     * @param event
     */
    void updateIssue(IssueBackToRepairedEvent event);

    /**
     * 更新缺陷 - 一站式研发--缺陷 流转
     *
     * @param event
     */
    void updateIssue(IssueCirculationedEvent event);

    /**
     * 更新缺陷 - 一站式研发--确认关闭 缺陷
     *
     * @param event
     */
    void updateIssue(IssueConfirmClosedEvent event);

    PageIssueVO pageIssue(IssueQueryParameter parameter, int pageNum, int size);

    List<IssueVO> findIssueVOList(PageLaneIssueQuery query, IssueQueryParameter parameter);

    List<IssueVO> doSelectByDefinedQuery(IssueQueryParameter issueQueryParameter);

    List<String> listStatusByDefinedQuery(IssueQueryParameter parameter);

    void updateIssue(IssueRefusedEvent event);

    void removeIssue(IssueRemovedEvent event);

    void updateIssue(IssueRelatedRequirementEvent event);

    void editIssue(IssueEditedEvent event);

    void deliveryIssue(IssueDeliveryValidatedEvent event);

    /**
     * 更新缺陷的修复版本 字段
     */
    void updateIssueFixVersionFiled(List<String> issueCodes, String versionCode, String versionName, String confirm);

    List<Map<Long, Long>> queryIssueGroupByHandle(String status);

    /**
     * 根据主键查询，不区分状态
     *
     * @param code
     * @return
     */
    IssueEntityDO selectByPrimaryKey(String code);

    List<IssueEntityDO> selectByProductCodeAndFindStageAndGmtCreate(List<String> productCode,
                                                                    IssueFindStage findStage,
                                                                    Date startDate, Date endDate);

    List<IssueEntityDO> selectByVersionCode(String versionCode);

    List<TransitionNodeEntityDO> selectIssueReopenList(List<String> businessCodes);

    List<TransitionNodeEntityDO> listIssueTransitionNode(List<String> issueCodes);

    StatisticsVersionIssueEntityDO selectStatisticsVersionIssueEntity(String versionCode);

    void updateStatisticsVersionIssueEntity(StatisticsVersionIssueEntityDO entityDO);

    void insertStatisticsVersionIssueEntity(StatisticsVersionIssueEntityDO entityDO);

    List<DevThingVO> listIssueByHandleUserId(Long handleUserId);

    void updateIssueHandleUserId(List<DevThingUpdateVO> vos);

    /**
     * 统计开发待修复缺陷个数
     *
     * @param statusList {@link IssueStatus}
     * @return
     */
    List<CountUserIssueNumVO> queryFixIssueGroupByDevelopUserId(List<IssueStatus> statusList);

    /**
     * 统计测试待验证缺陷个数
     * @param statusList {@link IssueStatus}
     * @return
     */
    List<CountUserIssueNumVO> queryTestIssueGroupByTestUserId(List<IssueStatus> statusList);

    /**
     * 根据修复版本编码查询缺陷数
     * @param fixedVersionCodes 版本编码
     * @return 缺陷数
     */
    Map<String, Long> getIssueCountByFixedVersionCode(List<String> fixedVersionCodes);
}
