package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.rpc.huiyan.StoreBrandVO;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreNameVO;
import com.zto.devops.qc.client.model.rpc.outlet.ItemTypeVO;
import com.zto.devops.qc.client.model.rpc.pipeline.UserRespVO;
import com.zto.devops.qc.client.model.rpc.waybill.LabelNameVO;

import java.util.List;

public interface BaseDataRepository {

    List<UserRespVO> queryUserAutoSearch(String keyWord);

    List<LabelNameVO> listAllTags();

    List<ItemTypeVO> queryDictionaries(String categoryCode);

    List<StoreBrandVO> queryStoreBrand();

    List<StoreNameVO> queryStoreName(String keyWord, String companyCode);

}
