package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.devops.qc.client.model.dto.TestReportEntityDO;
import com.zto.devops.qc.client.model.report.entity.ReportVO;
import com.zto.devops.qc.client.model.report.query.PageReportQuery;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanDto;

import java.util.List;

public interface TestReportRepository {

    List<TestReportEntityDO> selectTestReportByVersionAnReportTypeList(String versionCode, List<ReportType> reportTypeList);

    List<ReportVO> pageQuery(PageReportQuery pageReportQuery);

    TestPlanDto selectTestPlanMainByCode(String code);
}

