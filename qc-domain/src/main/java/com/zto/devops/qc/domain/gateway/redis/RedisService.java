package com.zto.devops.qc.domain.gateway.redis;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * redis service
 *
 * <AUTHOR>
 * @date 2023-03-27 19:58
 **/
public interface RedisService {

    boolean tryLock(String lockKey, String lockId, long expireSecondsTime);

    boolean releaseLock(String lockKey, String lockId);

    /**
     * has key
     *
     * @param key key
     * @return boolean
     */
    boolean hasKey(String key);

    /**
     * set key
     *
     * @param key      key
     * @param value    value
     * @param ttl      ttl
     * @param timeUnit timeunit
     */
    void setKey(String key, String value, long ttl, TimeUnit timeUnit);

    /**
     * delete key
     *
     * @param key
     * @return
     */
    boolean delete(String key);

    String getKey(String key);

    void setKey(String key, String value);

    void hashSet(String key, Object hashKey, Object value);

    Object hashGet(String key, Object hashKey);

    Long hashDelete(String key, Object hashKey);

    Map<Object, Object> hashEntries(String key);

    void addListOnRight(String key, String value);

    /**
     * 追加数据，设置过期时间
     *
     * @param key
     * @param value
     * @param timeoutInSeconds 单位： 秒
     */
    void addToListOnRightWithExpiration(String key, String value, long timeoutInSeconds);

    String getListOnLeft(String key);

    void setAliveTime(String key, long time, TimeUnit timeUnit);

    /**
     * 根据数据下标获取数据，下标开始为0，结束为-1时，获取全部数据
     *
     * @param key        key
     * @param beginIndex 开始下标
     * @param endIndex   结束下标
     * @return
     */
    List<String> getListRange(String key, int beginIndex, int endIndex);

    Long listSize(String key);

    Long listRemove(String key, Object value);

    void opsForZSetAddLast(String key, String value);

    void opsForZSetAddFirst(String key, String value);

    Set<String> opsForZSetRang(String key, long l1, long l2);

    void opsForZSetRemove(String key, Object... values);

}
