package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagBatchAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagRemovedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseTagQuery;

import java.util.List;

public interface TestcaseTagRepository {

    /**
     * 测试用例添加标签
     *
     * @param event
     */
    void addTestcaseTag(TestcaseTagAddedEvent event);

    /**
     * 测试用例批量添加标签
     *
     * @param event
     */
    void batchAddTestcaseTag(TestcaseTagBatchAddedEvent event);

    /**
     * 根据用例code集合查询测试用例标签
     *
     * @param caseCodes
     * @return
     */
    List<TagEntityDO> selectTestcaseTagByCaseCodeList(List<String> caseCodes);

    /**
     * 添加标签
     *
     * @param entityDO
     */
    void insertSelective(TagEntityDO entityDO);

    /**
     * 查询当前业务code使用以及关联当前标签数量
     *
     * @param businessCode
     * @param tagName
     * @return
     */
    int selectCountByExample(String businessCode, String tagName);

    /**
     * 查询当前业务code标签总数
     *
     * @param businessCode
     * @return
     */
    int selectCountByExample(String businessCode,DomainEnum domain);

    /**
     * 查询标签
     *
     * @param query
     * @return
     */
    List<TagVO> handle(ListTestcaseTagQuery query);

    List<TagVO> listTestcaseTagQuery(ListTestcaseTagQuery tagQuery);

    void removeTestcaseTag(TestcaseTagRemovedEvent event);
}
