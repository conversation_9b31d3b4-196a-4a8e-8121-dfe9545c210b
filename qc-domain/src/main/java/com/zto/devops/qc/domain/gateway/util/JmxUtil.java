package com.zto.devops.qc.domain.gateway.util;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.qc.client.model.dto.JmeterDocumentFullInfo;
import com.zto.devops.qc.client.model.dto.SceneLinkInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.Scene;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticNode;
import org.dom4j.Document;
import org.dom4j.DocumentException;

import java.io.IOException;
import java.util.List;

public interface JmxUtil {

    Document getJmeterDocument(String bucketName, String path, String fileName) throws IOException, DocumentException;

    AutomaticNode getJmeterPlanTree(Document document) throws ServiceException;

//    void saveJmxFile(Document document, String bucketName,String ossPath, String ossJmxFileName);

//    JmeterDocumentFullInfo createNewJmeterDocument(String sceneCode, Integer sceneVersion, String sceneName);

    void createJmeterScript(String sceneCode, Integer sceneVersion, String sceneName,
                            String ossPath, String jmxFileName, String productCode,
                            Integer sceneType, JSONObject runningSet);

    void createJmeterScript(DebugTaskInfo debugTaskInfo);

    void modifyThreadGroupSerialize(Document document, String method, String time);

    void modifyOssFile(Document document, String ossPath);
}
