package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.model.issue.event.CommentAddedEvent;
import com.zto.devops.qc.client.model.issue.event.CommentRemovedSimpleEvent;

import java.util.List;

public interface CommentRepository {

    void addComment(CommentAddedEvent commentAddedEvent);

    void removeComment(CommentRemovedSimpleEvent event);

    List<CommentVO> queryCommentByBusinessCode(String businessCode);

    CommentVO queryCommentByCode(String code);

}
