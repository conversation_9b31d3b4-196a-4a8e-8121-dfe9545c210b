package com.zto.devops.qc.domain.gateway.zcat;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.service.agent.model.DependencyQueryReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.QueryAppIdTagsReq;
import com.zto.devops.qc.domain.model.coverage.ZcatMetricsVO;

import java.util.List;
import java.util.Map;


public interface ZCatService {

    Map<String, Map<String, String>> queryAppIdTags(QueryAppIdTagsReq req);

    JSONObject queryDependencyService(DependencyQueryReq req);

    List<ZcatMetricsVO> getInterfaceCallsNumber(Map<String, Object> reqMap);

}
