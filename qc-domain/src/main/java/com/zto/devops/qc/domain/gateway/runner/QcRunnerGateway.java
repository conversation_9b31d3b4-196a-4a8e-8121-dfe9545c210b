package com.zto.devops.qc.domain.gateway.runner;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;

public interface QcRunnerGateway {

    boolean isWhiteList(User user);

    void debug(DebugTaskInfo debugTaskInfo, Map<String, Object> context, Map<String, Pair<DebugTaskInfo, Map<String, Object>>> debugTaskInfoMap);

    JSONObject queryDbConfig(String dbId);
}
