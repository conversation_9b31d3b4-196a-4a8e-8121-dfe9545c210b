package com.zto.devops.qc.domain.service;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.testmanager.apitest.*;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTaskRespTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.channel.TestEventEnums;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SaveDebugInfoCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskResp;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.Scene;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.DataCenterRequestComponent;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.DubboRequestComponent;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.HttpRequestComponent;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.IfController;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.lines.Line;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata.GlobalEnvVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskTerminatedEvent;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.domain.converter.ApiTestDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.debug.IQcDebugService;
import com.zto.devops.qc.domain.gateway.file.FileUtilService;
import com.zto.devops.qc.domain.gateway.jenkins.IJenkinsService;
import com.zto.devops.qc.domain.gateway.metadata.MetaDataService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.repository.LinkMapRepository;
import com.zto.devops.qc.domain.gateway.repository.TmDebugRecordRepository;
import com.zto.devops.qc.domain.gateway.repository.TmSceneDebugRecordRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.runner.QcRunnerGateway;
import com.zto.devops.qc.domain.gateway.util.ApiDebugLogService;
import com.zto.devops.qc.domain.gateway.util.JmxUtil;
import com.zto.devops.qc.domain.gateway.util.ShellParseUtilService;
import com.zto.devops.qc.domain.gateway.zbase.ZbaseService;
import com.zto.devops.qc.domain.gateway.zms.ZMSTemplateService;
import com.zto.devops.qc.domain.util.GenerateApiCaseUtil;
import com.zto.devops.qc.domain.util.LinkMap;
import com.zto.devops.qc.domain.util.jmeter.ProcessManager;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE;
import static com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE;

@Slf4j
@Component
public class LinkMapDomainService extends BaseDomainService {

    @Autowired
    private ZtoOssService ztoOssService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ApiTestRepository apiTestRepository;
    @Autowired
    private LinkMapRepository linkMapRepository;
    @Autowired
    private QcConfigBasicService qcConfigBasicService;
    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;
    @Autowired
    private AuthCookieDomainService authCookieDomainService;
    @Autowired
    private JmxUtil jmxUtil;
    @Autowired
    private ApiDebugLogService apiDebugLogService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private IProductRpcService iProductRpcService;
    @Autowired
    private IJenkinsService jenkinsService;
    @Autowired
    private ApiTestDomainConverter apiTestDomainConverter;
    @Autowired
    private ApiTestQueryDomainService apiTestQueryDomainService;
    @Autowired
    private ZMSTemplateService zmsTemplate;
    @Autowired
    private IQcDebugService qcDebugService;
    @Autowired
    private ZbaseService zbaseService;
    @Autowired
    private ShellParseUtilService shellParseUtil;
    @Autowired
    private TmDebugRecordRepository tmDebugRecordRepository;
    @Autowired
    private QcConfigBasicService config;
    @Autowired
    private FileUtilService fileUtilService;

    @Autowired
    private QcRunnerGateway qcRunnerGateway;

    @Autowired
    private TmSceneDebugRecordRepository tmSceneDebugRecordRepository;

    private static final String DEVOPS_WS_MESSAGE_MQ = "devops_ws_message_MQ";
    private static String preFix = "Debug-Task-";
    private static String flagStartDebugTask = "0";
    private static String flagStartCreateScript = "1";
    private static String flagCreateScriptFinish = "2";
    private static String flagCreateScriptError = "2error";
    private static String flagRunScript = "3";
    private static String debugPathHome = "/data/jmeter/";
    private static final String TOTAL = "TOTAL";
    private static final String CURRENT = "CURRENT";
    private static String X_TOKEN = "X-Token";
    private static String X_OPENID = "X-OpenId";

    private final Executor DEBUG_TASK_EXECUTOR = new ThreadPoolExecutor(
            10,
            200,
            30L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix(preFix).build());


    public JSONObject getVariable(GetVariableReq req) {
        String sceneCode = req.getSceneCode();
        Integer sceneVersion = req.getSceneVersion();
        JSONObject jmeterVariable = qcConfigBasicService.getJmeterVariable();
        Scene scene = this.getScene(sceneCode, sceneVersion);
        Map<String, Line> lineMap = new HashMap<>();
        Map<String, Node> nodeMap = JSON.parseObject(scene.getNodes().toJSONString(), new TypeReference<Map<String, Node>>() {
        });
        if (nodeMap == null || nodeMap.size() <= 0) {
            throw new ServiceException("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]中没有有效节点!");
        }

        /**
         * 连线*
         */
        Set<String> lineKeyset = scene.getLines().keySet();
        LinkMap<ComponentBaseInfo> graph = new LinkMap();
        if (lineKeyset != null) {
            for (String lineCode : lineKeyset) {
                JSONObject lineJson = scene.getLines().getJSONObject(lineCode);
                Line line = JSON.toJavaObject(lineJson, Line.class);
                if (nodeMap.containsKey(line.getInNodeCode()) && nodeMap.containsKey(line.getOutNodeCode())) {
                    graph.addEdge(nodeMap.get(line.getInNodeCode()), nodeMap.get(line.getOutNodeCode()));
                } else {
                    throw new ServiceException("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]中的场景连线不正确!");
                }
                lineMap.put(line.getInNodeCode() + line.getOutNodeCode(), line);
            }
        }
        Map<String, String> variableInfoMap = new HashMap<>();
        Map<String, Integer> variableDistanceMap = new HashMap<>();
        String endNode = req.getNodeCode();
        for (String startNode : scene.getStartNode()) {
            List<List<ComponentBaseInfo>> paths = graph.findAllPaths(nodeMap.get(startNode), nodeMap.get(endNode), true);
            if (CollectionUtil.isEmpty(paths)) {
                log.info("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]+" + "开始节点[" + startNode + "]-结束节点[" + endNode + "]没有生成任何链路!");
                continue;
            }
            log.info("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]+" + "开始节点[" + startNode + "]-结束节点[" + endNode + "]生成链路:" + JSON.toJSONString(paths));
            for (List<ComponentBaseInfo> nodes : paths) {
                if (CollectionUtil.isEmpty(nodes)) {
                    continue;
                }
                Integer distance = 0;
                for (int index = nodes.size() - 1; index >= 0; index--) {
                    ComponentBaseInfo componentBaseInfo = nodes.get(index);
                    if (componentBaseInfo.getType() != LinkMapTypeEnum.NODE) {
                        continue;
                    }
                    if (nodeMap.containsKey(componentBaseInfo.getCode())) {
                        Node currentNode = nodeMap.get(componentBaseInfo.getCode());
                        if (currentNode.getPreComponents() != null) {
                            this.getPreVariable(currentNode.getName(), currentNode.getPreComponents(), variableInfoMap, variableDistanceMap, distance);
                        }
                        if (currentNode.getPostComponents() != null) {
                            this.getPostVariable(currentNode.getName(), currentNode.getPostComponents(), variableInfoMap, variableDistanceMap, distance);
                        }
                    }
                    distance = distance + 1;
                }
            }
        }
        //场景变量
        Set<String> variableKeySet = variableInfoMap.keySet();
        List<ApiTestVariableVO> voList = apiTestRepository.querySceneVariableExcludeKeySet(req.getSceneCode(), variableKeySet, VariableTypeEnum.VARIABLE);
        if (CollectionUtil.isNotEmpty(voList)) {
            voList.forEach(vo -> {
                variableInfoMap.put(vo.getVariableKey(), "场景配置-自定义变量");
                if (StringUtil.isNotEmpty(vo.getVariableValue()) && vo.getVariableValue().startsWith("${__dataExclusiveLock")) {
                    if (redisService.hasKey(vo.getProductCode() + "_" + vo.getVariableKey())) {
                        String variableInfo = redisService.getKey(vo.getProductCode() + "_" + vo.getVariableKey());
                        JSONObject object = JSONObject.parseObject(variableInfo);
                        int total = object.getInteger(TOTAL);
                        int current = object.getInteger(CURRENT);
                        if (current > total) {
                            current = 1;
                        }
                        String variable = redisService.getKey(vo.getProductCode() + "_" + vo.getVariableKey() + "_" + current);
                        JSONObject variableObj = JSONObject.parseObject(variable);
                        if (Objects.isNull(variableObj)) {
                            return;
                        }
                        for (Map.Entry<String, Object> entry : variableObj.entrySet()) {
                            variableInfoMap.put(vo.getVariableKey() + "." + entry.getKey(), "场景配置-自定义变量");
                        }
                    }
                }
            });
        }
        JSONArray globalVariable = jmeterVariable.getJSONArray("global");
        for (String variable : variableInfoMap.keySet()) {
            String sourceNodeName = variableInfoMap.get(variable);
            JSONObject objectJson = new JSONObject();
            objectJson.put("variable", variable);
            objectJson.put("sourceNodeName", sourceNodeName);
            globalVariable.add(objectJson);
        }

        return jmeterVariable;
    }

    private void getPreVariable(String nodeName, JSONArray preComponents, Map<String, String> variableInfoMap, Map<String, Integer> variableDistanceMap, Integer distance) {
        for (int i = 0; i < preComponents.size(); i++) {
            JSONObject componentJSON = preComponents.getJSONObject(i);
            if (StringUtil.isEmpty(componentJSON.getString("type"))) {
                continue;
            }
            LinkMapTypeEnum linkMapTypeEnum = LinkMapTypeEnum.valueOf(componentJSON.getString("type"));
//            if (LinkMapTypeEnum.JDBC == linkMapTypeEnum) {
            if (componentJSON.containsKey("variableExtraction")) {
                JSONArray variableExtraction = componentJSON.getJSONArray("variableExtraction");
                for (int j = 0; j < variableExtraction.size(); j++) {
                    JSONObject objectJson = variableExtraction.getJSONObject(j);
                    if (StringUtil.isBlank(objectJson.getString("name"))) {
                        continue;
                    }
                    if (!variableDistanceMap.containsKey(objectJson.getString("name"))) {
                        variableInfoMap.put(objectJson.getString("name"), nodeName);
                        variableDistanceMap.put(objectJson.getString("name"), distance);
                    } else {
                        if (variableDistanceMap.get(objectJson.getString("name")) > distance) {
                            variableInfoMap.put(objectJson.getString("name"), nodeName);
                            variableDistanceMap.put(objectJson.getString("name"), distance);
                        }
                    }
                }
            }
//            }
        }
    }

    private void getPostVariable(String nodeName, JSONArray postComponents, Map<String, String> variableInfoMap, Map<String, Integer> variableDistanceMap, Integer distance) {

        for (int i = 0; i < postComponents.size(); i++) {
            JSONObject componentJSON = postComponents.getJSONObject(i);
            if (StringUtil.isEmpty(componentJSON.getString("type"))) {
                continue;
            }
            LinkMapTypeEnum linkMapTypeEnum = LinkMapTypeEnum.valueOf(componentJSON.getString("type"));
//            if (LinkMapTypeEnum.JDBC == linkMapTypeEnum || LinkMapTypeEnum.EXTRACTOR_JSON == linkMapTypeEnum) {
            if (componentJSON.containsKey("variableExtraction")) {
                JSONArray variableExtraction = componentJSON.getJSONArray("variableExtraction");
                for (int j = 0; j < variableExtraction.size(); j++) {
                    JSONObject objectJson = variableExtraction.getJSONObject(j);
                    if (StringUtil.isBlank(objectJson.getString("name"))) {
                        continue;
                    }
                    if (!variableDistanceMap.containsKey(objectJson.getString("name"))) {
                        variableInfoMap.put(objectJson.getString("name"), nodeName);
                        variableDistanceMap.put(objectJson.getString("name"), distance);
                    } else {
                        if (variableDistanceMap.get(objectJson.getString("name")) > distance) {
                            variableInfoMap.put(objectJson.getString("name"), nodeName);
                            variableDistanceMap.put(objectJson.getString("name"), distance);
                        }
                    }
                }
            }
//            }
        }
    }

    public Scene getScene(String sceneCode, Integer sceneVersion) {
        SceneInfoEntityDO sceneInfoEntityDO = apiTestRepository.querySceneInfoByCodeAndVersion(sceneCode, sceneVersion);
        if (sceneInfoEntityDO == null) {
            throw new ServiceException("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]不存在!");
        }

//        String sceneLinkMapJsonString = ztoOssService.getObjectTextWithRange("autojmx",sceneInfoEntityDO.getSceneOssPath()+sceneInfoEntityDO.getSceneOssFile());
        String sceneLinkMapJsonString = sceneInfoEntityDO.getSceneBackData();
        if (StringUtil.isEmpty(sceneLinkMapJsonString)) {
            throw new ServiceException("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]图数据内容为空!");
        }

        Scene scene = null;
        try {
            scene = JSON.parseObject(sceneLinkMapJsonString, Scene.class);
        } catch (Exception ex) {
            log.info("", ex);
            throw new ServiceException("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]场景节点格式不正确!");
        }

        return scene;

    }

    public Scene getScene(SceneInfoEntityDO sceneInfoEntityDO) {
        return this.getScene(sceneInfoEntityDO.getSceneCode(), sceneInfoEntityDO.getSceneVersion());
    }

    @Transactional(rollbackFor = Exception.class)
    public int generateLink(String sceneCode, Integer sceneVersion) {
        SceneInfoEntityDO sceneInfoEntityDO = apiTestRepository.querySceneInfoByCodeAndVersion(sceneCode, sceneVersion);
        Scene scene = this.getScene(sceneInfoEntityDO);
        String changeContent = scene.getLines().toJSONString();
        Map<String, Line> lineMap = new HashMap<>();
        Map<String, ComponentBaseInfo> nodeMap = JSON.parseObject(scene.getNodes().toJSONString(), new TypeReference<Map<String, ComponentBaseInfo>>() {
        });
        if (nodeMap == null || nodeMap.size() <= 0) {
            throw new ServiceException("场景图记录[" + sceneCode + "]版本号[" + sceneVersion + "]中没有有效节点!");
        }
        for (String nodeId : nodeMap.keySet()) {
            ComponentBaseInfo nodeBaseInfo = nodeMap.get(nodeId);
            if (nodeBaseInfo != null) {
                changeContent = changeContent + nodeBaseInfo.getName();
            }
        }

        String sceneBackDataMd5 = DigestUtil.md5Hex(changeContent);

        if (sceneBackDataMd5.equals(sceneInfoEntityDO.getSceneBackDataMd5())) {
            return 1;
        }

        sceneInfoEntityDO.setSceneBackDataMd5(sceneBackDataMd5);

        linkMapRepository.deleteBySceneInfo(sceneInfoEntityDO.getSceneCode(), sceneInfoEntityDO.getSceneVersion());

        User user = new User(sceneInfoEntityDO.getModifierId(), sceneInfoEntityDO.getModifier());

        log.info("场景图记录[" + sceneCode + "]版本号[" + sceneVersion + "]+" + "开始计算链路!");
        /**
         * 连线*
         */
        Set<String> lineKeyset = scene.getLines().keySet();
        LinkMap<ComponentBaseInfo> graph = new LinkMap();
        if (lineKeyset != null) {
            for (String lineCode : lineKeyset) {
                JSONObject lineJson = scene.getLines().getJSONObject(lineCode);
                Line line = JSON.toJavaObject(lineJson, Line.class);
                if (nodeMap.containsKey(line.getInNodeCode()) && nodeMap.containsKey(line.getOutNodeCode())) {
                    graph.addEdge(nodeMap.get(line.getInNodeCode()), nodeMap.get(line.getOutNodeCode()));
                } else {
                    throw new ServiceException("场景图记录[" + sceneCode + "]版本号[" + sceneVersion + "]中的场景连线不正确!");
                }
                lineMap.put(line.getInNodeCode() + line.getOutNodeCode(), line);
            }
        }
        int linkCount = 0;
        for (String startNode : scene.getStartNode()) {
            for (String endNode : scene.getEndNode()) {
                List<List<ComponentBaseInfo>> paths = graph.findAllPaths(nodeMap.get(startNode), nodeMap.get(endNode), true);
                if (CollectionUtil.isEmpty(paths)) {
                    log.info("场景图记录[" + sceneCode + "]版本号[" + sceneVersion + "]+" + "开始节点[" + startNode + "]-结束节点[" + endNode + "]没有生成任何链路!");
                    continue;
                }
                linkCount = linkCount + paths.size();
                log.info("场景图记录[" + sceneCode + "]版本号[" + sceneVersion + "]+" + "开始节点[" + startNode + "]-结束节点[" + endNode + "]生成链路:" + JSON.toJSONString(paths));
                for (List<ComponentBaseInfo> nodes : paths) {
                    if (CollectionUtil.isEmpty(nodes)) {
                        continue;
                    }
                    log.info("linkinfo:{}-{}-componentCount:{}:{}", sceneCode, sceneVersion, nodes.size(), JSON.toJSONString(nodes));
                    List<SceneLinkInfoEntityDO> sceneLinkInfoEntityDOS = this.createLinkData(nodes, nodeMap, lineMap, sceneInfoEntityDO, user);
                    log.info("createLinkData:{}-{}-componentCount:{}:{}", sceneCode, sceneVersion, sceneLinkInfoEntityDOS.size(), JSON.toJSONString(sceneLinkInfoEntityDOS));
                    linkMapRepository.batchInsertLink(sceneLinkInfoEntityDOS);
                    log.info("链路{}信息插入数据库存成功.", JSON.toJSONString(sceneLinkInfoEntityDOS));

                }
            }
        }
        log.info("场景图记录[" + sceneCode + "]版本号[" + sceneVersion + "]中有效链路数量为{}!", linkCount);
        if (linkCount <= 0) {
            throw new ServiceException("场景图记录[" + sceneCode + "]版本号[" + sceneVersion + "]中有效链路数量为0!");
        }

        apiTestRepository.updateSceneInfoByPrimaryKey(sceneInfoEntityDO);
        return 0;

    }

    private List<SceneLinkInfoEntityDO> createLinkData(List<ComponentBaseInfo> nodes, Map<String, ComponentBaseInfo> nodeMap, Map<String, Line> lineMap, SceneInfoEntityDO sceneInfoEntityDO, User user) {
        List<SceneLinkInfoEntityDO> linkInfoEntityDOS = new ArrayList<>();
        String lastNodeCode = "";
        String linkMapCode = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        int sn = 0;
        for (int i = 0; i < nodes.size(); i++) {
            String nodeCode = nodes.get(i).getCode();

            /**
             * 先看这个节点前是不是有线，有的话，就把线加入上去*
             */
            String lineKey = lastNodeCode + nodeCode;
            if (lineMap.containsKey(lineKey)) {
                SceneLinkInfoEntityDO linkLineInfoEntityDO = new SceneLinkInfoEntityDO();
                linkLineInfoEntityDO.setProductCode(sceneInfoEntityDO.getProductCode());
                linkLineInfoEntityDO.setSceneCode(sceneInfoEntityDO.getSceneCode());
                linkLineInfoEntityDO.setSceneVersion(sceneInfoEntityDO.getSceneVersion());
                linkLineInfoEntityDO.setEnable(true);
                linkLineInfoEntityDO.setLinkMapCode(linkMapCode);
                linkLineInfoEntityDO.setSequenceNumber(sn);
                linkLineInfoEntityDO.preCreate(user);
                linkLineInfoEntityDO.setLinkComponentCode(lineMap.get(lineKey).getCode());
                String lineName = "";
                if (!StringUtil.isBlank(lineMap.get(lineKey).getName())) {
                    lineName = lineMap.get(lineKey).getName();
                } else {
                    JSONObject controllerJSON = lineMap.get(lineKey).getIfController();
                    IfController controller = JSONObject.toJavaObject(controllerJSON, IfController.class);
                    if (controller != null) {
                        if (!StringUtil.isNotBlank(controller.getName())) {
                            lineName = controller.getName();
                        }
                    }
                }
                if (StringUtil.isNotEmpty(lineName)) {
                    linkLineInfoEntityDO.setLinkComponentName(lineName);
                    linkLineInfoEntityDO.setLinkComponentType(lineMap.get(lineKey).getType().name());
                    linkInfoEntityDOS.add(linkLineInfoEntityDO);
                    sn = sn + 1;
                }
                lastNodeCode = "";

            }

            if (!nodeMap.containsKey(nodeCode)) {
                throw new ServiceException("场景记录[" + sceneInfoEntityDO.getSceneCode() + "]版本号[" + sceneInfoEntityDO.getSceneVersion() + "]中没有找到[" + nodeCode + "]节点!");
            }
            SceneLinkInfoEntityDO linkInfoEntityDO = new SceneLinkInfoEntityDO();
            linkInfoEntityDO.setProductCode(sceneInfoEntityDO.getProductCode());
            linkInfoEntityDO.setSceneCode(sceneInfoEntityDO.getSceneCode());
            linkInfoEntityDO.setSceneVersion(sceneInfoEntityDO.getSceneVersion());
            linkInfoEntityDO.setEnable(true);
            linkInfoEntityDO.setLinkMapCode(linkMapCode);
            linkInfoEntityDO.setSequenceNumber(sn);
            linkInfoEntityDO.preCreate(user);
            linkInfoEntityDO.setLinkComponentCode(nodeCode);
            linkInfoEntityDO.setLinkComponentName(nodeMap.get(nodeCode).getName());
            linkInfoEntityDO.setLinkComponentType(nodeMap.get(nodeCode).getType().name());
            linkInfoEntityDOS.add(linkInfoEntityDO);
            lastNodeCode = nodeCode;
            sn = sn + 1;

        }
        return linkInfoEntityDOS;
    }

    public void executeAbort(String taskId) {
        if (redisService.hasKey(preFix + taskId)) {
            String buildId = redisService.getKey(preFix + taskId);
            AutomaticTaskTerminatedEvent automaticTaskTerminatedEvent = new AutomaticTaskTerminatedEvent();
            automaticTaskTerminatedEvent.setType(AutomaticRecordTypeEnum.JMETER_DEBUG);
            automaticTaskTerminatedEvent.setTaskId(taskId);
            automaticTaskTerminatedEvent.setCode(taskId);
            automaticTaskTerminatedEvent.setBuildId(buildId);
            DEBUG_TASK_EXECUTOR.execute(() -> {
                try {
                    jenkinsService.terminate(automaticTaskTerminatedEvent);
                } catch (Exception ex) {
                    log.error("", ex);
                }
            });
        }
    }

    public void debugCallBackEnd(String taskId, DebugTaskResp<String> resp, Long sleep, boolean isDocument) {
        if (isDocument) {
            return;
        }
        if (sleep > 0L) {
            try {
                Thread.sleep(sleep.longValue());
            } catch (Exception sex) {
                log.error("", sex);
            }
        }
//        WebSocketMessage message = new WebSocketMessage();
//        message.setKey(taskId);
//        message.setData(resp);
        log.info("开始发送消息。{}", taskId);
//        zmsTemplate.send(DEVOPS_WS_MESSAGE_MQ, message);
//        emit(TestEventEnums.TEST_AUTOMATIC_DEBUG_END.toReactiveId(taskId), resp);
        sendDebugCallbackMessage(taskId, resp);
        log.info("结束发送消息。{}", taskId);
        if (redisService.hasKey("Debug-Task-" + taskId)) {
            redisService.delete("Debug-Task-" + taskId);
        }
        if (redisService.hasKey(taskId)) {
            redisService.setAliveTime(taskId, 1, TimeUnit.MINUTES);
        }
        try {
            this.updateDebugRecord(taskId, setAutomaticStatus(resp.getType()));
        } catch (Exception e) {
            log.error("", e);
        }
        qcDebugService.removeTaskId(taskId);
    }


    protected void sendDebugCallbackMessage(String taskId, DebugTaskResp message) {
        emit(TestEventEnums.TEST_AUTOMATIC_DEBUG_END.toReactiveId(taskId), message);
    }


    /**
     * document 运行
     *
     * @param debugTaskInfo
     * @param user
     * @return
     */
    public DebugTaskInfo debugWithoutNode(DebugTaskInfo debugTaskInfo, User user) {
        log.info("文档调试开始, taskId:{}, debugTaskInfo:{}", debugTaskInfo.getTaskId(), JSON.toJSONString(debugTaskInfo));
        return debug(debugTaskInfo, user, true);
    }

    public DebugTaskInfo debugOnNode(DebugOnNodeReq req, User user) {
        String taskId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        //调用公共的刷日志方法-取要调试的链路开始
        DebugTaskInfo debugTaskInfo = this.getDebugTaskInfo(taskId, req.getSceneCode(), req.getNodeCode(), req.getDebugType(), req.getStatus());
        debugTaskInfo.setZtoenv(req.getZtoenv());
        debugTaskInfo.setSceneCode(req.getSceneCode());
        debugTaskInfo.setEnvName(req.getEnvName());
        if (UseCaseFactoryTypeEnum.CREATE.getCode() == debugTaskInfo.getSceneType()
                && CollectionUtil.isNotEmpty(req.getInputParameter())) {
            debugTaskInfo.getScene().setInputParameter(req.getInputParameter());
        }
        debugTaskInfo.getScene().setSceneCode(req.getSceneCode());
        debugTaskInfo.setRequestType("SCENEDEBUG");
        return debug(debugTaskInfo, user, false);
    }

    private boolean invokeQcRunnerIfNecessary(DebugTaskInfo debugTaskInfo, User user) {
        // 直接将原有配置的白名单改为黑名单，后续会剔除此代码
        boolean blackList = qcRunnerGateway.isWhiteList(user);
        if (blackList) {
            return false;
        }
        apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "任务: " + debugTaskInfo.getTaskId() + "开始性能优化版本调用...", false);

        Transaction transaction = Cat.newTransaction("PerformanceScene", debugTaskInfo.getTaskId());
        try {
            Map<String, Object> context = prepareSceneExecutionData(debugTaskInfo, user);
            Map<String, Pair<DebugTaskInfo, Map<String, Object>>> debugTaskInfoMap = prepareSceneDataGenerationData(debugTaskInfo, user);

            qcRunnerGateway.debug(debugTaskInfo, context, debugTaskInfoMap);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception ex) {
            apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "任务: " + debugTaskInfo.getTaskId() + "开始性能优化版本调用...", false);
            Cat.logError(ex);
            transaction.setStatus(ex);

            DebugTaskResp<String> debugTaskResp = new DebugTaskResp();
            debugTaskResp.setTaskId(debugTaskInfo.getTaskId());
            debugTaskResp.setType(DebugTaskRespTypeEnum.Exception);
            debugTaskResp.setData(ex.getMessage());
            apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "造数调用异常：" + ex.getMessage(), false);
            debugCallBackEnd(debugTaskInfo.getTaskId(), debugTaskResp, 0L, false);
        } finally {
            transaction.complete();
        }
        return true;
    }

    private Map<String, Pair<DebugTaskInfo, Map<String, Object>>> prepareSceneDataGenerationData(DebugTaskInfo debugTaskInfo, User user) {
        Map<String, Pair<DebugTaskInfo, Map<String, Object>>> debugTaskInfoMap = new HashMap<>();
        Scene scene = debugTaskInfo.getScene();
        JSONObject nodes = scene.getNodes();
        for (String nodeCode : nodes.keySet()) {
            Node node = JSON.toJavaObject(nodes.getJSONObject(nodeCode), Node.class);
            findDataGenerationDebugInfo(node, debugTaskInfo.getTaskId(), user, debugTaskInfoMap);
        }
        return debugTaskInfoMap;
    }

    private Map<String, Object> prepareSceneExecutionData(DebugTaskInfo debugTaskInfo, User user) {
        Scene scene = debugTaskInfo.getScene();
        scene.setSceneCode(debugTaskInfo.getSceneCode());
        String productCode = debugTaskInfo.getProductCode();
        List<ApiTestVariableVO> globalHttpHeaderVariables = apiTestRepository.querySceneVariable(productCode, scene.getSceneCode(),
                VariableTypeEnum.VARIABLE, SubVariableTypeEnum.HEADER, Collections.singletonList(VariableUsageTypeEnum.UNKNOWN));
        List<ApiTestVariableVO> globalCustomVariables = apiTestRepository.querySceneVariable(productCode, scene.getSceneCode(),
                VariableTypeEnum.VARIABLE, SubVariableTypeEnum.CUSTOM, Collections.singletonList(VariableUsageTypeEnum.UNKNOWN));
        Map<String, Object> context = new HashMap<>();
        for (ApiTestVariableVO variable : globalCustomVariables) {
            context.put(variable.getVariableKey(), variable.getVariableValue());
        }

        JSONObject nodes = scene.getNodes();
        for (String nodeCode : nodes.keySet()) {
            Node node = JSON.toJavaObject(nodes.getJSONObject(nodeCode), Node.class);
            dealHttpNode(node, globalHttpHeaderVariables, productCode, debugTaskInfo.getTaskId());
            nodes.put(nodeCode, node);
        }
        return context;
    }


    /**
     * 之所以独立方法，是为了避免递归
     *
     * @param node
     * @param taskId
     * @param user
     * @param debugTaskInfoMap
     */
    private void findDataGenerationDebugInfo(Node node, String taskId, User user, Map<String, Pair<DebugTaskInfo, Map<String, Object>>> debugTaskInfoMap) {
        JSONObject sampler = node.getSampler();
        if (Objects.isNull(sampler)) {
            log.error("taskId: {} 中包含无效的节点： {}", taskId, JSON.toJSONString(node));
            return;
        }
        if (!LinkMapTypeEnum.DataCenter_REQUEST_COMPONENT.name().equals(sampler.getString("type"))) {
            return;
        }

        String sceneCode = sampler.getString("dataCenterSceneCode");
        DebugTaskInfo dataGenerationDebugTaskInfo = getDataGenerationDebugTaskInfo(taskId, sceneCode);
        Map<String, Object> context = prepareSceneExecutionData(dataGenerationDebugTaskInfo, user);
        Pair<DebugTaskInfo, Map<String, Object>> data = Pair.of(dataGenerationDebugTaskInfo, context);
        debugTaskInfoMap.put(sceneCode, data);
    }

    /**
     * 处理HTTP 节点需要的cookie和全局请求头
     *
     * @param node
     * @param globalHttpHeaderVariables
     * @param productCode
     * @param taskId
     */
    private void dealHttpNode(Node node, List<ApiTestVariableVO> globalHttpHeaderVariables, String productCode, String taskId) {
        JSONObject sampler = node.getSampler();
        boolean contains = Arrays.asList(HTTP_REQUEST_COMPONENT_FORM_TYPE.name(), HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE.name()).contains(sampler.getString("type"));
        if (!contains) {
            return;
        }
        JSONArray httpRequestHeader = sampler.getJSONArray("httpRequestHeader");
        if (Objects.isNull(httpRequestHeader)) {
            httpRequestHeader = new JSONArray();
            sampler.put("httpRequestHeader", httpRequestHeader);
        }
        final JSONArray finalHttpRequestHeader = httpRequestHeader;

        // 处理全局的请求头
        globalHttpHeaderVariables.forEach(v -> {
            String headerKey = v.getVariableKey();
            String headerValue = v.getVariableValue();

            JSONObject header = new JSONObject();
            header.put("name", headerKey);
            header.put("value", headerValue);
            finalHttpRequestHeader.add(header);
        });

        // 处理cookie
        String userCode = node.getUser();
        if (StringUtils.isNotEmpty(userCode)) {
            ApiTestVariableVO variable = apiTestRepository.getApiTestVariableByVariableCode(userCode);
            if (null == variable) {
                String errorMsg = String.format("节点：%s, 节点code: %s, 依赖的变量 %s 不存在！", node.getName(), node.getCode(), userCode);
                apiDebugLogService.setApiDebugLog(taskId, errorMsg, false);
                throw new ServiceException(errorMsg);
            }
            String namespace;
            if (Boolean.FALSE.equals(node.getGatewaySource())) {
                namespace = this.getNameSpace(sampler.getString("serverNameOrIp"));
            } else {
                namespace = this.getNameSpace(node.getRequestUrl());
            }

            Map<String, String> cookieMap = authCookieDomainService.getDataToLogin(variable, namespace, productCode);
            Optional<String> cookieOptional = cookieMap.entrySet().stream().findAny().map(Map.Entry::getValue);


            if (cookieOptional.isPresent()) {
                apiDebugLogService.setApiDebugLog(taskId, String.format("获取到用户, \nvariable: %s  \nnamespace %s  \nuserCode: %s Cookie： %s",
                        variable.getVariableCode(), namespace, userCode, cookieOptional.get()), false);
                sampler.put("userCookie", cookieOptional.get());
            } else {
                log.error("造数优化获取cookie失败：{}", JSON.toJSONString(node));
            }
        }
    }

    public DebugTaskInfo debug(DebugTaskInfo debugTaskInfo, User user, boolean isDocument) {
        DEBUG_TASK_EXECUTOR.execute(() -> {
            try {
                this.insertDebugRecord(debugTaskInfo.getProductCode(), debugTaskInfo.getTaskId(), user);
                if (!isDocument) {
                    this.saveSceneDebugInfo(debugTaskInfo.getProductCode(), debugTaskInfo.getTaskId(), debugTaskInfo.getSceneCode(), user);
                }
            } catch (Exception e) {
                log.error("", e);
            }

            boolean performanceExecute = invokeQcRunnerIfNecessary(debugTaskInfo, user);
            if (performanceExecute) {
                return;
            }

            boolean createJmxSuccess = true;
            try {
                apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "开始生成调试脚本...", isDocument);
                log.info("开始生成调试脚本 >>> " + debugTaskInfo.getTaskId());
                this.createDebugFile(debugTaskInfo, isDocument);
                log.info("生成调试脚本结束 >>> " + debugTaskInfo.getTaskId());
                apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "生成调试脚本完成...", isDocument);
            } catch (Exception ex) {
                createJmxSuccess = false;
                log.info("生成调试脚本异常 >>> " + debugTaskInfo.getTaskId());
                log.info("", ex);
                DebugTaskResp<String> debugTaskResp = new DebugTaskResp();
                debugTaskResp.setTaskId(debugTaskInfo.getTaskId());
                debugTaskResp.setType(DebugTaskRespTypeEnum.Exception);
                debugTaskResp.setData(ex.getMessage());
                apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "生成调试脚本异常：" + ex.getMessage(), isDocument);
                debugCallBackEnd(debugTaskInfo.getTaskId(), debugTaskResp, 2000L, isDocument);
            }
            if (createJmxSuccess) {
                log.info("开始调用debug的任务调度器...... >>> " + debugTaskInfo.getTaskId());
                apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "开始调用debug的任务调度器.....", isDocument);
                try {
                    qcDebugService.execute(debugTaskInfo, isDocument);
                    log.info("调用debug的任务调度器完成...... >>> " + debugTaskInfo.getTaskId());
                    apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "调试任务已经成功提交到debug.....", isDocument);
                } catch (Exception ex) {
                    log.error("", ex);
                    log.info("调试脚本debug调用异常 >>> " + debugTaskInfo.getTaskId());
                    DebugTaskResp<String> debugTaskResp = new DebugTaskResp();
                    debugTaskResp.setTaskId(debugTaskInfo.getTaskId());
                    debugTaskResp.setType(DebugTaskRespTypeEnum.Exception);
                    debugTaskResp.setData(ex.getMessage());
                    apiDebugLogService.setApiDebugLog(debugTaskInfo.getTaskId(), "debug调用异常：" + ex.getMessage(), isDocument);
                    debugCallBackEnd(debugTaskInfo.getTaskId(), debugTaskResp, 2000L, isDocument);
                }
            }
            /**
             * 清除机器上的执行文件*
             */
            try {
                Thread.sleep(2000L);
                String[] command = new String[]{
                        "rm",
                        "-rf",
                        "/data/jmeter/" + debugTaskInfo.getTaskId() + "/"
                };
                ProcessManager.execute(command, debugTaskInfo.getTaskId(), ProcessManager.flagTaskRM);
                if (ProcessManager.processHandel.containsKey(debugTaskInfo.getTaskId() + ProcessManager.flagTaskRM)) {

                    Process process = ProcessManager.processHandel.get(debugTaskInfo.getTaskId() + ProcessManager.flagTaskRM);
                    while (process.isAlive()) {
                        Thread.sleep(500L);
                    }
                    ProcessManager.processHandel.remove(debugTaskInfo.getTaskId() + ProcessManager.flagTaskRM);
                    process.destroy();
                }
            } catch (Exception ex) {
                log.error("", ex);
            }

        });
        return debugTaskInfo;
    }


    public void debugCallBackLog(DebugCallBackLogReq req) {
        if (StringUtils.isNotEmpty(req.getBuildId())) {
            log.info("DebugCallBackLogReq >>> {}", JsonUtil.toJSON(req));
            if (!redisService.hasKey(preFix + req.getTaskId())) {
                redisService.setKey(preFix + req.getTaskId(), req.getBuildId(), 8L, TimeUnit.HOURS);
            }
        }
        apiDebugLogService.setApiDebugLog(req.getTaskId(), req.getExceptionReason(), false);

    }

    private void debugExecute(DebugTaskInfo taskInfo) {
        SimpleQueryVO product = iProductRpcService.getProductVO(taskInfo.getProductCode());
        String productName = null == product ? "" : product.getProductName();
        boolean result = jenkinsService.executeDebug(taskInfo, System.getProperty("titans.dubbo.tag"), productName);
        log.info("场景图调用jenkins调试执行：" + !result);
    }

    private void createDebugFile(DebugTaskInfo debugTaskInfo, boolean idDocument) {
        //调用公共的刷日志方法-取要调试的链路结果
        File file = new File(debugPathHome + debugTaskInfo.getTaskId() + "/");
        if (!file.exists() && !file.isDirectory()) {
            file.mkdirs();
        }

        /**
         * 设置调试链路的节点
         */
        this.setDebugNodes(debugTaskInfo);

        /**
         * 设置非网关接口环境配置
         */
        this.setDebugEnvUrl(debugTaskInfo);

        /**
         * 生成数据文件
         * */
        this.createSimpleDataFile(debugTaskInfo);

        /**
         * 生成变量文件
         */
        this.createVariableFile(debugTaskInfo);

        /**
         * 生成redisConfig文件
         */
        this.createRedisConfigFile(debugTaskInfo, Boolean.FALSE, Strings.EMPTY);

        /**
         * 生成jmx测试脚本*
         */
        this.createScript(debugTaskInfo);
    }

    /**
     * *
     *
     * @param taskId
     * @param sceneCode
     * @param nodeCode
     * @param debugType
     * @return DebugTaskInfo 后面生成数据，生成脚本等，都带着这个数据结构下传
     */
    private DebugTaskInfo getDebugTaskInfo(String taskId, String sceneCode, String nodeCode, DebugTypeEnum debugType, SceneInfoStatusEnum status) {
        DebugTaskInfo debugTaskInfo = new DebugTaskInfo();
        if (null == status) {
            status = SceneInfoStatusEnum.edit;
        }
        SceneInfoEntityDO sceneInfoEntityDO = apiTestRepository.queryLatestSceneInfo(sceneCode, status);
        if (null == sceneInfoEntityDO && SceneInfoStatusEnum.publish.equals(status)) {
            sceneInfoEntityDO = apiTestRepository.queryLatestSceneInfo(sceneCode, SceneInfoStatusEnum.edit);
        }
        if (sceneInfoEntityDO == null) {
            throw new ServiceException("场景记录[" + sceneCode + "]的场景不存在！");
        }
        String productCode = sceneInfoEntityDO.getProductCode();
        Integer sceneType = sceneInfoEntityDO.getSceneType();
        Integer sceneVersion = sceneInfoEntityDO.getSceneVersion();
        String sceneName = sceneInfoEntityDO.getSceneName();
        String sceneLinkMapJsonString = sceneInfoEntityDO.getSceneBackData();
        if (StringUtil.isEmpty(sceneLinkMapJsonString)) {
            throw new ServiceException("图数据内容为空!");
        }

        Scene scene = null;
        Map<String, Line> lineMap = new HashMap<>();
        try {
            scene = JSON.parseObject(sceneLinkMapJsonString, Scene.class);
        } catch (Exception ex) {
            log.info("", ex);
            throw new ServiceException("场景节点格式不正确!");
        }

        Map<String, ComponentBaseInfo> nodeMap = JSON.parseObject(scene.getNodes().toJSONString(), new TypeReference<Map<String, ComponentBaseInfo>>() {
        });

        if (nodeMap == null || nodeMap.size() <= 0) {
            throw new ServiceException("没有有效节点!");
        }
        if (!nodeMap.containsKey(nodeCode)) {
            throw new ServiceException("当前节点已经被删除，无法调试!");
        }

        LinkMap<ComponentBaseInfo> graph = new LinkMap();
        /**
         * 连线*
         */
        Set<String> lineKeyset = scene.getLines().keySet();
        ComponentBaseInfo startNode = nodeMap.get(nodeCode);
        ComponentBaseInfo endNode = nodeMap.get(nodeCode);
        if (lineKeyset != null && DebugTypeEnum.FIRSTLY == debugType) {
            for (String lineCode : lineKeyset) {
                JSONObject lineJson = scene.getLines().getJSONObject(lineCode);
                Line line = JSON.toJavaObject(lineJson, Line.class);
                if (nodeMap.containsKey(line.getInNodeCode()) && nodeMap.containsKey(line.getOutNodeCode())) {
                    graph.addEdge(nodeMap.get(line.getInNodeCode()), nodeMap.get(line.getOutNodeCode()));
                } else {
                    throw new ServiceException("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]中的场景连线不正确!");
                }
                lineMap.put(line.getInNodeCode() + line.getOutNodeCode(), line);
            }
            if (CollectionUtil.isNotEmpty(scene.getStartNode())) {
                if (!nodeMap.containsKey(scene.getStartNode().get(0))) {
                    throw new ServiceException("链路开始节点不存在!");
                }
                startNode = nodeMap.get(scene.getStartNode().get(0));
            }
        }

        List<List<ComponentBaseInfo>> paths = graph.findAllPaths(startNode, endNode, true);
        if (CollectionUtil.isEmpty(paths)) {
            throw new ServiceException("链路计算结果为空!");
        }

        List<ComponentBaseInfo> firstLink = paths.get(0);
        List<SceneLinkInfoEntityDO> firstLinkInfo = this.createLinkData(firstLink, nodeMap, lineMap, sceneInfoEntityDO, null);
        debugTaskInfo.setTaskId(taskId);
        debugTaskInfo.setSceneCode(sceneCode);
        debugTaskInfo.setSceneVersion(sceneVersion);
        debugTaskInfo.setProductCode(productCode);
        debugTaskInfo.setSceneType(sceneType);
        debugTaskInfo.setScene(scene);
        JSONObject nodes = scene.getNodes();
        if (firstLinkInfo != null) {
            for (SceneLinkInfoEntityDO sceneLinkInfoEntityDO : firstLinkInfo) {
                if (LinkMapTypeEnum.valueOf(sceneLinkInfoEntityDO.getLinkComponentType()) == LinkMapTypeEnum.NODE) {
                    JSONObject nodeInfoJson = nodes.getJSONObject(sceneLinkInfoEntityDO.getLinkComponentCode());
                    JSONObject sampler = nodeInfoJson.getJSONObject("sampler");
                    HttpRequestComponent httpRequestComponent = JSON.toJavaObject(sampler, HttpRequestComponent.class);
                    sceneLinkInfoEntityDO.setMockSwitch(httpRequestComponent.getMockSwitch());
                }
            }
        }
        debugTaskInfo.setLinkBaseInfo(firstLinkInfo);
        debugTaskInfo.setProductCode(productCode);
        debugTaskInfo.setSceneName(sceneName);
        return debugTaskInfo;

    }

    /**
     * 在用例工厂中使用造数，直接迁移的原有方法，剔除了无用参数
     * todo 原有逻辑：优先查找已发布造数，若不存在则使用编辑中的造数   这是否会导致用户理解偏差？
     *
     * @param taskId
     * @param sceneCode
     * @return
     */
    private DebugTaskInfo getDataGenerationDebugTaskInfo(String taskId, String sceneCode) {
        DebugTaskInfo debugTaskInfo = new DebugTaskInfo();
        SceneInfoEntityDO sceneInfoEntityDO = apiTestRepository.queryLatestSceneInfo(sceneCode, SceneInfoStatusEnum.publish);
        if (null == sceneInfoEntityDO) {
            sceneInfoEntityDO = apiTestRepository.queryLatestSceneInfo(sceneCode, SceneInfoStatusEnum.edit);
        }
        if (sceneInfoEntityDO == null) {
            throw new ServiceException("场景记录[" + sceneCode + "]的场景不存在！");
        }
        String productCode = sceneInfoEntityDO.getProductCode();
        Integer sceneType = sceneInfoEntityDO.getSceneType();
        Integer sceneVersion = sceneInfoEntityDO.getSceneVersion();
        String sceneName = sceneInfoEntityDO.getSceneName();
        String sceneLinkMapJsonString = sceneInfoEntityDO.getSceneBackData();
        if (StringUtil.isEmpty(sceneLinkMapJsonString)) {
            throw new ServiceException("图数据内容为空!");
        }

        Scene scene = null;
        Map<String, Line> lineMap = new HashMap<>();
        try {
            scene = JSON.parseObject(sceneLinkMapJsonString, Scene.class);
        } catch (Exception ex) {
            log.info("", ex);
            throw new ServiceException("场景节点格式不正确!");
        }

        Map<String, ComponentBaseInfo> nodeMap = JSON.parseObject(scene.getNodes().toJSONString(), new TypeReference<Map<String, ComponentBaseInfo>>() {
        });

        if (nodeMap == null || nodeMap.size() <= 0) {
            throw new ServiceException("没有有效节点!");
        }

        LinkMap<ComponentBaseInfo> graph = new LinkMap();
        /**
         * 连线*
         */
        Set<String> lineKeyset = scene.getLines().keySet();
        ComponentBaseInfo startNode = nodeMap.get(scene.getStartNode().stream().findAny().get());
        ComponentBaseInfo endNode = nodeMap.get(scene.getEndNode().stream().findAny().get());
        for (String lineCode : lineKeyset) {
            JSONObject lineJson = scene.getLines().getJSONObject(lineCode);
            Line line = JSON.toJavaObject(lineJson, Line.class);
            if (nodeMap.containsKey(line.getInNodeCode()) && nodeMap.containsKey(line.getOutNodeCode())) {
                graph.addEdge(nodeMap.get(line.getInNodeCode()), nodeMap.get(line.getOutNodeCode()));
            } else {
                throw new ServiceException("场景记录[" + sceneCode + "]版本号[" + sceneVersion + "]中的场景连线不正确!");
            }
            lineMap.put(line.getInNodeCode() + line.getOutNodeCode(), line);
        }
        if (CollectionUtil.isNotEmpty(scene.getStartNode())) {
            if (!nodeMap.containsKey(scene.getStartNode().get(0))) {
                throw new ServiceException("链路开始节点不存在!");
            }
            startNode = nodeMap.get(scene.getStartNode().get(0));
        }

        List<List<ComponentBaseInfo>> paths = graph.findAllPaths(startNode, endNode, true);
        if (CollectionUtil.isEmpty(paths)) {
            throw new ServiceException("链路计算结果为空!");
        }

        List<ComponentBaseInfo> firstLink = paths.get(0);
        List<SceneLinkInfoEntityDO> firstLinkInfo = this.createLinkData(firstLink, nodeMap, lineMap, sceneInfoEntityDO, null);
        debugTaskInfo.setTaskId(taskId);
        debugTaskInfo.setSceneCode(sceneCode);
        debugTaskInfo.setSceneVersion(sceneVersion);
        debugTaskInfo.setProductCode(productCode);
        debugTaskInfo.setSceneType(sceneType);
        debugTaskInfo.setScene(scene);
        JSONObject nodes = scene.getNodes();
        for (SceneLinkInfoEntityDO sceneLinkInfoEntityDO : firstLinkInfo) {
            if (LinkMapTypeEnum.valueOf(sceneLinkInfoEntityDO.getLinkComponentType()) == LinkMapTypeEnum.NODE) {
                JSONObject nodeInfoJson = nodes.getJSONObject(sceneLinkInfoEntityDO.getLinkComponentCode());
                JSONObject sampler = nodeInfoJson.getJSONObject("sampler");
                HttpRequestComponent httpRequestComponent = JSON.toJavaObject(sampler, HttpRequestComponent.class);
                sceneLinkInfoEntityDO.setMockSwitch(httpRequestComponent.getMockSwitch());
            }
        }
        debugTaskInfo.setLinkBaseInfo(firstLinkInfo);
        debugTaskInfo.setProductCode(productCode);
        debugTaskInfo.setSceneName(sceneName);
        return debugTaskInfo;

    }


    private void createSimpleDataFile(DebugTaskInfo debugTaskInfo) {
        log.info("开始生成数据文件 >>> {}", debugTaskInfo.getTaskId());
        try {
            Map<String, Map<String, Object>> linkMap = new HashMap<>();
            debugTaskInfo.getScene().getNodes().keySet().stream()
                    .filter(key -> debugTaskInfo.getDebugNodes().contains(key))
                    .forEach(key -> {
                        Node node = JSON.parseObject(debugTaskInfo.getScene().getNodes().getString(key), Node.class);
                        ApiTestDataVO reqData = GenerateApiCaseUtil.transNodeToCase(node);
                        ApiCaseEntityDO nodeCase = new ApiCaseEntityDO();
                        nodeCase.setNodeCode(node.getCode());
                        nodeCase.setSceneCode(debugTaskInfo.getSceneCode());
                        nodeCase.setUserVariableCode(node.getUser());
                        nodeCase.setReqData(JSON.toJSONString(reqData));
                        List<ApiCaseEntityDO> singleCase = Collections.singletonList(nodeCase);
                        List<Map<String, Map<String, Object>>> singleNode =
                                dealNodeMap(singleCase, true, node.getSampler().getString("serverNameOrIp"), debugTaskInfo.getProductCode());
                        linkMap.putAll(singleNode.get(0));
                    });
            if (UseCaseFactoryTypeEnum.CREATE.getCode() == debugTaskInfo.getSceneType()) {
                Map<String, Object> caseMap = GenerateApiCaseUtil.dealDebugDataParameterMap(debugTaskInfo);
                linkMap.put(debugTaskInfo.getSceneCode(), caseMap);
            }
            File dataFile = new File(debugPathHome + debugTaskInfo.getTaskId() + "/data.json");
            fileUtilService.makesureDirExists(dataFile.getParent());
            fileUtilService.write(JSON.toJSONString(Collections.singleton(linkMap)), dataFile);
            log.info("数据文件保存成功！>>> {}", dataFile.getPath());
        } catch (Exception e) {
            log.error("数据文件保存失败！", e);
            throw new ServiceException("数据文件保存失败！" + e.getMessage() + debugTaskInfo.getTaskId());
        }
    }

    private void createScript(DebugTaskInfo debugTaskInfo) {
        log.info("开始生成脚本文件 >>> {}", debugTaskInfo.getTaskId());
        try {
            jmxUtil.createJmeterScript(debugTaskInfo);
        } catch (Exception e) {
            log.error("脚本文件生成失败！", e);
            throw new ServiceException("脚本文件生成失败！" + debugTaskInfo.getTaskId());
        }
        log.info("生成脚本文件成功 >>> {}", debugTaskInfo.getTaskId());
    }

    private void createVariableFile(DebugTaskInfo debugTaskInfo) {
        log.info("开始生成变量文件 >>> {}", debugTaskInfo.getTaskId());
        List<Node> nodeList = debugTaskInfo.getScene().getNodes().keySet().stream()
                .map(key -> JSON.parseObject(debugTaskInfo.getScene().getNodes().getString(key), Node.class))
                .collect(Collectors.toList());
        Map<String, String> variableMap = new HashMap<>();
        Map<String, String> dateCenterVariableMap = new HashMap<>();
        Map<String, String> userMap = new HashMap<>();
        this.getUser(userMap, nodeList, true, null, debugTaskInfo.getProductCode());
        this.setUserCookies(userMap, variableMap, debugTaskInfo.getProductCode());
        if (debugTaskInfo.getSceneType() == 1) {
            this.addVariableMap(debugTaskInfo.getProductCode(), debugTaskInfo.getSceneCode(), variableMap);
        } else {
            this.addVariableMap(debugTaskInfo.getProductCode(), debugTaskInfo.getSceneCode(), dateCenterVariableMap);
        }
        if (MapUtils.isNotEmpty(variableMap)) {
            this.writeFile(debugTaskInfo, JSON.parseObject(JSON.toJSONString(variableMap)), "variable.json");
        }
        if (MapUtils.isNotEmpty(dateCenterVariableMap)) {
            this.writeFile(debugTaskInfo, JSON.parseObject(JSON.toJSONString(variableMap)), "dataCenterVariable.json");
        } else {
            this.writeFile(debugTaskInfo, new JSONObject(), "dataCenterVariable.json");
        }
        // 造数出入参设置
        JSONObject variableJson = this.addDataCenterVariable(debugTaskInfo);
        if (!variableJson.isEmpty()) {
            this.writeFile(debugTaskInfo, variableJson, "dataCenterInputVariable.json");
            this.writeFile(debugTaskInfo, variableJson, "data/dataCenterInputVariable.json");
        }
    }

    private void writeFile(DebugTaskInfo debugTaskInfo, JSONObject variableJson, String fileName) {
        try {
            File variableFile = new File(debugPathHome + debugTaskInfo.getTaskId() + "/" + fileName);
            fileUtilService.makesureDirExists(variableFile.getParent());
            fileUtilService.write(JSON.toJSONString(variableJson), variableFile);
            log.info("变量文件保存成功！>>> {}", variableFile.getPath());
        } catch (Exception e) {
            log.error("变量文件保存失败！", e);
            throw new ServiceException("变量文件保存失败！" + debugTaskInfo.getTaskId());
        }
    }

    public void uploadRedisConfig(SceneInfoEntityDO sceneInfoEntityDO, String taskId, String productCode) {
        if (null == sceneInfoEntityDO || StringUtil.isEmpty(sceneInfoEntityDO.getSceneBackData()) || StringUtils.isBlank(taskId)) {
            log.warn("uploadRedisConfig_param_is_blank");
            return;
        }
        try {
            Scene scene = JSON.parseObject(sceneInfoEntityDO.getSceneBackData(), Scene.class);
            if (null == scene) {
                log.warn("uploadRedisConfig_scene_is_null");
                return;
            }
            DebugTaskInfo debugTaskInfo = new DebugTaskInfo();
            debugTaskInfo.setScene(scene);
            debugTaskInfo.setTaskId(taskId);
            debugTaskInfo.setProductCode(productCode);
            createRedisConfigFile(debugTaskInfo, Boolean.TRUE,
                    sceneInfoEntityDO.getSceneOssPath()
                            + (sceneInfoEntityDO.getStatus().equals(SceneInfoStatusEnum.edit) ? "/edit" : "/publish"));
        } catch (Exception ex) {
            log.warn("uploadRedisConfig_parse_backData_error_{}", ex.getMessage());
        }
    }

    private void createRedisConfigFile(DebugTaskInfo debugTaskInfo, Boolean ossFlag, String ossPath) {
        log.info("开始生成redis配置文件_sceneCode:{},taskId:{}", debugTaskInfo.getScene().getSceneCode(), debugTaskInfo.getTaskId());
        List<Node> nodeList = debugTaskInfo
                .getScene()
                .getNodes()
                .keySet()
                .stream()
                .map(key -> JSON.parseObject(debugTaskInfo.getScene().getNodes().getString(key), Node.class))
                .collect(Collectors.toList());
        Map<String, Map<String, String>> redisMap = this.getRedisConfig(debugTaskInfo.getProductCode(), nodeList);
        if (MapUtils.isEmpty(redisMap)) {
            log.warn("产品[{}]获取redis配置为空_sceneCode:{}！", debugTaskInfo.getProductCode(), debugTaskInfo.getScene().getSceneCode());
            return;
        }
        try {
            if (ossFlag) {
                ztoOssService.createObject("autojmx",
                        ossPath + "/data/redisConfig.json",
                        JSONObject.toJSONString(Collections.singletonList(redisMap)));
            } else {
                File variableFile = new File(debugPathHome + debugTaskInfo.getTaskId() + "/redisConfig.json");
                fileUtilService.makesureDirExists(variableFile.getParent());
                fileUtilService.write(JSON.toJSONString(Collections.singletonList(redisMap)), variableFile);
                log.info("redis配置文件保存成功_path: {}", variableFile.getPath());
            }
        } catch (Exception e) {
            log.error("redis配置文件保存失败！", e);
            throw new ServiceException("redis配置文件保存失败！" + debugTaskInfo.getTaskId());
        }
    }

    private Map<String, Map<String, String>> getRedisConfig(String productCode, List<Node> nodeList) {
        Set<String> clusterIdList = new HashSet<>();
        nodeList.forEach(node -> {
            buildClusterIdList(node.getPreComponents(), clusterIdList);
            buildClusterIdList(node.getPostComponents(), clusterIdList);
        });
        return apiTestQueryDomainService.listRedisConfig(productCode, clusterIdList);
    }

    private void buildClusterIdList(JSONArray components, Set<String> clusterIdList) {
        if (null == components) {
            return;
        }
        for (int i = 0; i < components.size(); i++) {
            JSONObject componentJSON = components.getJSONObject(i);
            if (StringUtil.isEmpty(componentJSON.getString("type"))
                    || !componentJSON.getString("type").equals(LinkMapTypeEnum.REDIS.name())) {
                continue;
            }
            clusterIdList.add(componentJSON.getString("clusterId"));
        }
    }

    public void getUser(Map<String, String> userMap, List<Node> nodeList, Boolean isDebug, String envUrl, String productCode) {
        nodeList.stream()
                .forEach(node -> {
                    if (node.getSampler().getString("type").equals(LinkMapTypeEnum.DataCenter_REQUEST_COMPONENT.name())) {
                        this.getDataCenterUsers(node, userMap, isDebug, productCode);
                    } else {
                        if (StringUtil.isNotEmpty(node.getUser())) {
                            String nameSpace;
                            if (Boolean.FALSE.equals(node.getGatewaySource())) {
                                nameSpace = this.getNameSpace(node.getSampler().getString("serverNameOrIp"));
                            } else {
                                nameSpace = this.getNameSpace(node.getRequestUrl());
                            }
                            if (StringUtil.isEmpty(nameSpace) && StringUtil.isNotEmpty(envUrl)) {
                                nameSpace = this.getNameSpace(envUrl);
                            }
                            if (productCode.equals("PRO2202168000")) {
                                userMap.put(node.getUser(), nameSpace);
                            } else {
                                userMap.put(node.getUser() + "_" + nameSpace, nameSpace);
                            }
                        }
                    }
                });
    }

    public String getNameSpace(String url) {
        log.info("getNameSpace url : {}", url);
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        int startIndex = url.contains("//") ? url.indexOf("//") + 2 : 0;
        int endIndex = url.indexOf(".", startIndex);
        String nameSpace = null;
        if (endIndex != -1) {
            nameSpace = url.substring(startIndex, endIndex);
        } else {
            log.error("未找到合适的截取范围. url : {}", url);
        }
        return nameSpace;
    }

    private void setDebugEnvUrl(DebugTaskInfo debugTaskInfo) {
        JSONObject nodes = new JSONObject();
        debugTaskInfo.getDebugNodes().forEach(key -> {
            JSONObject node = debugTaskInfo.getScene().getNodes().getJSONObject(key);
            if (Boolean.FALSE.equals(node.getBoolean("gatewaySource"))) {
                nodes.put(key, node);
            }
        });
        if (nodes.isEmpty()) {
            return;
        }
        String globalEnvName = UseCaseFactoryTypeEnum.SCENE.getCode() == debugTaskInfo.getSceneType() ?
                debugTaskInfo.getEnvName() : null;
        this.setNonGatewayApiEnvUrl(nodes, debugTaskInfo.getProductCode(), globalEnvName);
        debugTaskInfo.getScene().getNodes().putAll(nodes);
    }

    private void setDebugNodes(DebugTaskInfo debugTaskInfo) {
        List<String> debugNodes = debugTaskInfo.getLinkBaseInfo().stream()
                .filter(linkBaseInfo -> "NODE".equals(linkBaseInfo.getLinkComponentType()))
                .map(SceneLinkInfoEntityDO::getLinkComponentCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(debugNodes)) {
            throw new ServiceException("获取调试节点异常！");
        }
        debugTaskInfo.setDebugNodes(debugNodes);
    }

    public void generateDataCenterVariableFile(String productCode, String sceneCode, String sceneOssPath) {
        Map<String, String> variableMap = new HashMap<>();
        this.addVariableMap(productCode, sceneCode, variableMap);

        String ossPath = sceneOssPath + "/edit/variable/";
        boolean result = ztoOssService.createObject("autojmx", ossPath + "dataCenterVariable.json",
                JSONObject.toJSONString(variableMap));
        log.info("生成变量数据文件 {}：{}", ossPath + "dataCenterVariable.json", result);
    }

    public void generateSceneLinkDataFile(String sceneCode, Integer sceneVersion, String sceneOssPath, String productCode) {
        // 链路List
        List<SceneLinkInfoEntityDO> linkInfoList = linkMapRepository.getLinkInfoBySceneCode(sceneCode, sceneVersion);
        if (CollectionUtil.isEmpty(linkInfoList)) {
            throw new ServiceException("该场景下链路为空！场景code：" + sceneCode);
        }
        // 根据链路code给节点分组
        Map<String, List<SceneLinkInfoEntityDO>> linkMap = linkInfoList.stream()
                .filter(x -> x.getLinkComponentType().equals("NODE"))
                .collect(Collectors.groupingBy(SceneLinkInfoEntityDO::getLinkMapCode));
        if (null == linkMap || CollectionUtil.isEmpty(linkMap.keySet())) {
            throw new ServiceException("链路为空！场景code：" + sceneCode);
        }
        String ossPath = sceneOssPath + "/edit/data/";
        // 删除过期数据文件
        List<String> dataFileList = ztoOssService.getListObjectKey("autojmx", ossPath);
        if (CollectionUtil.isNotEmpty(dataFileList)) {
            dataFileList.forEach(path -> ztoOssService.cleanObject("autojmx", path));
        }
        // 获取节点用例文件
        List<ApiCaseEntityDO> nodeCaseList;
        try {
            String content = ztoOssService.getObjectTextWithRange("autojmx", sceneOssPath + "/edit/nodeCase.json");
            nodeCaseList = JSONArray.parseArray(content, ApiCaseEntityDO.class);
        } catch (IOException e) {
            throw new ServiceException("获取节点用例数据失败！" + e.getMessage());
        }
        for (String linkCode : linkMap.keySet()) {
            // 节点List
            List<String> nodeList = linkMap.get(linkCode).stream()
                    .map(SceneLinkInfoEntityDO::getLinkComponentCode).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(nodeList)) {
                throw new ServiceException("该链路下不存在节点！链路code：" + linkCode);
            }
            // 根据节点code给数据分组
            Map<String, List<ApiCaseEntityDO>> nodeMap = nodeCaseList.stream()
                    .filter(nodeCase -> nodeList.contains(nodeCase.getNodeCode()))
                    .collect(Collectors.groupingBy(ApiCaseEntityDO::getNodeCode));
            if (null == nodeMap) {
                throw new ServiceException("笛卡尔数据异常！链路code：" + linkCode);
            }
            // 数据笛卡尔积
            List<Map<String, Map<String, Object>>> dataList = getDataDescartes(nodeMap, productCode);
            // oss上传文件
            boolean result = ztoOssService.createObject("autojmx",
                    ossPath + linkCode + ".json",
                    JSONObject.toJSONString(dataList));
            log.info("生成链路测试数据文件 {}：{}", ossPath + linkCode + ".json", result);
        }
    }

    /**
     * 链路下所有节点数据笛卡尔积
     */
    private List<Map<String, Map<String, Object>>> getDataDescartes(Map<String, List<ApiCaseEntityDO>> nodeMap, String productCode) {
        List<Map<String, Map<String, Object>>> tempList = new ArrayList<>();
        for (String nodeCode : nodeMap.keySet()) {
            List<Map<String, Map<String, Object>>> deCartList = dealNodeMap(nodeMap.get(nodeCode), false, null, productCode);
            if (CollectionUtil.isNotEmpty(deCartList)) {
                if (CollectionUtil.isEmpty(tempList)) {
                    tempList = deCartList;
                } else {
                    tempList = tempList.stream().flatMap(termMap -> {
                        List<Map<String, Map<String, Object>>> sumList = new ArrayList<>();
                        for (Map<String, Map<String, Object>> deCartMap : deCartList) {
                            Map<String, Map<String, Object>> map = new HashMap<>();
                            map.putAll(deCartMap);
                            map.putAll(termMap);
                            sumList.add(map);
                        }
                        return sumList.stream();
                    }).collect(Collectors.toList());
                }
            }
        }
        return tempList;
    }

    /**
     * 节点数据处理
     */
    private List<Map<String, Map<String, Object>>> dealNodeMap(List<ApiCaseEntityDO> caseList, boolean debug, String envUrl, String productCode) {
        List<Map<String, Map<String, Object>>> deCartList = new ArrayList<>();
        for (ApiCaseEntityDO caseDO : caseList) {
            if (StringUtils.isBlank(caseDO.getReqData())) {
                continue;
            }
            Map<String, Map<String, Object>> nodeMap = new HashMap<>();
            ApiTestDataVO reqData = JSONObject.parseObject(caseDO.getReqData(), ApiTestDataVO.class);
            Map<String, Object> caseMap = dealCaseMap(reqData, caseDO.getUserVariableCode(), debug, envUrl, productCode);
            nodeMap.put(caseDO.getNodeCode(), caseMap);
            if (ApiTypeEnum.DATA_CENTER.equals(reqData.getType())) {
                try {
                    SceneInfoEntityDO scene = apiTestRepository.queryLatestSceneInfo(
                            String.valueOf(caseMap.get("sceneCode")), SceneInfoStatusEnum.publish);
                    List<SceneLinkInfoEntityDO> link =
                            linkMapRepository.getLinkInfoBySceneCode(scene.getSceneCode(), scene.getSceneVersion());
                    String path = scene.getSceneOssPath() + "/publish/data/" + link.get(0).getLinkMapCode() + ".json";
                    String data = ztoOssService.getObjectTextWithRange("autojmx", path);
                    JSONObject nodeData = JSON.parseArray(data).getJSONObject(0);
                    nodeData.forEach((k, v) -> nodeMap.put(k, nodeData.getJSONObject(k).getInnerMap()));
                } catch (Exception e) {
                    log.error("获取造数节点[{}]数据文件异常！", caseDO.getNodeCode(), e);
                    throw new ServiceException("获取造数节点[" + caseDO.getNodeCode() + "数据文件异常！");
                }
            }
            deCartList.add(nodeMap);
        }
        return deCartList;
    }

    private Map<String, Object> dealCaseMap(
            ApiTestDataVO reqData, String userVariableCode, boolean debug, String envUrl, String productCode) {
        Map<String, Object> caseMap = new HashMap<>();
        if (null != reqData.getType()) {
            caseMap.put("type", reqData.getType().getValue());
        }
        if (CollectionUtil.isNotEmpty(reqData.getData())) {
            caseMap.put("data", GenerateApiCaseUtil.buildNodeData(reqData.getData()));
        }
        if (ApiTypeEnum.HTTP.equals(reqData.getType())) {
            String path = reqData.getPathUrl();
            if (StringUtils.isNotEmpty(path)) {
                if (CollectionUtil.isNotEmpty(reqData.getPathParams())) {
                    path = GenerateApiCaseUtil.replacePathValue(path, reqData.getPathParams());
                }
                if (StringUtils.isNotEmpty(reqData.getRequestBody())
                        && CollectionUtil.isNotEmpty(reqData.getParams())) {
                    path = GenerateApiCaseUtil.buildUrlParameter(path, reqData.getParams());
                }
                caseMap.put("path", path);
            }
            if (StringUtils.isEmpty(reqData.getRequestBody())) {
                caseMap.put("parameter", reqData.getParams());
            } else {
                caseMap.put("parameter", reqData.getRequestBody());
            }
            JSONObject headers = new JSONObject();
            if (null != reqData.getRequestHeader()) {
                headers = reqData.getRequestHeader();
            }
            if (StringUtils.isNotBlank(userVariableCode)) {
                this.addHeadersCookies(userVariableCode, reqData, headers, debug, envUrl, productCode);
            }
            caseMap.put("header", headers);
        }
        if (ApiTypeEnum.DUBBO.equals(reqData.getType())) {
            caseMap.put("args", reqData.getArgs());
            caseMap.put("attachmentArgs", reqData.getAttachmentArgs());
        }
        if (ApiTypeEnum.ZMS.equals(reqData.getType())) {
            caseMap.put("parameter", reqData.getMsgBody());
        }
        if (ApiTypeEnum.DATA_CENTER.equals(reqData.getType())) {
            caseMap.put("input", reqData.getInputParams());
            caseMap.put("output", reqData.getOutputParams());
            caseMap.put("sceneCode", reqData.getDataCenterSceneCode());
        }
        return caseMap;
    }

    private void addHeadersCookies(String userVariableCode, ApiTestDataVO reqData, JSONObject headers, Boolean debug, String envUrl, String productCode) {
        ApiTestVariableVO variable = apiTestRepository.getApiTestVariableByVariableCode(userVariableCode);
        if (Objects.isNull(variable)) {
            return;
        }
        String nameSpace = null;
        if (Boolean.FALSE.equals(reqData.getGatewaySource())) {
            if (debug) {
                nameSpace = this.getNameSpace(envUrl);
            } else {
                headers.put("cookie", userVariableCode + "_${ztoEnvNamespace}");
            }
        } else {
            nameSpace = this.getNameSpace(reqData.getRequestUrl());
        }
        this.addCookies(nameSpace, userVariableCode, variable, headers, productCode);
    }

    private void addCookies(String nameSpace, String userVariableCode, ApiTestVariableVO variable, JSONObject headers, String productCode) {
        log.info(">>>>>>>nameSpace : {}, userVariableCode : {}, variable : {}", nameSpace, userVariableCode, JSON.toJSONString(variable));
        String cookieValue;
        // 技术中台特殊处理
        if (productCode.equals("PRO2202168000")) {
            cookieValue = "${" + userVariableCode + "}";
        } else {
            cookieValue = "${" + userVariableCode + "_" + nameSpace + "}";
        }
        if (variable.getSubVariableType().equals(SubVariableTypeEnum.SSO)) {
            headers.put("cookie", cookieValue);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.ZBASE)) {
            headers.put("cookie", cookieValue);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.SECRISK)) {
            headers.put("cookie", cookieValue);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.KDGJ)) {
            headers.put("cookie", cookieValue);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.ZZT)) {
            String tokenValue = StringUtils.isBlank(nameSpace) ? "${" + userVariableCode + "_" + X_TOKEN + "}" : "${" + userVariableCode + "_" + nameSpace + "_" + X_TOKEN + "}";
            String openIdValue = StringUtils.isBlank(nameSpace) ? "${" + userVariableCode + "_" + X_OPENID + "}" : "${" + userVariableCode + "_" + nameSpace + "_" + X_OPENID + "}";
            headers.put(X_TOKEN, tokenValue);
            headers.put(X_OPENID, openIdValue);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.CLIENT)) {
            headers.put("token", cookieValue);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.GH_THIRD)) {
            headers.put("cookie", cookieValue);
        }
    }

    /**
     * 增加数据驱动变量
     *
     * @param productCode
     * @param vo
     * @param variableMap
     */
    public void addDataDriven(String productCode, ApiTestVariableVO vo, Map<String, String> variableMap) {
        if (!redisService.hasKey(vo.getProductCode() + "_" + vo.getVariableKey())) {
            this.initCacheVariable(productCode, vo.getVariableKey(), vo.getVariableValue());
        }
        String variableInfo = redisService.getKey(vo.getProductCode() + "_" + vo.getVariableKey());
        log.info("变量{} = {}", vo.getProductCode() + "_" + vo.getVariableKey(), variableInfo);
        JSONObject object = JSONObject.parseObject(variableInfo);
        int total = object.getInteger(TOTAL);
        int current = object.getInteger(CURRENT);
        if (current > total) {
            current = 1;
        }
        String variable = redisService.getKey(vo.getProductCode() + "_" + vo.getVariableKey() + "_" + current);
        log.info("变量{} = {}", vo.getProductCode() + "_" + vo.getVariableKey() + "_" + current, variable);
        variableMap.put(vo.getVariableKey(), variable);
        JSONObject variableObj = JSONObject.parseObject(variable);
        if (Objects.isNull(variableObj)) {
            return;
        }
        for (Map.Entry<String, Object> entry : variableObj.entrySet()) {
            log.info("{} = {}", entry.getKey(), entry.getValue());
            variableMap.put(vo.getVariableKey() + "." + entry.getKey(), String.valueOf(entry.getValue()));
        }
        this.modifyCacheVariable(productCode, vo.getVariableKey(), total, current + 1);
    }

    /**
     * 添加变量集合
     *
     * @param productCode
     * @param sceneCode
     * @param variableMap
     */
    public void addVariableMap(String productCode, String sceneCode, Map<String, String> variableMap) {
        List<ApiTestVariableVO> apiTestVariableVOList = apiTestRepository.querySceneVariable(productCode, sceneCode, VariableTypeEnum.VARIABLE, SubVariableTypeEnum.CUSTOM, Arrays.asList(VariableUsageTypeEnum.UNKNOWN));
        if (CollectionUtil.isEmpty(apiTestVariableVOList)) {
            return;
        }
        apiTestVariableVOList.stream().forEach(vo -> {
            if (StringUtil.isNotEmpty(vo.getVariableValue()) && vo.getVariableValue().startsWith("${__dataExclusiveLock")) {
                this.addDataDriven(productCode, vo, variableMap);
            }
            if (vo.getSceneType() == 2) {
                variableMap.put(vo.getVariableKey(), vo.getVariableValue());
            }
        });
    }

    /**
     * 初始化变量存缓存
     *
     * @param productCode
     * @param key
     * @param value
     */
    public void initCacheVariable(String productCode, String key, String value) {
        try {
            String newVariableValue = value.substring(22, value.length() - 2);
            JSONArray variableArr = JSONObject.parseArray(newVariableValue);
            this.modifyCacheVariable(productCode, key, variableArr.size(), 1);
            for (int i = 0; i < variableArr.size(); i++) {
                redisService.setKey(productCode + "_" + key + "_" + (i + 1), variableArr.getString(i));
            }
        } catch (Exception e) {
            log.error("解析变量值异常。", e);
            throw new ServiceException("解析变量值异常");
        }
    }

    private void modifyCacheVariable(String productCode, String key, int total, int current) {
        JSONObject object = new JSONObject();
        object.put(TOTAL, total);
        object.put(CURRENT, current);
        redisService.setKey(productCode + "_" + key, object.toJSONString());
    }

    public void setNonGatewayApiEnvUrl(JSONObject nodes, String productCode, String globalEnvName) {
        List<GlobalEnvVO> globalEnvList = metaDataService.getDocGlobalEnv(productCode);
        nodes.keySet().forEach(key -> {
            JSONObject node = nodes.getJSONObject(key);
            String envName = StringUtils.defaultIfEmpty(globalEnvName, node.getString("envName"));
            if (StringUtils.isEmpty(envName)) {
                return;
            }
            String envUrl = Optional.ofNullable(globalEnvList).orElse(Collections.emptyList()).stream()
                    .filter(globalEnv -> globalEnv.getEnvName().equals(envName))
                    .map(GlobalEnvVO::getEnvUrl)
                    .findAny()
                    .orElse(null);
            if (StringUtils.isEmpty(envUrl)) {
                throw new ServiceException("非网关接口请在zbase-文档中配置环境域名，环境名称和一站式空间名称对应=" + envName);
            }
            JSONObject sampler = node.getJSONObject("sampler");
            try {
                URL url = new URL(envUrl);
                sampler.put("protocol", url.getProtocol());
                sampler.put("serverNameOrIp", url.getHost());
                String ztoEnvPath = url.getPath();
                if (ztoEnvPath.endsWith("/")) {
                    ztoEnvPath = StringUtils.substringBeforeLast(ztoEnvPath, "/");
                }
                String pathUrl = sampler.getString("pathUrl").replace("${ztoEnvPath}", ztoEnvPath);
                sampler.put("pathUrl", pathUrl);
                sampler.put("portNumber", url.getPort());
            } catch (MalformedURLException e) {
                throw new ServiceException("环境域名解析失败！" + envUrl);
            }
        });
    }

    /**
     * 获取造数节点的用户信息
     *
     * @param dataCenterNode
     * @param map
     */
    public void getDataCenterUsers(Node dataCenterNode, Map<String, String> map, Boolean isDebug, String productCode) {
        DataCenterRequestComponent dataCenterRequestComponent = JSON.toJavaObject(dataCenterNode.getSampler(), DataCenterRequestComponent.class);
        if (StringUtil.isEmpty(dataCenterRequestComponent.getDataCenterSceneCode())) {
            return;
        }
        SceneInfoEntityDO sceneInfo;
        if (isDebug) {
            QueryLatestEditSceneInfoReq req = new QueryLatestEditSceneInfoReq();
            req.setSceneCode(dataCenterRequestComponent.getDataCenterSceneCode());
            sceneInfo = apiTestRepository.queryLatestEditSceneInfo(req);
        } else {
            sceneInfo = apiTestRepository.queryLatestSceneInfo(dataCenterRequestComponent.getDataCenterSceneCode(), SceneInfoStatusEnum.publish);
        }
        Scene scene = JSON.parseObject(sceneInfo.getSceneBackData(), Scene.class);
        List<Node> nodeList = scene.getNodes().keySet().stream()
                .map(key -> JSON.parseObject(scene.getNodes().getString(key), Node.class))
                .collect(Collectors.toList());
        nodeList.stream()
                .filter(node -> StringUtils.isNotEmpty(node.getUser()))
                .forEach(node -> {
                    String nameSpace;
                    if (Boolean.FALSE.equals(node.getGatewaySource())) {
                        nameSpace = this.getNameSpace(node.getSampler().getString("serverNameOrIp"));
                    } else {
                        nameSpace = this.getNameSpace(node.getRequestUrl());
                    }
                    if (productCode.equals("PRO2202168000")) {
                        map.put(node.getUser(), nameSpace);
                    } else {
                        map.put(node.getUser() + "_" + nameSpace, nameSpace);
                    }
                });
    }

    /**
     * 设置用户cookies
     *
     * @param userMap
     * @param variableMap
     */
    public void setUserCookies(Map<String, String> userMap, Map<String, String> variableMap, String productCode) {
        userMap.forEach((user, namespace) -> {
            try {
                ApiTestVariableVO variable = apiTestRepository.getApiTestVariableByVariableCode(user.split("_")[0]);
                if (null == variable) {
                    log.error("变量[{}]不存在！", user);
                    throw new ServiceException("变量[" + user + "]不存在！");
                }
                Map<String, String> cookieMap = authCookieDomainService.getDataToLogin(variable, namespace, productCode);
                if (MapUtils.isNotEmpty(cookieMap)) {
                    cookieMap.forEach((String key, String value) -> {
                        variableMap.put(key, value);
                    });
                }
            } catch (Exception e) {
                log.error("变量[{}]获取cookie失败！", user, e);
                throw new ServiceException("变量[" + user + "]获取cookie失败！" + e.getMessage());
            }
        });
    }

    public DebugApiTaskInfo debugApi(ApiDebugReq req, User user) {
        ApiTestCaseEntityDO apiCase = apiTestRepository.queryApiCaseByCodeAndStatus(req.getCaseCode(), ApiCaseStatusEnum.edit);
        if (null == apiCase) {
            throw new ServiceException("用例[" + req.getCaseCode() + "]不存在！");
        }
        if (StringUtil.isEmpty(apiCase.getApiCode())) {
            throw new ServiceException("用例[" + req.getCaseCode() + "]未选择接口！");
        }
        ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(apiCase.getApiCode());
        if (null == apiTest) {
            throw new ServiceException("用例[" + req.getCaseCode() + "]接口不存在！");
        }
        List<ApiTestCaseEntityDO> exceptionList = apiTestRepository.queryExceptionCaseByCodeList(req.getExceptionCodeList());
        if (CollectionUtil.isNotEmpty(exceptionList)) {
            exceptionList.sort(Comparator.comparingInt(i -> req.getExceptionCodeList().indexOf(i.getCaseCode())));
        }
        ApiTestCaseEntityDO normal = Optional.ofNullable(
                apiTestRepository.queryNormalCaseByParentCode(apiCase.getCaseCode())).orElse(apiCase);
        DebugApiTaskInfo debugApiTaskInfo = this.setDebugApiTaskInfo(apiCase, normal, exceptionList, null);
        debugApiTaskInfo.setZtoenv(req.getZtoenv());
        debugApiTaskInfo.setEnvName(req.getEnvName());
        DEBUG_TASK_EXECUTOR.execute(() -> {
            try {
                this.insertDebugRecord(debugApiTaskInfo.getProductCode(), debugApiTaskInfo.getTaskId(), user);
            } catch (Exception e) {
                log.error("", e);
            }
            boolean prepared = true;
            try {
                apiDebugLogService.setApiDebugLog(debugApiTaskInfo.getTaskId(), debugApiTaskInfo.getTaskId(), false);
                apiDebugLogService.setApiDebugLog(debugApiTaskInfo.getTaskId(), "开始准备数据文件...", false);
                log.info("开始准备数据文件 >>> {}", debugApiTaskInfo.getTaskId());
                this.preparedApiCaseData(debugApiTaskInfo, apiTest, normal, exceptionList, "Debug");
                log.info("数据文件准备完成 >>> {}", debugApiTaskInfo.getTaskId());
                apiDebugLogService.setApiDebugLog(debugApiTaskInfo.getTaskId(), "数据文件准备完成...", false);
            } catch (Exception ex) {
                prepared = false;
                log.error("数据文件准备异常 >>> {}", debugApiTaskInfo.getTaskId(), ex);
                DebugTaskResp<String> debugTaskResp = new DebugTaskResp<>();
                debugTaskResp.setTaskId(debugApiTaskInfo.getTaskId());
                debugTaskResp.setType(DebugTaskRespTypeEnum.Exception);
                debugTaskResp.setData(ex.getMessage());
                apiDebugLogService.setApiDebugLog(debugApiTaskInfo.getTaskId(), "数据文件准备异常：" + ex.getMessage(), false);
                debugCallBackEnd(debugApiTaskInfo.getTaskId(), debugTaskResp, 2000L, false);
            }
            if (prepared) {
                log.info("开始调用debug的任务调度器...... >>> {}", debugApiTaskInfo.getTaskId());
                apiDebugLogService.setApiDebugLog(debugApiTaskInfo.getTaskId(), "开始调用debug的任务调度器.....", false);
                try {
                    DebugTaskInfo debugTaskInfo = new DebugTaskInfo();
                    debugTaskInfo.setTaskId(debugApiTaskInfo.getTaskId());
                    debugTaskInfo.setZtoenv(debugApiTaskInfo.getZtoenv());
                    debugTaskInfo.setProductCode(debugApiTaskInfo.getProductCode());
                    debugTaskInfo.setRequestType("APIDEBUG");
                    qcDebugService.execute(debugTaskInfo, false);
                    log.info("调用debug的任务调度器完成...... >>> {}", debugApiTaskInfo.getTaskId());
                    apiDebugLogService.setApiDebugLog(debugApiTaskInfo.getTaskId(), "调试任务已经成功提交到debug.....", false);
                } catch (Exception ex) {
                    log.error("调试脚本debug调用异常 >>> {}", debugApiTaskInfo.getTaskId(), ex);
                    DebugTaskResp<String> debugTaskResp = new DebugTaskResp<>();
                    debugTaskResp.setTaskId(debugApiTaskInfo.getTaskId());
                    debugTaskResp.setType(DebugTaskRespTypeEnum.Exception);
                    debugTaskResp.setData(ex.getMessage());
                    apiDebugLogService.setApiDebugLog(debugApiTaskInfo.getTaskId(), "调试脚本debug调用异常：" + ex.getMessage(), false);
                    debugCallBackEnd(debugApiTaskInfo.getTaskId(), debugTaskResp, 2000L, false);
                }
            }
            /**
             * 清除机器上的执行文件*
             */
            try {
                Thread.sleep(2000L);
                String[] command = new String[]{
                        "rm",
                        "-rf",
                        "/data/jmeter/" + debugApiTaskInfo.getTaskId() + "/"
                };
                ProcessManager.execute(command, debugApiTaskInfo.getTaskId(), ProcessManager.flagTaskRM);
                if (ProcessManager.processHandel.containsKey(debugApiTaskInfo.getTaskId() + ProcessManager.flagTaskRM)) {

                    Process process = ProcessManager.processHandel.get(debugApiTaskInfo.getTaskId() + ProcessManager.flagTaskRM);
                    while (process.isAlive()) {
                        Thread.sleep(500L);
                    }
                    ProcessManager.processHandel.remove(debugApiTaskInfo.getTaskId() + ProcessManager.flagTaskRM);
                    process.destroy();
                }
            } catch (Exception ex) {
                log.error("", ex);
            }
        });
        return debugApiTaskInfo;
    }

    private DebugApiTaskInfo setDebugApiTaskInfo(
            ApiTestCaseEntityDO apiCase,
            ApiTestCaseEntityDO normal,
            List<ApiTestCaseEntityDO> exceptionList,
            String taskId) {
        if (StringUtils.isBlank(taskId)) {
            taskId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        }
        DebugApiTaskInfo debugApiTaskInfo = new DebugApiTaskInfo();
        debugApiTaskInfo.setTaskId(taskId);
        debugApiTaskInfo.setCaseCode(apiCase.getCaseCode());
        debugApiTaskInfo.setCaseName(apiCase.getCaseName());
        debugApiTaskInfo.setProductCode(apiCase.getProductCode());
        List<DebugApiCaseData> caseDataList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(exceptionList)) {
            for (int i = 0; i < exceptionList.size(); i++) {
                DebugApiCaseData apiCaseData = new DebugApiCaseData();
                apiCaseData.setIndex(i);
                apiCaseData.setCaseCode(exceptionList.get(i).getCaseCode());
                apiCaseData.setCaseName(exceptionList.get(i).getCaseName());
                caseDataList.add(apiCaseData);
            }
        }
        DebugApiCaseData apiCaseData = new DebugApiCaseData();
        apiCaseData.setIndex(caseDataList.size());
        apiCaseData.setCaseCode(normal.getCaseCode());
        apiCaseData.setCaseName(normal.getCaseName());
        caseDataList.add(apiCaseData);
        debugApiTaskInfo.setCaseDataList(caseDataList);
        return debugApiTaskInfo;
    }

    private void preparedApiCaseData(
            DebugApiTaskInfo debugApiTaskInfo,
            ApiTestEntityDO apiTest,
            ApiTestCaseEntityDO normal,
            List<ApiTestCaseEntityDO> exceptionList,
            String executeType) throws Exception {

        File file = new File(debugPathHome + debugApiTaskInfo.getTaskId() + "/");
        if (!file.exists() && !file.isDirectory()) {
            file.mkdirs();
        }

        Node caseReqData = JSON.parseObject(normal.getCaseReqData(), Node.class);
        if (Boolean.FALSE.equals(caseReqData.getGatewaySource())) {
            JSONObject nodes = new JSONObject();
            nodes.put(normal.getCaseCode(), caseReqData);
            this.setNonGatewayApiEnvUrl(nodes, debugApiTaskInfo.getProductCode(), debugApiTaskInfo.getEnvName());
            caseReqData = nodes.getObject(normal.getCaseCode(), Node.class);
        }

        ApiTestDataVO reqData = GenerateApiCaseUtil.transApiNormalCase(caseReqData);
        String envUrl = caseReqData.getSampler().getString("serverNameOrIp");
        Map<String, Object> normalData = dealCaseMap(reqData, caseReqData.getUser(), true, envUrl, debugApiTaskInfo.getProductCode());
        normalData.put("dataName", normal.getCaseName());
        normalData.put("dataCode", normal.getCaseCode());
        this.setApiCaseData(normalData, debugApiTaskInfo.getCaseDataList().size() - 1, caseReqData.getSampler(), apiTest.getApiType());

        List<Map<String, Object>> exceptionDatas = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(exceptionList)) {
            for (int i = 0; i < exceptionList.size(); i++) {
                ApiCaseExceptionVO vo = JSON.parseObject(exceptionList.get(i).getCaseReqData(), ApiCaseExceptionVO.class);
                ApiTestDataVO data = GenerateApiCaseUtil.transApiExceptionCase(reqData, vo, apiTest);
                Map<String, Object> map = dealCaseMap(data, caseReqData.getUser(), true, envUrl, debugApiTaskInfo.getProductCode());
                map.put("dataName", exceptionList.get(i).getCaseName());
                map.put("dataCode", exceptionList.get(i).getCaseCode());
                this.setApiCaseData(map, i, caseReqData.getSampler(), apiTest.getApiType());
                map.put("resAssert", vo.getAsserts());
                exceptionDatas.add(map);
            }
        }

        if (CollectionUtil.isNotEmpty(caseReqData.getPreComponents())) {
            setJdbcComponentConfig(caseReqData.getPreComponents(), debugApiTaskInfo.getTaskId(), file.getPath());
            setRedisComponentConfig(caseReqData.getPreComponents(), debugApiTaskInfo.getTaskId(), file.getPath());
            setEsComponentConfig(caseReqData.getPreComponents(), debugApiTaskInfo.getTaskId(), file.getPath());
        }
        if (CollectionUtil.isNotEmpty(caseReqData.getPostComponents())) {
            setIfAssertComponent(caseReqData.getPostComponents());
            setJdbcComponentConfig(caseReqData.getPostComponents(), debugApiTaskInfo.getTaskId(), file.getPath());
            setRedisComponentConfig(caseReqData.getPostComponents(), debugApiTaskInfo.getTaskId(), file.getPath());
            setEsComponentConfig(caseReqData.getPostComponents(), debugApiTaskInfo.getTaskId(), file.getPath());
            setJSONPostProcessorConfig(caseReqData.getPostComponents(), debugApiTaskInfo.getTaskId(), file.getPath());
        }

        Map<String, Object> data = new HashMap<>();
        data.put("taskCode", debugApiTaskInfo.getTaskId());
        data.put("productCode", debugApiTaskInfo.getProductCode());
        data.put("apiCode", apiTest.getApiCode());
        data.put("apiName", apiTest.getApiName());
        data.put("caseCode", debugApiTaskInfo.getCaseCode());
        data.put("caseName", debugApiTaskInfo.getCaseName());
        data.put("reqType", apiTest.getApiType());
        if (ApiTypeEnum.HTTP.equals(apiTest.getApiType())) {
            HttpRequestComponent sampler = caseReqData.getSampler().toJavaObject(HttpRequestComponent.class);
            data.put("reqMethod", sampler.getMethod());
            if (HTTP_REQUEST_COMPONENT_FORM_TYPE.equals(sampler.getType()) && StringUtils.isEmpty(sampler.getBodyData())) {
                data.put("paramType", "Form");
            } else {
                data.put("paramType", "Body");
            }
            data.put("followRedirects", sampler.getFollowRedirects());
            data.put("reqHeader", normalData.get("header"));
        }
        data.put("executeType", executeType);
        data.put("exceptionDatas", exceptionDatas);
        data.put("normalData", normalData);
        data.put("preComponents", caseReqData.getPreComponents());
        data.put("postComponents", caseReqData.getPostComponents());

        File dataFile = new File(file, "data/data.json");
        fileUtilService.makesureDirExists(dataFile.getParent());
        fileUtilService.write(JSON.toJSONString(Collections.singleton(data)), dataFile);
        log.info("数据文件保存成功_path: {}", dataFile.getPath());

        if (CollectionUtil.isNotEmpty(caseReqData.getDataCenter())) {
            getDataCenterSceneInfo(caseReqData.getDataCenter());
            File dataCenterFile = new File(file, "data/dataCenter.json");
            fileUtilService.makesureDirExists(dataFile.getParent());
            fileUtilService.write(caseReqData.getDataCenter().toJSONString(), dataCenterFile);
            log.info("造数文件保存成功_path: {}", dataCenterFile.getPath());

            JSONArray dataCenterArray = caseReqData.getDataCenter();
            for (int i = 0; i < dataCenterArray.size(); i++) {
                JSONObject dataCenterObj = dataCenterArray.getJSONObject(i);
                if (null == dataCenterObj) {
                    continue;
                }
                JSONArray inputArray = dataCenterObj.getJSONArray("input");
                if (CollectionUtils.isNotEmpty(inputArray)) {
                    JSONObject variableJson = new JSONObject();
                    String dataCenterCode = dataCenterObj.getString("dataCenterSceneCode");
                    // 造数变量
                    JSONObject dataCenterVariable = dealInputParameter(dataCenterCode, inputArray);
                    variableJson.put("dataCenterInputVariable", dataCenterVariable);
                    if (!variableJson.isEmpty()) {
                        File dataCenterPath = new File(file.getPath() + "/" + dataCenterCode + "/variable/");
                        if (!dataCenterPath.exists() && !dataCenterPath.isDirectory()) {
                            dataCenterPath.mkdirs();
                        }
                        File dataCenterInputFile = new File(dataCenterPath, "dataCenterInputVariable.json");
                        fileUtilService.makesureDirExists(dataFile.getParent());
                        fileUtilService.write(JSON.toJSONString(variableJson), dataCenterInputFile);
                    }
                }
            }
        }

        Map<String, String> variableMap = new HashMap<>();
        Map<String, String> userMap = new HashMap<>();
        if (StringUtil.isNotEmpty(caseReqData.getUser())) {
            String nameSpace;
            if (Boolean.FALSE.equals(caseReqData.getGatewaySource())) {
                nameSpace = this.getNameSpace(caseReqData.getSampler().getString("serverNameOrIp"));
            } else {
                nameSpace = this.getNameSpace(caseReqData.getRequestUrl());
            }
            userMap.put(caseReqData.getUser() + "_" + nameSpace, nameSpace);
        }
        this.setUserCookies(userMap, variableMap, debugApiTaskInfo.getProductCode());
        if (MapUtils.isNotEmpty(variableMap)) {
            File variableFile = new File(file, "variable.json");
            fileUtilService.write(JSON.toJSONString(variableMap), variableFile);
            log.info("变量文件保存成功_path: {}", variableFile.getPath());
        }

        Map<String, Map<String, String>> redisMap = this.getRedisConfig(debugApiTaskInfo.getProductCode(), Collections.singletonList(caseReqData));
        if (MapUtils.isNotEmpty(redisMap)) {
            File redisConfigFile = new File(file, "redisConfig.json");
            fileUtilService.write(JSON.toJSONString(Collections.singletonList(redisMap)), redisConfigFile);
            log.info("redis配置文件保存成功_path: {}", redisConfigFile.getPath());
        }
    }

    private void setApiCaseData(
            Map<String, Object> map,
            int index,
            JSONObject jsonObject,
            ApiTypeEnum apiType) {
        map.put("index", index);
        if (ApiTypeEnum.HTTP.equals(apiType)) {
            HttpRequestComponent sampler = jsonObject.toJavaObject(HttpRequestComponent.class);
            map.put("protocol", sampler.getProtocol());
            map.put("serverNameOrIp", sampler.getServerNameOrIp());
            map.put("pathUrl", sampler.getPathUrl());
        }
        if (ApiTypeEnum.DUBBO.equals(apiType)) {
            DubboRequestComponent sampler = jsonObject.toJavaObject(DubboRequestComponent.class);
            map.put("timeOut", sampler.getTimeout());
            map.put("version", sampler.getVersion());
            map.put("retries", sampler.getRetries());
            map.put("group", sampler.getGroup());
            map.put("interfaceName", sampler.getInterfaceName());
            map.put("method", sampler.getMethodName());
        }
    }

    public boolean executeApi(ApiDebugReq req, String taskId, List<ApiTestCaseEntityDO> caseDataList) throws Exception {
        ApiTestCaseEntityDO apiCase = apiTestRepository.queryApiCaseByCodeAndStatus(req.getCaseCode(), ApiCaseStatusEnum.publish);
        if (null == apiCase) {
            throw new ServiceException("用例[" + req.getCaseCode() + "]不存在！");
        }
        if (StringUtil.isEmpty(apiCase.getApiCode())) {
            throw new ServiceException("用例[" + req.getCaseCode() + "]未选择接口！");
        }
        ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(apiCase.getApiCode());
        if (null == apiTest) {
            throw new ServiceException("用例[" + req.getCaseCode() + "]接口不存在！");
        }
        List<ApiTestCaseEntityDO> exceptionList = caseDataList.stream()
                .filter(caseData -> ApiCaseTypeEnum.EXCEPTION_CASE.getCode() == caseData.getCaseType())
                .collect(Collectors.toList());
        ApiTestCaseEntityDO normal = caseDataList.stream()
                .filter(caseData -> ApiCaseTypeEnum.NORMAL_CASE.getCode() == caseData.getCaseType())
                .findAny().orElse(apiCase);

        DebugApiTaskInfo debugApiTaskInfo = this.setDebugApiTaskInfo(apiCase, normal, exceptionList, taskId);
        debugApiTaskInfo.setZtoenv(req.getZtoenv());
        debugApiTaskInfo.setEnvName(req.getEnvName());

        this.preparedApiCaseData(debugApiTaskInfo, apiTest, normal, exceptionList, "Execute");

        DebugTaskInfo debugTaskInfo = new DebugTaskInfo();
        debugTaskInfo.setTaskId(debugApiTaskInfo.getTaskId());
        debugTaskInfo.setZtoenv(debugApiTaskInfo.getZtoenv());
        debugTaskInfo.setProductCode(debugApiTaskInfo.getProductCode());
        debugTaskInfo.setRequestType("APITEST");
        boolean result = qcDebugService.executeApi(debugTaskInfo);
        /**
         * 清除机器上的执行文件*
         */
        try {
            Thread.sleep(2000L);
            String[] command = new String[]{
                    "rm",
                    "-rf",
                    "/data/jmeter/" + debugApiTaskInfo.getTaskId() + "/"
            };
            ProcessManager.execute(command, debugApiTaskInfo.getTaskId(), ProcessManager.flagTaskRM);
            if (ProcessManager.processHandel.containsKey(debugApiTaskInfo.getTaskId() + ProcessManager.flagTaskRM)) {

                Process process = ProcessManager.processHandel.get(debugApiTaskInfo.getTaskId() + ProcessManager.flagTaskRM);
                while (process.isAlive()) {
                    Thread.sleep(500L);
                }
                ProcessManager.processHandel.remove(debugApiTaskInfo.getTaskId() + ProcessManager.flagTaskRM);
                process.destroy();
            }
        } catch (Exception ex) {
            log.error("", ex);
        }

        return result;
    }

    private void getDataCenterSceneInfo(JSONArray dataCenters) {
        if (CollectionUtil.isEmpty(dataCenters)) {
            return;
        }
        for (int i = 0; i < dataCenters.size(); i++) {
            JSONObject dataCenterObj = dataCenters.getJSONObject(i);
            SceneInfoEntityDO scene = apiTestRepository.queryLatestSceneInfo(
                    String.valueOf(dataCenterObj.get("dataCenterSceneCode")), SceneInfoStatusEnum.publish);
            if (null == scene) {
                continue;
            }
            List<SceneLinkInfoEntityDO> link =
                    linkMapRepository.getLinkInfoBySceneCode(scene.getSceneCode(), scene.getSceneVersion());
            String path = scene.getSceneOssPath() + "/publish/data/" + link.get(0).getLinkMapCode() + ".json";
            String data;
            try {
                data = ztoOssService.getObjectTextWithRange("autojmx", path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            JSONObject nodeData = JSON.parseArray(data).getJSONObject(0);
            dataCenterObj.put("dataCenterDatas", nodeData);
            JSONArray input = dataCenterObj.getJSONArray("input");
            if (null != input && input.size() > 0) {
                for (int j = 0; j < input.size(); j++) {
                    input.getJSONObject(j).put("key", input.getJSONObject(j).get("name"));
                }
            }
            JSONArray output = dataCenterObj.getJSONArray("output");
            if (null != output && output.size() > 0) {
                for (int j = 0; j < output.size(); j++) {
                    output.getJSONObject(j).put("key", output.getJSONObject(j).get("name"));
                }
            }
        }
    }

    private void setJdbcComponentConfig(JSONArray components, String taskId, String path) throws IOException {
        for (int i = 0; i < components.size(); i++) {
            JSONObject component = components.getJSONObject(i);
            if (LinkMapTypeEnum.JDBC.name().equals(component.getString("type"))) {
                JSONObject physicalDbInfo = zbaseService.queryPhysicalDbInfo(component.getString("dbId"));
                if (Objects.isNull(physicalDbInfo)) {
                    throw new ServiceException("获取数据库物理信息异常：" + component.getString("dbId"));
                }
                JSONObject dbAccountInfo = zbaseService.queryDbAccountInfo(physicalDbInfo);
                if (Objects.isNull(dbAccountInfo) || !"SYS000".equals(dbAccountInfo.getString("statusCode"))) {
                    throw new ServiceException("获取数据库账户信息异常：" + component.getString("dbId"));
                }
                String driver, dbUrl;
                if ("mysql".equalsIgnoreCase(dbAccountInfo.getString("dbType").trim())) {
                    driver = "com.mysql.jdbc.Driver";
                    dbUrl = String.format("jdbc:mysql://%s:%s/%s", dbAccountInfo.getString("ip"), dbAccountInfo.getString("port"), dbAccountInfo.getString("schema"));
                } else {
                    driver = "oracle.jdbc.OracleDriver";
                    dbUrl = String.format("**************************", dbAccountInfo.getString("ip"), dbAccountInfo.getString("port"), dbAccountInfo.getString("schema"));
                }
                component.put("driver", driver);
                component.put("dataBaseUrl", dbUrl);
                component.put("userName", dbAccountInfo.getJSONObject("result").getString("username"));
                component.put("passWord", dbAccountInfo.getJSONObject("result").getString("password"));
                JSONArray variableExtraction = component.getJSONArray("variableExtraction");
                if (CollectionUtil.isNotEmpty(variableExtraction)) {
                    String filename = String.format("data/%s.json", aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                    File file = new File(path, filename);
                    fileUtilService.makesureDirExists(file.getParent());
                    fileUtilService.write(JSONArray.toJSONString(variableExtraction), file);
                    component.put("filePath", String.format("/home/<USER>/jenkins/debug/%s/%s", taskId, filename));
                }
            }
        }
    }

    private void setRedisComponentConfig(JSONArray components, String taskId, String path) throws Exception {
        for (int i = 0; i < components.size(); i++) {
            JSONObject component = components.getJSONObject(i);
            if (LinkMapTypeEnum.REDIS.name().equals(component.getString("type"))) {
                JSONArray variableExtraction = component.getJSONArray("variableExtraction");
                if (CollectionUtil.isNotEmpty(variableExtraction)) {
                    String filename = String.format("data/%s.json", aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                    File file = new File(path, filename);
                    fileUtilService.makesureDirExists(file.getParent());
                    fileUtilService.write(component.toJSONString(), file);
                    component.put("filePath", String.format("/home/<USER>/jenkins/debug/%s/%s", taskId, filename));
                }
            }
        }
    }

    private void setEsComponentConfig(JSONArray components, String taskId, String path) throws Exception {
        for (int i = 0; i < components.size(); i++) {
            JSONObject component = components.getJSONObject(i);
            if (LinkMapTypeEnum.ES.name().equals(component.getString("type"))) {
                JSONArray variableExtraction = component.getJSONArray("variableExtraction");
                if (CollectionUtil.isNotEmpty(variableExtraction)) {
                    ProxyExecDTO dto = new ProxyExecDTO();
                    dto.setPath(component.getString("path"));
                    dto.setMethod(component.getString("requestType"));
                    dto.setClusterId(component.getLong("clusterId"));
                    dto.setUserName(Strings.EMPTY);
                    dto.setBodyString(component.getJSONObject("script"));
                    component.put("proxyExecBodyJsonStr", JSON.toJSONString(dto));
                    String filename = String.format("data/%s.json", aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                    File file = new File(path, filename);
                    fileUtilService.makesureDirExists(file.getParent());
                    fileUtilService.write(component.toJSONString(), file);
                    component.put("filePath", String.format("/home/<USER>/jenkins/debug/%s/%s", taskId, filename));
                }
            }
        }
    }

    private void setJSONPostProcessorConfig(JSONArray components, String taskId, String path) throws Exception {
        for (int i = 0; i < components.size(); i++) {
            JSONObject component = components.getJSONObject(i);
            if (LinkMapTypeEnum.EXTRACTOR_JSON.name().equals(component.getString("type"))) {
                JSONArray variableExtraction = component.getJSONArray("variableExtraction");
                if (CollectionUtil.isNotEmpty(variableExtraction)) {
                    Map<String, String> requestVarMaps = new HashMap<>();
                    Map<String, String> responseVarMaps = new HashMap<>();
                    variableExtraction.forEach(var -> {
                        JSONObject varObj = (JSONObject) var;
                        if (StringUtil.isEmpty(varObj.getString("name").trim()) || StringUtil.isEmpty(varObj.getString("value").trim())) {
                            return;
                        }
                        if ("responseData".equals(varObj.getString("source"))) {
                            responseVarMaps.put(varObj.getString("name").trim(), varObj.getString("value").trim());
                        }
                        if ("requestData".equals(varObj.getString("source"))) {
                            requestVarMaps.put(varObj.getString("name").trim(), varObj.getString("value").trim());
                        }
                        if (varObj.getString("value").trim().indexOf("$responseData") == 0) {
                            responseVarMaps.put(varObj.getString("name").trim(), varObj.getString("value").trim().replaceFirst("responseData", ""));
                        }
                        if (varObj.getString("value").trim().indexOf("$requestData") == 0) {
                            requestVarMaps.put(varObj.getString("name").trim(), varObj.getString("value").trim().replaceFirst("requestData", ""));
                        }
                    });
                    if (MapUtils.isNotEmpty(responseVarMaps) && !responseVarMaps.isEmpty()) {
                        JSONObject responseExtractJson = buildExtractorJson(responseVarMaps);
                        String responseExtractFilename = String.format("data/%s.json", aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                        File responseExtractFile = new File(path, responseExtractFilename);
                        fileUtilService.makesureDirExists(responseExtractFile.getParent());
                        fileUtilService.write(responseExtractJson.toJSONString(), responseExtractFile);
                        component.put("responseExtract", "true");
                        component.put("responseExtractFilePath", String.format("/home/<USER>/jenkins/debug/%s/%s", taskId, responseExtractFilename));
                    } else {
                        component.put("responseExtract", "false");
                    }
                    if (MapUtils.isNotEmpty(requestVarMaps) && !requestVarMaps.isEmpty()) {
                        JSONObject requestExtractJson = buildExtractorJson(requestVarMaps);
                        String requestExtractFilename = String.format("data/%s.json", aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                        File requestExtractFile = new File(path, requestExtractFilename);
                        fileUtilService.makesureDirExists(requestExtractFile.getParent());
                        fileUtilService.write(requestExtractJson.toJSONString(), requestExtractFile);
                        component.put("requestExtract", "true");
                        component.put("requestExtractFilePath", String.format("/home/<USER>/jenkins/debug/%s/%s", taskId, requestExtractFilename));
                    } else {
                        component.put("requestExtract", "false");
                    }
                }
            }
        }
    }

    private JSONObject buildExtractorJson(Map<String, String> varMaps) {
        JSONObject extractJson = new JSONObject();
        String referenceNames = "";
        String jsonPathExprs = "";
        String matchNumbers = "";
        String defaultValues = "";
        List<String> varsNames = new ArrayList<>();
        for (String varName : varMaps.keySet()) {
            if (StringUtil.isEmpty(referenceNames)) {
                referenceNames = referenceNames + varName;
                jsonPathExprs = jsonPathExprs + varMaps.get(varName);
                matchNumbers = matchNumbers + "1";
                defaultValues = defaultValues + " ";
            } else {
                referenceNames = referenceNames + ";" + varName;
                jsonPathExprs = jsonPathExprs + ";" + varMaps.get(varName);
                matchNumbers = matchNumbers + ";1";
                defaultValues = defaultValues + "; ";
            }
            varsNames.add(varName);
        }
        extractJson.put("referenceNames", referenceNames);
        extractJson.put("jsonPathExpr", jsonPathExprs);
        extractJson.put("matchNumbers", matchNumbers);
        extractJson.put("defaultValues", defaultValues);
        extractJson.put("varsNames", JSON.toJSONString(varsNames));
        return extractJson;
    }

    private void setIfAssertComponent(JSONArray components) {
        for (int i = 0; i < components.size(); i++) {
            JSONObject component = components.getJSONObject(i);
            if (LinkMapTypeEnum.IF_ASSERT_BEANSHELL.name().equals(component.getString("type"))) {
                JSONArray conditionExpected = component.getJSONArray("conditionExpected");
                if (CollectionUtil.isNotEmpty(conditionExpected)) {
                    StringBuilder assertCommand = new StringBuilder();
                    for (int j = 0; j < conditionExpected.size(); j++) {
                        JSONObject assertObject = conditionExpected.getJSONObject(j);
                        if (null != assertObject
                                && StringUtils.isNotBlank(assertObject.getString("condition"))
                                && StringUtils.isNotBlank(assertObject.getString("expected"))) {
                            assertCommand.append("if(").append(assertObject.getString("condition"))
                                    .append("){\n").append(assertObject.getString("expected"))
                                    .append(";\n").append("}\n");
                        }
                    }
                    component.put("conditionExpected", assertCommand.toString());
                }
            }
        }
    }

    /**
     * 插入调试记录表
     *
     * @param productCode
     * @param taskId
     */
    public void insertDebugRecord(String productCode, String taskId, User user) {
        SimpleQueryVO product = iProductRpcService.getProductVO(productCode);
        String productName = null == product ? "" : product.getProductName();
        TmDebugRecordEntityDO entityDO = new TmDebugRecordEntityDO();
        entityDO.setDebugCode(taskId);
        entityDO.setProductCode(productCode);
        entityDO.setProductName(productName);
        entityDO.setTaskId(taskId);
        entityDO.setStartTime(new Date());
        entityDO.setStatus(AutomaticStatusEnum.IN_PROGRESS);
        entityDO.preCreate(user);
        entityDO.preUpdate(user);
        tmDebugRecordRepository.insertDebugRecord(entityDO);
    }

    /**
     * 插入结果记录表
     *
     * @param productCode
     * @param taskId
     * @param sceneCode
     * @param user
     */
    private void saveSceneDebugInfo(String productCode, String taskId, String sceneCode, User user) {
        // scene_debug表插数据
        String recordCode = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        SaveDebugInfoCommand recordCommand = new SaveDebugInfoCommand(recordCode);
        recordCommand.setRecordCode(recordCode);
        recordCommand.setTaskId(taskId);
        recordCommand.setProductCode(productCode);
        recordCommand.setSceneCode(sceneCode);
        recordCommand.setBucketName("autojmx");
        recordCommand.setTransactor(user);
        tmSceneDebugRecordRepository.insertSelective(recordCommand);
    }

    /**
     * 更新调试记录
     *
     * @param taskId
     * @param status
     */
    public void updateDebugRecord(String taskId, AutomaticStatusEnum status) {
        TmDebugRecordEntityDO entityDO = tmDebugRecordRepository.queryDebugRecordByTaskId(taskId);
        if (null == entityDO) {
            return;
        }
        entityDO.setFinishTime(new Date());
        entityDO.setStatus(status);
        entityDO.setExecLogFile(String.format("%s/%s/%s/%s.log", config.getAmazonS3Config().getEndPoint(), "autojtl", taskId, taskId));
        entityDO.setGmtModified(new Date());
        tmDebugRecordRepository.updateDebugRecord(entityDO);
    }

    private AutomaticStatusEnum setAutomaticStatus(DebugTaskRespTypeEnum debugTaskRespType) {
        if (null == debugTaskRespType) {
            return AutomaticStatusEnum.UNKNOWN;
        }
        switch (debugTaskRespType) {
            case Exception:
                return AutomaticStatusEnum.ERROR;
            case SUCCESS:
                return AutomaticStatusEnum.SUCCESS;
            case FAILED:
                return AutomaticStatusEnum.FAIL;
            default:
                return AutomaticStatusEnum.UNKNOWN;
        }
    }

    private JSONObject addDataCenterVariable(DebugTaskInfo debugTaskInfo) {
        JSONObject variableJson = new JSONObject();
        if (UseCaseFactoryTypeEnum.CREATE.getCode() == debugTaskInfo.getSceneType()) {
            // 造数变量
            JSONObject dataCenterVariable = dealInputParameter(
                    debugTaskInfo.getSceneCode(),
                    debugTaskInfo.getScene().getInputParameter());
            variableJson.put("dataCenterInputVariable", dataCenterVariable);
            return variableJson;
        }
        // 遍历节点
        JSONObject nodes = debugTaskInfo.getScene().getNodes();
        if (null != nodes) {
            JSONObject dataCenterVariable = new JSONObject();
            nodes.keySet().forEach(key -> {
                JSONObject sampler = nodes.getJSONObject(key).getJSONObject("sampler");
                if (null != sampler && "DataCenter_REQUEST_COMPONENT".equals(sampler.getString("type"))) {
                    JSONArray inputArray = sampler.getJSONArray("inputParameter");
                    if (null != inputArray) {
                        dataCenterVariable.putAll(dealInputParameter(key, inputArray));
                    }
                }
            });
            if (!dataCenterVariable.isEmpty()) {
                variableJson.put("dataCenterInputVariable", dataCenterVariable);
            }
        }
        return variableJson;
    }


    private JSONObject dealInputParameter(String sceneCode, JSONArray inputParameter) {
        JSONObject dataCenterVariable = new JSONObject();
        JSONObject inputVariable = new JSONObject();
        if (null == inputParameter) {
            return dataCenterVariable;
        }
        for (int i = 0; i < inputParameter.size(); i++) {
            JSONObject parameter = inputParameter.getJSONObject(i);
            String key = parameter.getString("name");
            if ("e_input".equals(parameter.getString("variableType"))) {
                inputVariable.put(key, parameter.getString("value"));
            } else if ("e_select".equals(parameter.getString("variableType"))) {
                JSONObject valueObj = parameter.getJSONObject("value");
                if (null != valueObj) {
                    inputVariable.put(key + ".key", valueObj.getString("key"));
                    inputVariable.put(key + ".value", valueObj.get("value"));
                }
            } else if ("e_network".equals(parameter.getString("variableType"))) {
                String netWork = qcConfigBasicService.getSceneInputParameterNetwork();
                inputVariable.putAll(configParamValue(key, netWork, parameter));
            } else if ("e_personnel".equals(parameter.getString("variableType"))) {
                String personnel = qcConfigBasicService.getSceneInputParameterPersonnel();
                inputVariable.putAll(configParamValue(key, personnel, parameter));
            } else if ("e_department".equals(parameter.getString("variableType"))) {
                String department = qcConfigBasicService.getSceneInputParameterDepartment();
                inputVariable.putAll(configParamValue(key, department, parameter));
            } else if ("e_province".equals(parameter.getString("variableType"))) {
                String province = qcConfigBasicService.getSceneInputParameterProvince();
                inputVariable.putAll(configParamValue(key, province, parameter));
            } else if ("e_item".equals(parameter.getString("variableType"))) {
                inputVariable.putAll(configParamValue(key, "all", parameter));
            } else if ("e_label".equals(parameter.getString("variableType"))) {
                inputVariable.putAll(configParamValue(key, "all", parameter));
            } else if ("e_class".equals(parameter.getString("variableType"))) {
                inputVariable.putAll(configParamValue(key, "all", parameter));
            } else if ("e_store_brand".equals(parameter.getString("variableType"))) {
                inputVariable.putAll(configParamValue(key, "all", parameter));
            } else if ("e_store_name".equals(parameter.getString("variableType"))) {
                inputVariable.putAll(configParamValue(key, "all", parameter));
            }
        }
        dataCenterVariable.put(sceneCode, inputVariable);
        return dataCenterVariable;
    }

    private JSONObject configParamValue(String key, String config, JSONObject parameter) {
        JSONObject inputVariable = new JSONObject();
        if (StringUtils.isEmpty(config)) {
            return inputVariable;
        }
        JSONObject valueObj = parameter.getJSONObject("value");
        if (null == valueObj) {
            return inputVariable;
        }
        // 返回全部参数
        if ("all".equals(config)) {
            Set<String> valueKeySet = valueObj.keySet();
            for (String paramKey : valueKeySet) {
                if (StringUtils.isNotEmpty(paramKey)) {
                    inputVariable.put(key + "." + paramKey, valueObj.get(paramKey));
                }
            }
            return inputVariable;
        }
        // 返回配置的参数
        String[] paramArray = config.split(",");
        for (String paramKey : paramArray) {
            if (StringUtils.isNotEmpty(paramKey)) {
                inputVariable.put(key + "." + paramKey, valueObj.get(paramKey));
            }
        }
        return inputVariable;
    }
}
