package com.zto.devops.qc.domain.gateway.jenkins;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.jenkins.JenkinsBuild;
import com.zto.devops.qc.client.model.jenkins.JenkinsBuildDetail;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AnalysisAutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticNode;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseResultContentVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskExecutedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskTerminatedEvent;

import java.io.IOException;
import java.util.Date;
import java.util.List;

public interface IJenkinsService {

    String getResultFile(String ossPath, String filename);

    AutomaticTaskEntityDO execute(AutomaticTaskExecutedEvent event, List<AutomaticNode> execInfo);

    JSONObject getCaseFileContent(String caseFile) throws IOException;

    void terminate(AutomaticTaskTerminatedEvent event);

    boolean analyse(AnalysisAutomaticRecordVO vo, AutomaticRecordTypeEnum type);

    String getErrorLogFile(String ossPath);

    List<AutomaticNode> getCallbackFileContent(String filename) throws IOException;

    List<ExecuteCaseResultContentVO> getExecuteCaseResultContent(String resultFile);

    void uploadExcelToOss(String remote, String fileName, String local);

    AutomaticTaskEntityDO executeApiTest(AutomaticTaskExecutedEvent event);

    boolean executeDebug(DebugTaskInfo taskInfo, String dubboTag, String productName);

    List<JenkinsBuild> getBuilds(AutomaticRecordTypeEnum type);

    JenkinsBuildDetail getBuildDetail(AutomaticRecordTypeEnum type, String buildId);

    String getOssPath(Date date, String code);
}
