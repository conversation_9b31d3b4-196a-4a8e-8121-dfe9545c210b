package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.issue.event.*;

public interface IssueStorageService {
    void addIssue(IssueAddedEvent event);
    void updateIssue(IssueEditedEvent event);
    void updateIssue(IssueStartFixedEvent event);
    void updateIssue(IssueDelayFixedEvent event);
    void updateIssue(IssueDeliveryValidatedEvent event);
    void updateIssue(IssueValidatedAccessClosedEvent event);
    void updateIssue(IssueBackToRepairedEvent event);
    void updateIssue(IssueCirculationedEvent event);
    void updateIssue(IssueRefusedEvent event);
    void updateIssue(IssueReopenEvent event);
    void updateIssue(IssueConfirmClosedEvent event);
    void updateIssue(IssueRelatedRequirementEvent event);
    void removeIssue(IssueRemovedEvent event);
    void updateRelevantUser(RelevantUserAddedEvent event);
    void updateRelevantUser(RelevantUserRemovedSimpleEvent event);
    void addTag(TagAddedEvent event);
    void removeTag(TagRemovedSimpleEvent event);

}
