package com.zto.devops.qc.domain.gateway.jacoco;

import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoveragePublishVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public interface JacocoService {

    void socketConnect(FileOutputStream localFile, CoveragePublishVO entity, String ip) throws Exception;

    void executeMerge(String execPath);

    Map<String, Object> create(String sourcePath, final CoverageRecordGenerateVO entity, String localReportPath, String localPath, Logger logger, String uname, String pass, String products) throws IOException;

    void setLocalClassPath(CoverageRecordGenerateVO entity, String classPath);

    boolean isContainText(String recordUrl, String text) throws IOException;

    String getValueByReport(String recordUrl, String key) throws IOException;

    void copyJavaSrc(List<String> allSrcList, Map<String, List<String>> locatedSrc, File targetDir);
}