package com.zto.devops.qc.domain.gateway.util;

import com.alibaba.fastjson.JSONObject;

public interface ShellParseUtilService {

    String getAssertsAsString(JSONObject jsonObject);

    String getSqlAsString(String sqlStr);

    String getESRequestBodyAsString(String esBody);

    String getIfControllerLogScripts(String ifScript, String ifControllerComments);

    String getPollingWhileCondition();

    String getPollingIntervalScript(long interval);

    String getPollingConditionScript(String condition);

    String getPollingIgnoreScript();

    String getPollingAssertScript(String condition);
}
