package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.SceneLinkInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.ChangeLinkStatusCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.PageLinkMapVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageLinkMapReq;

import java.util.List;

public interface LinkMapRepository {

    List<String> queryLinkMapCodesBySceneCode(String sceneCode, Integer version);

    List<SceneLinkInfoEntityDO> getLinkInfo(String linkMapCode);

    PageLinkMapVO querySceneLink(PageLinkMapReq linkMapReq);

    void batchInsertLink(List<SceneLinkInfoEntityDO> list);

    List<SceneLinkInfoEntityDO> getLinkInfoBySceneCode(String sceneCode, Integer version);

    void updateSceneLinkInfoEnable(ChangeLinkStatusCommand command);

    void deleteBySceneInfo(String sceneCode, Integer version);
}
