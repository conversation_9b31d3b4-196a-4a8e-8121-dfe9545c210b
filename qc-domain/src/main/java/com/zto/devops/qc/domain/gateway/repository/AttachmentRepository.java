package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.event.AttachmentAddedEvent;
import com.zto.devops.qc.client.model.issue.event.AttachmentRemovedSimpleEvent;
import com.zto.devops.qc.client.model.issue.query.FindAttachmentByCodeQuery;
import com.zto.devops.qc.client.model.issue.query.ListAttachmentsByBusinessCodeQuery;

import java.util.List;

public interface AttachmentRepository {


    /**
     * 查询附件集合
     *
     * @param code
     * @return
     */
    List<AttachmentVO> findListByBusinessCode(String code);

    /**
     * 查询附件集合 - query版
     *
     * @param query
     * @return
     */
    List<AttachmentVO> query(ListAttachmentsByBusinessCodeQuery query);

    /**
     * 添加附件
     *
     * @param event
     */
    void handle(AttachmentAddedEvent event);

    /**
     * 根据code查询附件
     *
     * @param query
     * @return
     */
    AttachmentVO query(FindAttachmentByCodeQuery query);

    /**
     * 移除附件
     *
     * @param event
     */
    void handle(AttachmentRemovedSimpleEvent event);

    Integer getAttachmentCount(String code);

    void addAttachmentList(List<AttachmentVO> attachments, String businessCode, BaseEvent event);
}
