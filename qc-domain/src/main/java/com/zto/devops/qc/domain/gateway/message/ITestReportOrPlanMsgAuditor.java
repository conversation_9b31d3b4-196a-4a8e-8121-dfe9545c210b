package com.zto.devops.qc.domain.gateway.message;

import com.zto.devops.qc.client.model.message.TestPlanOrReportMsg;
import com.zto.devops.qc.client.model.testmanager.plan.event.SendTestPlanEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ReportAddedEvent;

public interface ITestReportOrPlanMsgAuditor {

    void sendMessage(TestPlanOrReportMsg testPlanOrReportMsg);

    TestPlanOrReportMsg doHandleReportAddedEvent(ReportAddedEvent reportAddedEvent);

    TestPlanOrReportMsg doHandleSendTestPlanEvent(SendTestPlanEvent event);
}
