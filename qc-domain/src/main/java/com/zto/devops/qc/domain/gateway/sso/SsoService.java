package com.zto.devops.qc.domain.gateway.sso;

import java.util.Map;

public interface SsoService {

    String getCookie(String key, String secret, String nameSpace) throws Exception;

    String getCookieGw(String key, String secret, String nameSpace) throws Exception;

    String getCookieKDGJ(String username, String password);

    Map<String, String> getOpenIdZZT(String key, String secret);

    String getCustomerClientToken(String username, String password);

    String getCookieGHThird(String key, String secret) throws Exception;

}
