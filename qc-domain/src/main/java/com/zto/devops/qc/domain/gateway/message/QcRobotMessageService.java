package com.zto.devops.qc.domain.gateway.message;

import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueDeliveryValidatedEvent;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.domain.model.Issue;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackEvent;

public interface QcRobotMessageService {
    void sendCoverageRateFailMessageEvent(CoverageRecordGenerateParameter parameter);

    void sendIssueMessageEvent(IssueAddedEvent event);

    void sendDeliveryIssueMessageEvent(IssueDeliveryValidatedEvent event, Issue issue);

    void sendScheduledFailMessageEvent(ExecuteCallbackEvent event);



}
