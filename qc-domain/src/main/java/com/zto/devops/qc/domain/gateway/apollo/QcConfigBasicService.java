package com.zto.devops.qc.domain.gateway.apollo;


import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.enums.testmanager.config.QcConfigEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.JmeterConfigVO;
import com.zto.devops.qc.client.model.testmanager.config.entity.ProjectProfileVO;
import com.zto.devops.qc.client.model.testmanager.config.entity.QcConfigVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.AmazonS3ConfigVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageConfigVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.GitLabConfigVO;

import java.util.List;

public interface QcConfigBasicService {

    /**
     * 获取配置
     *
     * @param qcConfigEnum
     * @return
     */
    QcConfigVO getConfig(QcConfigEnum qcConfigEnum);

    QcConfigVO getQcConfig();

    CoverageConfigVO getCoverageConfig();

    GitLabConfigVO getGitLabConfig();

    AmazonS3ConfigVO getAmazonS3ConfigIntranet();

    AmazonS3ConfigVO getAmazonS3Config();

    ProjectProfileVO getProjectProfileConfig();


    /**
     * 获取 callbackBucketName
     *
     * @return
     */
    String getCallbackBucketName();

    Long getCheckFileSize();

    String getHeartDubboTag();

    Integer getMediumSize();

    Integer getBigSize();

    Integer getMinSize();

    Integer getBusinessCount();

    Integer getProductTagMax();

    Integer getTestcaseTagMax();

    Integer getTestcaseImportCount();

    Integer getJobAutoTestAbortDuration();

    Integer getJobAnalysisAutomaticRecordAbortDuration();

    String getAutoJmxBucketName();

    JmeterConfigVO getJmeterConfig();

    Integer getJmeterDebugMaxCount();

    JSONObject getJmeterVariable();

    String getDubboZKTestAddress();

    String getZMSZKTestAddress();

    String getKnowledgeBaseUrl();

    String getDingDingKnowledgeBaseManagerUserId();

    String getApiMockBaseUrl();

    String getDomainConfig();

    String getCoverageInnerUrl();

    String getJmeterInitMethod();

    String getDebugWorkspace();

    Boolean getApiGatewayRule();

    String getSceneJmxUploadPermission();

    String getSceneInputParameterNetwork();

    String getSceneInputParameterPersonnel();

    String getSceneInputParameterDepartment();

    String getSceneInputParameterProvince();

    Integer getClearTestcaseDuration();

    String getDelayAcceptAuditSkipUrl();

    String getDelayAcceptAuditDoneSkipUrl();

    String getDelayAcceptAuditTitle();

    String getDelayAcceptAuditBizType();

    String getDelayAcceptAuditBizName();

    String getDelayAcceptAuditSubmitterCode();

    String getDelayAcceptAuditSubmitterName();

    String getChaosException();

    List<String> getEnableCoverageNamespace();

    Integer getDeleteCount();

}
