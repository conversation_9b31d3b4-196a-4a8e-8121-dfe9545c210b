package com.zto.devops.qc.client.model.testmanager.report.entity;

import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@GatewayModel(description = "安全测试结果")
@Data
public class PlanSecurityTestResultVO implements Serializable {

    @GatewayModelProperty(description = "安全测试人ID", required = false)
    private Long securityUserId;

    @GatewayModelProperty(description = "安全测试人名称", required = false)
    private String securityUserName;

    @GatewayModelProperty(description = "安全测试结果", required = false)
    private SecurityTestResult securityTestResult ;

    @GatewayModelProperty(description = "安全测试结果code", required = false)
    private String securityTestResultCode;

    @GatewayModelProperty(description = "安全测试结果描述", required = false)
    private String securityTestResultDesc;
}
