package com.zto.devops.qc.client.model.testmanager.report.entity;

import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ExternalTestReportDetailVO extends BaseReportInfoVO {

    @GatewayModelProperty(description = "是否包含zui应用（true：包含；false：不包含）")
    private Boolean zuiFlag;

    @GatewayModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    public ZUITestResultEnum getZuiTestResult() {
        if (null == this.zuiFlag || !zuiFlag) {
            return null;
        }
        return (null == this.zuiTestResult || ZUITestResultEnum.UNKNOWN.equals(this.zuiTestResult))
                ? ZUITestResultEnum.PASS
                : this.zuiTestResult;
    }

}
