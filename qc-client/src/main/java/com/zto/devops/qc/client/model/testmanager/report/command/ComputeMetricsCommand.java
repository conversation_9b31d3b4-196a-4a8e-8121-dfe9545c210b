package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ComputeMetricsCommand extends BaseCommand implements Serializable {

    /**
     * 版本编码
     */
    private List<String> versionCodes;

    public ComputeMetricsCommand(String aggregateId) {
        super(aggregateId);
    }
}
