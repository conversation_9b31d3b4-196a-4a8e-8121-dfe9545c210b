package com.zto.devops;

import com.alibaba.fastjson.JSON;
import com.zto.devops.qc.client.model.issue.entity.CountUserIssueNumVO;
import com.zto.devops.qc.domain.service.IssueCommandDomainService;
import com.zto.message.dto.clientobject.BatchMessageCO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.UUID;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class IssueTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "gaoyanqi2035");
        System.setProperty("env", "fat");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5664313,\"cnName\":\"高延奇\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\",\"userCode\":\"02100.3910\",\"empNo\":\"**********\"}");
    }

    @Autowired
    private IssueCommandDomainService issueCommandDomainService;

    @Test
    public void queryFixingIssue() {
        List<CountUserIssueNumVO> result = issueCommandDomainService.fixIssueMsg();
        log.info("result:{}", JSON.toJSONString(result));
        for (CountUserIssueNumVO vo : result) {
            if (vo.getHandleUserId() == 5952057l) {
                BatchMessageCO co = new BatchMessageCO();
                co.setBizId(UUID.randomUUID().toString());
                co.putPayload("count", vo.getCountNum());
                co.putPayload("receivedUser", vo.getHandleUserId());
                co.putPayload("linkUrl", "木哈哈哈哈哈哈哈");
            }
        }
    }

}
