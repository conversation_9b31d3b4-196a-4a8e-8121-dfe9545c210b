package com.zto.devops;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;

import java.util.HashMap;
import java.util.Map;

public class HutoolTest {
    public static void main(String[] args) {
        Map headers = new HashMap();
        HttpResponse response = HttpUtil.createGet("https://zbase-server.dev.ztosys.com/_index/database/view/dblresource/64/fat/physicaldbinfo")
                .addHeaders(headers)
                .execute();
        System.out.println(response.body());
    }
}
