package com.zto.devops;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO;
import com.zto.devops.qc.domain.service.CoverageDomainService;
import com.zto.devops.qc.domain.service.InterfaceCoverageDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @create 2022/9/18 10:52
 */
@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class InterfaceCoverageTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("logging.level.com.zto", "debug");
        System.setProperty("titans.dubbo.tag", "env42180dev");
        System.setProperty("env", "fat");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5431563,\"cnName\":\"许建峰\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\"," +
                "\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
        System.setProperty("javaagent", "D:\\fat-agent\\2.9.11.2\\agent.jar");
        System.setProperty("zss.worker.config.label", "xjf");
    }

    @Autowired
    private CoverageDomainService coverageDomainService;
    @Autowired
    private InterfaceCoverageDomainService interfaceCoverageDomainService;


    private static final Logger logger = Logger.getLogger(CoverageDomainService.class.getName());

    @Test
    public void saveCoveragePublish() {
        String flowCode = "DINT241112003001";
        String planCode = "EXE241209002033";
        User user = new User(123456L, "xjf");
        List<JacocoApplicationVO> apps = coverageDomainService.checkApps(flowCode, planCode, "DeployEndedEvent");
        coverageDomainService.saveCoveragePublish(apps, user);
    }

    @Test
    public void getInterfaceCallsNumber() {
        interfaceCoverageDomainService.getInterfaceCallsNumberFromZcat();
    }


}
